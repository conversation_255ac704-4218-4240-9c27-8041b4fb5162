<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Obsidian 项目任务管理器 - 高保真原型</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      :root {
        /* 统一的设计系统变量 */
        --primary-color: #667eea;
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-color: #f7fafc;
        --accent-color: #48bb78;
        --warning-color: #ed8936;
        --danger-color: #f56565;

        /* 字体系统 */
        --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
          "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
        --font-size-xs: 12px;
        --font-size-sm: 14px;
        --font-size-base: 16px;
        --font-size-lg: 18px;
        --font-size-xl: 20px;
        --font-size-2xl: 24px;
        --font-size-3xl: 32px;

        /* 间距系统 */
        --spacing-1: 4px;
        --spacing-2: 8px;
        --spacing-3: 12px;
        --spacing-4: 16px;
        --spacing-5: 20px;
        --spacing-6: 24px;
        --spacing-7: 28px;
        --spacing-8: 32px;
        --spacing-10: 40px;
        --spacing-12: 48px;
        --spacing-16: 64px;

        /* 圆角系统 */
        --radius-sm: 6px;
        --radius-base: 8px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
        --radius-full: 9999px;

        /* 阴影系统 */
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
        --shadow-base: 0 4px 12px rgba(0, 0, 0, 0.08);
        --shadow-md: 0 8px 24px rgba(0, 0, 0, 0.12);
        --shadow-lg: 0 16px 40px rgba(0, 0, 0, 0.16);

        /* 颜色系统 */
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;

        /* 侧边栏宽度 */
        --sidebar-width: 280px;
        --sidebar-collapsed-width: 64px;
        --right-panel-width: 320px;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: var(--font-family);
        background: var(--gray-50);
        color: var(--gray-800);
        line-height: 1.6;
        font-size: var(--font-size-base);
      }

      .app-container {
        display: flex;
        height: 100vh;
        overflow: hidden;
      }

      /* 左侧导航栏 */
      .sidebar {
        width: var(--sidebar-width);
        background: white;
        border-right: 1px solid var(--gray-200);
        display: flex;
        flex-direction: column;
        box-shadow: var(--shadow-base);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 10;
      }

      .sidebar.collapsed {
        width: var(--sidebar-collapsed-width);
      }

      .sidebar.collapsed .sidebar-text {
        opacity: 0;
        visibility: hidden;
      }

      .sidebar.collapsed .nav-item {
        justify-content: center;
        padding: var(--spacing-3) var(--spacing-4);
      }

      .sidebar.collapsed .sidebar-toggle i {
        transform: rotate(180deg);
      }

      .sidebar-header {
        padding: var(--spacing-6);
        border-bottom: 1px solid var(--gray-200);
        position: relative;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--gray-900);
      }

      .logo-icon {
        width: var(--spacing-8);
        height: var(--spacing-8);
        background: var(--primary-gradient);
        border-radius: var(--radius-base);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: var(--font-size-sm);
        flex-shrink: 0;
      }

      .sidebar-toggle {
        position: absolute;
        right: var(--spacing-4);
        top: 50%;
        transform: translateY(-50%);
        width: var(--spacing-6);
        height: var(--spacing-6);
        border: none;
        background: var(--gray-100);
        border-radius: var(--radius-sm);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-600);
        transition: all 0.2s;
      }

      .sidebar-toggle:hover {
        background: var(--gray-200);
        color: var(--gray-800);
      }

      .nav-section {
        padding: var(--spacing-5) 0;
      }

      .nav-title {
        padding: 0 var(--spacing-6) var(--spacing-3);
        font-size: var(--font-size-xs);
        font-weight: 600;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s;
      }

      .sidebar.collapsed .nav-title {
        opacity: 0;
        visibility: hidden;
      }

      .nav-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-3) var(--spacing-6);
        color: var(--gray-600);
        text-decoration: none;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        border-left: 3px solid transparent;
        font-size: var(--font-size-sm);
        font-weight: 500;
        position: relative;
      }

      .nav-item:hover {
        background: var(--gray-50);
        color: var(--gray-800);
        transform: translateX(2px);
      }

      .nav-item.active {
        background: rgba(102, 126, 234, 0.08);
        color: var(--primary-color);
        border-left-color: var(--primary-color);
        font-weight: 600;
      }

      .nav-icon {
        width: var(--spacing-5);
        height: var(--spacing-5);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        font-size: var(--font-size-sm);
      }

      .sidebar-text {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* 主内容区 */
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .main-header {
        background: white;
        padding: var(--spacing-5) var(--spacing-8);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 72px;
        box-shadow: var(--shadow-sm);
      }

      .search-bar {
        flex: 1;
        max-width: 400px;
        position: relative;
      }

      .search-input {
        width: 100%;
        padding: var(--spacing-3) var(--spacing-4) var(--spacing-3)
          var(--spacing-10);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        background: var(--gray-50);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: var(--font-family);
      }

      .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .search-input::placeholder {
        color: var(--gray-400);
      }

      .search-icon {
        position: absolute;
        left: var(--spacing-3);
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: var(--font-size-sm);
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
      }

      .user-profile {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-2);
        border-radius: var(--radius-base);
        cursor: pointer;
        transition: all 0.2s;
      }

      .user-profile:hover {
        background: var(--gray-50);
      }

      .user-avatar {
        width: var(--spacing-8);
        height: var(--spacing-8);
        border-radius: var(--radius-full);
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: var(--font-size-sm);
      }

      .user-info {
        display: flex;
        flex-direction: column;
      }

      .user-name {
        font-size: var(--font-size-sm);
        font-weight: 600;
        color: var(--gray-800);
        line-height: 1.2;
      }

      .user-role {
        font-size: var(--font-size-xs);
        color: var(--gray-500);
        line-height: 1.2;
      }

      .btn {
        padding: var(--spacing-3) var(--spacing-5);
        border: none;
        border-radius: var(--radius-base);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        font-family: var(--font-family);
        line-height: 1;
      }

      .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: var(--shadow-sm);
      }

      .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .btn-secondary {
        background: white;
        color: var(--gray-700);
        border: 1px solid var(--gray-200);
      }

      .btn-secondary:hover {
        background: var(--gray-50);
        border-color: var(--gray-300);
      }

      .btn-icon {
        width: var(--spacing-8);
        height: var(--spacing-8);
        border-radius: var(--radius-base);
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
        color: var(--gray-500);
      }

      .btn-icon:hover {
        background: var(--gray-100);
        color: var(--gray-700);
      }

      .content-area {
        flex: 1;
        padding: var(--spacing-8);
        overflow-y: auto;
        background: var(--gray-50);
      }

      /* 右侧面板 */
      .right-panel {
        width: var(--right-panel-width);
        background: white;
        border-left: 1px solid var(--gray-200);
        display: flex;
        flex-direction: column;
        box-shadow: var(--shadow-base);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 10;
      }

      .right-panel.collapsed {
        width: 0;
        overflow: hidden;
        border-left: none;
      }

      .right-panel.collapsed .panel-toggle {
        left: -var(--spacing-8);
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      .right-panel.collapsed .panel-toggle:hover {
        background: var(--primary-color);
        transform: scale(1.1);
      }

      .panel-header {
        padding: var(--spacing-6);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .panel-section {
        padding: var(--spacing-6);
        border-bottom: 1px solid var(--gray-100);
      }

      .panel-section:last-child {
        border-bottom: none;
      }

      .panel-title {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--spacing-4);
      }

      .panel-toggle {
        position: absolute;
        left: -var(--spacing-6);
        top: var(--spacing-6);
        width: var(--spacing-6);
        height: var(--spacing-6);
        border: none;
        background: white;
        border-radius: var(--radius-sm);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-600);
        transition: all 0.2s;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
      }

      .panel-toggle:hover {
        background: var(--gray-50);
        color: var(--gray-800);
      }

      /* 卡片样式 */
      .card {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--spacing-6);
        box-shadow: var(--shadow-base);
        border: 1px solid var(--gray-100);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--gray-200);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-5);
      }

      .card-title {
        font-size: var(--font-size-lg);
        font-weight: 600;
        color: var(--gray-900);
      }

      .hero-card {
        background: var(--primary-gradient);
        color: white;
        margin-bottom: var(--spacing-8);
        position: relative;
        overflow: hidden;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .hero-card::before {
        content: "";
        position: absolute;
        top: -50px;
        right: -50px;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
      }

      .hero-card::after {
        content: "";
        position: absolute;
        bottom: -30px;
        left: -30px;
        width: 120px;
        height: 120px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
      }

      .hero-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-2);
        position: relative;
        z-index: 1;
        line-height: 1.2;
      }

      .hero-subtitle {
        font-size: var(--font-size-base);
        opacity: 0.9;
        margin-bottom: var(--spacing-5);
        position: relative;
        z-index: 1;
        line-height: 1.5;
      }

      /* 统计卡片 */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--spacing-5);
        margin-bottom: var(--spacing-8);
      }

      .stat-card {
        background: white;
        border-radius: var(--radius-md);
        padding: var(--spacing-6);
        border: 1px solid var(--gray-200);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.2s;
      }

      .stat-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      .stat-card:hover::before {
        opacity: 1;
      }

      .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-3);
      }

      .stat-icon {
        width: var(--spacing-10);
        height: var(--spacing-10);
        border-radius: var(--radius-base);
        background: rgba(102, 126, 234, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        font-size: var(--font-size-lg);
      }

      .stat-value {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: var(--spacing-1);
        line-height: 1;
      }

      .stat-label {
        font-size: var(--font-size-sm);
        color: var(--gray-600);
        font-weight: 500;
        margin-bottom: var(--spacing-2);
      }

      .stat-trend {
        font-size: var(--font-size-xs);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: var(--spacing-1);
      }

      .stat-trend.positive {
        color: var(--accent-color);
      }

      .stat-trend.negative {
        color: var(--danger-color);
      }

      .stat-trend.neutral {
        color: var(--gray-500);
      }

      /* 项目列表 */
      .project-list {
        display: grid;
        gap: var(--spacing-4);
      }

      .project-card {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        padding: var(--spacing-5);
        background: white;
        border-radius: var(--radius-md);
        border: 1px solid var(--gray-200);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
      }

      .project-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: var(--shadow-base);
      }

      .project-avatar {
        width: var(--spacing-12);
        height: var(--spacing-12);
        border-radius: var(--radius-md);
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: var(--font-size-lg);
        flex-shrink: 0;
      }

      .project-info {
        flex: 1;
        min-width: 0;
      }

      .project-name {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--spacing-1);
        line-height: 1.3;
      }

      .project-desc {
        font-size: var(--font-size-sm);
        color: var(--gray-600);
        line-height: 1.4;
      }

      .project-meta {
        display: flex;
        align-items: center;
        gap: var(--spacing-4);
        margin-top: var(--spacing-2);
      }

      .project-status {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-1);
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 500;
      }

      .project-status.active {
        background: rgba(72, 187, 120, 0.1);
        color: var(--accent-color);
      }

      .project-status.completed {
        background: rgba(102, 126, 234, 0.1);
        color: var(--primary-color);
      }

      .project-progress {
        width: 140px;
        text-align: right;
        flex-shrink: 0;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: var(--gray-200);
        border-radius: var(--radius-sm);
        overflow: hidden;
        margin-bottom: var(--spacing-2);
      }

      .progress-fill {
        height: 100%;
        background: var(--primary-gradient);
        border-radius: var(--radius-sm);
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .progress-text {
        font-size: var(--font-size-xs);
        color: var(--gray-600);
        font-weight: 500;
      }

      /* 任务列表 */
      .task-list {
        display: flex;
        flex-direction: column;
      }

      .task-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-4) 0;
        border-bottom: 1px solid var(--gray-100);
        transition: all 0.2s;
      }

      .task-item:hover {
        background: var(--gray-50);
        margin: 0 calc(-1 * var(--spacing-6));
        padding-left: var(--spacing-6);
        padding-right: var(--spacing-6);
        border-radius: var(--radius-base);
      }

      .task-item:last-child {
        border-bottom: none;
      }

      .task-checkbox {
        width: var(--spacing-5);
        height: var(--spacing-5);
        border: 2px solid var(--gray-300);
        border-radius: var(--radius-sm);
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .task-checkbox.completed {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
      }

      .task-checkbox:hover {
        border-color: var(--primary-color);
      }

      .task-content {
        flex: 1;
        min-width: 0;
      }

      .task-title {
        font-size: var(--font-size-sm);
        color: var(--gray-800);
        margin-bottom: var(--spacing-1);
        font-weight: 500;
        line-height: 1.4;
      }

      .task-title.completed {
        text-decoration: line-through;
        opacity: 0.6;
      }

      .task-meta {
        font-size: var(--font-size-xs);
        color: var(--gray-500);
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
      }

      .task-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        opacity: 0;
        transition: opacity 0.2s;
      }

      .task-item:hover .task-actions {
        opacity: 1;
      }

      .task-priority {
        width: var(--spacing-2);
        height: var(--spacing-2);
        border-radius: var(--radius-full);
        flex-shrink: 0;
      }

      .priority-high {
        background: var(--danger-color);
      }
      .priority-medium {
        background: var(--warning-color);
      }
      .priority-low {
        background: var(--accent-color);
      }

      .task-assignee {
        width: var(--spacing-6);
        height: var(--spacing-6);
        border-radius: var(--radius-full);
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-xs);
        font-weight: 600;
        flex-shrink: 0;
      }

      /* 响应式设计 */
      @media (max-width: 1200px) {
        .right-panel {
          width: 280px;
        }

        .stats-grid {
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
      }

      @media (max-width: 1024px) {
        .right-panel.collapsed {
          width: 0;
        }

        .kanban-board {
          gap: var(--spacing-4);
        }

        .kanban-column {
          min-width: 280px;
        }
      }

      @media (max-width: 768px) {
        .sidebar {
          width: var(--sidebar-collapsed-width);
        }

        .sidebar .sidebar-text {
          display: none;
        }

        .content-area {
          padding: var(--spacing-5);
        }

        .stats-grid {
          grid-template-columns: 1fr;
          gap: var(--spacing-4);
        }

        .main-header {
          padding: var(--spacing-4);
        }

        .search-bar {
          max-width: 200px;
        }

        .user-info {
          display: none;
        }

        .page-tabs {
          gap: var(--spacing-4);
          overflow-x: auto;
        }

        .kanban-board {
          gap: var(--spacing-3);
        }

        .kanban-column {
          min-width: 260px;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: var(--font-size-2xl);
        }

        .hero-subtitle {
          font-size: var(--font-size-sm);
        }

        .project-card {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-3);
        }

        .project-progress {
          width: 100%;
          text-align: left;
        }
      }

      /* 加载动画 */
      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid var(--gray-200);
        border-radius: 50%;
        border-top-color: var(--primary-color);
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      /* 工具提示 */
      .tooltip {
        position: relative;
        cursor: help;
      }

      .tooltip::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--gray-800);
        color: white;
        padding: var(--spacing-2) var(--spacing-3);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s;
        z-index: 1000;
        margin-bottom: var(--spacing-2);
      }

      .tooltip:hover::before {
        opacity: 1;
        visibility: visible;
      }

      /* 拖拽效果 */
      .dragging {
        opacity: 0.5;
        transform: rotate(5deg);
        z-index: 1000;
      }

      .drag-over {
        background: rgba(102, 126, 234, 0.1);
        border: 2px dashed var(--primary-color);
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      ::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: var(--radius-sm);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: var(--radius-sm);
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
      }

      /* 焦点样式 */
      .btn:focus,
      .search-input:focus,
      .nav-item:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }

      /* 选择状态 */
      .selected {
        background: rgba(102, 126, 234, 0.1);
        border-color: var(--primary-color);
      }

      /* 状态指示器 */
      .status-indicator {
        width: var(--spacing-2);
        height: var(--spacing-2);
        border-radius: var(--radius-full);
        display: inline-block;
        margin-right: var(--spacing-2);
      }

      .status-online { background: var(--accent-color); }
      .status-busy { background: var(--danger-color); }
      .status-away { background: var(--warning-color); }
      .status-offline { background: var(--gray-400); }

      /* 页面切换 */
      .page {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
      }

      .page.active {
        display: block;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .page-tabs {
        display: flex;
        gap: var(--spacing-8);
        margin-bottom: var(--spacing-8);
        border-bottom: 1px solid var(--gray-200);
      }

      .page-tab {
        padding: var(--spacing-3) 0;
        font-weight: 500;
        color: var(--gray-500);
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.2s;
        font-size: var(--font-size-sm);
        position: relative;
      }

      .page-tab:hover {
        color: var(--gray-700);
      }

      .page-tab.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        font-weight: 600;
      }

      /* 看板样式 */
      .kanban-board {
        display: flex;
        gap: var(--spacing-5);
        overflow-x: auto;
        padding-bottom: var(--spacing-5);
        min-height: 600px;
      }

      .kanban-column {
        min-width: 300px;
        background: var(--gray-100);
        border-radius: var(--radius-md);
        padding: var(--spacing-5);
        display: flex;
        flex-direction: column;
      }

      .kanban-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-4);
      }

      .kanban-title {
        font-size: var(--font-size-base);
        font-weight: 600;
        color: var(--gray-800);
      }

      .kanban-count {
        background: var(--gray-300);
        color: var(--gray-700);
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: 600;
      }

      .kanban-count.todo {
        background: var(--gray-300);
      }
      .kanban-count.progress {
        background: var(--primary-color);
        color: white;
      }
      .kanban-count.review {
        background: var(--warning-color);
        color: white;
      }
      .kanban-count.done {
        background: var(--accent-color);
        color: white;
      }

      .kanban-cards {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-3);
        flex: 1;
      }

      .kanban-card {
        background: white;
        border-radius: var(--radius-base);
        padding: var(--spacing-4);
        border: 1px solid var(--gray-200);
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
      }

      .kanban-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-base);
      }

      .kanban-card.priority-high {
        border-left: 4px solid var(--danger-color);
      }

      .kanban-card.priority-medium {
        border-left: 4px solid var(--warning-color);
      }

      .kanban-card.priority-low {
        border-left: 4px solid var(--accent-color);
      }

      .kanban-card-title {
        font-size: var(--font-size-sm);
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: var(--spacing-2);
        line-height: 1.4;
      }

      .kanban-card-project {
        font-size: var(--font-size-xs);
        color: var(--gray-500);
        margin-bottom: var(--spacing-3);
      }

      .kanban-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .kanban-card-date {
        font-size: var(--font-size-xs);
        color: var(--gray-500);
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <!-- 左侧导航栏 -->
      <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <div class="logo-icon">PTM</div>
            <span class="sidebar-text">项目任务管理器</span>
          </div>
          <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-chevron-left"></i>
          </button>
        </div>

        <div class="nav-section">
          <div class="nav-title sidebar-text">概览</div>
          <a href="#" class="nav-item active" data-page="dashboard">
            <div class="nav-icon"><i class="fas fa-chart-pie"></i></div>
            <span class="sidebar-text">仪表板</span>
          </a>
          <a href="#" class="nav-item" data-page="projects">
            <div class="nav-icon"><i class="fas fa-folder"></i></div>
            <span class="sidebar-text">项目</span>
          </a>
          <a href="#" class="nav-item" data-page="tasks">
            <div class="nav-icon"><i class="fas fa-check-square"></i></div>
            <span class="sidebar-text">任务</span>
          </a>
          <a href="#" class="nav-item" data-page="kanban">
            <div class="nav-icon"><i class="fas fa-columns"></i></div>
            <span class="sidebar-text">看板</span>
          </a>
          <a href="#" class="nav-item" data-page="gantt">
            <div class="nav-icon"><i class="fas fa-chart-gantt"></i></div>
            <span class="sidebar-text">甘特图</span>
          </a>
        </div>

        <div class="nav-section">
          <div class="nav-title sidebar-text">敏捷开发</div>
          <a href="#" class="nav-item" data-page="sprints">
            <div class="nav-icon"><i class="fas fa-running"></i></div>
            <span class="sidebar-text">冲刺</span>
          </a>
          <a href="#" class="nav-item" data-page="reports">
            <div class="nav-icon"><i class="fas fa-chart-bar"></i></div>
            <span class="sidebar-text">报告</span>
          </a>
        </div>

        <div class="nav-section">
          <div class="nav-title sidebar-text">设置</div>
          <a href="#" class="nav-item">
            <div class="nav-icon"><i class="fas fa-cog"></i></div>
            <span class="sidebar-text">设置</span>
          </a>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <div class="main-header">
          <div class="search-bar">
            <input
              type="text"
              class="search-input"
              placeholder="搜索项目、任务或团队成员..."
            />
            <div class="search-icon"><i class="fas fa-search"></i></div>
          </div>
          <div class="header-actions">
            <button class="btn-icon" data-tooltip="通知">
              <i class="fas fa-bell"></i>
            </button>
            <button class="btn-icon" data-tooltip="切换右侧面板" onclick="toggleRightPanel()">
              <i class="fas fa-sidebar" id="rightPanelIcon"></i>
            </button>
            <button class="btn btn-primary">
              <i class="fas fa-plus"></i>
              <span>新建项目</span>
            </button>
            <div class="user-profile">
              <div class="user-avatar">张</div>
              <div class="user-info">
                <div class="user-name">张三</div>
                <div class="user-role">项目经理</div>
              </div>
              <i
                class="fas fa-chevron-down"
                style="color: var(--gray-400); font-size: var(--font-size-xs)"
              ></i>
            </div>
          </div>
        </div>

        <div class="content-area">
          <!-- 仪表板页面 -->
          <div class="page active" id="dashboard">
            <div class="hero-card">
              <h1 class="hero-title">欢迎回来！</h1>
              <p class="hero-subtitle">
                今天是高效工作的好日子，让我们一起完成更多任务
              </p>
              <button
                class="btn"
                style="
                  background: rgba(255, 255, 255, 0.2);
                  color: white;
                  border: 1px solid rgba(255, 255, 255, 0.3);
                "
              >
                开始工作
              </button>
            </div>

            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">12</div>
                <div class="stat-label">活跃项目</div>
                <div class="stat-trend">↗️ +2 本周</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">48</div>
                <div class="stat-label">待完成任务</div>
                <div class="stat-trend">↘️ -5 今天</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">85%</div>
                <div class="stat-label">完成率</div>
                <div class="stat-trend">↗️ +3% 本月</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">6</div>
                <div class="stat-label">团队成员</div>
                <div class="stat-trend">→ 无变化</div>
              </div>
            </div>

            <div class="card">
              <h2 style="margin-bottom: 20px; color: #1a202c">最近项目</h2>
              <div class="project-list">
                <div class="project-card">
                  <div class="project-avatar">WD</div>
                  <div class="project-info">
                    <div class="project-name">网站重设计</div>
                    <div class="project-desc">更新公司官网的用户界面和体验</div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <div class="progress-text">75% 完成</div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-avatar">MA</div>
                  <div class="project-info">
                    <div class="project-name">移动应用开发</div>
                    <div class="project-desc">开发跨平台的移动应用程序</div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 45%"></div>
                    </div>
                    <div class="progress-text">45% 完成</div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-avatar">DM</div>
                  <div class="project-info">
                    <div class="project-name">数据迁移</div>
                    <div class="project-desc">将旧系统数据迁移到新平台</div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 90%"></div>
                    </div>
                    <div class="progress-text">90% 完成</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 项目页面 -->
          <div class="page" id="projects">
            <div class="page-tabs">
              <div class="page-tab active">所有项目</div>
              <div class="page-tab">进行中</div>
              <div class="page-tab">已完成</div>
              <div class="page-tab">已暂停</div>
            </div>

            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">15</div>
                <div class="stat-label">总项目数</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">8</div>
                <div class="stat-label">进行中</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">6</div>
                <div class="stat-label">已完成</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">1</div>
                <div class="stat-label">已暂停</div>
              </div>
            </div>

            <div class="card">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 20px;
                "
              >
                <h2 style="color: #1a202c">项目列表</h2>
                <button class="btn btn-primary">新建项目</button>
              </div>

              <div class="project-list">
                <div class="project-card">
                  <div class="project-avatar">WD</div>
                  <div class="project-info">
                    <div class="project-name">网站重设计</div>
                    <div class="project-desc">
                      更新公司官网的用户界面和体验 • 开始于 2024-01-15
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <div class="progress-text">75% 完成</div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-avatar">MA</div>
                  <div class="project-info">
                    <div class="project-name">移动应用开发</div>
                    <div class="project-desc">
                      开发跨平台的移动应用程序 • 开始于 2024-02-01
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 45%"></div>
                    </div>
                    <div class="progress-text">45% 完成</div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-avatar">DM</div>
                  <div class="project-info">
                    <div class="project-name">数据迁移</div>
                    <div class="project-desc">
                      将旧系统数据迁移到新平台 • 开始于 2024-01-20
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 90%"></div>
                    </div>
                    <div class="progress-text">90% 完成</div>
                  </div>
                </div>

                <div class="project-card">
                  <div class="project-avatar">API</div>
                  <div class="project-info">
                    <div class="project-name">API 重构</div>
                    <div class="project-desc">
                      重构后端API架构以提高性能 • 开始于 2024-02-10
                    </div>
                  </div>
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: 30%"></div>
                    </div>
                    <div class="progress-text">30% 完成</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务页面 -->
          <div class="page" id="tasks">
            <div class="page-tabs">
              <div class="page-tab active">所有任务</div>
              <div class="page-tab">我的任务</div>
              <div class="page-tab">今日任务</div>
              <div class="page-tab">逾期任务</div>
            </div>

            <div class="card">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 20px;
                "
              >
                <h2 style="color: #1a202c">任务列表</h2>
                <button class="btn btn-primary">新建任务</button>
              </div>

              <div>
                <div class="task-item">
                  <div class="task-checkbox"></div>
                  <div class="task-content">
                    <div class="task-title">完成首页设计稿</div>
                    <div class="task-meta">
                      网站重设计 • 截止日期：2024-03-15 • 分配给：张三
                    </div>
                  </div>
                  <div class="task-priority priority-high"></div>
                </div>

                <div class="task-item">
                  <div class="task-checkbox completed"></div>
                  <div class="task-content">
                    <div
                      class="task-title"
                      style="text-decoration: line-through; opacity: 0.6"
                    >
                      数据库架构设计
                    </div>
                    <div class="task-meta">
                      移动应用开发 • 已完成 • 分配给：李四
                    </div>
                  </div>
                  <div class="task-priority priority-medium"></div>
                </div>

                <div class="task-item">
                  <div class="task-checkbox"></div>
                  <div class="task-content">
                    <div class="task-title">API 接口文档编写</div>
                    <div class="task-meta">
                      API 重构 • 截止日期：2024-03-20 • 分配给：王五
                    </div>
                  </div>
                  <div class="task-priority priority-medium"></div>
                </div>

                <div class="task-item">
                  <div class="task-checkbox"></div>
                  <div class="task-content">
                    <div class="task-title">用户测试反馈整理</div>
                    <div class="task-meta">
                      网站重设计 • 截止日期：2024-03-18 • 分配给：赵六
                    </div>
                  </div>
                  <div class="task-priority priority-low"></div>
                </div>

                <div class="task-item">
                  <div class="task-checkbox"></div>
                  <div class="task-content">
                    <div class="task-title">数据迁移脚本测试</div>
                    <div class="task-meta">
                      数据迁移 • 截止日期：2024-03-12 • 分配给：孙七
                    </div>
                  </div>
                  <div class="task-priority priority-high"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 看板页面 -->
          <div class="page" id="kanban">
            <div class="card-header">
              <h1 class="card-title">看板视图</h1>
              <div style="display: flex; gap: var(--spacing-3);">
                <button class="btn btn-secondary">
                  <i class="fas fa-filter"></i>
                  <span>筛选</span>
                </button>
                <button class="btn btn-primary">
                  <i class="fas fa-plus"></i>
                  <span>添加列</span>
                </button>
              </div>
            </div>

            <div class="kanban-board">
              <!-- 待办事项列 -->
              <div class="kanban-column">
                <div class="kanban-header">
                  <h3 class="kanban-title">待办事项</h3>
                  <span class="kanban-count todo">5</span>
                </div>
                <div class="kanban-cards">
                  <div class="kanban-card priority-high">
                    <div class="kanban-card-title">完成首页设计稿</div>
                    <div class="kanban-card-project">网站重设计</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">张</div>
                      <div class="kanban-card-date">3月15日</div>
                    </div>
                  </div>
                  
                  <div class="kanban-card priority-medium">
                    <div class="kanban-card-title">API 接口文档编写</div>
                    <div class="kanban-card-project">API 重构</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">王</div>
                      <div class="kanban-card-date">3月20日</div>
                    </div>
                  </div>
                  
                  <div class="kanban-card priority-low">
                    <div class="kanban-card-title">用户测试反馈整理</div>
                    <div class="kanban-card-project">网站重设计</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">赵</div>
                      <div class="kanban-card-date">3月18日</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 进行中列 -->
              <div class="kanban-column">
                <div class="kanban-header">
                  <h3 class="kanban-title">进行中</h3>
                  <span class="kanban-count progress">3</span>
                </div>
                <div class="kanban-cards">
                  <div class="kanban-card priority-high">
                    <div class="kanban-card-title">移动端界面开发</div>
                    <div class="kanban-card-project">移动应用开发</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">李</div>
                      <div class="kanban-card-date">进行中</div>
                    </div>
                  </div>
                  
                  <div class="kanban-card priority-medium">
                    <div class="kanban-card-title">数据库优化</div>
                    <div class="kanban-card-project">系统优化</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">孙</div>
                      <div class="kanban-card-date">进行中</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 待审核列 -->
              <div class="kanban-column">
                <div class="kanban-header">
                  <h3 class="kanban-title">待审核</h3>
                  <span class="kanban-count review">2</span>
                </div>
                <div class="kanban-cards">
                  <div class="kanban-card priority-medium">
                    <div class="kanban-card-title">用户反馈分析报告</div>
                    <div class="kanban-card-project">网站重设计</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">周</div>
                      <div class="kanban-card-date">待审核</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 已完成列 -->
              <div class="kanban-column">
                <div class="kanban-header">
                  <h3 class="kanban-title">已完成</h3>
                  <span class="kanban-count done">8</span>
                </div>
                <div class="kanban-cards">
                  <div class="kanban-card priority-low" style="opacity: 0.8;">
                    <div class="kanban-card-title">数据库架构设计</div>
                    <div class="kanban-card-project">移动应用开发</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">李</div>
                      <div class="kanban-card-date" style="color: var(--accent-color);">✓ 已完成</div>
                    </div>
                  </div>
                  
                  <div class="kanban-card priority-medium" style="opacity: 0.8;">
                    <div class="kanban-card-title">需求分析文档</div>
                    <div class="kanban-card-project">项目启动</div>
                    <div class="kanban-card-footer">
                      <div class="task-assignee">张</div>
                      <div class="kanban-card-date" style="color: var(--accent-color);">✓ 已完成</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel" id="rightPanel">
        <button class="panel-toggle" id="panelToggle">
          <i class="fas fa-chevron-right"></i>
        </button>
        <div class="panel-header">
          <div class="panel-title">侧边面板</div>
          <button class="btn-icon" onclick="toggleRightPanel()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="panel-section">
          <div class="panel-title">今日统计</div>
          <div style="text-align: center; margin-bottom: 20px">
            <div
              style="
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(
                  #667eea 0deg 270deg,
                  #e2e8f0 270deg 360deg
                );
                margin: 0 auto 16px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <div
                style="
                  width: 80px;
                  height: 80px;
                  background: white;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex-direction: column;
                "
              >
                <div style="font-size: 24px; font-weight: 700; color: #1a202c">
                  75%
                </div>
                <div style="font-size: 12px; color: #718096">完成率</div>
              </div>
            </div>
            <div style="font-size: 14px; color: #718096">
              今天完成了 6 个任务
            </div>
          </div>
        </div>

        <div class="panel-section">
          <div class="panel-title">即将到期</div>
          <div style="display: flex; flex-direction: column; gap: 12px">
            <div
              style="
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                background: #fef5e7;
                border-radius: 8px;
                border-left: 3px solid #ed8936;
              "
            >
              <div style="flex: 1">
                <div style="font-size: 14px; font-weight: 500; color: #1a202c">
                  数据迁移脚本测试
                </div>
                <div style="font-size: 12px; color: #718096">明天到期</div>
              </div>
              <div class="task-priority priority-high"></div>
            </div>

            <div
              style="
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                background: #fef5e7;
                border-radius: 8px;
                border-left: 3px solid #ed8936;
              "
            >
              <div style="flex: 1">
                <div style="font-size: 14px; font-weight: 500; color: #1a202c">
                  完成首页设计稿
                </div>
                <div style="font-size: 12px; color: #718096">3天后到期</div>
              </div>
              <div class="task-priority priority-high"></div>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <div class="panel-title">团队成员</div>
          <div style="display: flex; flex-direction: column; gap: 12px">
            <div style="display: flex; align-items: center; gap: 12px">
              <div style="position: relative;">
                <div
                  style="
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    font-size: 14px;
                  "
                >
                  张
                </div>
                <div class="status-indicator status-online" style="position: absolute; bottom: 0; right: 0;"></div>
              </div>
              <div style="flex: 1">
                <div style="font-size: 14px; font-weight: 500; color: #1a202c">
                  张三
                </div>
                <div style="font-size: 12px; color: #718096">前端开发</div>
              </div>
            </div>

            <div style="display: flex; align-items: center; gap: 12px">
              <div style="position: relative;">
                <div
                  style="
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    font-size: 14px;
                  "
                >
                  李
                </div>
                <div class="status-indicator status-online" style="position: absolute; bottom: 0; right: 0;"></div>
              </div>
              <div style="flex: 1">
                <div style="font-size: 14px; font-weight: 500; color: #1a202c">
                  李四
                </div>
                <div style="font-size: 12px; color: #718096">后端开发</div>
              </div>
            </div>

            <div style="display: flex; align-items: center; gap: 12px">
              <div style="position: relative;">
                <div
                  style="
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    font-size: 14px;
                  "
                >
                  王
                </div>
                <div class="status-indicator status-away" style="position: absolute; bottom: 0; right: 0;"></div>
              </div>
              <div style="flex: 1">
                <div style="font-size: 14px; font-weight: 500; color: #1a202c">
                  王五
                </div>
                <div style="font-size: 12px; color: #718096">UI设计师</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 全局状态管理
      const AppState = {
        sidebarCollapsed: false,
        rightPanelCollapsed: false,
        currentPage: 'dashboard'
      };

      // 左侧边栏折叠功能
      function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const toggleIcon = sidebarToggle.querySelector('i');
        
        AppState.sidebarCollapsed = !AppState.sidebarCollapsed;
        
        if (AppState.sidebarCollapsed) {
          sidebar.classList.add('collapsed');
          toggleIcon.className = 'fas fa-chevron-right';
        } else {
          sidebar.classList.remove('collapsed');
          toggleIcon.className = 'fas fa-chevron-left';
        }
        
        // 保存状态到localStorage
        localStorage.setItem('sidebarCollapsed', AppState.sidebarCollapsed);
      }

      // 右侧面板折叠功能
      function toggleRightPanel() {
        const rightPanel = document.getElementById('rightPanel');
        const panelToggle = document.getElementById('panelToggle');
        const rightPanelIcon = document.getElementById('rightPanelIcon');
        const toggleIcon = panelToggle.querySelector('i');
        
        AppState.rightPanelCollapsed = !AppState.rightPanelCollapsed;
        
        if (AppState.rightPanelCollapsed) {
          rightPanel.classList.add('collapsed');
          toggleIcon.className = 'fas fa-chevron-left';
          if (rightPanelIcon) rightPanelIcon.className = 'fas fa-sidebar';
        } else {
          rightPanel.classList.remove('collapsed');
          toggleIcon.className = 'fas fa-chevron-right';
          if (rightPanelIcon) rightPanelIcon.className = 'fas fa-sidebar';
        }
        
        // 保存状态到localStorage
        localStorage.setItem('rightPanelCollapsed', AppState.rightPanelCollapsed);
      }

      // 初始化应用状态
      function initializeApp() {
        // 恢复侧边栏状态
        const savedSidebarState = localStorage.getItem('sidebarCollapsed');
        if (savedSidebarState === 'true') {
          AppState.sidebarCollapsed = true;
          toggleSidebar();
        }
        
        // 恢复右侧面板状态
        const savedRightPanelState = localStorage.getItem('rightPanelCollapsed');
        if (savedRightPanelState === 'true') {
          AppState.rightPanelCollapsed = true;
          toggleRightPanel();
        }
        
        // 响应式处理
        handleResponsiveLayout();
        window.addEventListener('resize', handleResponsiveLayout);
      }

      // 响应式布局处理
      function handleResponsiveLayout() {
        const width = window.innerWidth;
        const sidebar = document.getElementById('sidebar');
        const rightPanel = document.getElementById('rightPanel');
        
        if (width <= 768) {
          // 小屏幕自动折叠侧边栏
          if (!AppState.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
          }
        } else if (width <= 1024) {
          // 中等屏幕自动折叠右侧面板
          if (!AppState.rightPanelCollapsed) {
            rightPanel.classList.add('collapsed');
          }
        }
      }

      // 页面切换功能
      function switchPage(pageId) {
        // 移除所有活跃状态
        document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
        document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
        
        // 添加活跃状态
        document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
        document.getElementById(pageId).classList.add('active');
        
        AppState.currentPage = pageId;
        localStorage.setItem('currentPage', pageId);
      }

      // 页面切换事件监听
      document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
          e.preventDefault();
          const pageId = item.getAttribute('data-page');
          if (pageId) {
            switchPage(pageId);
          }
        });
      });

      // 标签页切换功能
      document.querySelectorAll('.page-tab').forEach(tab => {
        tab.addEventListener('click', () => {
          document.querySelectorAll('.page-tab').forEach(t => t.classList.remove('active'));
          tab.classList.add('active');
        });
      });

      // 任务复选框功能
      document.querySelectorAll('.task-checkbox').forEach(checkbox => {
        checkbox.addEventListener('click', (e) => {
          e.stopPropagation();
          checkbox.classList.toggle('completed');
          
          // 添加完成动画
          if (checkbox.classList.contains('completed')) {
            checkbox.innerHTML = '<i class="fas fa-check"></i>';
            // 可以在这里添加任务完成的逻辑
            showNotification('任务已完成！', 'success');
          } else {
            checkbox.innerHTML = '';
          }
        });
      });

      // 搜索功能
      let searchTimeout;
      document.querySelector('.search-input').addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          const searchTerm = e.target.value.toLowerCase();
          performSearch(searchTerm);
        }, 300);
      });

      // 执行搜索
      function performSearch(searchTerm) {
        if (!searchTerm) {
          clearSearchHighlights();
          return;
        }
        
        console.log('搜索:', searchTerm);
        // 这里可以添加实际的搜索逻辑
        highlightSearchResults(searchTerm);
      }

      // 高亮搜索结果
      function highlightSearchResults(searchTerm) {
        // 简单的搜索高亮实现
        const elements = document.querySelectorAll('.project-name, .task-title, .kanban-card-title');
        elements.forEach(element => {
          const text = element.textContent;
          if (text.toLowerCase().includes(searchTerm)) {
            element.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
          }
        });
      }

      // 清除搜索高亮
      function clearSearchHighlights() {
        const elements = document.querySelectorAll('.project-name, .task-title, .kanban-card-title');
        elements.forEach(element => {
          element.style.backgroundColor = '';
        });
      }

      // 通知系统
      function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: ${type === 'success' ? 'var(--accent-color)' : 'var(--primary-color)'};
          color: white;
          padding: var(--spacing-4) var(--spacing-6);
          border-radius: var(--radius-base);
          box-shadow: var(--shadow-md);
          z-index: 1000;
          animation: slideIn 0.3s ease-out;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
          notification.style.animation = 'slideOut 0.3s ease-in forwards';
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }

      // 添加动画样式
      const style = document.createElement('style');
      style.textContent = `
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes slideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
        
        .notification {
          font-family: var(--font-family);
          font-size: var(--font-size-sm);
          font-weight: 500;
        }
      `;
      document.head.appendChild(style);

      // 侧边栏切换按钮事件监听
      document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
      document.getElementById('panelToggle').addEventListener('click', toggleRightPanel);

      // 键盘快捷键
      document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + B 切换左侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
          e.preventDefault();
          toggleSidebar();
        }
        
        // Ctrl/Cmd + Shift + B 切换右侧面板
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'B') {
          e.preventDefault();
          toggleRightPanel();
        }
        
        // ESC 键清除搜索
        if (e.key === 'Escape') {
          document.querySelector('.search-input').value = '';
          clearSearchHighlights();
        }
      });

      // 用户配置文件下拉菜单
      document.querySelector('.user-profile').addEventListener('click', (e) => {
        e.stopPropagation();
        // 这里可以添加用户菜单的逻辑
        showNotification('用户菜单功能待实现', 'info');
      });

      // 点击外部关闭下拉菜单
      document.addEventListener('click', () => {
        // 关闭所有下拉菜单的逻辑
      });

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', initializeApp);

      // 页面可见性变化处理
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          // 页面重新可见时的逻辑
          console.log('页面重新激活');
        }
      });

      // 模拟数据更新
      function simulateDataUpdate() {
        // 模拟实时数据更新
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
          const currentValue = parseInt(stat.textContent);
          if (!isNaN(currentValue)) {
            // 随机小幅度变化
            const change = Math.floor(Math.random() * 3) - 1;
            stat.textContent = Math.max(0, currentValue + change);
          }
        });
      }

      // 每30秒模拟数据更新
      setInterval(simulateDataUpdate, 30000);

      console.log('项目任务管理器原型已加载完成');
    </script>
  </body>
</html>
