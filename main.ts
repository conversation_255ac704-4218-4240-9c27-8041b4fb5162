import { App, Editor, MarkdownView, MarkdownFileInfo, Modal, Notice, Plugin, PluginSettingTab, Setting, WorkspaceLeaf } from 'obsidian';

interface ProjectTaskManagerSettings {
	// General Settings
	enableProjectMode: boolean;
	enableIndependentTaskMode: boolean;
	defaultView: 'dashboard' | 'kanban' | 'list' | 'gantt';
	
	// Tasks Plugin Integration
	enableTasksPluginSync: boolean;
	tasksPluginCompatibility: boolean;
	
	// Performance Settings
	enableCaching: boolean;
	maxCacheSize: number;
	enableVirtualScrolling: boolean;
	
	// UI Settings
	showStatusBar: boolean;
	showRibbonIcon: boolean;
	compactMode: boolean;
}

const DEFAULT_SETTINGS: ProjectTaskManagerSettings = {
	enableProjectMode: true,
	enableIndependentTaskMode: true,
	defaultView: 'dashboard',
	enableTasksPluginSync: false,
	tasksPluginCompatibility: true,
	enableCaching: true,
	maxCacheSize: 1000,
	enableVirtualScrolling: true,
	showStatusBar: true,
	showRibbonIcon: true,
	compactMode: false
}

export default class ProjectTaskManagerPlugin extends Plugin {
	settings: ProjectTaskManagerSettings = DEFAULT_SETTINGS;
	
	// Core service instances (will be implemented in later tasks)
	private projectManager: any; // ProjectManager
	private taskManager: any; // TaskManager
	private sprintManager: any; // SprintManager
	private uiManager: any; // UIManager
	private dataManager: any; // DataManager
	private ptmManager: any; // PTMManager
	private tasksPluginBridge: any; // TasksPluginBridge

	async onload() {
		console.log('Loading Project Task Manager plugin...');
		
		// Load settings first
		await this.loadSettings();
		
		// Initialize core services
		await this.initializeServices();
		
		// Register views and UI components
		this.registerViews();
		
		// Register commands
		this.registerCommands();
		
		// Setup event listeners
		this.setupEventListeners();
		
		// Add settings tab
		this.addSettingTab(new ProjectTaskManagerSettingTab(this.app, this));
		
		console.log('Project Task Manager plugin loaded successfully');
	}

	onunload() {
		console.log('Unloading Project Task Manager plugin...');
		
		// Cleanup services
		this.cleanupServices();
		
		console.log('Project Task Manager plugin unloaded');
	}

	private async initializeServices() {
		try {
			// Initialize PTM manager (this will create all other services internally)
			const { PTMManager } = await import('./src/services/PTMManager');
			this.ptmManager = new PTMManager(this.app, {
				dataManagerOptions: {
					enableCaching: this.settings.enableCaching,
					maxCacheSize: this.settings.maxCacheSize,
					dataFileName: 'project-task-data.json'
				},
				tasksPluginBridgeOptions: {
					enableSync: this.settings.enableTasksPluginSync,
					defaultProjectId: 'default'
				}
			});
			await this.ptmManager.initialize();

			// Get references to the services created by PTMManager
			this.dataManager = this.ptmManager.getDataManager();
			this.projectManager = this.ptmManager.getProjectManager();
			this.taskManager = this.ptmManager.getTaskManager();
			this.tasksPluginBridge = this.ptmManager.getTasksPluginBridge();

			// TODO: Initialize other services in later tasks
			// this.sprintManager = new SprintManager(this.app, this.dataManager);
			// this.uiManager = new UIManager(this.app);
			
			console.log('Core services initialized successfully');
		} catch (error) {
			console.error('Error initializing services:', error);
			new Notice('插件初始化失败，请检查控制台错误信息');
			throw error; // 重新抛出错误以便调试
		}
	}

	private registerViews() {
		// Register Project Dashboard View
		this.registerView(
			'project-task-manager-dashboard',
			(leaf) => {
				const { ProjectDashboardView } = require('./src/ui/views/ProjectDashboardView');
				return new ProjectDashboardView(leaf, this.ptmManager);
			}
		);
		
		// Register Task List View
		this.registerView(
			'project-task-manager-task-list',
			(leaf) => {
				const { TaskListView } = require('./src/ui/views/TaskListView');
				return new TaskListView(leaf, this.ptmManager);
			}
		);
		
		// Register Kanban View
		this.registerView(
			'project-task-manager-kanban',
			(leaf) => {
				const { KanbanView } = require('./src/ui/views/KanbanView');
				return new KanbanView(leaf, this.ptmManager);
			}
		);
		
		// Register Sprint View
		this.registerView(
			'project-task-manager-sprint',
			(leaf) => {
				const { SprintView } = require('./src/ui/views/SprintView');
				return new SprintView(leaf, this.ptmManager);
			}
		);
		
		// Register Gantt View
		this.registerView(
			'project-task-manager-gantt',
			(leaf) => {
				const { GanttView } = require('./src/ui/views/GanttView');
				return new GanttView(leaf, this.ptmManager);
			}
		);
		
		console.log('Views registered');
	}

	private registerCommands() {
		// Project Management Commands
		this.addCommand({
			id: 'open-project-dashboard',
			name: 'Open Project Dashboard',
			callback: () => {
				this.openProjectDashboard();
			}
		});

		this.addCommand({
			id: 'create-new-project',
			name: 'Create New Project',
			callback: () => {
				this.createNewProject();
			}
		});

		// Task Management Commands
		this.addCommand({
			id: 'create-task-from-selection',
			name: 'Create Task from Selection',
			editorCallback: (editor: Editor, ctx: MarkdownView | MarkdownFileInfo) => {
				if (ctx instanceof MarkdownView) {
					this.createTaskFromSelection(editor, ctx);
				}
			}
		});

		this.addCommand({
			id: 'create-new-task',
			name: 'Create New Task',
			callback: () => {
				this.createNewTask();
			}
		});

		this.addCommand({
			id: 'open-task-list',
			name: 'Open Task List',
			callback: () => {
				this.openTaskList();
			}
		});

		// View Commands
		this.addCommand({
			id: 'open-kanban-board',
			name: 'Open Kanban Board',
			callback: () => {
				this.openKanbanBoard();
			}
		});

		this.addCommand({
			id: 'open-gantt-chart',
			name: 'Open Gantt Chart',
			callback: () => {
				this.openGanttChart();
			}
		});

		this.addCommand({
			id: 'open-sprint-board',
			name: 'Open Sprint Board',
			callback: () => {
				this.openSprintBoard();
			}
		});

		// Sprint Management Commands
		this.addCommand({
			id: 'create-new-sprint',
			name: 'Create New Sprint',
			callback: () => {
				this.createNewSprint();
			}
		});

		// PTM File Commands
		this.addCommand({
			id: 'create-ptm-project',
			name: 'Create PTM Project File',
			callback: () => {
				this.createPTMProject();
			}
		});

		this.addCommand({
			id: 'load-ptm-project',
			name: 'Load PTM Project File',
			callback: () => {
				this.loadPTMProject();
			}
		});

		this.addCommand({
			id: 'backup-ptm-project',
			name: 'Backup PTM Project',
			callback: () => {
				this.backupPTMProject();
			}
		});

		// Emergency cleanup command
		this.addCommand({
			id: 'clear-auto-synced-tasks',
			name: 'Clear Auto-synced Tasks (Emergency Cleanup)',
			callback: () => {
				this.clearAutoSyncedTasks();
			}
		});

		// Clear duplicate test data command
		this.addCommand({
			id: 'clear-duplicate-test-data',
			name: 'Clear Duplicate Test Data (Cleanup)',
			callback: () => {
				this.clearDuplicateTestData();
			}
		});

		// 任务同步相关命令
		this.addCommand({
			id: 'sync-tasks-from-vault',
			name: 'Sync Tasks from Vault (Manual)',
			callback: () => {
				this.syncTasksFromVault();
			}
		});

		this.addCommand({
			id: 'sync-tasks-to-vault',
			name: 'Sync Tasks to Vault (Manual)',
			callback: () => {
				this.syncTasksToVault();
			}
		});

		this.addCommand({
			id: 'enable-auto-sync',
			name: 'Enable Auto Sync (Careful!)',
			callback: () => {
				this.enableAutoSync();
			}
		});

		this.addCommand({
			id: 'disable-auto-sync',
			name: 'Disable Auto Sync',
			callback: () => {
				this.disableAutoSync();
			}
		});

		// Debug and data inspection commands
		this.addCommand({
			id: 'show-task-data-info',
			name: 'Show Task Data Info (Debug)',
			callback: () => {
				this.showTaskDataInfo();
			}
		});

		this.addCommand({
			id: 'analyze-sync-differences',
			name: 'Analyze Sync Differences (Debug)',
			callback: () => {
				this.analyzeSyncDifferences();
			}
		});

		// Toggle Commands
		this.addCommand({
			id: 'toggle-project-mode',
			name: 'Toggle Project Mode',
			callback: () => {
				this.toggleProjectMode();
			}
		});
	}

	private setupEventListeners() {
		// Add ribbon icon if enabled
		if (this.settings.showRibbonIcon) {
			const ribbonIconEl = this.addRibbonIcon('list-checks', 'Project Task Manager', (evt: MouseEvent) => {
				this.openProjectDashboard();
			});
			ribbonIconEl.addClass('project-task-manager-ribbon-class');
			
			// 确保图标可见并添加自定义样式
			ribbonIconEl.style.display = 'flex';
			ribbonIconEl.style.alignItems = 'center';
			ribbonIconEl.style.justifyContent = 'center';
			ribbonIconEl.style.minWidth = '28px';
			ribbonIconEl.style.minHeight = '28px';
			ribbonIconEl.style.padding = '4px';
			ribbonIconEl.style.borderRadius = '4px';
			ribbonIconEl.style.backgroundColor = 'var(--interactive-accent)';
			ribbonIconEl.style.color = 'var(--text-on-accent)';
			
			// 添加悬停效果
			ribbonIconEl.addEventListener('mouseenter', () => {
				ribbonIconEl.style.backgroundColor = 'var(--interactive-accent-hover)';
			});
			ribbonIconEl.addEventListener('mouseleave', () => {
				ribbonIconEl.style.backgroundColor = 'var(--interactive-accent)';
			});
		}

		// Add status bar if enabled
		if (this.settings.showStatusBar) {
			const statusBarItemEl = this.addStatusBarItem();
			statusBarItemEl.setText('PTM Ready');
		}

		// File system events for PTM files
		this.registerEvent(
			this.app.vault.on('create', (file) => {
				if (file.path.endsWith('.ptm')) {
					console.log('PTM file created:', file.path);
					// TODO: Handle PTM file creation
				}
			})
		);

		this.registerEvent(
			this.app.vault.on('modify', (file) => {
				if (file.path.endsWith('.ptm')) {
					console.log('PTM file modified:', file.path);
					// TODO: Handle PTM file modification
				}
			})
		);

		// Markdown file events for task detection
		this.registerEvent(
			this.app.vault.on('modify', (file) => {
				if (file.path.endsWith('.md') && this.settings.tasksPluginCompatibility) {
					// TODO: Scan for task changes
				}
			})
		);
	}

	private cleanupServices() {
		// Cleanup PTM manager
		if (this.ptmManager) {
			this.ptmManager.cleanup();
			this.ptmManager = null;
		}

		// TODO: Cleanup other services when implemented
		this.projectManager = null;
		this.taskManager = null;
		this.sprintManager = null;
		this.uiManager = null;
		this.dataManager = null;
		this.tasksPluginBridge = null;
	}

	// Command implementations
	private async openProjectDashboard() {
		try {
			// Get or create the project dashboard leaf
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.setViewState({
				type: 'project-task-manager-dashboard',
				active: true
			});
			
			// Reveal the leaf to make it visible
			this.app.workspace.revealLeaf(leaf);
			
			new Notice('Project Dashboard opened');
		} catch (error) {
			console.error('Error opening Project Dashboard:', error);
			new Notice('Failed to open Project Dashboard');
		}
	}

	private async createNewProject() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// 创建项目创建模态框
			const { ProjectCreateModal } = await import('./src/ui/components/common/ProjectCreateModal');
			const modal = new ProjectCreateModal(this.app, this.ptmManager);
			modal.open();
		} catch (error) {
			console.error('Error opening project creation modal:', error);
			new Notice('打开项目创建对话框失败');
		}
	}

	private async createTaskFromSelection(editor: Editor, view: MarkdownView) {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			const selection = editor.getSelection();
			const taskTitle = selection || '新任务';

			// 创建任务创建模态框
			const { TaskCreateModal } = await import('./src/ui/components/common/TaskCreateModal');
			const modal = new TaskCreateModal(this.app, this.ptmManager, {
				initialTitle: taskTitle,
				sourceFile: view.file?.path
			});
			modal.open();
		} catch (error) {
			console.error('Error opening task creation modal:', error);
			new Notice('打开任务创建对话框失败');
		}
	}

	private async createNewTask() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// 创建任务创建模态框
			const { TaskCreateModal } = await import('./src/ui/components/common/TaskCreateModal');
			const modal = new TaskCreateModal(this.app, this.ptmManager);
			modal.open();
		} catch (error) {
			console.error('Error opening task creation modal:', error);
			new Notice('打开任务创建对话框失败');
		}
	}

	private async openTaskList() {
		try {
			// Get or create the task list leaf
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.setViewState({
				type: 'project-task-manager-task-list',
				active: true
			});
			
			// Reveal the leaf to make it visible
			this.app.workspace.revealLeaf(leaf);
			
			new Notice('任务列表已打开');
		} catch (error) {
			console.error('Error opening Task List:', error);
			new Notice('打开任务列表失败');
		}
	}

	private async openKanbanBoard() {
		try {
			// Get or create the kanban view leaf
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.setViewState({
				type: 'project-task-manager-kanban',
				active: true
			});
			
			// Reveal the leaf to make it visible
			this.app.workspace.revealLeaf(leaf);
			
			new Notice('看板视图已打开');
		} catch (error) {
			console.error('Error opening Kanban Board:', error);
			new Notice('打开看板视图失败');
		}
	}

	private async openGanttChart() {
		try {
			// Get or create the gantt view leaf
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.setViewState({
				type: 'project-task-manager-gantt',
				active: true
			});
			
			// Reveal the leaf to make it visible
			this.app.workspace.revealLeaf(leaf);
			
			new Notice('甘特图已打开');
		} catch (error) {
			console.error('Error opening Gantt Chart:', error);
			new Notice('打开甘特图失败');
		}
	}

	private async openSprintBoard() {
		try {
			// Get or create the sprint view leaf
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.setViewState({
				type: 'project-task-manager-sprint',
				active: true
			});
			
			// Reveal the leaf to make it visible
			this.app.workspace.revealLeaf(leaf);
			
			new Notice('Sprint 管理面板已打开');
		} catch (error) {
			console.error('Error opening Sprint Board:', error);
			new Notice('打开 Sprint 管理面板失败');
		}
	}

	private async createNewSprint() {
		new Notice('Creating new sprint...');
		// TODO: Implement in sprint management tasks
	}

	private async createPTMProject() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// Simple project creation dialog
			const projectName = await this.promptForInput('项目名称', '请输入项目名称');
			if (!projectName) return;

			const result = await this.ptmManager.createPTMProject(projectName);
			new Notice(`PTM项目已创建: ${result.project.name}`);
		} catch (error) {
			console.error('Error creating PTM project:', error);
			new Notice(`创建PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async loadPTMProject() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// Get all PTM files and let user choose
			const ptmFiles = await this.ptmManager.ptmHandler?.getAllPTMFiles() || [];
			if (ptmFiles.length === 0) {
				new Notice('未找到PTM项目文件');
				return;
			}

			// For now, load the first PTM file found
			// TODO: Implement file selection dialog
			const firstFile = ptmFiles[0];
			const project = await this.ptmManager.loadPTMProject(firstFile.path);
			new Notice(`PTM项目已加载: ${project.name}`);
		} catch (error) {
			console.error('Error loading PTM project:', error);
			new Notice(`加载PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async backupPTMProject() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			const ptmProjects = this.ptmManager.getAllPTMProjects();
			if (ptmProjects.length === 0) {
				new Notice('没有可备份的PTM项目');
				return;
			}

			// For now, backup the first project
			// TODO: Implement project selection dialog
			const firstProject = ptmProjects[0];
			const backupPath = await this.ptmManager.backupPTMProject(firstProject.project.id);
			new Notice(`项目备份已创建: ${backupPath}`);
		} catch (error) {
			console.error('Error backing up PTM project:', error);
			new Notice(`备份PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async toggleProjectMode() {
		this.settings.enableProjectMode = !this.settings.enableProjectMode;
		await this.saveSettings();
		new Notice(`Project mode ${this.settings.enableProjectMode ? 'enabled' : 'disabled'}`);
	}

	private async clearAutoSyncedTasks() {
		try {
			if (!this.tasksPluginBridge) {
				new Notice('TasksPluginBridge未初始化');
				return;
			}

			const deletedCount = await this.tasksPluginBridge.clearAutoSyncedTasks();
			new Notice(`已清理 ${deletedCount} 个自动同步的任务`);
		} catch (error) {
			console.error('Error clearing auto-synced tasks:', error);
			new Notice(`清理任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async clearDuplicateTestData() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			const taskRepository = this.ptmManager.getTaskRepository();
			const projectRepository = this.ptmManager.getProjectRepository();
			const kanbanManager = this.ptmManager.getKanbanManager();
			
			let deletedProjects = 0;
			let deletedTasks = 0;
			let deletedKanbans = 0;

			// 获取所有项目和任务
			const allProjects = await projectRepository.findAll();
			const allTasks = await taskRepository.findAll();
			const allKanbans = await kanbanManager.getAllKanbans();

			console.log(`开始清理重复数据: ${allTasks.length} 个任务, ${allProjects.length} 个项目`);

			// 清理重复任务（基于标题和项目ID的组合）
			const taskMap = new Map<string, any[]>();
			allTasks.forEach((task: any) => {
				const key = `${task.projectId}-${task.title}`;
				if (!taskMap.has(key)) {
					taskMap.set(key, []);
				}
				taskMap.get(key)!.push(task);
			});

			// 删除重复任务（保留第一个，删除其余的）
			for (const [key, tasks] of taskMap) {
				if (tasks.length > 1) {
					console.log(`发现重复任务: ${key}, 数量: ${tasks.length}`);
					// 保留第一个（通常是最早创建的），删除其余的
					for (let i = 1; i < tasks.length; i++) {
						await taskRepository.delete(tasks[i].id);
						deletedTasks++;
					}
				}
			}

			// 查找重复的测试项目（名称为"测试项目"的项目，只保留第一个）
			const testProjects = allProjects.filter((p: any) => p.name === '测试项目');
			if (testProjects.length > 1) {
				// 保留第一个，删除其余的
				for (let i = 1; i < testProjects.length; i++) {
					const projectToDelete = testProjects[i];
					
					// 删除该项目的所有任务
					const projectTasks = allTasks.filter((t: any) => t.projectId === projectToDelete.id);
					for (const task of projectTasks) {
						await taskRepository.delete(task.id);
						deletedTasks++;
					}
					
					// 删除该项目的所有看板
					const projectKanbans = allKanbans.filter((k: any) => k.projectId === projectToDelete.id);
					for (const kanban of projectKanbans) {
						await kanbanManager.deleteKanban(kanban.id);
						deletedKanbans++;
					}
					
					// 删除项目
					await projectRepository.delete(projectToDelete.id);
					deletedProjects++;
				}
			}

			// 查找孤立的测试任务（项目ID不存在的任务）
			const validProjectIds = new Set(allProjects.map((p: any) => p.id));
			const orphanedTasks = allTasks.filter((t: any) => !validProjectIds.has(t.projectId));
			for (const task of orphanedTasks) {
				await taskRepository.delete(task.id);
				deletedTasks++;
			}

			// 查找重复的测试看板（名称为"测试看板"的看板，每个项目只保留第一个）
			const projectKanbanMap = new Map<string, any[]>();
			for (const kanban of allKanbans) {
				if (kanban.name === '测试看板') {
					if (!projectKanbanMap.has(kanban.projectId)) {
						projectKanbanMap.set(kanban.projectId, []);
					}
					projectKanbanMap.get(kanban.projectId)!.push(kanban);
				}
			}

			for (const [projectId, kanbans] of projectKanbanMap) {
				if (kanbans.length > 1) {
					// 保留第一个，删除其余的
					for (let i = 1; i < kanbans.length; i++) {
						await kanbanManager.deleteKanban(kanbans[i].id);
						deletedKanbans++;
					}
				}
			}

			console.log(`清理重复数据完成: 删除了 ${deletedProjects} 个项目, ${deletedTasks} 个任务, ${deletedKanbans} 个看板`);
			
			return {
				deletedProjects,
				deletedTasks,
				deletedKanbans
			};
		} catch (error) {
			console.error('Error clearing duplicate test data:', error);
			throw error;
		}
	}

	private async showTaskDataInfo() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			const taskRepository = this.ptmManager.getTaskRepository();
			const projectRepository = this.ptmManager.getProjectRepository();
			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			
			const allTasks = await taskRepository.findAll();
			const allProjects = await projectRepository.findAll();
			
			// 分析任务数据的详细信息
			const taskAnalysis = this.analyzeTaskData(allTasks, allProjects);
			
			// 同步差异分析
			let syncAnalysis;
			try {
				syncAnalysis = await this.performSyncAnalysis(tasksPluginBridge);
			} catch (error) {
				console.error('Error in sync analysis:', error);
				syncAnalysis = {
					totalMarkdownTasks: 0,
					filteredTasks: 0,
					noTaskTagTasks: 0,
					tooLongTasks: 0,
					sampleFilteredTasks: []
				};
			}
			
			// Calculate data size
			const dataPath = '.obsidian/plugins/obsidian-project-task-manager/project-task-data.json';
			let dataSize = 'Unknown';
			try {
				const stat = await this.app.vault.adapter.stat(dataPath);
				if (stat) {
					dataSize = `${(stat.size / 1024 / 1024).toFixed(2)} MB`;
				}
			} catch (e) {
				// File doesn't exist or can't be read
			}
			
			const info = `
📊 任务数据详细分析:

🗃️ 数据文件: ${dataSize}
📁 项目数量: ${allProjects.length}
📋 插件中任务总数: ${allTasks.length}

📊 任务状态分布:
- 待办: ${allTasks.filter((t: any) => t.status === 'todo').length}
- 进行中: ${allTasks.filter((t: any) => t.status === 'in-progress').length}  
- 已完成: ${allTasks.filter((t: any) => t.status === 'completed').length}
- 已取消: ${allTasks.filter((t: any) => t.status === 'cancelled').length}

🔍 同步差异分析:
📝 Markdown中任务总数: ${syncAnalysis.totalMarkdownTasks}
✅ 符合同步条件: ${syncAnalysis.filteredTasks}
❌ 被过滤任务: ${syncAnalysis.totalMarkdownTasks - syncAnalysis.filteredTasks}
  - 缺少#task标签: ${syncAnalysis.noTaskTagTasks}
  - 文本太长: ${syncAnalysis.tooLongTasks}

🔍 数据异常分析:
${taskAnalysis.summary}

🔗 有链接笔记的任务: ${allTasks.filter((t: any) => t.linkedNotes && t.linkedNotes.length > 0).length}

💡 提示: 打开开发者控制台查看详细分析数据
			`.trim();
			
			console.log('=== 详细任务数据分析 ===');
			console.log('所有项目:', allProjects);
			console.log('插件中的任务:', allTasks);
			console.log('任务分析:', taskAnalysis);
			console.log('同步分析:', syncAnalysis);
			console.log('===================');
			
			new Notice(info, 20000);
		} catch (error) {
			console.error('Error getting task data info:', error);
			new Notice(`获取任务数据失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async performSyncAnalysis(tasksPluginBridge: any): Promise<{
		totalMarkdownTasks: number;
		filteredTasks: number;
		noTaskTagTasks: number;
		tooLongTasks: number;
		sampleFilteredTasks: any[];
	}> {
		try {
			console.log('开始同步分析...');
			const markdownFiles = this.app.vault.getMarkdownFiles();
			console.log(`找到 ${markdownFiles.length} 个Markdown文件`);
			
			let totalMarkdownTasks = 0;
			let filteredTasks = 0;
			let tooLongTasks = 0;
			let noTaskTagTasks = 0;
			const sampleFilteredTasks: any[] = [];

			for (const file of markdownFiles) {
				try {
					const content = await this.app.vault.read(file);
					const tasksInFile = tasksPluginBridge.parseTasksFromMarkdown(content, file.path);
					
					for (const task of tasksInFile) {
						totalMarkdownTasks++;
						
						// 检查过滤条件
						const hasTaskTag = task.tags && task.tags.includes('task');
						const hasReasonableLength = task.description && task.description.length <= 300;
						const isNotTooLong = task.originalText && task.originalText.length <= 500;
						
						if (!hasTaskTag) {
							noTaskTagTasks++;
							if (sampleFilteredTasks.length < 3) {
								sampleFilteredTasks.push({
									reason: '缺少#task标签',
									file: file.path,
									line: task.lineNumber || 0,
									text: (task.originalText || '').substring(0, 80) + '...'
								});
							}
						} else if (!hasReasonableLength || !isNotTooLong) {
							tooLongTasks++;
							if (sampleFilteredTasks.length < 3) {
								sampleFilteredTasks.push({
									reason: '文本太长',
									file: file.path,
									line: task.lineNumber || 0,
									text: (task.originalText || '').substring(0, 80) + '...',
									length: Math.max(
										(task.description || '').length, 
										(task.originalText || '').length
									)
								});
							}
						} else {
							filteredTasks++;
						}
					}
				} catch (fileError) {
					console.warn(`无法处理文件 ${file.path}:`, fileError);
				}
			}

			console.log('=== 同步差异详细分析 ===');
			console.log('Markdown任务总数:', totalMarkdownTasks);
			console.log('符合条件的任务:', filteredTasks);
			console.log('缺少#task标签:', noTaskTagTasks);
			console.log('文本太长:', tooLongTasks);
			console.log('被过滤任务示例:', sampleFilteredTasks);
			console.log('===================');

			return {
				totalMarkdownTasks,
				filteredTasks,
				noTaskTagTasks,
				tooLongTasks,
				sampleFilteredTasks
			};
		} catch (error) {
			console.error('同步分析过程中出错:', error);
			throw error;
		}
	}

	private analyzeTaskData(allTasks: any[], allProjects: any[]): {
		summary: string;
		duplicateProjects: any[];
		duplicateTasks: any[];
		orphanedTasks: any[];
		suspiciousPatterns: string[];
	} {
		const analysis = {
			summary: '',
			duplicateProjects: [] as any[],
			duplicateTasks: [] as any[],
			orphanedTasks: [] as any[],
			suspiciousPatterns: [] as string[]
		};

		// 检查重复项目
		const projectNames = new Map<string, any[]>();
		allProjects.forEach(project => {
			if (!projectNames.has(project.name)) {
				projectNames.set(project.name, []);
			}
			projectNames.get(project.name)!.push(project);
		});

		for (const [name, projects] of projectNames) {
			if (projects.length > 1) {
				analysis.duplicateProjects.push(...projects.slice(1));
			}
		}

		// 检查重复任务
		const taskTitles = new Map<string, any[]>();
		allTasks.forEach(task => {
			const key = `${task.projectId}-${task.title}`;
			if (!taskTitles.has(key)) {
				taskTitles.set(key, []);
			}
			taskTitles.get(key)!.push(task);
		});

		for (const [key, tasks] of taskTitles) {
			if (tasks.length > 1) {
				analysis.duplicateTasks.push(...tasks.slice(1));
			}
		}

		// 检查孤立任务
		const validProjectIds = new Set(allProjects.map(p => p.id));
		analysis.orphanedTasks = allTasks.filter(task => !validProjectIds.has(task.projectId));

		// 检查可疑模式
		if (allTasks.length > 5000) {
			analysis.suspiciousPatterns.push(`任务数量异常高 (${allTasks.length})`);
		}

		if (analysis.duplicateProjects.length > 0) {
			analysis.suspiciousPatterns.push(`发现 ${analysis.duplicateProjects.length} 个重复项目`);
		}

		if (analysis.duplicateTasks.length > 0) {
			analysis.suspiciousPatterns.push(`发现 ${analysis.duplicateTasks.length} 个重复任务`);
		}

		if (analysis.orphanedTasks.length > 0) {
			analysis.suspiciousPatterns.push(`发现 ${analysis.orphanedTasks.length} 个孤立任务`);
		}

		// 检查测试数据
		const testProjects = allProjects.filter(p => p.name === '测试项目');
		if (testProjects.length > 1) {
			analysis.suspiciousPatterns.push(`发现 ${testProjects.length} 个测试项目`);
		}

		const testTasks = allTasks.filter(t => t.title && (
			t.title.includes('测试') || 
			t.title.includes('设计用户界面') || 
			t.title.includes('实现登录功能')
		));
		if (testTasks.length > 10) {
			analysis.suspiciousPatterns.push(`发现 ${testTasks.length} 个疑似测试任务`);
		}

		// 生成摘要
		if (analysis.suspiciousPatterns.length > 0) {
			analysis.summary = `⚠️ 发现数据异常:\n${analysis.suspiciousPatterns.map(p => `- ${p}`).join('\n')}`;
		} else {
			analysis.summary = '✅ 数据看起来正常';
		}

		return analysis;
	}

	// 任务同步方法实现
	private async syncTasksFromVault() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			new Notice('开始从库中同步任务...');
			
			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			
			// 临时启用同步以执行手动同步
			const originalSyncSetting = tasksPluginBridge.options.enableSync;
			tasksPluginBridge.options.enableSync = true;
			
			try {
				await tasksPluginBridge.syncFromMarkdown();
				
				// 获取同步后的统计信息
				const taskRepository = this.ptmManager.getTaskRepository();
				const allTasks = await taskRepository.findAll();
				
				new Notice(`同步完成！当前任务总数: ${allTasks.length}`);
			} finally {
				// 恢复原始设置
				tasksPluginBridge.options.enableSync = originalSyncSetting;
			}
		} catch (error) {
			console.error('Error syncing tasks from vault:', error);
			new Notice(`同步失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async syncTasksToVault() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			new Notice('开始将任务同步到库中...');
			
			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			await tasksPluginBridge.syncToMarkdown();
			
			new Notice('任务已同步到Markdown文件中');
		} catch (error) {
			console.error('Error syncing tasks to vault:', error);
			new Notice(`同步失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async enableAutoSync() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// 警告用户自动同步的风险
			const confirmed = await this.confirmAction(
				'启用自动同步',
				'⚠️ 警告：自动同步可能会导致大量任务被重复创建。\n\n建议先手动同步一次，确认结果正常后再启用自动同步。\n\n确定要启用自动同步吗？'
			);

			if (!confirmed) return;

			// 更新设置
			this.settings.enableTasksPluginSync = true;
			await this.saveSettings();

			// 重新初始化TasksPluginBridge以启用同步
			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			tasksPluginBridge.options.enableSync = true;

			new Notice('自动同步已启用');
		} catch (error) {
			console.error('Error enabling auto sync:', error);
			new Notice(`启用自动同步失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async disableAutoSync() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			// 更新设置
			this.settings.enableTasksPluginSync = false;
			await this.saveSettings();

			// 重新初始化TasksPluginBridge以禁用同步
			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			tasksPluginBridge.options.enableSync = false;

			new Notice('自动同步已禁用');
		} catch (error) {
			console.error('Error disabling auto sync:', error);
			new Notice(`禁用自动同步失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async analyzeSyncDifferences() {
		try {
			if (!this.ptmManager) {
				new Notice('PTM管理器未初始化');
				return;
			}

			new Notice('开始分析同步差异...');

			const tasksPluginBridge = this.ptmManager.getTasksPluginBridge();
			const taskRepository = this.ptmManager.getTaskRepository();

			// 获取所有Markdown文件中的任务
			const markdownFiles = this.app.vault.getMarkdownFiles();
			let totalMarkdownTasks = 0;
			let filteredTasks = 0;
			let tooLongTasks = 0;
			let noTaskTagTasks = 0;
			const sampleFilteredTasks: any[] = [];

			for (const file of markdownFiles) {
				const content = await this.app.vault.read(file);
				const tasksInFile = tasksPluginBridge.parseTasksFromMarkdown(content, file.path);
				
				for (const task of tasksInFile) {
					totalMarkdownTasks++;
					
					// 检查过滤条件
					const hasTaskTag = task.tags.includes('task') || tasksPluginBridge.TASK_TAG_REGEX?.test(task.originalText);
					const hasReasonableLength = task.description.length <= 300;
					const isNotTooLong = task.originalText.length <= 500;
					
					if (!hasTaskTag) {
						noTaskTagTasks++;
						if (sampleFilteredTasks.length < 5) {
							sampleFilteredTasks.push({
								reason: '缺少#task标签',
								file: file.path,
								line: task.lineNumber,
								text: task.originalText.substring(0, 100) + '...'
							});
						}
					} else if (!hasReasonableLength) {
						tooLongTasks++;
						if (sampleFilteredTasks.length < 5) {
							sampleFilteredTasks.push({
								reason: '描述太长',
								file: file.path,
								line: task.lineNumber,
								text: task.originalText.substring(0, 100) + '...',
								length: task.description.length
							});
						}
					} else if (!isNotTooLong) {
						tooLongTasks++;
						if (sampleFilteredTasks.length < 5) {
							sampleFilteredTasks.push({
								reason: '原始文本太长',
								file: file.path,
								line: task.lineNumber,
								text: task.originalText.substring(0, 100) + '...',
								length: task.originalText.length
							});
						}
					} else {
						filteredTasks++;
					}
				}
			}

			// 获取插件中的任务数量
			const allTasks = await taskRepository.findAll();
			const syncedTasks = allTasks.length;

			const info = `
🔍 同步差异分析报告:

📊 统计信息:
- Markdown文件中的任务总数: ${totalMarkdownTasks}
- 符合同步条件的任务: ${filteredTasks}
- 插件中的任务数量: ${syncedTasks}
- 差异: ${Math.abs(filteredTasks - syncedTasks)}

🚫 被过滤的任务:
- 缺少#task标签: ${noTaskTagTasks}
- 文本太长: ${tooLongTasks}

📝 被过滤任务示例:
${sampleFilteredTasks.map(task => 
	`- ${task.reason}: ${task.file}:${task.line}\n  "${task.text}"${task.length ? ` (长度: ${task.length})` : ''}`
).join('\n')}

💡 建议:
${filteredTasks > syncedTasks ? 
	'- 可能有重复任务被去重了\n- 或者同步过程中有错误' : 
	'- 检查被过滤的任务是否需要添加#task标签\n- 考虑缩短过长的任务描述'}
			`.trim();

			console.log('=== 同步差异详细分析 ===');
			console.log('Markdown任务总数:', totalMarkdownTasks);
			console.log('符合条件的任务:', filteredTasks);
			console.log('插件中的任务:', syncedTasks);
			console.log('被过滤的任务示例:', sampleFilteredTasks);
			console.log('===================');

			new Notice(info, 20000);
		} catch (error) {
			console.error('Error analyzing sync differences:', error);
			new Notice(`分析失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	// Helper method for user confirmation
	private async confirmAction(title: string, message: string): Promise<boolean> {
		return new Promise((resolve) => {
			const modal = new ConfirmModal(this.app, title, message, (result) => {
				resolve(result);
			});
			modal.open();
		});
	}

	// Helper method for user input
	private async promptForInput(title: string, placeholder: string): Promise<string | null> {
		return new Promise((resolve) => {
			const modal = new InputModal(this.app, title, placeholder, (result) => {
				resolve(result);
			});
			modal.open();
		});
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}

class ProjectTaskManagerSettingTab extends PluginSettingTab {
	plugin: ProjectTaskManagerPlugin;

	constructor(app: App, plugin: ProjectTaskManagerPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const {containerEl} = this;

		containerEl.empty();

		// General Settings Section
		containerEl.createEl('h2', {text: 'General Settings'});

		new Setting(containerEl)
			.setName('Enable Project Mode')
			.setDesc('Enable project-based task organization')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableProjectMode)
				.onChange(async (value) => {
					this.plugin.settings.enableProjectMode = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Enable Independent Task Mode')
			.setDesc('Allow task management without projects')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableIndependentTaskMode)
				.onChange(async (value) => {
					this.plugin.settings.enableIndependentTaskMode = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Default View')
			.setDesc('Choose the default view when opening the plugin')
			.addDropdown(dropdown => dropdown
				.addOption('dashboard', 'Dashboard')
				.addOption('kanban', 'Kanban Board')
				.addOption('list', 'Task List')
				.addOption('gantt', 'Gantt Chart')
				.setValue(this.plugin.settings.defaultView)
				.onChange(async (value: string) => {
					this.plugin.settings.defaultView = value as 'dashboard' | 'kanban' | 'list' | 'gantt';
					await this.plugin.saveSettings();
				}));

		// Tasks Plugin Integration Section
		containerEl.createEl('h2', {text: 'Tasks Plugin Integration'});

		new Setting(containerEl)
			.setName('Enable Tasks Plugin Sync')
			.setDesc('Synchronize with the Tasks plugin')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableTasksPluginSync)
				.onChange(async (value) => {
					this.plugin.settings.enableTasksPluginSync = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Tasks Plugin Compatibility')
			.setDesc('Maintain compatibility with Tasks plugin emoji syntax')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.tasksPluginCompatibility)
				.onChange(async (value) => {
					this.plugin.settings.tasksPluginCompatibility = value;
					await this.plugin.saveSettings();
				}));

		// Performance Settings Section
		containerEl.createEl('h2', {text: 'Performance Settings'});

		new Setting(containerEl)
			.setName('Enable Caching')
			.setDesc('Cache data for better performance')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableCaching)
				.onChange(async (value) => {
					this.plugin.settings.enableCaching = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Max Cache Size')
			.setDesc('Maximum number of items to cache')
			.addText(text => text
				.setPlaceholder('1000')
				.setValue(this.plugin.settings.maxCacheSize.toString())
				.onChange(async (value) => {
					const numValue = parseInt(value);
					if (!isNaN(numValue) && numValue > 0) {
						this.plugin.settings.maxCacheSize = numValue;
						await this.plugin.saveSettings();
					}
				}));

		new Setting(containerEl)
			.setName('Enable Virtual Scrolling')
			.setDesc('Use virtual scrolling for large task lists')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.enableVirtualScrolling)
				.onChange(async (value) => {
					this.plugin.settings.enableVirtualScrolling = value;
					await this.plugin.saveSettings();
				}));

		// UI Settings Section
		containerEl.createEl('h2', {text: 'UI Settings'});

		new Setting(containerEl)
			.setName('Show Status Bar')
			.setDesc('Display plugin status in the status bar')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.showStatusBar)
				.onChange(async (value) => {
					this.plugin.settings.showStatusBar = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Show Ribbon Icon')
			.setDesc('Display plugin icon in the left ribbon')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.showRibbonIcon)
				.onChange(async (value) => {
					this.plugin.settings.showRibbonIcon = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Compact Mode')
			.setDesc('Use compact layout for better space utilization')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.compactMode)
				.onChange(async (value) => {
					this.plugin.settings.compactMode = value;
					await this.plugin.saveSettings();
				}));
	}
}

class ConfirmModal extends Modal {
	private title: string;
	private message: string;
	private onSubmit: (result: boolean) => void;

	constructor(app: App, title: string, message: string, onSubmit: (result: boolean) => void) {
		super(app);
		this.title = title;
		this.message = message;
		this.onSubmit = onSubmit;
	}

	onOpen() {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: this.title });
		contentEl.createEl('p', { text: this.message });

		const buttonContainer = contentEl.createDiv({ cls: 'modal-button-container' });
		buttonContainer.style.display = 'flex';
		buttonContainer.style.gap = '10px';
		buttonContainer.style.justifyContent = 'flex-end';
		buttonContainer.style.marginTop = '20px';

		const cancelButton = buttonContainer.createEl('button', { text: '取消' });
		cancelButton.onclick = () => {
			this.close();
			this.onSubmit(false);
		};

		const confirmButton = buttonContainer.createEl('button', { text: '确认', cls: 'mod-cta' });
		confirmButton.onclick = () => {
			this.close();
			this.onSubmit(true);
		};
	}

	onClose() {
		const { contentEl } = this;
		contentEl.empty();
	}
}

class InputModal extends Modal {
	private title: string;
	private placeholder: string;
	private onSubmit: (result: string) => void;
	private inputEl!: HTMLInputElement;

	constructor(app: App, title: string, placeholder: string, onSubmit: (result: string) => void) {
		super(app);
		this.title = title;
		this.placeholder = placeholder;
		this.onSubmit = onSubmit;
	}

	onOpen() {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: this.title });

		this.inputEl = contentEl.createEl('input', {
			type: 'text',
			placeholder: this.placeholder
		});
		this.inputEl.style.width = '100%';
		this.inputEl.style.marginBottom = '20px';

		const buttonContainer = contentEl.createDiv({ cls: 'modal-button-container' });
		buttonContainer.style.display = 'flex';
		buttonContainer.style.gap = '10px';
		buttonContainer.style.justifyContent = 'flex-end';

		const cancelButton = buttonContainer.createEl('button', { text: '取消' });
		cancelButton.onclick = () => {
			this.close();
			this.onSubmit('');
		};

		const submitButton = buttonContainer.createEl('button', { text: '确认', cls: 'mod-cta' });
		submitButton.onclick = () => {
			this.close();
			this.onSubmit(this.inputEl.value);
		};

		this.inputEl.addEventListener('keydown', (e) => {
			if (e.key === 'Enter') {
				this.close();
				this.onSubmit(this.inputEl.value);
			}
		});

		this.inputEl.focus();
	}

	onClose() {
		const { contentEl } = this;
		contentEl.empty();
	}
}