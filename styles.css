/*
Project Task Manager Plugin Styles
This CSS file provides comprehensive styling for the PTM plugin components
with full Obsidian theme integration and responsive design support.
*/

/* CSS Variables for theme integration */
:root {
  /* Spacing */
  --ptm-spacing-xs: 0.25rem;
  --ptm-spacing-sm: 0.5rem;
  --ptm-spacing-md: 1rem;
  --ptm-spacing-lg: 1.5rem;
  --ptm-spacing-xl: 2rem;
  
  /* Border radius */
  --ptm-radius-sm: 0.25rem;
  --ptm-radius-md: 0.375rem;
  --ptm-radius-lg: 0.5rem;
  
  /* Shadows */
  --ptm-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ptm-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ptm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --ptm-transition: all 0.2s ease-in-out;
  
  /* Z-index */
  --ptm-z-dropdown: 1000;
  --ptm-z-modal: 1050;
  --ptm-z-tooltip: 1100;
}

.project-task-manager-ribbon-class {
    color: var(--text-muted);
}

.project-task-manager-ribbon-class:hover {
    color: var(--text-normal);
}

/* Project Dashboard Styles */
.ptm-dashboard {
    padding: 20px;
}

.ptm-project-card {
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: var(--background-primary);
}

.ptm-project-title {
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 8px;
    color: var(--text-normal);
}

.ptm-project-description {
    color: var(--text-muted);
    margin-bottom: 12px;
}

.ptm-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-modifier-border);
    border-radius: 4px;
    overflow: hidden;
}

.ptm-progress-fill {
    height: 100%;
    background: var(--interactive-accent);
    transition: width 0.3s ease;
}

/* Task List Styles */
.ptm-task-list {
    list-style: none;
    padding: 0;
}

.ptm-task-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--background-modifier-border);
}

.ptm-task-checkbox {
    margin-right: 12px;
}

.ptm-task-title {
    flex: 1;
    color: var(--text-normal);
}

.ptm-task-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.ptm-priority-high {
    background: var(--text-error);
    color: white;
}

.ptm-priority-medium {
    background: var(--text-warning);
    color: white;
}

.ptm-priority-low {
    background: var(--text-success);
    color: white;
}

/* Kanban Board Styles */
.ptm-kanban-board {
    display: flex;
    gap: 16px;
    padding: 20px;
    overflow-x: auto;
}

.ptm-kanban-column {
    min-width: 300px;
    background: var(--background-secondary);
    border-radius: 8px;
    padding: 16px;
}

.ptm-column-header {
    font-weight: bold;
    margin-bottom: 16px;
    color: var(--text-normal);
    text-align: center;
}

.ptm-task-card {
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    cursor: grab;
    transition: box-shadow 0.2s ease;
}

.ptm-task-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ptm-task-card.dragging {
    opacity: 0.5;
    cursor: grabbing;
}
/*
 ==========================================================================
   Base Layout Components
   ========================================================================== */

/* Container */
.ptm-container {
  width: 100%;
  margin: 0 auto;
}

.ptm-container--sm { max-width: 640px; }
.ptm-container--md { max-width: 768px; }
.ptm-container--lg { max-width: 1024px; }
.ptm-container--xl { max-width: 1280px; }
.ptm-container--full { max-width: 100%; }
.ptm-container--padded { padding: var(--ptm-spacing-md); }

/* Grid Layout */
.ptm-grid {
  display: grid;
  width: 100%;
}

.ptm-grid--cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.ptm-grid--cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.ptm-grid--cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.ptm-grid--cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.ptm-grid--cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.ptm-grid--cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.ptm-grid--gap-none { gap: 0; }
.ptm-grid--gap-sm { gap: var(--ptm-spacing-sm); }
.ptm-grid--gap-md { gap: var(--ptm-spacing-md); }
.ptm-grid--gap-lg { gap: var(--ptm-spacing-lg); }

/* Responsive grid */
.ptm-grid--responsive.ptm-grid--cols-2 {
  grid-template-columns: 1fr;
}

.ptm-grid--responsive.ptm-grid--cols-3 {
  grid-template-columns: 1fr;
}

.ptm-grid--responsive.ptm-grid--cols-4 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .ptm-grid--responsive.ptm-grid--cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .ptm-grid--responsive.ptm-grid--cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .ptm-grid--responsive.ptm-grid--cols-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .ptm-grid--responsive.ptm-grid--cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .ptm-grid--responsive.ptm-grid--cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Flex Layout */
.ptm-flex {
  display: flex;
}

.ptm-flex--row { flex-direction: row; }
.ptm-flex--column { flex-direction: column; }

.ptm-flex--align-start { align-items: flex-start; }
.ptm-flex--align-center { align-items: center; }
.ptm-flex--align-end { align-items: flex-end; }
.ptm-flex--align-stretch { align-items: stretch; }

.ptm-flex--justify-start { justify-content: flex-start; }
.ptm-flex--justify-center { justify-content: center; }
.ptm-flex--justify-end { justify-content: flex-end; }
.ptm-flex--justify-between { justify-content: space-between; }
.ptm-flex--justify-around { justify-content: space-around; }

.ptm-flex--wrap { flex-wrap: wrap; }

.ptm-flex--gap-sm { gap: var(--ptm-spacing-sm); }
.ptm-flex--gap-md { gap: var(--ptm-spacing-md); }
.ptm-flex--gap-lg { gap: var(--ptm-spacing-lg); }

/* Stack Layout */
.ptm-stack {
  display: flex;
  flex-direction: column;
}

/* ==========================================================================
   Button Component
   ========================================================================== */

.ptm-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--ptm-radius-md);
  font-weight: 500;
  transition: var(--ptm-transition);
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.ptm-button:focus {
  outline: 2px solid var(--interactive-accent);
  outline-offset: 2px;
}

.ptm-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button sizes */
.ptm-button--sm {
  height: 2rem;
  padding: 0 var(--ptm-spacing-sm);
  font-size: 0.875rem;
}

.ptm-button--md {
  height: 2.5rem;
  padding: 0 var(--ptm-spacing-md);
  font-size: 0.875rem;
}

.ptm-button--lg {
  height: 3rem;
  padding: 0 var(--ptm-spacing-lg);
  font-size: 1rem;
}

/* Button variants */
.ptm-button--primary {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent, white);
  border-color: var(--interactive-accent);
}

.ptm-button--primary:hover:not(.ptm-button--disabled) {
  background-color: var(--interactive-accent-hover);
  border-color: var(--interactive-accent-hover);
}

.ptm-button--secondary {
  background-color: var(--interactive-normal);
  color: var(--text-normal);
  border-color: var(--background-modifier-border);
}

.ptm-button--secondary:hover:not(.ptm-button--disabled) {
  background-color: var(--interactive-hover);
  border-color: var(--background-modifier-border-hover);
}

.ptm-button--ghost {
  background-color: transparent;
  color: var(--text-normal);
  border-color: transparent;
}

.ptm-button--ghost:hover:not(.ptm-button--disabled) {
  background-color: var(--interactive-hover);
}

.ptm-button--destructive {
  background-color: var(--text-error);
  color: white;
  border-color: var(--text-error);
}

.ptm-button--destructive:hover:not(.ptm-button--disabled) {
  opacity: 0.9;
}

/* Button content */
.ptm-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

.ptm-button__text {
  display: flex;
  align-items: center;
}

.ptm-button__icon + .ptm-button__text {
  margin-left: var(--ptm-spacing-sm);
}

.ptm-button--icon-only {
  width: auto;
  aspect-ratio: 1;
}

/* Button loading state */
.ptm-button--loading .ptm-button__text,
.ptm-button--loading .ptm-button__icon {
  opacity: 0;
}

.ptm-button__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1rem;
  height: 1rem;
}

.ptm-spinner {
  width: 100%;
  height: 100%;
  animation: ptm-spin 1s linear infinite;
}

.ptm-spinner__circle {
  stroke: currentColor;
  stroke-linecap: round;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: ptm-spinner-dash 2s ease-in-out infinite;
}

@keyframes ptm-spin {
  to { transform: rotate(360deg); }
}

@keyframes ptm-spinner-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* ==========================================================================
   Card Component
   ========================================================================== */

.ptm-card {
  border-radius: var(--ptm-radius-lg);
  transition: var(--ptm-transition);
  overflow: hidden;
}

.ptm-card--default {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
}

.ptm-card--elevated {
  background-color: var(--background-primary);
  box-shadow: var(--ptm-shadow-md);
  border: 1px solid var(--background-modifier-border);
}

.ptm-card--outlined {
  background-color: var(--background-primary);
  border: 2px solid var(--background-modifier-border-hover);
}

.ptm-card:hover {
  border-color: var(--background-modifier-border-hover);
}

.ptm-card--elevated:hover {
  box-shadow: var(--ptm-shadow-lg);
}

/* Card padding */
.ptm-card--no-padding { padding: 0; }
.ptm-card--padding-sm { padding: var(--ptm-spacing-sm); }
.ptm-card--padding-md { padding: var(--ptm-spacing-md); }
.ptm-card--padding-lg { padding: var(--ptm-spacing-lg); }

/* Card sections */
.ptm-card__header {
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: var(--background-secondary);
}

.ptm-card__content {
  padding: var(--ptm-spacing-md);
}

.ptm-card__footer {
  padding: var(--ptm-spacing-md);
  border-top: 1px solid var(--background-modifier-border);
  background-color: var(--background-secondary);
}

.ptm-card--no-padding .ptm-card__header,
.ptm-card--no-padding .ptm-card__content,
.ptm-card--no-padding .ptm-card__footer {
  padding: var(--ptm-spacing-md);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 767px) {
  .ptm-container--padded {
    padding: var(--ptm-spacing-sm);
  }
  
  .ptm-button--md {
    height: 2.25rem;
    padding: 0 var(--ptm-spacing-sm);
    font-size: 0.8125rem;
  }
  
  .ptm-card__header,
  .ptm-card__content,
  .ptm-card__footer {
    padding: var(--ptm-spacing-sm);
  }
}

/* ==========================================================================
   Theme Integration
   ========================================================================== */

/* Theme-specific adjustments */
.theme-dark {
  --ptm-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --ptm-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --ptm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   Accessibility
   ========================================================================== */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .ptm-button,
  .ptm-card,
  .ptm-spinner {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ptm-button {
    border-width: 2px;
  }
  
  .ptm-card {
    border-width: 2px;
  }
}

/* Focus visible for keyboard navigation */
.ptm-button:focus-visible {
  outline: 2px solid var(--interactive-accent);
  outline-offset: 2px;
}

.ptm-card:focus-visible {
  outline: 2px solid var(--interactive-accent);
  outline-offset: 2px;
}/* ===
=======================================================================
   Progress Ring Component
   ========================================================================== */

.ptm-progress-ring {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ptm-progress-ring__svg {
  transform: rotate(0deg);
  transition: var(--ptm-transition);
}

.ptm-progress-ring__progress {
  transition: stroke-dashoffset 0.5s ease-in-out;
}

.ptm-progress-ring__label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.ptm-progress-ring__percentage {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-normal);
  line-height: 1;
}

.ptm-progress-ring__text {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
  line-height: 1;
}

/* ==========================================================================
   Project Overview Card Component
   ========================================================================== */

.ptm-project-overview-card {
  min-width: 300px;
  max-width: 400px;
}

.ptm-project-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.ptm-project-description {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

.ptm-project-status {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.25rem 0.5rem;
  border-radius: var(--ptm-radius-sm);
  background-color: var(--background-modifier-border);
}

.ptm-project-stats {
  flex: 1;
}

.ptm-stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-right: 0.5rem;
}

.ptm-stat-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-normal);
}

.ptm-project-timeline {
  padding: var(--ptm-spacing-sm);
  background-color: var(--background-secondary);
  border-radius: var(--ptm-radius-md);
  border: 1px solid var(--background-modifier-border);
}

.ptm-timeline-label {
  font-size: 0.8125rem;
  color: var(--text-muted);
  margin-right: 0.5rem;
}

.ptm-timeline-value {
  font-size: 0.8125rem;
  font-weight: 500;
  color: var(--text-normal);
}

.ptm-project-alerts {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.ptm-alert {
  padding: 0.5rem;
  border-radius: var(--ptm-radius-sm);
  font-size: 0.8125rem;
  font-weight: 500;
}

.ptm-alert--error {
  background-color: color-mix(in srgb, var(--text-error) 10%, transparent);
  color: var(--text-error);
  border: 1px solid color-mix(in srgb, var(--text-error) 30%, transparent);
}

.ptm-alert--warning {
  background-color: color-mix(in srgb, var(--text-warning) 10%, transparent);
  color: var(--text-warning);
  border: 1px solid color-mix(in srgb, var(--text-warning) 30%, transparent);
}

.ptm-project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.ptm-tag {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background-color: var(--interactive-normal);
  color: var(--text-muted);
  border-radius: var(--ptm-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--background-modifier-border);
}

.ptm-tag:hover {
  background-color: var(--interactive-hover);
  color: var(--text-normal);
}/* ==
========================================================================
   Task List Component
   ========================================================================== */

.ptm-task-list-item {
  transition: var(--ptm-transition);
}

.ptm-task-list-item:hover {
  background-color: var(--interactive-hover);
}

.ptm-task-list-item:active {
  background-color: var(--interactive-normal);
}
/* Error
 handling styles */
.ptm-error-message {
  padding: var(--ptm-spacing-lg);
  text-align: center;
  color: var(--text-error);
  background-color: var(--background-modifier-error);
  border: 1px solid var(--text-error);
  border-radius: var(--ptm-radius-md);
  margin: var(--ptm-spacing-md);
}

.ptm-retry-button {
  margin-top: var(--ptm-spacing-md);
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
  border-radius: var(--ptm-radius-md);
  cursor: pointer;
  font-weight: 500;
}

.ptm-retry-button:hover {
  background-color: var(--interactive-accent-hover);
}

/* Task list view container */
.ptm-task-list-view-container {
  height: 100%;
  overflow: hidden;
}

/* ==========================================================================
   Kanban Board Styles
   ========================================================================== */

/* Kanban Board Container */
.ptm-kanban-board {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.ptm-kanban-content {
  flex: 1;
  overflow: auto;
  padding: var(--ptm-spacing-md);
}

.ptm-kanban-columns {
  display: flex;
  gap: var(--ptm-spacing-md);
  min-height: 100%;
  align-items: flex-start;
}

.ptm-kanban-swimlanes {
  display: flex;
  flex-direction: column;
  gap: var(--ptm-spacing-lg);
}

/* Kanban Column */
.ptm-kanban-column {
  flex: 0 0 320px;
  min-width: 300px;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  transition: var(--ptm-transition);
}

.ptm-kanban-column.drag-over {
  transform: scale(1.02);
  box-shadow: var(--ptm-shadow-lg);
}

.ptm-kanban-column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: var(--background-secondary);
}

.ptm-column-title {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-sm);
  flex: 1;
}

.ptm-column-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-normal);
}

.ptm-column-badges {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
}

.ptm-task-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
  height: 1.5rem;
  background-color: var(--interactive-normal);
  color: var(--text-muted);
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}

.ptm-wip-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.125rem 0.375rem;
  border-radius: var(--ptm-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid;
}

.ptm-wip-indicator.normal {
  background-color: var(--text-success);
  color: white;
  border-color: var(--text-success);
}

.ptm-wip-indicator.warning {
  background-color: var(--text-warning);
  color: white;
  border-color: var(--text-warning);
}

.ptm-wip-indicator.violation {
  background-color: var(--text-error);
  color: white;
  border-color: var(--text-error);
  animation: ptm-pulse 2s infinite;
}

@keyframes ptm-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.ptm-add-task-btn {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: bold;
}

.ptm-kanban-column-content {
  flex: 1;
  padding: var(--ptm-spacing-md);
  min-height: 200px;
}

.ptm-empty-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-xl);
  text-align: center;
  color: var(--text-muted);
  border: 2px dashed var(--background-modifier-border);
  border-radius: var(--ptm-radius-md);
  min-height: 150px;
}

.ptm-empty-column p {
  margin: 0 0 var(--ptm-spacing-md) 0;
  font-size: 0.875rem;
}

.ptm-task-list {
  display: flex;
  flex-direction: column;
  gap: var(--ptm-spacing-sm);
}

.ptm-drop-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed var(--interactive-accent);
  border-radius: var(--ptm-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.ptm-drop-zone {
  padding: var(--ptm-spacing-md);
  background-color: var(--interactive-accent);
  color: white;
  border-radius: var(--ptm-radius-md);
  font-weight: 600;
  font-size: 0.875rem;
}

/* Kanban Task Card */
.ptm-task-card {
  position: relative;
  cursor: grab;
  transition: var(--ptm-transition);
  user-select: none;
}

.ptm-task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ptm-shadow-md);
}

.ptm-task-card:active {
  cursor: grabbing;
}

.ptm-task-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(0.95);
  cursor: grabbing;
  z-index: 1000;
}

.ptm-task-card.overdue {
  border-left-color: var(--text-error) !important;
  background-color: rgba(239, 68, 68, 0.05);
}

.ptm-task-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ptm-spacing-xs);
}

.ptm-task-status {
  display: flex;
  align-items: center;
}

.ptm-status-emoji {
  font-size: 1rem;
}

.ptm-task-actions {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  opacity: 0;
  transition: var(--ptm-transition);
}

.ptm-task-card:hover .ptm-task-actions {
  opacity: 1;
}

.ptm-task-title {
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-task-title h4 {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-normal);
  line-height: 1.3;
}

.ptm-task-description {
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-task-description p {
  margin: 0;
  font-size: 0.8125rem;
  color: var(--text-muted);
  line-height: 1.4;
}

.ptm-task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-tag {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background-color: var(--interactive-normal);
  color: var(--text-muted);
  border-radius: var(--ptm-radius-sm);
  font-size: 0.6875rem;
  font-weight: 500;
  border: 1px solid var(--background-modifier-border);
}

.ptm-tag-more {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background-color: var(--background-modifier-border);
  color: var(--text-muted);
  border-radius: var(--ptm-radius-sm);
  font-size: 0.6875rem;
  font-weight: 500;
}

.ptm-task-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-task-assignee {
  display: flex;
  align-items: center;
}

.ptm-assignee-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: var(--interactive-accent);
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}

.ptm-task-due-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.ptm-task-due-date.overdue {
  color: var(--text-error);
  font-weight: 600;
}

.ptm-task-hours {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.ptm-task-indicators {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-sm);
}

.ptm-task-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Kanban Loading and Error States */
.ptm-kanban-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-xl);
  text-align: center;
  color: var(--text-muted);
}

.ptm-kanban-loading .ptm-spinner {
  width: 2rem;
  height: 2rem;
  margin-bottom: var(--ptm-spacing-md);
}

.ptm-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.ptm-error-state h3 {
  margin: 0 0 var(--ptm-spacing-sm) 0;
  color: var(--text-error);
}

.ptm-error-state p {
  margin: 0 0 var(--ptm-spacing-md) 0;
  color: var(--text-muted);
}

.ptm-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.ptm-empty-state h3 {
  margin: 0 0 var(--ptm-spacing-sm) 0;
  color: var(--text-normal);
}

.ptm-empty-state p {
  margin: 0;
  color: var(--text-muted);
}

/* Responsive Design for Kanban */
@media (max-width: 1024px) {
  .ptm-kanban-columns {
    flex-direction: column;
    gap: var(--ptm-spacing-lg);
  }
  
  .ptm-kanban-column {
    flex: none;
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .ptm-kanban-content {
    padding: var(--ptm-spacing-sm);
  }
  
  .ptm-kanban-column-header {
    padding: var(--ptm-spacing-sm);
  }
  
  .ptm-kanban-column-content {
    padding: var(--ptm-spacing-sm);
  }
  
  .ptm-task-card {
    margin-bottom: var(--ptm-spacing-sm);
  }
}/
* Sprint 管理样式 */
.sprint-view-container {
	padding: 20px;
	height: 100%;
	overflow-y: auto;
}

.sprint-view-header {
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 1px solid var(--background-modifier-border);
}

.project-selector {
	display: flex;
	align-items: center;
	gap: 10px;
}

.project-selector label {
	font-weight: 500;
	color: var(--text-normal);
}

/* Sprint Board */
.sprint-board {
	width: 100%;
}

.sprint-board-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20px;
	gap: 20px;
}

.sprint-board-title h2 {
	margin: 0 0 10px 0;
	color: var(--text-normal);
}

.sprint-stats {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
}

.sprint-stat {
	padding: 4px 8px;
	background: var(--background-secondary);
	border-radius: 4px;
	font-size: 0.9em;
	color: var(--text-muted);
}

.sprint-board-controls {
	display: flex;
	gap: 10px;
	align-items: center;
}

.sprint-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
	gap: 20px;
}

/* Sprint Card */
.sprint-card {
	background: var(--background-primary);
	border: 1px solid var(--background-modifier-border);
	border-radius: 8px;
	padding: 20px;
	transition: box-shadow 0.2s ease;
}

.sprint-card:hover {
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sprint-card.active {
	border-color: var(--color-accent);
	box-shadow: 0 0 0 1px var(--color-accent);
}

.sprint-card.completed {
	opacity: 0.8;
}

.sprint-card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 15px;
	gap: 15px;
}

.sprint-card-title h3 {
	margin: 0;
	color: var(--text-normal);
	font-size: 1.2em;
}

.sprint-goal {
	margin: 5px 0 0 0;
	color: var(--text-muted);
	font-size: 0.9em;
	line-height: 1.4;
}

.sprint-status-badge {
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 0.8em;
	font-weight: 500;
	color: white;
	white-space: nowrap;
}

.sprint-overdue-badge {
	padding: 2px 6px;
	background: var(--color-red);
	color: white;
	border-radius: 8px;
	font-size: 0.7em;
	font-weight: 500;
	margin-left: 5px;
}

.sprint-card-dates {
	margin-bottom: 15px;
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.sprint-date {
	display: flex;
	justify-content: space-between;
	font-size: 0.9em;
}

.sprint-date-label {
	color: var(--text-muted);
	font-weight: 500;
}

.sprint-duration {
	display: flex;
	justify-content: space-between;
	font-size: 0.9em;
	padding-top: 5px;
	border-top: 1px solid var(--background-modifier-border);
}

.sprint-remaining {
	color: var(--color-accent);
	font-weight: 500;
}

/* Sprint Stats */
.sprint-card-stats {
	margin-bottom: 20px;
}

.sprint-stat-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.sprint-stat-label {
	color: var(--text-muted);
	font-size: 0.9em;
}

.sprint-stat-value {
	color: var(--text-normal);
	font-weight: 500;
}

.sprint-progress-bar {
	flex: 1;
	height: 6px;
	background: var(--background-modifier-border);
	border-radius: 3px;
	margin: 0 10px;
	overflow: hidden;
}

.sprint-progress-fill {
	height: 100%;
	background: var(--color-accent);
	transition: width 0.3s ease;
}

.sprint-stat-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10px;
	margin: 10px 0;
	padding: 10px;
	background: var(--background-secondary);
	border-radius: 6px;
}

.sprint-stat-grid .sprint-stat-item {
	flex-direction: column;
	text-align: center;
	margin: 0;
}

/* Sprint Actions */
.sprint-card-actions {
	display: flex;
	justify-content: space-between;
	gap: 10px;
}

.sprint-action-group {
	display: flex;
	gap: 8px;
}

/* Sprint Edit Form */
.sprint-edit-form {
	width: 100%;
}

.sprint-name-input,
.sprint-goal-input {
	width: 100%;
	margin-bottom: 10px;
}

.sprint-goal-input {
	resize: vertical;
	min-height: 60px;
}

.sprint-edit-buttons {
	display: flex;
	gap: 8px;
}

/* Task Assignment Modal */
.task-assignment-search {
	margin-bottom: 20px;
}

.task-search-input {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid var(--background-modifier-border);
	border-radius: 4px;
	background: var(--background-primary);
	color: var(--text-normal);
}

.task-assignment-container {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20px;
	max-height: 500px;
}

.task-assignment-section h4 {
	margin: 0 0 15px 0;
	color: var(--text-normal);
	padding-bottom: 8px;
	border-bottom: 1px solid var(--background-modifier-border);
}

.task-assignment-list {
	max-height: 400px;
	overflow-y: auto;
	border: 1px solid var(--background-modifier-border);
	border-radius: 4px;
}

.task-assignment-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 12px;
	border-bottom: 1px solid var(--background-modifier-border);
	gap: 10px;
}

.task-assignment-item:last-child {
	border-bottom: none;
}

.task-info {
	flex: 1;
}

.task-title {
	font-weight: 500;
	color: var(--text-normal);
	margin-bottom: 4px;
}

.task-description {
	color: var(--text-muted);
	font-size: 0.9em;
	margin-bottom: 6px;
	line-height: 1.3;
}

.task-meta {
	display: flex;
	gap: 8px;
	align-items: center;
	font-size: 0.8em;
}

.task-status {
	font-weight: 500;
}

.task-hours {
	color: var(--text-muted);
}

.task-priority {
	padding: 2px 6px;
	border-radius: 8px;
	font-weight: 500;
	font-size: 0.7em;
}

.task-priority.priority-high {
	background: var(--color-red);
	color: white;
}

.task-priority.priority-medium {
	background: var(--color-orange);
	color: white;
}

.task-priority.priority-low {
	background: var(--color-green);
	color: white;
}

.task-action-button {
	padding: 6px 12px;
	border: none;
	border-radius: 4px;
	font-size: 0.8em;
	font-weight: 500;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.add-button {
	background: var(--color-accent);
	color: white;
}

.add-button:hover {
	background: var(--color-accent-hover);
}

.remove-button {
	background: var(--color-red);
	color: white;
}

.remove-button:hover {
	background: var(--color-red-hover);
}

.empty-task-list {
	padding: 40px 20px;
	text-align: center;
	color: var(--text-muted);
	font-style: italic;
}

/* Burndown Chart */
.burndown-chart-container {
	padding: 20px 0;
}

.burndown-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 15px;
	margin-bottom: 30px;
	padding: 20px;
	background: var(--background-secondary);
	border-radius: 8px;
}

.burndown-stat {
	text-align: center;
}

.stat-label {
	display: block;
	color: var(--text-muted);
	font-size: 0.9em;
	margin-bottom: 5px;
}

.stat-value {
	display: block;
	color: var(--text-normal);
	font-size: 1.2em;
	font-weight: 600;
}

.stat-value.ahead {
	color: var(--color-green);
}

.stat-value.behind {
	color: var(--color-red);
}

.burndown-chart {
	margin-bottom: 30px;
	text-align: center;
}

.burndown-legend {
	display: flex;
	justify-content: center;
	gap: 30px;
	margin-bottom: 30px;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 0.9em;
	color: var(--text-normal);
}

.legend-line {
	width: 30px;
	height: 3px;
	border-radius: 2px;
}

.legend-line.ideal {
	background: var(--text-muted);
	background-image: repeating-linear-gradient(
		45deg,
		transparent,
		transparent 3px,
		var(--background-primary) 3px,
		var(--background-primary) 6px
	);
}

.legend-line.actual {
	background: var(--color-accent);
}

.burndown-data-table {
	max-width: 400px;
	margin: 0 auto;
}

.burndown-data-table h4 {
	margin-bottom: 15px;
	text-align: center;
	color: var(--text-normal);
}

.burndown-data-table table {
	width: 100%;
	border-collapse: collapse;
}

.burndown-data-table th,
.burndown-data-table td {
	padding: 8px 12px;
	text-align: left;
	border-bottom: 1px solid var(--background-modifier-border);
}

.burndown-data-table th {
	background: var(--background-secondary);
	font-weight: 500;
	color: var(--text-normal);
}

.burndown-data-table td {
	color: var(--text-muted);
}

/* Sprint Retrospective */
.sprint-retrospective {
	max-height: 70vh;
	overflow-y: auto;
	padding-right: 10px;
}

.retrospective-stats {
	margin-bottom: 30px;
	padding: 20px;
	background: var(--background-secondary);
	border-radius: 8px;
}

.retrospective-stats h4 {
	margin: 0 0 15px 0;
	color: var(--text-normal);
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
	gap: 15px;
}

.stat-item {
	text-align: center;
}

.retrospective-section {
	margin-bottom: 30px;
}

.retrospective-section h4 {
	margin: 0 0 15px 0;
	color: var(--text-normal);
	font-size: 1.1em;
}

.retrospective-item {
	display: flex;
	gap: 10px;
	margin-bottom: 10px;
}

.retrospective-item textarea {
	flex: 1;
	min-height: 60px;
	padding: 8px 12px;
	border: 1px solid var(--background-modifier-border);
	border-radius: 4px;
	background: var(--background-primary);
	color: var(--text-normal);
	resize: vertical;
}

.remove-item-button {
	width: 30px;
	height: 30px;
	border: none;
	background: var(--color-red);
	color: white;
	border-radius: 4px;
	cursor: pointer;
	font-size: 16px;
	line-height: 1;
	flex-shrink: 0;
}

.remove-item-button:hover {
	background: var(--color-red-hover);
}

.add-item-button {
	padding: 8px 16px;
	border: 1px dashed var(--background-modifier-border);
	background: transparent;
	color: var(--text-muted);
	border-radius: 4px;
	cursor: pointer;
	font-size: 0.9em;
	transition: all 0.2s ease;
}

.add-item-button:hover {
	border-color: var(--color-accent);
	color: var(--color-accent);
	background: var(--background-secondary);
}

.velocity-adjustment {
	padding: 15px;
	background: var(--background-secondary);
	border-radius: 6px;
}

.velocity-adjustment label {
	display: flex;
	align-items: center;
	gap: 10px;
	color: var(--text-normal);
	font-weight: 500;
}

.velocity-adjustment input {
	width: 80px;
	padding: 4px 8px;
	border: 1px solid var(--background-modifier-border);
	border-radius: 4px;
	background: var(--background-primary);
	color: var(--text-normal);
}

.velocity-note {
	margin-top: 8px;
	font-size: 0.9em;
	color: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 768px) {
	.sprint-grid {
		grid-template-columns: 1fr;
	}
	
	.sprint-board-header {
		flex-direction: column;
		align-items: stretch;
	}
	
	.sprint-card-actions {
		flex-direction: column;
	}
	
	.task-assignment-container {
		grid-template-columns: 1fr;
	}
	
	.stats-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

/* ==================== 甘特图样式 ==================== */

/* 甘特图容器 */
.gantt-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-primary);
}

/* 甘特图工具栏 */
.gantt-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  flex-wrap: wrap;
  gap: var(--ptm-spacing-sm);
}

.gantt-toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-sm);
}

.gantt-toolbar-title {
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
  color: var(--text-normal);
}

.gantt-toolbar-group {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
}

.gantt-toolbar-label {
  font-size: 0.9em;
  color: var(--text-muted);
  white-space: nowrap;
}

.gantt-toolbar-select {
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9em;
}

.gantt-toolbar-btn {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-toolbar-btn:hover {
  background: var(--background-modifier-hover);
  border-color: var(--background-modifier-border-hover);
}

.gantt-toolbar-btn.active {
  background: var(--color-accent);
  color: var(--text-on-accent);
  border-color: var(--color-accent);
}

/* 甘特图内容区域 */
.gantt-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.gantt-main {
  flex: 1;
  overflow: auto;
}

/* 甘特图侧边栏 */
.gantt-sidebar {
  width: 300px;
  border-left: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  overflow-y: auto;
  flex-shrink: 0;
}

.gantt-sidebar-section {
  border-bottom: 1px solid var(--background-modifier-border);
}

.gantt-sidebar-header {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-md);
  cursor: pointer;
  font-weight: 500;
  color: var(--text-normal);
  transition: var(--ptm-transition);
}

.gantt-sidebar-header:hover {
  background: var(--background-modifier-hover);
}

.gantt-sidebar-content {
  padding: 0 var(--ptm-spacing-md) var(--ptm-spacing-md);
}

/* 统计项目 */
.gantt-stat-item {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-xs) 0;
  font-size: 0.9em;
  color: var(--text-muted);
}

/* 徽章 */
.gantt-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5em;
  height: 1.5em;
  padding: 0 var(--ptm-spacing-xs);
  background: var(--background-modifier-border);
  color: var(--text-muted);
  font-size: 0.75em;
  font-weight: 500;
  border-radius: 0.75em;
  margin-left: auto;
}

.gantt-badge.error {
  background: var(--color-red);
  color: var(--text-on-accent);
}

/* 任务项目 */
.gantt-task-item {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-xs);
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  transition: var(--ptm-transition);
  font-size: 0.9em;
}

.gantt-task-item:hover {
  background: var(--background-modifier-hover);
}

.gantt-task-item.selected {
  background: var(--color-accent);
  color: var(--text-on-accent);
}

.gantt-task-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.gantt-task-indicator.critical {
  background: var(--color-red);
}

.gantt-task-id {
  font-family: var(--font-monospace);
  font-size: 0.8em;
  color: var(--text-muted);
}

.gantt-task-slack {
  margin-left: auto;
  font-size: 0.8em;
  color: var(--text-muted);
}

/* 冲突项目 */
.gantt-conflict-item {
  padding: var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  margin-bottom: var(--ptm-spacing-sm);
  background: var(--background-primary);
}

.gantt-conflict-header {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-xs);
}

.gantt-conflict-severity {
  padding: 2px 6px;
  border-radius: var(--ptm-radius-sm);
  font-size: 0.75em;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
}

.gantt-conflict-resource {
  font-weight: 500;
  color: var(--text-normal);
}

.gantt-conflict-tasks {
  display: flex;
  flex-direction: column;
  gap: var(--ptm-spacing-xs);
  margin: var(--ptm-spacing-xs) 0;
}

.gantt-conflict-task {
  padding: var(--ptm-spacing-xs);
  background: var(--background-modifier-hover);
  border-radius: var(--ptm-radius-sm);
  font-family: var(--font-monospace);
  font-size: 0.8em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-conflict-task:hover {
  background: var(--background-modifier-border);
}

.gantt-conflict-type {
  font-size: 0.8em;
  color: var(--text-muted);
  font-style: italic;
}

/* 空状态 */
.gantt-sidebar-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-xl);
  text-align: center;
  color: var(--text-muted);
  height: 200px;
}

.gantt-sidebar-empty svg {
  opacity: 0.5;
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-sidebar-empty p {
  margin: var(--ptm-spacing-xs) 0;
  font-size: 0.9em;
}

/* 加载和错误状态 */
.gantt-loading,
.gantt-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--background-modifier-border);
  border-top: 3px solid var(--color-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--ptm-spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.gantt-error p {
  color: var(--color-red);
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-error button {
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-error button:hover {
  opacity: 0.8;
}

/* 无项目状态 */
.gantt-no-project {
  color: var(--text-muted);
}

.gantt-no-project h3 {
  color: var(--text-normal);
}

.gantt-no-project button {
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-no-project button:hover {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gantt-content {
    flex-direction: column;
  }
  
  .gantt-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--background-modifier-border);
    max-height: 300px;
  }
  
  .gantt-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .gantt-toolbar-section {
    justify-content: center;
  }
}

/* 甘特图库样式覆盖 - 简化版本 */
.gantt-main {
  min-height: 500px !important;
  height: 100% !important;
  background: var(--background-primary) !important;
}

.gantt-main .gantt-task-react {
  font-family: var(--font-interface) !important;
  width: 100% !important;
  height: 100% !important;
  background: var(--background-primary) !important;
}

/* 修复甘特图背景色问题 */
.gantt-task-react,
.gantt-task-react * {
  background-color: var(--background-primary) !important;
}

/* 甘特图表格区域 */
.gantt-task-react .gantt-table {
  background: var(--background-primary) !important;
  border-right: 1px solid var(--background-modifier-border) !important;
}

/* 甘特图图表区域 */
.gantt-task-react .gantt-chart {
  background: var(--background-primary) !important;
}

/* 修复SVG背景 */
.gantt-task-react svg {
  background: transparent !important;
}

/* 网格线颜色 */
.gantt-task-react .gantt-grid-row,
.gantt-task-react .gantt-grid-column {
  stroke: var(--background-modifier-border) !important;
}

/* 甘特图表格样式 - 强制重置 */
.gantt-task-react .gantt-table {
  display: table !important;
  width: 250px !important;
  background: var(--background-secondary) !important;
  border-right: 1px solid var(--background-modifier-border) !important;
  font-family: var(--font-interface) !important;
  font-size: 12px !important;
}

.gantt-task-react .gantt-table-header {
  display: table-header-group !important;
  background: var(--background-primary) !important;
  border-bottom: 1px solid var(--background-modifier-border) !important;
  color: var(--text-normal) !important;
  font-weight: 600 !important;
  height: 50px !important;
}

.gantt-task-react .gantt-table-body {
  display: table-row-group !important;
}

.gantt-task-react .gantt-table-row {
  display: table-row !important;
  height: 40px !important;
  border-bottom: 1px solid var(--background-modifier-border) !important;
  color: var(--text-normal) !important;
  background: var(--background-primary) !important;
}

.gantt-task-react .gantt-table-row:hover {
  background: var(--background-modifier-hover) !important;
}

.gantt-task-react .gantt-table-cell {
  display: table-cell !important;
  padding: 8px 12px !important;
  vertical-align: middle !important;
  border-right: 1px solid var(--background-modifier-border) !important;
  color: var(--text-normal) !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 修复任务名称显示 */
.gantt-task-react .gantt-table-cell .gantt-table-cell-content {
  color: var(--text-normal) !important;
  font-size: 12px !important;
}

/* 甘特图图表区域样式 */
.gantt-task-react .gantt-chart {
  background: var(--background-primary) !important;
}

.gantt-task-react .gantt-chart-header {
  background: var(--background-secondary) !important;
  border-bottom: 1px solid var(--background-modifier-border) !important;
  color: var(--text-normal) !important;
}

.gantt-task-react .gantt-chart-row {
  border-bottom: 1px solid var(--background-modifier-border) !important;
}

.gantt-task-react .gantt-chart-row:hover {
  background: var(--background-modifier-hover) !important;
}

/* 任务条样式 */
.gantt-task-react .gantt-task-bar {
  border-radius: var(--ptm-radius-sm) !important;
}

.gantt-task-react .gantt-task-bar-progress {
  border-radius: var(--ptm-radius-sm) !important;
}

.gantt-task-react .gantt-task-bar-handle {
  border-radius: 50% !important;
}

/* 依赖关系线样式 */
.gantt-task-react .gantt-dependency-line {
  stroke: var(--color-accent) !important;
  stroke-width: 2px !important;
}

.gantt-task-react .gantt-dependency-arrow {
  fill: var(--color-accent) !important;
}

/* 工具提示样式 */
.gantt-task-react .gantt-tooltip {
  background: var(--background-primary) !important;
  border: 1px solid var(--background-modifier-border) !important;
  border-radius: var(--ptm-radius-md) !important;
  box-shadow: var(--ptm-shadow-lg) !important;
  color: var(--text-normal) !important;
  font-family: var(--font-interface) !important;
  z-index: var(--ptm-z-tooltip) !important;
}

/* 修复可能的布局问题 */
.gantt-task-react svg {
  display: block !important;
}

.gantt-task-react .gantt-vertical-container {
  overflow: visible !important;
}/* ==
================== 甘特图视图样式 ==================== */

/* 甘特图视图容器 */
.gantt-view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-primary);
}

/* 甘特图视图头部 */
.gantt-view-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  flex-wrap: wrap;
  gap: var(--ptm-spacing-sm);
}

.project-selector {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-sm);
}

.project-selector label {
  font-weight: 500;
  color: var(--text-normal);
  white-space: nowrap;
}

.select-wrapper {
  position: relative;
  display: inline-block;
}

.select-wrapper select {
  appearance: none;
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-lg) var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9em;
  min-width: 200px;
  cursor: pointer;
}

.select-wrapper select:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px var(--color-accent-hover);
}

.select-icon {
  position: absolute;
  right: var(--ptm-spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.action-btn:hover {
  background: var(--background-modifier-hover);
  border-color: var(--background-modifier-border-hover);
}

.action-btn.active {
  background: var(--color-accent);
  color: var(--text-on-accent);
  border-color: var(--color-accent);
}

/* 甘特图视图内容 */
.gantt-view-content {
  flex: 1;
  overflow: hidden;
}

/* 甘特图视图加载状态 */
.gantt-view-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.gantt-view-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--background-modifier-border);
  border-top: 3px solid var(--color-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-view-loading p {
  color: var(--text-muted);
  font-size: 0.9em;
}

/* 甘特图视图错误状态 */
.gantt-view-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.gantt-view-error h3 {
  color: var(--color-red);
  margin-bottom: var(--ptm-spacing-sm);
}

.gantt-view-error p {
  color: var(--text-muted);
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-view-error button {
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-view-error button:hover {
  opacity: 0.8;
}

/* 甘特图视图空状态 */
.gantt-view-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
}

.empty-state {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--ptm-spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  color: var(--text-normal);
  margin-bottom: var(--ptm-spacing-sm);
}

.empty-state p {
  color: var(--text-muted);
  margin-bottom: var(--ptm-spacing-lg);
  line-height: 1.5;
}

.create-project-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  font-weight: 500;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.create-project-btn:hover {
  opacity: 0.8;
}

/* 甘特图测试样式 */
.gantt-test-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.gantt-test-header {
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
}

.gantt-test-header h2 {
  margin: 0 0 var(--ptm-spacing-xs) 0;
  color: var(--text-normal);
}

.gantt-test-header p {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.9em;
}

.gantt-test-loading,
.gantt-test-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
  text-align: center;
}

.gantt-test-error p {
  color: var(--color-red);
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-test-error button {
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-test-error button:hover {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .gantt-view-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .project-selector {
    justify-content: space-between;
  }
  
  .select-wrapper select {
    min-width: auto;
    flex: 1;
  }
  
  .header-actions {
    justify-content: center;
  }
}/* 甘
特图错误边界样式 */
.gantt-error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--ptm-spacing-xl);
  text-align: center;
  background: var(--background-primary);
}

.gantt-error-boundary h3 {
  color: var(--color-red);
  margin-bottom: var(--ptm-spacing-sm);
}

.gantt-error-boundary p {
  color: var(--text-muted);
  margin-bottom: var(--ptm-spacing-md);
  max-width: 500px;
  line-height: 1.5;
}

.gantt-error-boundary details {
  margin: var(--ptm-spacing-md) 0;
  text-align: left;
  max-width: 600px;
}

.gantt-error-boundary summary {
  cursor: pointer;
  color: var(--text-muted);
  font-size: 0.9em;
}

.gantt-error-boundary pre {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  padding: var(--ptm-spacing-sm);
  font-size: 0.8em;
  color: var(--color-red);
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.gantt-error-boundary button {
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: 1px solid var(--color-accent);
  border-radius: var(--ptm-radius-sm);
  background: var(--color-accent);
  color: var(--text-on-accent);
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-error-boundary button:hover {
  opacity: 0.8;
}

/* 甘特图无任务状态 */
.gantt-no-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--text-muted);
}

.gantt-no-tasks p {
  margin: var(--ptm-spacing-xs) 0;
  font-size: 0.9em;
}

.gantt-no-tasks p:first-child {
  font-weight: 500;
  color: var(--text-normal);
}/* 甘特图调试
信息样式 */
.gantt-debug-info {
  border-top: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
}

.gantt-debug-header {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  color: var(--text-normal);
  transition: var(--ptm-transition);
}

.gantt-debug-header:hover {
  background: var(--background-modifier-hover);
}

.gantt-debug-badge {
  margin-left: auto;
  padding: 2px 6px;
  background: var(--background-modifier-border);
  color: var(--text-muted);
  font-size: 0.75em;
  border-radius: 10px;
}

.gantt-debug-content {
  border-top: 1px solid var(--background-modifier-border);
}

.gantt-debug-tabs {
  display: flex;
  background: var(--background-primary);
  border-bottom: 1px solid var(--background-modifier-border);
}

.gantt-debug-tab {
  flex: 1;
  padding: var(--ptm-spacing-sm) var(--ptm-spacing-md);
  border: none;
  background: transparent;
  color: var(--text-muted);
  font-size: 0.85em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-debug-tab:hover {
  background: var(--background-modifier-hover);
  color: var(--text-normal);
}

.gantt-debug-tab.active {
  background: var(--background-secondary);
  color: var(--text-normal);
  border-bottom: 2px solid var(--color-accent);
}

.gantt-debug-panel {
  padding: var(--ptm-spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

.gantt-debug-summary {
  display: flex;
  gap: var(--ptm-spacing-md);
  margin-bottom: var(--ptm-spacing-md);
  padding: var(--ptm-spacing-sm);
  background: var(--background-primary);
  border-radius: var(--ptm-radius-sm);
}

.gantt-debug-stat {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gantt-debug-stat .label {
  font-size: 0.75em;
  color: var(--text-muted);
}

.gantt-debug-stat .value {
  font-size: 0.9em;
  font-weight: 500;
  color: var(--text-normal);
}

.gantt-debug-stat .value.success {
  color: var(--color-green);
}

.gantt-debug-stat .value.error {
  color: var(--color-red);
}

.gantt-debug-invalid,
.gantt-debug-valid {
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-debug-invalid h4,
.gantt-debug-valid h4 {
  margin: 0 0 var(--ptm-spacing-sm) 0;
  font-size: 0.9em;
  color: var(--text-normal);
}

.gantt-debug-task-item {
  padding: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-xs);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  border-left: 3px solid var(--color-green);
}

.gantt-debug-task-item.error {
  border-left-color: var(--color-red);
  background: rgba(255, 0, 0, 0.05);
}

.gantt-debug-task-item .task-name {
  font-weight: 500;
  font-size: 0.85em;
  color: var(--text-normal);
  margin-bottom: 2px;
}

.gantt-debug-task-item .task-details {
  display: flex;
  gap: var(--ptm-spacing-sm);
  font-size: 0.75em;
  color: var(--text-muted);
}

.gantt-debug-task-item .task-details span {
  white-space: nowrap;
}

.gantt-debug-task-item .task-issues {
  display: flex;
  flex-wrap: wrap;
  gap: var(--ptm-spacing-xs);
  margin-top: 2px;
}

.gantt-debug-task-item .issue {
  padding: 1px 4px;
  background: var(--color-red);
  color: white;
  font-size: 0.7em;
  border-radius: 2px;
}

.gantt-debug-more {
  text-align: center;
  color: var(--text-muted);
  font-size: 0.8em;
  font-style: italic;
  padding: var(--ptm-spacing-xs);
}

.gantt-debug-critical-task {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-xs);
  background: var(--background-primary);
  border-radius: var(--ptm-radius-sm);
  border-left: 3px solid var(--color-red);
}

.gantt-debug-critical-task .task-id {
  font-family: var(--font-monospace);
  font-size: 0.8em;
  color: var(--text-normal);
}

.gantt-debug-critical-task .slack {
  font-size: 0.75em;
  color: var(--text-muted);
}

.gantt-debug-conflict {
  padding: var(--ptm-spacing-sm);
  margin-bottom: var(--ptm-spacing-sm);
  background: var(--background-primary);
  border-radius: var(--ptm-radius-sm);
  border-left: 3px solid var(--color-orange);
}

.gantt-debug-conflict .conflict-header {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-xs);
}

.gantt-debug-conflict .conflict-severity {
  padding: 2px 6px;
  border-radius: var(--ptm-radius-sm);
  font-size: 0.7em;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.gantt-debug-conflict .conflict-severity.low {
  background: var(--color-yellow);
}

.gantt-debug-conflict .conflict-severity.medium {
  background: var(--color-orange);
}

.gantt-debug-conflict .conflict-severity.high {
  background: var(--color-red);
}

.gantt-debug-conflict .conflict-severity.critical {
  background: var(--color-red);
  animation: pulse 1s infinite;
}

.gantt-debug-conflict .conflict-resource {
  font-weight: 500;
  color: var(--text-normal);
}

.gantt-debug-conflict .conflict-tasks {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.8em;
  color: var(--text-muted);
  margin-bottom: var(--ptm-spacing-xs);
}

.gantt-debug-conflict .conflict-type {
  font-size: 0.75em;
  color: var(--text-muted);
  font-style: italic;
}

.gantt-debug-empty {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  padding: var(--ptm-spacing-lg);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}/* 输
入对话框样式 */
.input-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--ptm-z-modal);
}

.input-modal {
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-md);
  box-shadow: var(--ptm-shadow-lg);
  min-width: 400px;
  max-width: 90vw;
}

.input-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
}

.input-modal-header h3 {
  margin: 0;
  color: var(--text-normal);
  font-size: 1.1em;
  font-weight: 600;
}

.input-modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--ptm-radius-sm);
  transition: var(--ptm-transition);
}

.input-modal-close:hover {
  background: var(--background-modifier-hover);
  color: var(--text-normal);
}

.input-modal-form {
  padding: var(--ptm-spacing-md);
}

.input-modal-input {
  width: 100%;
  padding: var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.9em;
  margin-bottom: var(--ptm-spacing-md);
  box-sizing: border-box;
}

.input-modal-input:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 2px var(--color-accent-hover);
}

.input-modal-actions {
  display: flex;
  gap: var(--ptm-spacing-sm);
  justify-content: flex-end;
}

.input-modal-btn {
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-md);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  font-size: 0.9em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.input-modal-btn-cancel {
  background: var(--background-primary);
  color: var(--text-normal);
}

.input-modal-btn-cancel:hover {
  background: var(--background-modifier-hover);
}

.input-modal-btn-confirm {
  background: var(--color-accent);
  color: var(--text-on-accent);
  border-color: var(--color-accent);
}

.input-modal-btn-confirm:hover:not(:disabled) {
  opacity: 0.8;
}

.input-modal-btn-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}/
* 甘特图工具栏任务计数样式 */
.gantt-toolbar-task-count {
  margin-left: var(--ptm-spacing-md);
  font-size: 0.85em;
  color: var(--text-muted);
}

.task-count-limited {
  color: var(--color-orange);
  font-weight: 500;
}

.task-count-warning {
  color: var(--color-red);
  font-size: 0.8em;
}/* 甘特图库C
SS重置 - 确保基本样式不被覆盖 */
.gantt-main * {
  box-sizing: border-box !important;
}

/* 强制重置甘特图表格样式 */
.gantt-main .gantt-task-react table,
.gantt-main .gantt-task-react tbody,
.gantt-main .gantt-task-react thead,
.gantt-main .gantt-task-react tr,
.gantt-main .gantt-task-react td,
.gantt-main .gantt-task-react th {
  display: revert !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/* 确保甘特图表格可见 */
.gantt-main .gantt-task-react .gantt-table {
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 修复可能的隐藏问题 */
.gantt-main .gantt-task-react .gantt-table * {
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保文本可见 */
.gantt-main .gantt-task-react .gantt-table-cell,
.gantt-main .gantt-task-react .gantt-table-header-cell {
  color: var(--text-normal) !important;
  background: var(--background-primary) !important;
  border: 1px solid var(--background-modifier-border) !important;
  padding: 4px 8px !important;
  text-align: left !important;
  vertical-align: middle !important;
}

/* 甘特图SVG确保可见 */
.gantt-main .gantt-task-react svg {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 正常甘特图表格样式 */
.gantt-main .gantt-task-react .gantt-table {
  border-right: 1px solid var(--background-modifier-border) !important;
  background: var(--background-secondary) !important;
}

.gantt-main .gantt-task-react .gantt-table-row {
  border-bottom: 1px solid var(--background-modifier-border) !important;
  background: var(--background-primary) !important;
  min-height: 40px !important;
}

.gantt-main .gantt-task-react .gantt-table-row:hover {
  background: var(--background-modifier-hover) !important;
}

.gantt-main .gantt-task-react .gantt-table-cell {
  border-right: 1px solid var(--background-modifier-border) !important;
  background: transparent !important;
  color: var(--text-normal) !important;
  min-width: 50px !important;
  min-height: 30px !important;
}/* 甘特图
诊断工具样式 */
.gantt-diagnostic {
  border-top: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  padding: var(--ptm-spacing-md);
}

.gantt-diagnostic-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ptm-spacing-md);
}

.gantt-diagnostic-header h4 {
  margin: 0;
  color: var(--text-normal);
  font-size: 1em;
}

.diagnostic-refresh {
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.8em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.diagnostic-refresh:hover {
  background: var(--background-modifier-hover);
}

.gantt-diagnostic-content {
  display: flex;
  flex-direction: column;
  gap: var(--ptm-spacing-sm);
  margin-bottom: var(--ptm-spacing-md);
}

.diagnostic-item {
  padding: var(--ptm-spacing-sm);
  border-radius: var(--ptm-radius-sm);
  border-left: 3px solid;
}

.diagnostic-item.diagnostic-success {
  border-left-color: var(--color-green);
  background: rgba(76, 175, 80, 0.1);
}

.diagnostic-item.diagnostic-warning {
  border-left-color: var(--color-orange);
  background: rgba(255, 152, 0, 0.1);
}

.diagnostic-item.diagnostic-error {
  border-left-color: var(--color-red);
  background: rgba(244, 67, 54, 0.1);
}

.diagnostic-item.diagnostic-info {
  border-left-color: var(--color-blue);
  background: rgba(33, 150, 243, 0.1);
}

.diagnostic-header {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-xs);
  margin-bottom: var(--ptm-spacing-xs);
}

.diagnostic-category {
  font-weight: 500;
  color: var(--text-normal);
  font-size: 0.9em;
}

.diagnostic-message {
  color: var(--text-muted);
  font-size: 0.85em;
}

.diagnostic-details {
  font-size: 0.8em;
  color: var(--text-muted);
  margin-left: 24px;
  font-style: italic;
}

.status-success {
  color: var(--color-green);
}

.status-warning {
  color: var(--color-orange);
}

.status-error {
  color: var(--color-red);
}

.status-info {
  color: var(--color-blue);
}

.gantt-diagnostic-actions {
  display: flex;
  gap: var(--ptm-spacing-sm);
}

.gantt-diagnostic-actions button {
  padding: var(--ptm-spacing-xs) var(--ptm-spacing-sm);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--ptm-radius-sm);
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 0.8em;
  cursor: pointer;
  transition: var(--ptm-transition);
}

.gantt-diagnostic-actions button:hover {
  background: var(--background-modifier-hover);
}/* 项目
任务管理器模态框样式 */
.ptm-project-create-modal,
.ptm-task-create-modal {
	max-width: 600px;
	width: 90vw;
}

.ptm-modal-content {
	padding: 1rem;
}

.ptm-modal-content h2 {
	color: var(--text-normal);
	font-size: 1.25rem;
	font-weight: 600;
	margin: 0 0 1rem 0;
}

/* 表单样式 */
.ptm-form-group {
	margin-bottom: 1rem;
}

.ptm-form-group label {
	display: block;
	margin-bottom: 0.25rem;
	font-weight: 500;
	color: var(--text-normal);
	font-size: 0.875rem;
}

.ptm-form-group input,
.ptm-form-group textarea,
.ptm-form-group select {
	width: 100%;
	padding: 0.5rem;
	border: 1px solid var(--background-modifier-border);
	border-radius: 4px;
	background: var(--background-primary);
	color: var(--text-normal);
	font-size: 0.875rem;
	font-family: inherit;
}

.ptm-form-group input:focus,
.ptm-form-group textarea:focus,
.ptm-form-group select:focus {
	outline: none;
	border-color: var(--interactive-accent);
	box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.ptm-form-group textarea {
	resize: vertical;
	min-height: 80px;
}

.ptm-form-row {
	display: flex;
	gap: 1rem;
}

.ptm-form-row .ptm-form-group {
	flex: 1;
}

/* 复选框样式 */
.ptm-checkbox-label {
	display: flex !important;
	align-items: center;
	gap: 0.5rem;
	cursor: pointer;
	font-weight: normal !important;
}

.ptm-checkbox-label input[type="checkbox"] {
	width: auto !important;
	margin: 0;
}

/* 关联笔记行样式 */
.ptm-linked-note-row {
	display: flex;
	gap: 0.5rem;
	align-items: center;
	margin-bottom: 0.5rem;
}

.ptm-linked-note-row input {
	flex: 1;
}

/* 按钮样式 */
.ptm-button {
	padding: 0.5rem 1rem;
	border: none;
	border-radius: 4px;
	font-size: 0.875rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	font-family: inherit;
}

.ptm-button:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.ptm-button-primary {
	background: var(--interactive-accent);
	color: var(--text-on-accent);
}

.ptm-button-primary:hover:not(:disabled) {
	background: var(--interactive-accent-hover);
}

.ptm-button-secondary {
	background: var(--background-secondary);
	color: var(--text-normal);
	border: 1px solid var(--background-modifier-border);
}

.ptm-button-secondary:hover:not(:disabled) {
	background: var(--background-secondary-alt);
}

.ptm-button-danger {
	background: var(--text-error);
	color: var(--text-on-accent);
}

.ptm-button-danger:hover:not(:disabled) {
	background: var(--text-error);
	opacity: 0.8;
}

.ptm-button-small {
	padding: 0.25rem 0.5rem;
	font-size: 0.75rem;
}

/* 模态框操作按钮 */
.ptm-modal-actions {
	display: flex;
	justify-content: flex-end;
	gap: 0.5rem;
	margin-top: 1.5rem;
	padding-top: 1rem;
	border-top: 1px solid var(--background-modifier-border);
}

/* 加载动画 */
.ptm-spinner {
	display: inline-block;
	animation: ptm-spin 1s linear infinite;
}

.ptm-spinner__circle {
	stroke: var(--interactive-accent);
	stroke-dasharray: 31.416;
	stroke-dashoffset: 31.416;
	animation: ptm-spinner-dash 2s ease-in-out infinite;
}

@keyframes ptm-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes ptm-spinner-dash {
	0% {
		stroke-dasharray: 1, 200;
		stroke-dashoffset: 0;
	}
	50% {
		stroke-dasharray: 89, 200;
		stroke-dashoffset: -35px;
	}
	100% {
		stroke-dasharray: 89, 200;
		stroke-dashoffset: -124px;
	}
}

/* 响应式设计 */
@media (max-width: 768px) {
	.ptm-form-row {
		flex-direction: column;
		gap: 0;
	}
	
	.ptm-modal-actions {
		flex-direction: column-reverse;
	}
	
	.ptm-modal-actions .ptm-button {
		width: 100%;
	}
}

/* 左侧边栏图标样式 - 项目任务管理器 */
.project-task-manager-ribbon-class {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	min-width: 28px !important;
	min-height: 28px !important;
	padding: 4px !important;
	cursor: pointer !important;
	border-radius: 4px !important;
	transition: all 0.2s ease !important;
	background-color: var(--interactive-accent) !important;
	margin: 2px !important;
	border: 1px solid var(--background-modifier-border) !important;
}

.project-task-manager-ribbon-class:hover {
	background-color: var(--interactive-accent-hover) !important;
	transform: scale(1.05) !important;
}

.project-task-manager-ribbon-class svg {
	width: 18px !important;
	height: 18px !important;
	fill: var(--text-on-accent) !important;
	stroke: var(--text-on-accent) !important;
	opacity: 1 !important;
}

/* 确保图标在不同主题下都可见 */
.theme-dark .project-task-manager-ribbon-class {
	background-color: var(--interactive-accent) !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.theme-light .project-task-manager-ribbon-class {
	background-color: var(--interactive-accent) !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Homepage Dashboard 样式 */
.ptm-homepage {
  min-height: 100vh;
  background: var(--background-primary);
}

.ptm-homepage .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--ptm-spacing-lg);
}

.ptm-homepage .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--ptm-spacing-lg);
  flex-wrap: wrap;
  gap: var(--ptm-spacing-md);
}

.ptm-homepage .header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--interactive-accent), #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.ptm-homepage .header-actions {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-md);
}

/* 卡片样式 */
.ptm-homepage .card {
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-s);
}

.ptm-homepage .card-header {
  padding: var(--ptm-spacing-md);
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
}

.ptm-homepage .card-content {
  padding: var(--ptm-spacing-md);
}

/* 网格布局 */
.ptm-homepage .grid {
  display: grid;
  gap: var(--ptm-spacing-md);
}

.ptm-homepage .grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.ptm-homepage .grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* 统计卡片样式 */
.ptm-homepage .stat-card {
  text-align: center;
  padding: var(--ptm-spacing-lg);
  transition: transform 0.2s ease;
  cursor: pointer;
}

.ptm-homepage .stat-card:hover {
  transform: translateY(-2px);
}

.ptm-homepage .stat-icon {
  font-size: 2rem;
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-homepage .stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--interactive-accent);
  margin-bottom: var(--ptm-spacing-xs);
}

.ptm-homepage .stat-title {
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* 模块按钮样式 */
.ptm-homepage .module-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--ptm-spacing-sm);
}

.ptm-homepage .module-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--ptm-spacing-lg);
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--text-normal);
}

.ptm-homepage .module-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-s);
}

.ptm-homepage .module-icon {
  font-size: 2rem;
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-homepage .module-name {
  font-weight: 600;
  margin-bottom: var(--ptm-spacing-xs);
}

.ptm-homepage .module-desc {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-align: center;
}

/* 搜索框样式 */
.ptm-homepage .search-box {
  display: flex;
  align-items: center;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: 0.5rem;
  min-width: 250px;
}

.ptm-homepage .search-box input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-normal);
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

/* 按钮样式 */
.ptm-homepage .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 0.5rem;
}

.ptm-homepage .btn-primary {
  background: var(--interactive-accent);
  color: white;
}

.ptm-homepage .btn-primary:hover {
  background: var(--interactive-accent-hover);
}

.ptm-homepage .btn-secondary {
  background: var(--background-secondary);
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
}

.ptm-homepage .btn-secondary:hover {
  background: var(--background-modifier-hover);
}

/* 通知中心样式 */
.ptm-notification-center .ptm-notification-badge {
  animation: ptm-pulse 2s infinite;
}

@keyframes ptm-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.ptm-notification-panel {
  animation: ptm-slide-down 0.2s ease-out;
}

@keyframes ptm-slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 项目和任务项样式 */
.ptm-homepage .item {
  padding: var(--ptm-spacing-sm);
  background: var(--background-secondary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: var(--ptm-spacing-sm);
}

.ptm-homepage .item:hover {
  background: var(--background-modifier-hover);
  transform: translateX(4px);
}

.ptm-homepage .item-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.ptm-homepage .item-desc {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* 空状态样式 */
.ptm-homepage .empty-state {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--text-muted);
}

.ptm-homepage .empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* 通知徽章样式 */
.ptm-homepage .notification-btn {
  position: relative;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
}

.ptm-homepage .notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--text-error);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .ptm-homepage .header {
    flex-direction: column;
    gap: var(--ptm-spacing-md);
    align-items: stretch;
  }
  
  .ptm-homepage .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ptm-homepage .search-box {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .ptm-homepage .container {
    padding: var(--ptm-spacing-sm);
  }
  
  .ptm-homepage .header h1 {
    font-size: 1.5rem;
  }
  
  .ptm-homepage .grid-4,
  .ptm-homepage .grid-3 {
    grid-template-columns: 1fr;
  }
  
  .ptm-homepage .module-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ptm-homepage .flex {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .ptm-homepage .module-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .ptm-quick-access .ptm-grid {
    grid-template-columns: 1fr;
  }
  
  .ptm-notification-panel {
    width: calc(100vw - 2rem);
    right: -1rem;
  }
}

/* 骨架屏动画 */
.ptm-skeleton {
  background: linear-gradient(
    90deg,
    var(--background-secondary) 25%,
    var(--background-modifier-hover) 50%,
    var(--background-secondary) 75%
  );
  background-size: 200% 100%;
  animation: ptm-skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes ptm-skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态样式 */
.ptm-error-fallback {
  text-align: center;
  padding: var(--ptm-spacing-xl);
}

.ptm-error-fallback details {
  text-align: left;
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: var(--ptm-spacing-md);
  margin-top: var(--ptm-spacing-md);
}

.ptm-error-fallback summary {
  cursor: pointer;
  font-weight: 600;
  padding: var(--ptm-spacing-xs);
}

.ptm-error-fallback pre {
  font-size: 0.75rem;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 深色主题适配 */
.theme-dark .ptm-homepage {
  background: var(--background-primary);
}

.theme-dark .ptm-stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.theme-dark .ptm-notification-panel {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* 浅色主题适配 */
.theme-light .ptm-stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-light .ptm-notification-panel {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}/* 确保
必要的CSS变量已定义 */
:root {
  --ptm-spacing-xs: 0.25rem;
  --ptm-spacing-sm: 0.5rem;
  --ptm-spacing-md: 1rem;
  --ptm-spacing-lg: 1.5rem;
  --ptm-spacing-xl: 2rem;
}

/* 工具类 */
.ptm-homepage .flex {
  display: flex;
}

.ptm-homepage .flex-between {
  justify-content: space-between;
}

.ptm-homepage .flex-center {
  align-items: center;
}

.ptm-homepage .gap-sm {
  gap: var(--ptm-spacing-sm);
}

.ptm-homepage .gap-md {
  gap: var(--ptm-spacing-md);
}

.ptm-homepage .mb-lg {
  margin-bottom: var(--ptm-spacing-lg);
}

.ptm-homepage .text-center {
  text-align: center;
}