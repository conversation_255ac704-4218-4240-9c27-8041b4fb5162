/* src/ui/components/common/Button.module.css */
.button {
  font-family: var(--ptm-font-family);
  font-size: var(--ptm-text-sm);
  font-weight: var(--ptm-font-medium);
  line-height: var(--ptm-leading-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ptm-spacing-2);
  padding: var(--ptm-spacing-3) var(--ptm-spacing-5);
  border: none;
  border-radius: var(--ptm-radius-base);
  cursor: pointer;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  background: none !important;
  color: inherit !important;
  text-decoration: none !important;
}
.button:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}
.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
.primary {
  background: var(--ptm-primary-gradient) !important;
  color: var(--ptm-text-inverse) !important;
  box-shadow: var(--ptm-shadow-sm);
}
.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--ptm-shadow-md);
}
.secondary {
  background: var(--ptm-bg-primary) !important;
  color: var(--ptm-text-primary) !important;
  border: 1px solid var(--ptm-border-normal) !important;
}
.secondary:hover:not(:disabled) {
  background: var(--ptm-bg-secondary) !important;
  border-color: var(--ptm-border-dark) !important;
}
.ghost {
  background: transparent !important;
  color: var(--ptm-text-secondary) !important;
}
.ghost:hover:not(:disabled) {
  background: var(--ptm-bg-secondary) !important;
  color: var(--ptm-text-primary) !important;
}
.danger {
  background: var(--ptm-danger) !important;
  color: var(--ptm-text-inverse) !important;
}
.danger:hover:not(:disabled) {
  background: var(--ptm-danger) !important;
  opacity: 0.9;
}
.small {
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  font-size: var(--ptm-text-xs);
}
.large {
  padding: var(--ptm-spacing-4) var(--ptm-spacing-6);
  font-size: var(--ptm-text-base);
}
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.iconOnly {
  width: var(--ptm-spacing-8);
  height: var(--ptm-spacing-8);
  padding: 0;
  border-radius: var(--ptm-radius-base);
}
.iconOnly.small {
  width: var(--ptm-spacing-6);
  height: var(--ptm-spacing-6);
}
.iconOnly.large {
  width: var(--ptm-spacing-10);
  height: var(--ptm-spacing-10);
}
.loading {
  position: relative;
  color: transparent !important;
}
.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
@media (max-width: 768px) {
  .button {
    padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
    font-size: var(--ptm-text-xs);
  }
  .large {
    padding: var(--ptm-spacing-3) var(--ptm-spacing-5);
    font-size: var(--ptm-text-sm);
  }
}

/* src/ui/components/sprint/SprintRetrospective.module.css */
.ptmModalContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ptmModalBg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}
.ptmModal {
  position: relative;
  background-color: var(--ptm-background-primary, var(--background-primary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 8px;
  box-shadow: var(--ptm-shadow-large, 0 8px 32px rgba(0, 0, 0, 0.2));
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ptmModalHeader {
  padding: 16px 20px;
  border-bottom: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--ptm-background-secondary, var(--background-secondary));
}
.ptmModalTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmModalCloseButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--ptm-text-muted, var(--text-muted));
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}
.ptmModalCloseButton:hover {
  background-color: var(--ptm-background-hover, var(--background-modifier-hover));
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmModalContent {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}
.ptmSprintRetrospective {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.ptmRetrospectiveStats {
  background-color: var(--ptm-background-secondary, var(--background-secondary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 6px;
  padding: 16px;
}
.ptmStatsTitle {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmStatsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}
.ptmStatItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  border-radius: 4px;
  border: 1px solid var(--ptm-border-light, var(--background-modifier-border-hover));
}
.ptmStatLabel {
  font-size: 12px;
  color: var(--ptm-text-muted, var(--text-muted));
  margin-bottom: 4px;
}
.ptmStatValue {
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmRetrospectiveSection {
  background-color: var(--ptm-background-secondary, var(--background-secondary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 6px;
  padding: 16px;
}
.ptmSectionTitle {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmRetrospectiveItem {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: flex-start;
}
.ptmRetrospectiveTextarea {
  flex: 1;
  min-height: 60px;
  padding: 8px 12px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  font-family: var(--ptm-font-family, var(--font-interface));
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.2s ease;
}
.ptmRetrospectiveTextarea:focus {
  outline: none;
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  box-shadow: 0 0 0 2px var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.2));
}
.ptmRetrospectiveTextarea::placeholder {
  color: var(--ptm-text-placeholder, var(--text-faint));
}
.ptmRemoveItemButton {
  background: none;
  border: none;
  color: var(--ptm-text-danger, var(--text-error));
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}
.ptmRemoveItemButton:hover {
  background-color: var(--ptm-background-danger-hover, rgba(var(--color-red-rgb), 0.1));
}
.ptmAddItemButton {
  background: none;
  border: 1px dashed var(--ptm-border-color, var(--background-modifier-border));
  color: var(--ptm-text-muted, var(--text-muted));
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  width: 100%;
}
.ptmAddItemButton:hover {
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  color: var(--ptm-accent-color, var(--interactive-accent));
  background-color: var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.05));
}
.ptmVelocityAdjustment {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.ptmVelocityLabel {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: var(--ptm-text-primary, var(--text-normal));
}
.ptmVelocityInput {
  padding: 6px 10px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  font-size: 14px;
  width: 120px;
}
.ptmVelocityInput:focus {
  outline: none;
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  box-shadow: 0 0 0 2px var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.2));
}
.ptmVelocityNote {
  font-size: 12px;
  color: var(--ptm-text-muted, var(--text-muted));
  font-style: italic;
}
.ptmModalButtonContainer {
  padding: 16px 20px;
  border-top: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background-color: var(--ptm-background-secondary, var(--background-secondary));
}
.ptmButton {
  padding: 8px 16px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}
.ptmButton:hover {
  background-color: var(--ptm-background-hover, var(--background-modifier-hover));
}
.ptmButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.ptmButtonPrimary {
  background-color: var(--ptm-accent-color, var(--interactive-accent));
  color: var(--ptm-accent-text, var(--text-on-accent));
  border-color: var(--ptm-accent-color, var(--interactive-accent));
}
.ptmButtonPrimary:hover:not(:disabled) {
  background-color: var(--ptm-accent-color-hover, var(--interactive-accent-hover));
  border-color: var(--ptm-accent-color-hover, var(--interactive-accent-hover));
}
@media (max-width: 768px) {
  .ptmModal {
    width: 95vw;
    margin: 20px;
  }
  .ptmStatsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  .ptmModalButtonContainer {
    flex-direction: column;
  }
  .ptmButton {
    width: 100%;
  }
}
@media (prefers-color-scheme: dark) {
  .ptmModal {
    box-shadow: var(--ptm-shadow-large, 0 8px 32px rgba(0, 0, 0, 0.4));
  }
}

/* node_modules/gantt-task-react/dist/index.css */
._3_ygE {
  display: table;
  border-bottom: #e6e4e4 1px solid;
  border-top: #e6e4e4 1px solid;
  border-left: #e6e4e4 1px solid;
}
._1nBOt {
  display: table-row;
  list-style: none;
}
._2eZzQ {
  border-right: 1px solid rgb(196, 196, 196);
  opacity: 1;
  margin-left: -2px;
}
._WuQ0f {
  display: table-cell;
  vertical-align: -webkit-baseline-middle;
  vertical-align: middle;
}
._3ZbQT {
  display: table;
  border-bottom: #e6e4e4 1px solid;
  border-left: #e6e4e4 1px solid;
}
._34SS0 {
  display: table-row;
  text-overflow: ellipsis;
}
._34SS0:nth-of-type(even) {
  background-color: #f5f5f5;
}
._3lLk3 {
  display: table-cell;
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
._nI1Xw {
  display: flex;
}
._2QjE6 {
  color: rgb(86 86 86);
  font-size: 0.6rem;
  padding: 0.15rem 0.2rem 0rem 0.2rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
}
._2TfEi {
  font-size: 0.6rem;
  padding-left: 1rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
._3T42e {
  background: #fff;
  padding: 12px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
._29NTg {
  font-size: 12px;
  margin-bottom: 6px;
  color: #666;
}
._25P-K {
  position: absolute;
  display: flex;
  flex-shrink: 0;
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
._3gVAq {
  visibility: hidden;
  position: absolute;
  display: flex;
  pointer-events: none;
}
._1eT-t {
  overflow: hidden auto;
  width: 1rem;
  flex-shrink: 0;
  scrollbar-width: thin;
}
._1eT-t::-webkit-scrollbar {
  width: 1.1rem;
  height: 1.1rem;
}
._1eT-t::-webkit-scrollbar-corner {
  background: transparent;
}
._1eT-t::-webkit-scrollbar-thumb {
  border: 6px solid transparent;
  background: rgba(0, 0, 0, 0.2);
  background: var(--palette-black-alpha-20, rgba(0, 0, 0, 0.2));
  border-radius: 10px;
  background-clip: padding-box;
}
._1eT-t::-webkit-scrollbar-thumb:hover {
  border: 4px solid transparent;
  background: rgba(0, 0, 0, 0.3);
  background: var(--palette-black-alpha-30, rgba(0, 0, 0, 0.3));
  background-clip: padding-box;
}
._2dZTy {
  fill: #fff;
}
._2dZTy:nth-child(even) {
  fill: #f5f5f5;
}
._3rUKi {
  stroke: #ebeff2;
}
._RuwuK {
  stroke: #e6e4e4;
}
._9w8d5 {
  text-anchor: middle;
  fill: #333;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}
._1rLuZ {
  stroke: #e6e4e4;
}
._2q1Kt {
  text-anchor: middle;
  fill: #555;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}
._35nLX {
  fill: #ffffff;
  stroke: #e0e0e0;
  stroke-width: 1.4;
}
._KxSXS {
  cursor: pointer;
  outline: none;
}
._KxSXS:hover ._3w_5u {
  visibility: visible;
  opacity: 1;
}
._3w_5u {
  fill: #ddd;
  cursor: ew-resize;
  opacity: 0;
  visibility: hidden;
}
._31ERP {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  stroke-width: 0;
}
._RRr13 {
  cursor: pointer;
  outline: none;
}
._2P2B1 {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
._1KJ6x {
  cursor: pointer;
  outline: none;
}
._2RbVy {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.6;
}
._2pZMF {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
._3zRJQ {
  fill: #fff;
  text-anchor: middle;
  font-weight: lighter;
  dominant-baseline: central;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}
._3KcaM {
  fill: #555;
  text-anchor: start;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}
._CZjuD {
  overflow: hidden;
  font-size: 0;
  margin: 0;
  padding: 0;
}
._2B2zv {
  margin: 0;
  padding: 0;
  overflow: hidden;
}
._3eULf {
  display: flex;
  padding: 0;
  margin: 0;
  list-style: none;
  outline: none;
  position: relative;
}
._2k9Ys {
  overflow: auto;
  max-width: 100%;
  scrollbar-width: thin;
  height: 1.2rem;
}
._2k9Ys::-webkit-scrollbar {
  width: 1.1rem;
  height: 1.1rem;
}
._2k9Ys::-webkit-scrollbar-corner {
  background: transparent;
}
._2k9Ys::-webkit-scrollbar-thumb {
  border: 6px solid transparent;
  background: rgba(0, 0, 0, 0.2);
  background: var(--palette-black-alpha-20, rgba(0, 0, 0, 0.2));
  border-radius: 10px;
  background-clip: padding-box;
}
._2k9Ys::-webkit-scrollbar-thumb:hover {
  border: 4px solid transparent;
  background: rgba(0, 0, 0, 0.3);
  background: var(--palette-black-alpha-30, rgba(0, 0, 0, 0.3));
  background-clip: padding-box;
}
@media only screen and (max-device-width: 1024px) and (-webkit-min-device-pixel-ratio: 2) {
}
._19jgW {
  height: 1px;
}
/*# sourceMappingURL=data:application/json;base64,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 */
