# Git 版本控制规范

## 分支管理策略

### 1. 分支模型

采用简化的 Git Flow 模型，适应插件开发的敏捷性需求：

```
main                    # 主分支，生产环境代码
├── develop            # 开发分支，集成最新功能
│   ├── feature/xxx    # 功能分支
│   ├── bugfix/xxx     # 问题修复分支
│   └── hotfix/xxx     # 紧急修复分支
└── release/x.x.x      # 发布分支
```

### 2. 分支命名规范

```bash
# ✅ 正确的分支命名
feature/task-list-view          # 功能开发
feature/tasks-plugin-integration
bugfix/cache-memory-leak        # 问题修复
bugfix/ptm-file-validation
hotfix/critical-data-loss       # 紧急修复
release/v1.1.0                  # 版本发布

# ❌ 错误的分支命名
TaskList                        # 没有前缀和描述
fix-bug                        # 描述不明确
feature-123                    # 使用数字不够描述性
```

### 3. 分支操作流程

#### 功能开发流程
```bash
# 1. 从 develop 分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/task-list-view

# 2. 开发过程中定期同步 develop
git fetch origin
git rebase origin/develop

# 3. 完成开发后推送分支
git push origin feature/task-list-view

# 4. 创建 Pull Request 到 develop
# 5. 代码审查通过后合并
# 6. 删除功能分支
git branch -d feature/task-list-view
git push origin --delete feature/task-list-view
```

#### 发布流程
```bash
# 1. 从 develop 创建发布分支
git checkout develop
git checkout -b release/v1.1.0

# 2. 更新版本号和发布准备
npm run version-bump 1.1.0
git add .
git commit -m "chore: bump version to 1.1.0"

# 3. 发布测试和修复
# 只允许修复性提交，不允许新功能

# 4. 合并到 main 和 develop
git checkout main
git merge release/v1.1.0
git tag v1.1.0
git checkout develop
git merge release/v1.1.0

# 5. 推送并清理
git push origin main develop --tags
git branch -d release/v1.1.0
```

## 提交规范

### 1. 提交消息格式

采用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 2. 提交类型 (type)

```bash
feat        # 新功能
fix         # 问题修复
docs        # 文档更新
style       # 代码格式调整（不影响功能）
refactor    # 代码重构（不是新功能，也不是修复）
perf        # 性能优化
test        # 添加或修改测试
chore       # 构建过程或辅助工具的变动
ci          # CI/CD 相关变更
build       # 构建系统或外部依赖的变更
revert      # 回滚之前的提交
```

### 3. 提交范围 (scope)

```bash
# 按功能模块划分
(ui)           # UI 组件和界面
(services)     # 业务逻辑和服务层
(models)       # 数据模型
(integration)  # 第三方集成
(config)       # 配置相关
(docs)         # 文档
(tests)        # 测试
```

### 4. 提交消息示例

```bash
# ✅ 好的提交消息
feat(ui): add task list view component
fix(services): resolve memory leak in cache manager
docs(readme): update installation instructions
style(ui): format code according to prettier rules
refactor(models): simplify task dependency validation
perf(services): optimize data loading with virtual scrolling
test(ui): add unit tests for Button component
chore(deps): update dependencies to latest versions

# ✅ 带有详细说明的提交
feat(integration): implement Tasks plugin bidirectional sync

- Add TasksPluginBridge class for handling sync operations
- Support emoji status mapping between PTM and Tasks plugin
- Implement conflict resolution for concurrent edits
- Add periodic sync mechanism with configurable interval

Closes #123
```

```bash
# ❌ 不好的提交消息
update files                    # 太模糊
fix bug                        # 没有说明修复了什么
add new feature                # 没有具体描述功能
WIP                           # 不应该提交未完成的工作
various changes               # 包含多个不相关的变更
```

### 5. 提交最佳实践

#### 原子性提交
```bash
# ✅ 一个提交只做一件事
git add src/ui/components/Button.tsx
git commit -m "feat(ui): add Button component with variants"

git add src/ui/components/Button.test.tsx
git commit -m "test(ui): add Button component tests"

# ❌ 一个提交包含多个不相关的变更
git add .
git commit -m "add Button component and fix cache bug and update docs"
```

#### 提交前检查
```bash
# 提交前的检查清单
npm run lint          # 代码风格检查
npm run test          # 运行测试
npm run build         # 确保能够构建成功
```

## 代码审查流程

### 1. Pull Request 规范

#### PR 标题格式
```
<type>[scope]: <description>

# 示例
feat(ui): implement task list view
fix(services): resolve data synchronization issue
docs: update development setup guide
```

#### PR 描述模板
```markdown
## 变更类型
- [ ] 新功能 (feature)
- [ ] 问题修复 (bugfix)
- [ ] 重构 (refactor)
- [ ] 文档更新 (docs)
- [ ] 其他 (请说明)

## 变更描述
简要描述本次变更的内容和原因。

## 测试
- [ ] 已添加单元测试
- [ ] 已添加集成测试
- [ ] 已手动测试
- [ ] 测试覆盖率维持或提升

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 已更新相关文档
- [ ] 没有引入破坏性变更
- [ ] 已考虑性能影响
- [ ] 已测试不同主题（深色/浅色）

## 关联问题
Closes #issue_number
```

### 2. 代码审查要点

#### 功能性审查
- 是否按照需求正确实现功能
- 是否处理了边界情况和错误场景
- 是否考虑了用户体验

#### 代码质量审查
- 代码是否清晰易读
- 是否遵循项目的编码规范
- 是否有适当的注释和文档
- 是否有重复代码需要重构

#### 性能审查
- 是否有性能瓶颈
- 是否正确使用了缓存
- 是否避免了不必要的渲染

#### 安全审查
- 是否有安全隐患
- 输入验证是否充分
- 是否正确处理了用户数据

### 3. 审查反馈处理

```bash
# 处理审查反馈的流程
# 1. 根据反馈修改代码
git add .
git commit -m "address review feedback: improve error handling"

# 2. 如果需要修改历史提交，使用 rebase
git rebase -i HEAD~3  # 交互式 rebase 最近 3 个提交

# 3. 强制推送更新 PR（谨慎使用）
git push --force-with-lease origin feature/task-list-view
```

## 版本管理

### 1. 语义化版本控制

遵循 [Semantic Versioning](https://semver.org/) 规范：

```
MAJOR.MINOR.PATCH

MAJOR: 不兼容的 API 变更
MINOR: 向后兼容的功能新增
PATCH: 向后兼容的问题修复
```

#### 版本号示例
```bash
1.0.0    # 首个稳定版本
1.1.0    # 添加新功能（向后兼容）
1.1.1    # 修复问题（向后兼容）
2.0.0    # 重大变更（可能不兼容）
```

### 2. 标签管理

```bash
# 创建发布标签
git tag -a v1.1.0 -m "Release version 1.1.0

Features:
- Add task list view with virtual scrolling
- Implement Tasks plugin integration
- Add dark theme support

Bug fixes:
- Fix memory leak in cache manager
- Resolve PTM file validation issues"

# 推送标签
git push origin v1.1.0

# 列出所有标签
git tag -l

# 删除标签（如果需要）
git tag -d v1.1.0
git push origin :refs/tags/v1.1.0
```

### 3. 变更日志

维护 `CHANGELOG.md` 文件记录版本变更：

```markdown
# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Added
- Work in progress features

### Changed
- Modified existing features

### Fixed
- Bug fixes

## [1.1.0] - 2024-01-15

### Added
- Task list view with virtual scrolling
- Tasks plugin bidirectional synchronization
- Dark theme support for all components
- PTM file format validation

### Changed
- Improved cache management performance
- Updated UI component styling system

### Fixed
- Memory leak in data manager cache
- PTM file parsing edge cases
- React component re-rendering issues

### Deprecated
- Old task creation API (use TaskManager.createTask instead)

## [1.0.0] - 2024-01-01

### Added
- Initial release with core functionality
- Project and task management
- Basic UI components
- Obsidian plugin integration
```

## 协作规范

### 1. 团队工作流

```bash
# 每日开始工作前
git checkout develop
git pull origin develop

# 开始新任务
git checkout -b feature/new-task-from-issue-123

# 定期同步（避免分支分歧过大）
git fetch origin
git rebase origin/develop

# 完成开发后推送
git push origin feature/new-task-from-issue-123
```

### 2. 冲突解决

```bash
# 如果 rebase 时遇到冲突
git rebase origin/develop

# 解决冲突后
git add .
git rebase --continue

# 如果冲突太复杂，可以取消 rebase
git rebase --abort

# 使用 merge 替代（不推荐，但有时必要）
git merge origin/develop
```

### 3. 紧急修复流程

```bash
# 1. 从 main 分支创建热修复分支
git checkout main
git checkout -b hotfix/critical-data-loss

# 2. 修复问题
# 编写最小化的修复代码

# 3. 测试验证
npm run test
npm run build

# 4. 快速合并到 main 和 develop
git checkout main
git merge hotfix/critical-data-loss
git tag v1.0.1
git checkout develop
git merge hotfix/critical-data-loss

# 5. 推送和清理
git push origin main develop --tags
git branch -d hotfix/critical-data-loss
```

## 工具配置

### 1. Git 配置

```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 设置默认分支名
git config --global init.defaultBranch main

# 设置 push 默认行为
git config --global push.default current

# 启用颜色输出
git config --global color.ui auto

# 设置编辑器
git config --global core.editor "code --wait"

# 设置合并工具
git config --global merge.tool vscode
```

### 2. .gitignore 配置

```gitignore
# 依赖
node_modules/
*.log
npm-debug.log*

# 构建输出
main.js
*.js.map

# 开发工具
.vscode/settings.json
.idea/

# 系统文件
.DS_Store
Thumbs.db

# 测试覆盖率
coverage/

# 临时文件
*.tmp
*.temp
.cache/

# Obsidian 特定
.obsidian/workspace*
.obsidian/app.json
.obsidian/appearance.json
.obsidian/hotkeys.json
.obsidian/graph.json

# 插件开发
*.zip
releases/
```

### 3. Git Hooks

```bash
#!/bin/sh
# .git/hooks/pre-commit
# 提交前自动检查代码质量

echo "Running pre-commit checks..."

# 运行 linter
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ Linting failed. Please fix the errors before committing."
  exit 1
fi

# 运行测试
npm run test
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Please fix the tests before committing."
  exit 1
fi

echo "✅ All checks passed!"
```

---

**重要原则**:
1. 保持提交历史的整洁和可读性
2. 每个提交都应该是可构建和可测试的
3. 使用描述性的提交消息和分支名称
4. 定期同步远程分支避免冲突
5. 遵循团队约定的工作流程
description:
globs:
alwaysApply: false
---
