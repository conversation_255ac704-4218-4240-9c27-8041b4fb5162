# 样式和 Obsidian 主题集成规范

## 主题集成原则

### 1. 使用 Obsidian CSS 变量
始终使用 Obsidian 提供的 CSS 变量，确保与用户主题保持一致：

```css
/* ✅ 正确 - 使用 Obsidian CSS 变量 */
.ptm-card {
  background-color: var(--background-primary);
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
}

/* ❌ 错误 - 硬编码颜色 */
.ptm-card {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid #e0e0e0;
}
```

### 2. 常用 Obsidian CSS 变量

#### 背景色变量
```css
--background-primary          /* 主背景色 */
--background-primary-alt      /* 备用主背景色 */
--background-secondary        /* 次要背景色 */
--background-secondary-alt    /* 备用次要背景色 */
--background-modifier-border  /* 边框颜色 */
--background-modifier-form-field /* 表单字段背景 */
--background-modifier-box-shadow /* 阴影颜色 */
```

#### 文本颜色变量
```css
--text-normal                 /* 普通文本 */
--text-muted                  /* 次要文本 */
--text-faint                  /* 很淡的文本 */
--text-accent                 /* 强调文本 */
--text-accent-hover           /* 强调文本悬停 */
--text-on-accent              /* 强调背景上的文本 */
```

#### 交互元素变量
```css
--interactive-normal          /* 交互元素普通状态 */
--interactive-hover           /* 交互元素悬停状态 */
--interactive-accent          /* 交互元素强调色 */
--interactive-accent-hover    /* 交互元素强调悬停 */
```

## 组件样式规范

### 1. CSS 类命名约定
使用 BEM 方法论的变体，以 `ptm-` 为前缀：

```css
/* 组件基础类 */
.ptm-component {}

/* 组件变体 */
.ptm-component--variant {}

/* 组件元素 */
.ptm-component__element {}

/* 组件状态 */
.ptm-component.is-active {}
.ptm-component.is-disabled {}
```

### 2. 具体组件示例

#### Button 组件样式
```css
.ptm-button {
  /* 基础样式 */
  padding: var(--size-4-2) var(--size-4-4);
  border-radius: var(--button-radius);
  border: none;
  font-size: var(--font-ui-small);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* 使用 Obsidian 变量 */
  background-color: var(--interactive-normal);
  color: var(--text-normal);
}

.ptm-button:hover {
  background-color: var(--interactive-hover);
}

.ptm-button--primary {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
}

.ptm-button--primary:hover {
  background-color: var(--interactive-accent-hover);
}

.ptm-button--secondary {
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
}

.ptm-button--ghost {
  background-color: transparent;
  border: 1px solid transparent;
}

.ptm-button--ghost:hover {
  background-color: var(--background-modifier-hover);
}

.ptm-button.is-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

#### Card 组件样式
```css
.ptm-card {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-m);
  padding: var(--size-4-4);
  box-shadow: var(--shadow-s);
}

.ptm-card--elevated {
  box-shadow: var(--shadow-l);
}

.ptm-card--interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.ptm-card--interactive:hover {
  border-color: var(--background-modifier-border-hover);
  box-shadow: var(--shadow-m);
}

.ptm-card__header {
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: var(--size-4-2);
  margin-bottom: var(--size-4-3);
}

.ptm-card__title {
  font-size: var(--font-ui-medium);
  font-weight: var(--font-weight-semibold);
  color: var(--text-normal);
  margin: 0;
}

.ptm-card__subtitle {
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  margin: var(--size-2-1) 0 0 0;
}
```

### 3. 响应式设计规范

```css
/* 使用 Obsidian 的响应式断点 */
.ptm-component {
  /* 默认桌面样式 */
}

/* 移动设备 */
@media (max-width: 768px) {
  .ptm-component {
    padding: var(--size-4-2);
    font-size: var(--font-ui-smaller);
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .ptm-component {
    padding: var(--size-4-3);
  }
}
```

## Layout 系统

### 1. Container 组件
```css
.ptm-container {
  width: 100%;
  max-width: var(--file-line-width);
  margin: 0 auto;
  padding: 0 var(--size-4-4);
}

.ptm-container--full {
  max-width: none;
}

.ptm-container--narrow {
  max-width: 600px;
}
```

### 2. Stack 布局组件
```css
.ptm-stack {
  display: flex;
  flex-direction: column;
}

.ptm-stack--horizontal {
  flex-direction: row;
}

.ptm-stack--center {
  align-items: center;
}

.ptm-stack--space-between {
  justify-content: space-between;
}

/* 间距变体 */
.ptm-stack--xs > * + * { margin-top: var(--size-2-1); }
.ptm-stack--sm > * + * { margin-top: var(--size-2-2); }
.ptm-stack--md > * + * { margin-top: var(--size-4-2); }
.ptm-stack--lg > * + * { margin-top: var(--size-4-4); }
.ptm-stack--xl > * + * { margin-top: var(--size-4-6); }

/* 水平间距 */
.ptm-stack--horizontal.ptm-stack--xs > * + * { 
  margin-top: 0; 
  margin-left: var(--size-2-1); 
}
```

### 3. Grid 布局组件
```css
.ptm-grid {
  display: grid;
  gap: var(--size-4-3);
}

.ptm-grid--2 { grid-template-columns: repeat(2, 1fr); }
.ptm-grid--3 { grid-template-columns: repeat(3, 1fr); }
.ptm-grid--4 { grid-template-columns: repeat(4, 1fr); }

/* 响应式网格 */
.ptm-grid--responsive {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 768px) {
  .ptm-grid--2,
  .ptm-grid--3,
  .ptm-grid--4 {
    grid-template-columns: 1fr;
  }
}
```

## 动画和过渡

### 1. 基础过渡
```css
.ptm-transition {
  transition: all var(--anim-duration-fast) var(--anim-motion-smooth);
}

.ptm-transition--slow {
  transition: all var(--anim-duration-moderate) var(--anim-motion-smooth);
}
```

### 2. 状态变化动画
```css
.ptm-fade-in {
  animation: ptm-fade-in var(--anim-duration-fast) var(--anim-motion-smooth);
}

@keyframes ptm-fade-in {
  from { opacity: 0; transform: translateY(4px); }
  to { opacity: 1; transform: translateY(0); }
}

.ptm-slide-in {
  animation: ptm-slide-in var(--anim-duration-moderate) var(--anim-motion-smooth);
}

@keyframes ptm-slide-in {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
```

## 主题检测和适配

### 1. 主题检测工具类
```typescript
// src/ui/utils/theme.ts
export class ThemeManager {
  private app: App;

  constructor(app: App) {
    this.app = app;
  }

  isDark(): boolean {
    return document.body.hasClass('theme-dark');
  }

  isLight(): boolean {
    return !this.isDark();
  }

  getCurrentTheme(): 'light' | 'dark' {
    return this.isDark() ? 'dark' : 'light';
  }

  onThemeChange(callback: (theme: 'light' | 'dark') => void): () => void {
    const observer = new MutationObserver(() => {
      callback(this.getCurrentTheme());
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }
}
```

### 2. 组件中使用主题
```typescript
// 在 React 组件中使用
const Component: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const themeManager = new ThemeManager(app);
    setTheme(themeManager.getCurrentTheme());

    const cleanup = themeManager.onThemeChange(setTheme);
    return cleanup;
  }, []);

  return (
    <div className={`ptm-component ptm-component--${theme}`}>
      {/* 组件内容 */}
    </div>
  );
};
```

## 无障碍性 (Accessibility)

### 1. 基础无障碍性规范
```css
/* 焦点状态 */
.ptm-button:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .ptm-card {
    border-width: 2px;
  }
}

/* 减少动画支持 */
@media (prefers-reduced-motion: reduce) {
  .ptm-transition,
  .ptm-fade-in,
  .ptm-slide-in {
    animation: none;
    transition: none;
  }
}
```

### 2. ARIA 属性支持
```typescript
// 在组件中正确使用 ARIA 属性
<button
  className="ptm-button"
  aria-label="创建新项目"
  aria-disabled={isLoading}
  aria-describedby="button-help-text"
>
  {isLoading ? '创建中...' : '创建项目'}
</button>
```

---

**重要提醒**: 
1. 始终在开发时测试深色和浅色主题
2. 确保足够的颜色对比度
3. 支持键盘导航
4. 使用语义化的 HTML 结构
description:
globs:
alwaysApply: false
---
