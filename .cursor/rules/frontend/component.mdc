# React 组件开发规范

## 组件设计原则

### 1. 组件分类

#### 基础组件 (src/ui/components/common/)
- **目的**: 提供可复用的基础 UI 元素
- **特点**: 无状态、纯展示、高度可配置
- **示例**: Button, Card, ProgressRing

```typescript
// ✅ 正确示例
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  onClick, 
  children 
}) => {
  return (
    <button 
      className={`ptm-button ptm-button--${variant} ptm-button--${size}`}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  );
};
```

#### 业务组件 (src/ui/components/dashboard/, etc.)
- **目的**: 实现特定业务功能
- **特点**: 有状态、业务逻辑、数据交互
- **示例**: ProjectDashboard, TaskList, ProjectOverviewCard

```typescript
// ✅ 正确示例
interface ProjectDashboardProps {
  app: App;
  ptmManager: PTMManager;
}

export const ProjectDashboard: React.FC<ProjectDashboardProps> = ({ 
  app, 
  ptmManager 
}) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectData = await ptmManager.getProjects();
      setProjects(projectData);
    } catch (error) {
      console.error('Failed to load projects:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      {/* 组件内容 */}
    </Container>
  );
};
```

### 2. 组件命名规范

```typescript
// ✅ 组件名使用 PascalCase
export const ProjectOverviewCard: React.FC<Props> = () => {};

// ✅ Props 接口以组件名 + Props 命名
interface ProjectOverviewCardProps {
  project: Project;
  onEdit?: (project: Project) => void;
}

// ✅ 文件名与组件名一致
// 文件: ProjectOverviewCard.tsx
// 组件: ProjectOverviewCard
```

### 3. Props 设计规范

#### 必需 vs 可选 Props
```typescript
interface ComponentProps {
  // 必需属性 - 组件正常工作所需
  id: string;
  title: string;
  
  // 可选属性 - 有合理默认值
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  
  // 回调函数 - 总是可选的
  onClick?: (id: string) => void;
  onSubmit?: (data: FormData) => void;
}
```

#### 避免 Props 过度传递
```typescript
// ❌ 避免这样做 - Props 层级过深
<Component 
  prop1={value1}
  prop2={value2}
  prop3={value3}
  prop4={value4}
  prop5={value5}
/>

// ✅ 使用配置对象
interface ComponentConfig {
  ui: UIConfig;
  behavior: BehaviorConfig;
  data: DataConfig;
}

<Component config={componentConfig} />
```

## 状态管理规范

### 1. 本地状态 (useState)
适用于组件内部的简单状态：

```typescript
const [isLoading, setIsLoading] = useState(false);
const [formData, setFormData] = useState<FormData>({});
const [showModal, setShowModal] = useState(false);
```

### 2. 复杂状态 (useReducer)
适用于复杂的状态逻辑：

```typescript
interface TaskState {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  selectedTaskId: string | null;
}

type TaskAction = 
  | { type: 'LOAD_TASKS_START' }
  | { type: 'LOAD_TASKS_SUCCESS'; payload: Task[] }
  | { type: 'LOAD_TASKS_ERROR'; payload: string }
  | { type: 'SELECT_TASK'; payload: string };

const taskReducer = (state: TaskState, action: TaskAction): TaskState => {
  switch (action.type) {
    case 'LOAD_TASKS_START':
      return { ...state, loading: true, error: null };
    case 'LOAD_TASKS_SUCCESS':
      return { ...state, loading: false, tasks: action.payload };
    // ... 其他 cases
    default:
      return state;
  }
};
```

### 3. 副作用管理 (useEffect)

```typescript
// ✅ 清晰的依赖数组
useEffect(() => {
  loadProjects();
}, [projectId]); // 明确依赖

// ✅ 清理副作用
useEffect(() => {
  const interval = setInterval(updateProgress, 1000);
  return () => clearInterval(interval);
}, []);

// ✅ 条件执行
useEffect(() => {
  if (isAuthenticated && projectId) {
    fetchProjectData(projectId);
  }
}, [isAuthenticated, projectId]);
```

## 错误处理规范

### 1. 组件层错误边界
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ComponentErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card variant="error">
          <p>组件加载失败，请刷新重试</p>
        </Card>
      );
    }

    return this.props.children;
  }
}
```

### 2. 异步操作错误处理
```typescript
const handleSubmit = async (data: FormData) => {
  try {
    setLoading(true);
    setError(null);
    
    await ptmManager.createProject(data);
    
    // 成功处理
    onSuccess?.();
  } catch (error) {
    const errorMessage = error instanceof Error 
      ? error.message 
      : '操作失败，请重试';
    setError(errorMessage);
  } finally {
    setLoading(false);
  }
};
```

## 性能优化规范

### 1. 避免不必要的重渲染
```typescript
// ✅ 使用 React.memo
const TaskItem = React.memo<TaskItemProps>(({ task, onToggle }) => {
  return (
    <div onClick={() => onToggle(task.id)}>
      {task.title}
    </div>
  );
});

// ✅ 使用 useCallback 稳定回调函数
const handleTaskToggle = useCallback((taskId: string) => {
  setTasks(prev => prev.map(task => 
    task.id === taskId 
      ? { ...task, completed: !task.completed }
      : task
  ));
}, []);

// ✅ 使用 useMemo 缓存计算结果
const filteredTasks = useMemo(() => {
  return tasks.filter(task => task.status === selectedStatus);
}, [tasks, selectedStatus]);
```

### 2. 虚拟化长列表
```typescript
// 对于大量数据的列表，使用虚拟滚动
const TaskList: React.FC<TaskListProps> = ({ tasks }) => {
  const [startIndex, setStartIndex] = useState(0);
  const [endIndex, setEndIndex] = useState(50);
  
  const visibleTasks = useMemo(() => {
    return tasks.slice(startIndex, endIndex);
  }, [tasks, startIndex, endIndex]);

  return (
    <div className="task-list-container">
      {visibleTasks.map(task => (
        <TaskItem key={task.id} task={task} />
      ))}
    </div>
  );
};
```

## 测试规范

### 1. 组件测试结构
```typescript
// TaskItem.test.tsx
describe('TaskItem', () => {
  const mockTask: Task = {
    id: '1',
    title: 'Test Task',
    status: 'TODO',
    priority: 'MEDIUM'
  };

  it('renders task title correctly', () => {
    render(<TaskItem task={mockTask} />);
    expect(screen.getByText('Test Task')).toBeInTheDocument();
  });

  it('calls onToggle when clicked', () => {
    const mockOnToggle = jest.fn();
    render(<TaskItem task={mockTask} onToggle={mockOnToggle} />);
    
    fireEvent.click(screen.getByText('Test Task'));
    expect(mockOnToggle).toHaveBeenCalledWith('1');
  });
});
```

### 2. 异步操作测试
```typescript
it('loads projects on mount', async () => {
  const mockProjects = [{ id: '1', name: 'Test Project' }];
  const mockPTMManager = {
    getProjects: jest.fn().mockResolvedValue(mockProjects)
  };

  render(<ProjectDashboard ptmManager={mockPTMManager} />);
  
  await waitFor(() => {
    expect(screen.getByText('Test Project')).toBeInTheDocument();
  });
});
```

---

**重要提醒**: 所有组件必须支持 Obsidian 的深色/浅色主题，使用 CSS 变量而非硬编码颜色值。
description:
globs:
alwaysApply: false
---
