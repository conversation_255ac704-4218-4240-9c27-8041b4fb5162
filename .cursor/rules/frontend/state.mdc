# 状态管理和数据流规范

## 状态管理策略

### 1. 状态分层架构

```
┌─────────────────────────────────────────┐
│            UI State                     │
│  ├── Loading states                     │
│  ├── Form states                        │
│  ├── Modal/Dialog states                │
│  └── UI preferences                     │
├─────────────────────────────────────────┤
│         Application State               │
│  ├── Current project                    │
│  ├── Selected tasks                     │
│  ├── View modes                         │
│  └── User settings                      │
├─────────────────────────────────────────┤
│          Business Data                  │
│  ├── Projects collection                │
│  ├── Tasks collection                   │
│  ├── Sprints collection                 │
│  └── Workflows collection               │
└─────────────────────────────────────────┘
```

### 2. 状态管理工具选择

#### 本地组件状态 (useState)
适用于简单的 UI 状态：

```typescript
// ✅ 适合使用 useState 的场景
const [isModalOpen, setIsModalOpen] = useState(false);
const [inputValue, setInputValue] = useState('');
const [isLoading, setIsLoading] = useState(false);
```

#### 复杂组件状态 (useReducer)
适用于复杂的状态逻辑：

```typescript
// ✅ 适合使用 useReducer 的场景
interface TaskFormState {
  data: Partial<Task>;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

type TaskFormAction = 
  | { type: 'SET_FIELD'; field: keyof Task; value: any }
  | { type: 'SET_ERROR'; field: string; error: string }
  | { type: 'CLEAR_ERRORS' }
  | { type: 'SET_SUBMITTING'; isSubmitting: boolean }
  | { type: 'RESET_FORM' };

const taskFormReducer = (state: TaskFormState, action: TaskFormAction): TaskFormState => {
  switch (action.type) {
    case 'SET_FIELD':
      return {
        ...state,
        data: { ...state.data, [action.field]: action.value },
        isDirty: true
      };
    case 'SET_ERROR':
      return {
        ...state,
        errors: { ...state.errors, [action.field]: action.error }
      };
    // ... 其他 cases
    default:
      return state;
  }
};
```

#### 全局状态 (Context + useReducer)
适用于跨组件共享的应用状态：

```typescript
// ✅ 全局应用状态管理
interface AppState {
  currentProject: Project | null;
  projects: Project[];
  tasks: Task[];
  user: User | null;
  settings: AppSettings;
}

type AppAction = 
  | { type: 'SET_CURRENT_PROJECT'; project: Project }
  | { type: 'ADD_PROJECT'; project: Project }
  | { type: 'UPDATE_PROJECT'; id: string; updates: Partial<Project> }
  | { type: 'DELETE_PROJECT'; id: string }
  | { type: 'LOAD_DATA_SUCCESS'; projects: Project[]; tasks: Task[] };

const AppContext = createContext<{
  state: AppState;
  dispatch: Dispatch<AppAction>;
} | null>(null);

export const useAppState = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppState must be used within AppProvider');
  }
  return context;
};
```

## 数据流模式

### 1. 单向数据流
```
User Action → Component → Business Logic → Data Layer → Update State → Re-render
```

### 2. 数据获取模式

#### 基础数据获取 Hook
```typescript
// src/ui/hooks/useProjects.ts
interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useProjects = (ptmManager: PTMManager): UseProjectsReturn => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await ptmManager.getProjects();
      setProjects(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取项目失败');
    } finally {
      setLoading(false);
    }
  }, [ptmManager]);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return { projects, loading, error, refetch: fetchProjects };
};
```

#### 带缓存的数据获取
```typescript
// src/ui/hooks/useCachedData.ts
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresIn: number;
}

export const useCachedData = <T>(
  key: string,
  fetcher: () => Promise<T>,
  options: { cacheTime?: number } = {}
) => {
  const { cacheTime = 5 * 60 * 1000 } = options; // 5 分钟默认缓存
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: string | null;
  }>({ data: null, loading: true, error: null });

  useEffect(() => {
    const loadData = async () => {
      // 检查缓存
      const cached = getCacheEntry<T>(key);
      if (cached && Date.now() - cached.timestamp < cached.expiresIn) {
        setState({ data: cached.data, loading: false, error: null });
        return;
      }

      // 获取新数据
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        const data = await fetcher();
        
        // 更新缓存
        setCacheEntry(key, { data, timestamp: Date.now(), expiresIn: cacheTime });
        setState({ data, loading: false, error: null });
      } catch (error) {
        setState({ 
          data: null, 
          loading: false, 
          error: error instanceof Error ? error.message : '数据加载失败' 
        });
      }
    };

    loadData();
  }, [key, fetcher, cacheTime]);

  return state;
};
```

### 3. 表单状态管理

#### 表单 Hook 模式
```typescript
// src/ui/hooks/useForm.ts
interface UseFormOptions<T> {
  initialValues: T;
  validate?: (values: T) => Record<string, string>;
  onSubmit: (values: T) => Promise<void>;
}

export const useForm = <T extends Record<string, any>>(
  options: UseFormOptions<T>
) => {
  const [values, setValues] = useState<T>(options.initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
    
    // 清除字段错误
    if (errors[field as string]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  }, [errors]);

  const setFieldError = useCallback((field: string, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const validate = useCallback(() => {
    if (options.validate) {
      const validationErrors = options.validate(values);
      setErrors(validationErrors);
      return Object.keys(validationErrors).length === 0;
    }
    return true;
  }, [values, options.validate]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (!validate()) return;

    try {
      setIsSubmitting(true);
      await options.onSubmit(values);
      setIsDirty(false);
    } catch (error) {
      if (error instanceof Error) {
        setErrors({ _form: error.message });
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validate, options.onSubmit]);

  const reset = useCallback(() => {
    setValues(options.initialValues);
    setErrors({});
    setIsDirty(false);
    setIsSubmitting(false);
  }, [options.initialValues]);

  return {
    values,
    errors,
    isSubmitting,
    isDirty,
    setValue,
    setFieldError,
    handleSubmit,
    reset,
    isValid: Object.keys(errors).length === 0
  };
};
```

## 状态同步和持久化

### 1. 本地存储同步
```typescript
// src/ui/hooks/useLocalStorage.ts
export const useLocalStorage = <T>(
  key: string, 
  initialValue: T
): [T, (value: T) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  }, [key]);

  return [storedValue, setValue];
};
```

### 2. Obsidian 设置同步
```typescript
// src/ui/hooks/useSettings.ts
export const useSettings = (plugin: Plugin) => {
  const [settings, setSettings] = useState(plugin.settings);

  const updateSetting = useCallback(async <K extends keyof typeof settings>(
    key: K,
    value: typeof settings[K]
  ) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    plugin.settings = newSettings;
    await plugin.saveSettings();
  }, [settings, plugin]);

  return { settings, updateSetting };
};
```

## 性能优化模式

### 1. 状态选择器模式
```typescript
// 避免不必要的重渲染
const useTaskSelector = <T>(selector: (tasks: Task[]) => T) => {
  const { state } = useAppState();
  return useMemo(() => selector(state.tasks), [state.tasks, selector]);
};

// 使用示例
const CompletedTasksCount: React.FC = () => {
  const completedCount = useTaskSelector(
    tasks => tasks.filter(task => task.status === 'COMPLETED').length
  );

  return <span>已完成: {completedCount}</span>;
};
```

### 2. 状态归一化
```typescript
// ✅ 归一化状态结构
interface NormalizedState {
  projects: {
    byId: Record<string, Project>;
    allIds: string[];
  };
  tasks: {
    byId: Record<string, Task>;
    allIds: string[];
    byProject: Record<string, string[]>;
  };
}

// Helper functions
const getAllProjects = (state: NormalizedState): Project[] =>
  state.projects.allIds.map(id => state.projects.byId[id]);

const getProjectTasks = (state: NormalizedState, projectId: string): Task[] =>
  (state.tasks.byProject[projectId] || []).map(id => state.tasks.byId[id]);
```

### 3. 虚拟化状态
```typescript
// 大数据集的虚拟化处理
interface VirtualizedListState {
  items: Task[];
  visibleRange: { start: number; end: number };
  scrollTop: number;
  itemHeight: number;
  containerHeight: number;
}

const useVirtualizedTasks = (tasks: Task[], containerHeight: number) => {
  const [state, setState] = useState<VirtualizedListState>({
    items: tasks,
    visibleRange: { start: 0, end: Math.ceil(containerHeight / 40) },
    scrollTop: 0,
    itemHeight: 40,
    containerHeight
  });

  const handleScroll = useCallback((scrollTop: number) => {
    const start = Math.floor(scrollTop / state.itemHeight);
    const visibleCount = Math.ceil(containerHeight / state.itemHeight);
    const end = Math.min(start + visibleCount + 2, tasks.length);

    setState(prev => ({
      ...prev,
      scrollTop,
      visibleRange: { start, end }
    }));
  }, [state.itemHeight, containerHeight, tasks.length]);

  const visibleItems = useMemo(() => 
    tasks.slice(state.visibleRange.start, state.visibleRange.end),
    [tasks, state.visibleRange]
  );

  return { visibleItems, handleScroll, ...state };
};
```

## 错误状态管理

### 1. 全局错误处理
```typescript
interface ErrorState {
  errors: Array<{
    id: string;
    message: string;
    type: 'error' | 'warning' | 'info';
    timestamp: number;
  }>;
}

const useErrorHandler = () => {
  const [errors, setErrors] = useState<ErrorState['errors']>([]);

  const addError = useCallback((message: string, type: 'error' | 'warning' | 'info' = 'error') => {
    const error = {
      id: Date.now().toString(),
      message,
      type,
      timestamp: Date.now()
    };
    setErrors(prev => [...prev, error]);

    // 自动清除错误
    setTimeout(() => {
      setErrors(prev => prev.filter(e => e.id !== error.id));
    }, 5000);
  }, []);

  const removeError = useCallback((id: string) => {
    setErrors(prev => prev.filter(e => e.id !== id));
  }, []);

  return { errors, addError, removeError };
};
```

---

**重要原则**:
1. 保持状态结构扁平化
2. 避免深层嵌套的状态更新
3. 使用 TypeScript 确保状态类型安全
4. 适当使用 memo 化避免不必要的计算
5. 状态更新要保证不可变性
description:
globs:
alwaysApply: false
---
