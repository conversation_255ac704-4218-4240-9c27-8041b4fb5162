# 系统架构规范

## 架构概览

基于分层架构模式，确保代码的可维护性和扩展性。

```
┌─────────────────────────────────────────────────────────────┐
│                    Obsidian Plugin API                     │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (React + TypeScript)                             │
│  ├── Project Dashboard    ├── Kanban Board                  │
│  ├── Task List View       ├── Component Showcase           │
│  ├── Settings Panel       └── Progress Visualization       │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                       │
│  ├── ProjectManager       ├── TaskManager                   │
│  ├── PTMManager           ├── SprintManager (Future)        │
│  └── WorkflowEngine (Future)                               │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── DataManager          ├── Repository Pattern            │
│  ├── ProjectRepository    ├── TaskRepository                │
│  ├── PTMFileHandler       └── Cache Management              │
├─────────────────────────────────────────────────────────────┤
│  Integration Layer                                          │
│  ├── TasksPluginBridge    ├── Obsidian Theme Integration    │
│  ├── File System Watcher  └── React Renderer                │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块职责

### UI Layer (src/ui/)
**职责**: 用户界面展示和交互
- `App.tsx`: 主应用组件和路由管理
- `components/`: 可复用 UI 组件
- `utils/`: UI 工具函数（主题、渲染器）

**原则**:
- 纯展示逻辑，不包含业务逻辑
- 通过 props 和回调与业务层通信
- 支持 Obsidian 主题系统

### Business Logic Layer (src/services/)
**职责**: 核心业务逻辑处理
- `PTMManager.ts`: 主服务协调器
- `ProjectManager.ts`: 项目管理逻辑
- `TaskManager.ts`: 任务管理逻辑
- `TasksPluginBridge.ts`: Tasks 插件集成

**原则**:
- 单一职责，每个 Manager 负责特定领域
- 使用依赖注入模式
- 通过 Repository 访问数据

### Data Layer (src/services/)
**职责**: 数据存储和访问
- `DataManager.ts`: 统一数据管理和缓存
- `Repository.ts`: 仓库模式基类
- `ProjectRepository.ts`: 项目数据仓库
- `TaskRepository.ts`: 任务数据仓库
- `PTMFileHandler.ts`: PTM 文件格式处理

**原则**:
- Repository 模式封装数据访问
- 使用 JSON 文件持久化
- 实现缓存机制提高性能

### Models Layer (src/models/)
**职责**: 数据模型和类型定义
- `Project.ts`: 项目实体模型
- `Task.ts`: 任务实体模型
- `Sprint.ts`: Sprint 实体模型
- `Workflow.ts`: 工作流模型
- `ValidationSchemas.ts`: 数据验证规则

## 目录结构规范

```
src/
├── models/                    # 数据模型层
│   ├── index.ts              # 统一导出
│   ├── Project.ts            # 项目模型
│   ├── Task.ts               # 任务模型
│   ├── Sprint.ts             # Sprint 模型
│   ├── Workflow.ts           # 工作流模型
│   └── ValidationSchemas.ts  # 验证模式
├── services/                  # 业务逻辑层
│   ├── DataManager.ts        # 数据管理器
│   ├── Repository.ts         # 仓库基类
│   ├── ProjectRepository.ts  # 项目仓库
│   ├── TaskRepository.ts     # 任务仓库
│   ├── ProjectManager.ts     # 项目管理器
│   ├── TaskManager.ts        # 任务管理器
│   ├── PTMManager.ts         # 主服务协调器
│   ├── PTMFileHandler.ts     # 文件处理器
│   └── TasksPluginBridge.ts  # Tasks 插件桥接
└── ui/                       # UI 层
    ├── App.tsx              # 主应用组件
    ├── components/          # UI 组件
    │   ├── common/         # 通用组件
    │   ├── dashboard/      # 仪表板组件
    │   ├── layout/         # 布局组件
    │   └── index.ts        # 组件导出
    └── utils/              # UI 工具
        ├── ReactRenderer.tsx # React 渲染器
        └── theme.ts         # 主题工具
```

## 数据流设计

### 单向数据流
```
User Action → UI Component → Business Logic → Data Layer → Update UI
```

### 依赖注入模式
```typescript
class PTMManager {
  constructor(
    app: App,
    dataManager: DataManager,
    projectRepository: ProjectRepository,
    taskRepository: TaskRepository
  )
}
```

### 事件驱动架构
- 使用 Obsidian 事件系统
- 文件变更自动同步
- 任务状态自动更新

## 扩展性考虑

### 模块化设计
- 每个功能模块独立
- 通过接口定义边界
- 支持插件式扩展

### 未来功能预留
- Sprint 管理模块接口
- 甘特图可视化组件
- 工作流引擎扩展点
- 第三方集成接口

## 性能架构

### 缓存策略
- 内存缓存常用数据
- 增量更新机制
- 延迟加载非关键数据

### 渲染优化
- 虚拟滚动大数据列表
- React.memo 防止无效渲染
- 使用 Web Worker 处理计算密集型任务

---

**重要**: 所有新增模块必须遵循此架构规范，确保系统的一致性和可维护性。
description:
globs:
alwaysApply: false
---
