# PTM 文件格式和数据结构规范

## PTM 文件格式概述

PTM (Project Task Manager) 文件格式是基于 JSON 的项目配置和数据存储格式，用于：
- 项目配置持久化
- 数据导入导出
- 项目模板创建
- 跨平台数据交换

## 文件结构规范

### 1. 基础文件结构

```json
{
  "version": "1.0.0",
  "metadata": {
    "name": "项目名称",
    "description": "项目描述",
    "tags": ["标签1", "标签2"],
    "author": "作者",
    "created": "2024-01-01T00:00:00.000Z",
    "lastModified": "2024-01-01T00:00:00.000Z"
  },
  "project": {
    // 项目基础信息
  },
  "tasks": [
    // 任务列表
  ],
  "sprints": [
    // Sprint 列表
  ],
  "workflows": [
    // 工作流定义
  ],
  "settings": {
    // 项目特定设置
  },
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 2. 详细数据结构

#### 项目结构 (Project)
```typescript
interface Project {
  id: string;
  name: string;
  description?: string;
  startDate: string; // ISO 8601 格式
  endDate?: string;
  status: ProjectStatus;
  priority: Priority;
  tags: string[];
  settings: ProjectSettings;
  progress: number; // 0-100
  
  // 统计信息
  statistics?: {
    totalTasks: number;
    completedTasks: number;
    activeTasks: number;
    overdueTasks: number;
  };
  
  // 扩展属性
  customFields?: Record<string, any>;
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}
```

#### 任务结构 (Task)
```typescript
interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  
  // 项目关联
  projectId: string;
  
  // 层级关系
  parentId?: string;
  subtasks?: string[]; // 子任务ID列表
  
  // 依赖关系
  dependencies?: TaskDependency[];
  
  // 时间管理
  startDate?: string;
  dueDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  
  // 分配信息
  assignee?: string;
  tags: string[];
  
  // Tasks 插件兼容
  tasksPluginData?: {
    originalText: string;
    filePath: string;
    lineNumber: number;
    emoji: string;
  };
  
  // 扩展属性
  customFields?: Record<string, any>;
  attachments?: TaskAttachment[];
  comments?: TaskComment[];
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  BLOCKED = 'blocked',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

interface TaskDependency {
  taskId: string;
  type: DependencyType;
}

enum DependencyType {
  FINISH_TO_START = 'finish_to_start',
  START_TO_START = 'start_to_start',
  FINISH_TO_FINISH = 'finish_to_finish',
  START_TO_FINISH = 'start_to_finish'
}

interface TaskAttachment {
  id: string;
  name: string;
  type: 'file' | 'link' | 'note';
  path: string;
  size?: number;
  createdAt: string;
}

interface TaskComment {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt?: string;
}
```

#### Sprint 结构
```typescript
interface Sprint {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  
  // 时间范围
  startDate: string;
  endDate: string;
  
  // 任务分配
  taskIds: string[];
  
  // Sprint 目标
  goals: string[];
  
  // 容量规划
  capacity?: {
    totalHours: number;
    availableHours: number;
    committedHours: number;
  };
  
  // Sprint 状态
  status: SprintStatus;
  
  // 回顾信息
  retrospective?: {
    whatWentWell: string[];
    whatCouldImprove: string[];
    actionItems: string[];
  };
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

enum SprintStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}
```

#### 工作流结构
```typescript
interface Workflow {
  id: string;
  name: string;
  description?: string;
  
  // 状态定义
  states: WorkflowState[];
  
  // 转换规则
  transitions: WorkflowTransition[];
  
  // 应用范围
  applicableToProjects: string[];
  
  // 是否为默认工作流
  isDefault: boolean;
  
  // 时间戳
  createdAt: string;
  updatedAt: string;
}

interface WorkflowState {
  id: string;
  name: string;
  color: string;
  isInitial: boolean;
  isFinal: boolean;
  description?: string;
}

interface WorkflowTransition {
  id: string;
  name: string;
  fromStateId: string;
  toStateId: string;
  conditions?: TransitionCondition[];
  actions?: TransitionAction[];
}

interface TransitionCondition {
  type: 'user_role' | 'field_value' | 'custom';
  field?: string;
  operator?: 'equals' | 'not_equals' | 'contains';
  value?: any;
}

interface TransitionAction {
  type: 'set_field' | 'notify' | 'create_task' | 'custom';
  field?: string;
  value?: any;
  template?: string;
}
```

## 文件操作规范

### 1. 文件创建
```typescript
export class PTMFileHandler {
  async createPTMFile(
    filePath: string, 
    project: Project, 
    initialData?: Partial<PTMFileContent>
  ): Promise<TFile> {
    const ptmContent: PTMFileContent = {
      version: '1.0.0',
      metadata: {
        name: project.name,
        description: project.description,
        tags: project.tags,
        author: await this.getCurrentUser(),
        created: new Date().toISOString(),
        lastModified: new Date().toISOString()
      },
      project,
      tasks: initialData?.tasks || [],
      sprints: initialData?.sprints || [],
      workflows: initialData?.workflows || this.getDefaultWorkflows(),
      settings: {
        defaultWorkflow: 'default',
        enableTimeTracking: true,
        enableDependencies: true,
        autoArchiveCompleted: false,
        sprintDuration: 14,
        workingDays: [1, 2, 3, 4, 5], // Monday to Friday
        customFields: {},
        ...initialData?.settings
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const jsonContent = JSON.stringify(ptmContent, null, 2);
    const fileContent = this.addFileHeader(jsonContent, ptmContent.metadata);

    return await this.app.vault.create(filePath, fileContent);
  }

  private addFileHeader(jsonContent: string, metadata: PTMMetadata): string {
    const header = [
      '/*',
      ` * PTM Project File: ${metadata.name}`,
      ` * Description: ${metadata.description || 'No description'}`,
      ` * Created: ${metadata.created}`,
      ` * Author: ${metadata.author || 'Unknown'}`,
      ` * Version: 1.0.0`,
      ' *',
      ' * This file contains project configuration and task data.',
      ' * Do not edit manually unless you know what you are doing.',
      ' */',
      ''
    ].join('\n');

    return header + jsonContent;
  }
}
```

### 2. 数据验证
```typescript
export class PTMValidator {
  static validatePTMContent(content: any): ValidationResult {
    const errors: string[] = [];
    
    // 版本检查
    if (!content.version) {
      errors.push('PTM 文件缺少版本信息');
    } else if (!this.isSupportedVersion(content.version)) {
      errors.push(`不支持的 PTM 文件版本: ${content.version}`);
    }

    // 必需字段检查
    if (!content.project) {
      errors.push('PTM 文件缺少项目信息');
    } else {
      errors.push(...this.validateProject(content.project));
    }

    // 任务验证
    if (content.tasks) {
      errors.push(...this.validateTasks(content.tasks));
    }

    // 关系一致性检查
    errors.push(...this.validateRelationships(content));

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private static validateProject(project: any): string[] {
    const errors: string[] = [];
    
    if (!project.id) errors.push('项目缺少 ID');
    if (!project.name) errors.push('项目缺少名称');
    if (!project.status) errors.push('项目缺少状态');
    
    if (project.startDate && !this.isValidDate(project.startDate)) {
      errors.push('项目开始日期格式无效');
    }
    
    if (project.endDate && !this.isValidDate(project.endDate)) {
      errors.push('项目结束日期格式无效');
    }

    return errors;
  }

  private static validateTasks(tasks: any[]): string[] {
    const errors: string[] = [];
    const taskIds = new Set<string>();
    
    for (const [index, task] of tasks.entries()) {
      const taskPrefix = `任务 ${index + 1}:`;
      
      if (!task.id) {
        errors.push(`${taskPrefix} 缺少 ID`);
      } else if (taskIds.has(task.id)) {
        errors.push(`${taskPrefix} ID 重复: ${task.id}`);
      } else {
        taskIds.add(task.id);
      }
      
      if (!task.title) errors.push(`${taskPrefix} 缺少标题`);
      if (!task.status) errors.push(`${taskPrefix} 缺少状态`);
      if (!task.projectId) errors.push(`${taskPrefix} 缺少项目关联`);
      
      // 验证父子关系
      if (task.parentId && !taskIds.has(task.parentId)) {
        // 注意：这里可能需要延迟验证，因为父任务可能在后面定义
      }
    }

    return errors;
  }

  private static validateRelationships(content: any): string[] {
    const errors: string[] = [];
    
    if (!content.tasks || !content.project) {
      return errors;
    }

    const projectId = content.project.id;
    const taskIds = new Set(content.tasks.map((t: any) => t.id));
    
    // 验证任务的项目关联
    for (const task of content.tasks) {
      if (task.projectId !== projectId) {
        errors.push(`任务 ${task.title} 的项目 ID 不匹配`);
      }
      
      // 验证父任务存在
      if (task.parentId && !taskIds.has(task.parentId)) {
        errors.push(`任务 ${task.title} 的父任务不存在: ${task.parentId}`);
      }
      
      // 验证依赖任务存在
      if (task.dependencies) {
        for (const dep of task.dependencies) {
          if (!taskIds.has(dep.taskId)) {
            errors.push(`任务 ${task.title} 的依赖任务不存在: ${dep.taskId}`);
          }
        }
      }
    }

    return errors;
  }

  private static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  private static isSupportedVersion(version: string): boolean {
    const supportedVersions = ['1.0.0'];
    return supportedVersions.includes(version);
  }
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}
```

### 3. 版本迁移
```typescript
export class PTMVersionMigrator {
  static async migrate(content: any, targetVersion: string = '1.0.0'): Promise<any> {
    const currentVersion = content.version || '0.9.0';
    
    if (currentVersion === targetVersion) {
      return content;
    }

    const migrations = this.getMigrationPath(currentVersion, targetVersion);
    let migratedContent = { ...content };

    for (const migration of migrations) {
      migratedContent = await migration.migrate(migratedContent);
    }

    return migratedContent;
  }

  private static getMigrationPath(from: string, to: string): Migration[] {
    // 返回从 from 版本到 to 版本的迁移路径
    const migrations: Migration[] = [];
    
    if (from === '0.9.0' && to === '1.0.0') {
      migrations.push(new Migration_0_9_0_to_1_0_0());
    }

    return migrations;
  }
}

abstract class Migration {
  abstract readonly fromVersion: string;
  abstract readonly toVersion: string;
  abstract migrate(content: any): Promise<any>;
}

class Migration_0_9_0_to_1_0_0 extends Migration {
  readonly fromVersion = '0.9.0';
  readonly toVersion = '1.0.0';

  async migrate(content: any): Promise<any> {
    const migrated = { ...content };
    
    // 更新版本号
    migrated.version = '1.0.0';
    
    // 添加新的必需字段
    if (!migrated.metadata) {
      migrated.metadata = {
        name: migrated.project?.name || 'Unnamed Project',
        description: migrated.project?.description,
        tags: migrated.project?.tags || [],
        created: migrated.createdAt || new Date().toISOString(),
        lastModified: new Date().toISOString()
      };
    }

    // 迁移任务结构
    if (migrated.tasks) {
      migrated.tasks = migrated.tasks.map((task: any) => ({
        ...task,
        // 添加新字段的默认值
        customFields: task.customFields || {},
        attachments: task.attachments || [],
        comments: task.comments || []
      }));
    }

    return migrated;
  }
}
```

### 4. 导入导出功能
```typescript
export class PTMImportExport {
  async exportProject(projectId: string, options: ExportOptions = {}): Promise<string> {
    const project = await this.projectRepository.findById(projectId);
    if (!project) {
      throw new Error('项目未找到');
    }

    const tasks = await this.taskRepository.findByProject(projectId);
    const sprints = await this.sprintRepository.findByProject(projectId);
    
    const ptmContent: PTMFileContent = {
      version: '1.0.0',
      metadata: {
        name: project.name,
        description: project.description,
        tags: project.tags,
        created: project.createdAt,
        lastModified: new Date().toISOString(),
        exportedAt: new Date().toISOString(),
        exportOptions: options
      },
      project,
      tasks: options.includeTasks !== false ? tasks : [],
      sprints: options.includeSprints !== false ? sprints : [],
      workflows: await this.getProjectWorkflows(projectId),
      settings: project.settings,
      createdAt: project.createdAt,
      updatedAt: new Date().toISOString()
    };

    return JSON.stringify(ptmContent, null, 2);
  }

  async importProject(ptmContent: string): Promise<{ project: Project; tasks: Task[]; sprints: Sprint[] }> {
    let content: PTMFileContent;
    
    try {
      // 移除可能的文件头注释
      const cleanContent = this.removeFileHeader(ptmContent);
      content = JSON.parse(cleanContent);
    } catch (error) {
      throw new Error('PTM 文件格式无效');
    }

    // 验证内容
    const validation = PTMValidator.validatePTMContent(content);
    if (!validation.isValid) {
      throw new Error(`PTM 文件验证失败: ${validation.errors.join(', ')}`);
    }

    // 版本迁移
    content = await PTMVersionMigrator.migrate(content);

    // 生成新的 ID 避免冲突
    const idMapping = this.generateNewIds(content);
    content = this.remapIds(content, idMapping);

    // 导入数据
    const project = await this.projectRepository.create(content.project);
    const tasks = await this.batchCreateTasks(content.tasks);
    const sprints = await this.batchCreateSprints(content.sprints);

    return { project, tasks, sprints };
  }

  private removeFileHeader(content: string): string {
    const lines = content.split('\n');
    const jsonStartIndex = lines.findIndex(line => line.trim().startsWith('{'));
    return jsonStartIndex >= 0 ? lines.slice(jsonStartIndex).join('\n') : content;
  }

  private generateNewIds(content: PTMFileContent): Map<string, string> {
    const idMapping = new Map<string, string>();
    
    // 为项目生成新 ID
    idMapping.set(content.project.id, this.generateProjectId());
    
    // 为任务生成新 ID
    for (const task of content.tasks) {
      idMapping.set(task.id, this.generateTaskId());
    }
    
    // 为 Sprint 生成新 ID
    for (const sprint of content.sprints) {
      idMapping.set(sprint.id, this.generateSprintId());
    }

    return idMapping;
  }

  private remapIds(content: PTMFileContent, idMapping: Map<string, string>): PTMFileContent {
    const remapped = { ...content };
    
    // 重新映射项目 ID
    remapped.project = {
      ...content.project,
      id: idMapping.get(content.project.id)!
    };

    // 重新映射任务 ID 和关系
    remapped.tasks = content.tasks.map(task => ({
      ...task,
      id: idMapping.get(task.id)!,
      projectId: idMapping.get(task.projectId)!,
      parentId: task.parentId ? idMapping.get(task.parentId) : undefined,
      dependencies: task.dependencies?.map(dep => ({
        ...dep,
        taskId: idMapping.get(dep.taskId)!
      }))
    }));

    // 重新映射 Sprint ID
    remapped.sprints = content.sprints.map(sprint => ({
      ...sprint,
      id: idMapping.get(sprint.id)!,
      projectId: idMapping.get(sprint.projectId)!,
      taskIds: sprint.taskIds.map(taskId => idMapping.get(taskId)!).filter(Boolean)
    }));

    return remapped;
  }
}

interface ExportOptions {
  includeTasks?: boolean;
  includeSprints?: boolean;
  includeWorkflows?: boolean;
  includeSettings?: boolean;
  compressData?: boolean;
}
```

---

**重要原则**:
1. 所有日期使用 ISO 8601 格式
2. 保持向后兼容性
3. 实现数据验证和错误处理
4. 支持版本迁移机制
5. 提供完整的导入导出功能
description:
globs:
alwaysApply: false
---
