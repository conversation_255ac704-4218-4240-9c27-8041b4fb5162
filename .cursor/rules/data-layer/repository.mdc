# 仓库模式和数据访问规范

## 仓库模式概述

仓库模式（Repository Pattern）用于封装数据访问逻辑，提供统一的数据操作接口，隔离业务逻辑与数据存储实现细节。

## 基础架构

### 1. 仓库基类定义

```typescript
// src/services/Repository.ts
export interface Repository<T> {
  // 基础 CRUD 操作
  create(entity: Omit<T, 'id'>): Promise<T>;
  findById(id: string): Promise<T | null>;
  findAll(): Promise<T[]>;
  update(id: string, updates: Partial<T>): Promise<T>;
  delete(id: string): Promise<boolean>;
  
  // 查询操作
  findBy(criteria: Partial<T>): Promise<T[]>;
  findOne(criteria: Partial<T>): Promise<T | null>;
  count(criteria?: Partial<T>): Promise<number>;
  exists(id: string): Promise<boolean>;
}

export abstract class BaseRepository<T extends { id: string }> implements Repository<T> {
  protected dataManager: DataManager;
  protected collectionName: string;
  protected cache: Map<string, T> = new Map();

  constructor(dataManager: DataManager, collectionName: string) {
    this.dataManager = dataManager;
    this.collectionName = collectionName;
  }

  // 抽象方法 - 子类必须实现
  protected abstract validateEntity(entity: Partial<T>): Promise<void>;
  protected abstract generateId(): string;

  // 具体实现方法
  async create(entity: Omit<T, 'id'>): Promise<T> {
    await this.validateEntity(entity);
    
    const newEntity = {
      ...entity,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as T;

    await this.dataManager.set(`${this.collectionName}.${newEntity.id}`, newEntity);
    this.cache.set(newEntity.id, newEntity);
    
    return newEntity;
  }

  async findById(id: string): Promise<T | null> {
    // 先检查缓存
    if (this.cache.has(id)) {
      return this.cache.get(id)!;
    }

    // 从存储中获取
    const entity = await this.dataManager.get<T>(`${this.collectionName}.${id}`);
    if (entity) {
      this.cache.set(id, entity);
    }
    
    return entity;
  }

  async findAll(): Promise<T[]> {
    const allData = await this.dataManager.getCollection<T>(this.collectionName);
    
    // 更新缓存
    allData.forEach(entity => {
      this.cache.set(entity.id, entity);
    });
    
    return allData;
  }

  async update(id: string, updates: Partial<T>): Promise<T> {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error(`Entity with id ${id} not found`);
    }

    const updatedEntity = {
      ...existing,
      ...updates,
      id, // 确保 ID 不被修改
      updatedAt: new Date().toISOString()
    };

    await this.validateEntity(updatedEntity);
    await this.dataManager.set(`${this.collectionName}.${id}`, updatedEntity);
    this.cache.set(id, updatedEntity);
    
    return updatedEntity;
  }

  async delete(id: string): Promise<boolean> {
    const exists = await this.exists(id);
    if (!exists) {
      return false;
    }

    await this.dataManager.delete(`${this.collectionName}.${id}`);
    this.cache.delete(id);
    
    return true;
  }

  async findBy(criteria: Partial<T>): Promise<T[]> {
    const allEntities = await this.findAll();
    return allEntities.filter(entity => this.matchesCriteria(entity, criteria));
  }

  async findOne(criteria: Partial<T>): Promise<T | null> {
    const results = await this.findBy(criteria);
    return results.length > 0 ? results[0] : null;
  }

  async count(criteria?: Partial<T>): Promise<number> {
    if (criteria) {
      const filtered = await this.findBy(criteria);
      return filtered.length;
    }
    const all = await this.findAll();
    return all.length;
  }

  async exists(id: string): Promise<boolean> {
    const entity = await this.findById(id);
    return entity !== null;
  }

  // 辅助方法
  protected matchesCriteria(entity: T, criteria: Partial<T>): boolean {
    return Object.entries(criteria).every(([key, value]) => {
      return entity[key as keyof T] === value;
    });
  }

  // 缓存管理
  protected clearCache(): void {
    this.cache.clear();
  }

  protected getCacheSize(): number {
    return this.cache.size;
  }
}
```

### 2. 具体仓库实现

#### 项目仓库
```typescript
// src/services/ProjectRepository.ts
export class ProjectRepository extends BaseRepository<Project> {
  constructor(dataManager: DataManager) {
    super(dataManager, 'projects');
  }

  protected async validateEntity(entity: Partial<Project>): Promise<void> {
    if (!entity.name || entity.name.trim().length === 0) {
      throw new Error('项目名称不能为空');
    }

    if (entity.name.length > 100) {
      throw new Error('项目名称不能超过100个字符');
    }

    if (entity.startDate && entity.endDate) {
      const start = new Date(entity.startDate);
      const end = new Date(entity.endDate);
      if (start >= end) {
        throw new Error('项目结束日期必须晚于开始日期');
      }
    }

    // 检查项目名称唯一性
    if (entity.name) {
      const existing = await this.findByName(entity.name);
      if (existing && existing.id !== entity.id) {
        throw new Error('项目名称已存在');
      }
    }
  }

  protected generateId(): string {
    return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 扩展方法
  async findByName(name: string): Promise<Project | null> {
    return this.findOne({ name } as Partial<Project>);
  }

  async findByStatus(status: ProjectStatus): Promise<Project[]> {
    return this.findBy({ status } as Partial<Project>);
  }

  async findActive(): Promise<Project[]> {
    return this.findBy({ 
      status: 'ACTIVE' as ProjectStatus 
    } as Partial<Project>);
  }

  async getProjectStatistics(projectId: string): Promise<ProjectStatistics> {
    const project = await this.findById(projectId);
    if (!project) {
      throw new Error('项目未找到');
    }

    // 获取项目任务统计
    const taskRepo = new TaskRepository(this.dataManager);
    const tasks = await taskRepo.findByProject(projectId);
    
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
    const inProgressTasks = tasks.filter(t => t.status === 'IN_PROGRESS').length;
    const blockedTasks = tasks.filter(t => t.status === 'BLOCKED').length;

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      blockedTasks,
      progress: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      estimatedCompletion: this.calculateEstimatedCompletion(project, tasks)
    };
  }

  private calculateEstimatedCompletion(project: Project, tasks: Task[]): Date | null {
    // 基于任务进度和历史速度计算预期完成时间
    // 实现估算逻辑...
    return null;
  }
}
```

#### 任务仓库
```typescript
// src/services/TaskRepository.ts
export class TaskRepository extends BaseRepository<Task> {
  constructor(dataManager: DataManager) {
    super(dataManager, 'tasks');
  }

  protected async validateEntity(entity: Partial<Task>): Promise<void> {
    if (!entity.title || entity.title.trim().length === 0) {
      throw new Error('任务标题不能为空');
    }

    if (entity.title.length > 200) {
      throw new Error('任务标题不能超过200个字符');
    }

    if (entity.dueDate) {
      const dueDate = new Date(entity.dueDate);
      const now = new Date();
      if (dueDate <= now) {
        console.warn('任务截止日期已过期');
      }
    }

    // 验证依赖关系
    if (entity.dependencies && entity.dependencies.length > 0) {
      await this.validateDependencies(entity.id!, entity.dependencies);
    }
  }

  protected generateId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 任务特定方法
  async findByProject(projectId: string): Promise<Task[]> {
    return this.findBy({ projectId } as Partial<Task>);
  }

  async findByStatus(status: TaskStatus): Promise<Task[]> {
    return this.findBy({ status } as Partial<Task>);
  }

  async findByPriority(priority: Priority): Promise<Task[]> {
    return this.findBy({ priority } as Partial<Task>);
  }

  async findSubTasks(parentId: string): Promise<Task[]> {
    return this.findBy({ parentId } as Partial<Task>);
  }

  async findRootTasks(projectId: string): Promise<Task[]> {
    const projectTasks = await this.findByProject(projectId);
    return projectTasks.filter(task => !task.parentId);
  }

  async getDependentTasks(taskId: string): Promise<Task[]> {
    const allTasks = await this.findAll();
    return allTasks.filter(task => 
      task.dependencies && task.dependencies.includes(taskId)
    );
  }

  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task> {
    const task = await this.findById(taskId);
    if (!task) {
      throw new Error('任务未找到');
    }

    // 更新任务状态
    const updatedTask = await this.update(taskId, { status });

    // 处理子任务状态联动
    await this.handleSubTaskStatusUpdate(updatedTask);

    // 处理父任务状态联动
    if (updatedTask.parentId) {
      await this.handleParentTaskStatusUpdate(updatedTask.parentId);
    }

    return updatedTask;
  }

  private async handleSubTaskStatusUpdate(parentTask: Task): Promise<void> {
    if (parentTask.status === 'COMPLETED') {
      const subTasks = await this.findSubTasks(parentTask.id);
      for (const subTask of subTasks) {
        if (subTask.status !== 'COMPLETED') {
          await this.updateTaskStatus(subTask.id, 'COMPLETED');
        }
      }
    }
  }

  private async handleParentTaskStatusUpdate(parentId: string): Promise<void> {
    const subTasks = await this.findSubTasks(parentId);
    if (subTasks.length === 0) return;

    const allCompleted = subTasks.every(task => task.status === 'COMPLETED');
    if (allCompleted) {
      await this.updateTaskStatus(parentId, 'COMPLETED');
    }
  }

  private async validateDependencies(taskId: string, dependencies: string[]): Promise<void> {
    // 检查依赖任务是否存在
    for (const depId of dependencies) {
      const depTask = await this.findById(depId);
      if (!depTask) {
        throw new Error(`依赖任务 ${depId} 不存在`);
      }

      // 检查循环依赖
      if (await this.hasCircularDependency(taskId, depId)) {
        throw new Error('检测到循环依赖');
      }
    }
  }

  private async hasCircularDependency(taskId: string, dependencyId: string): Promise<boolean> {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = async (currentId: string): Promise<boolean> => {
      if (recursionStack.has(currentId)) {
        return true; // 发现循环
      }
      if (visited.has(currentId)) {
        return false;
      }

      visited.add(currentId);
      recursionStack.add(currentId);

      const currentTask = await this.findById(currentId);
      if (currentTask && currentTask.dependencies) {
        for (const depId of currentTask.dependencies) {
          if (await dfs(depId)) {
            return true;
          }
        }
      }

      recursionStack.delete(currentId);
      return false;
    };

    return await dfs(dependencyId);
  }
}
```

## 数据管理器集成

### 1. DataManager 接口
```typescript
export interface DataManagerInterface {
  // 基础操作
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  delete(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  
  // 集合操作
  getCollection<T>(collectionName: string): Promise<T[]>;
  setCollection<T>(collectionName: string, items: T[]): Promise<void>;
  
  // 缓存管理
  clearCache(): void;
  getCacheStats(): CacheStats;
  
  // 事务支持
  transaction<T>(callback: () => Promise<T>): Promise<T>;
}
```

### 2. 缓存策略
```typescript
interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
  enableLRU: boolean;
}

export class CacheManager {
  private cache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }

    // 更新 LRU
    if (this.config.enableLRU) {
      entry.lastAccessed = Date.now();
    }

    return entry.value as T;
  }

  set<T>(key: string, value: T): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, {
      value,
      created: Date.now(),
      lastAccessed: Date.now()
    });
  }

  private evictOldest(): void {
    if (this.config.enableLRU) {
      // 删除最久未使用的项
      let oldestKey = '';
      let oldestTime = Date.now();

      for (const [key, entry] of this.cache) {
        if (entry.lastAccessed < oldestTime) {
          oldestTime = entry.lastAccessed;
          oldestKey = key;
        }
      }

      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    } else {
      // 删除最早创建的项
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }
  }

  private isExpired(entry: CacheEntry): boolean {
    return (Date.now() - entry.created) > this.config.ttl;
  }
}
```

## 性能优化

### 1. 批量操作
```typescript
export class BatchOperations {
  static async batchCreate<T>(
    repository: Repository<T>, 
    entities: Omit<T, 'id'>[]
  ): Promise<T[]> {
    const results: T[] = [];
    
    // 使用事务确保一致性
    await repository.dataManager.transaction(async () => {
      for (const entity of entities) {
        const created = await repository.create(entity);
        results.push(created);
      }
    });

    return results;
  }

  static async batchUpdate<T>(
    repository: Repository<T>,
    updates: Array<{ id: string; data: Partial<T> }>
  ): Promise<T[]> {
    const results: T[] = [];

    await repository.dataManager.transaction(async () => {
      for (const { id, data } of updates) {
        const updated = await repository.update(id, data);
        results.push(updated);
      }
    });

    return results;
  }
}
```

### 2. 查询优化
```typescript
export class QueryOptimizer {
  static createIndex<T>(
    items: T[], 
    keySelector: (item: T) => string
  ): Map<string, T[]> {
    const index = new Map<string, T[]>();
    
    for (const item of items) {
      const key = keySelector(item);
      if (!index.has(key)) {
        index.set(key, []);
      }
      index.get(key)!.push(item);
    }
    
    return index;
  }

  static async paginatedQuery<T>(
    repository: Repository<T>,
    criteria: Partial<T>,
    page: number,
    pageSize: number
  ): Promise<{ items: T[]; total: number; hasMore: boolean }> {
    const allItems = await repository.findBy(criteria);
    const total = allItems.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const items = allItems.slice(startIndex, endIndex);
    
    return {
      items,
      total,
      hasMore: endIndex < total
    };
  }
}
```

## 错误处理和日志

### 1. 仓库错误处理
```typescript
export class RepositoryError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: any
  ) {
    super(message);
    this.name = 'RepositoryError';
  }
}

export enum ErrorCodes {
  ENTITY_NOT_FOUND = 'ENTITY_NOT_FOUND',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  DUPLICATE_ENTITY = 'DUPLICATE_ENTITY',
  DEPENDENCY_VIOLATION = 'DEPENDENCY_VIOLATION',
  STORAGE_ERROR = 'STORAGE_ERROR'
}
```

### 2. 操作日志
```typescript
export class RepositoryLogger {
  static log(operation: string, entity: string, details?: any): void {
    console.log(`[Repository] ${operation} ${entity}`, details);
  }

  static error(operation: string, entity: string, error: Error): void {
    console.error(`[Repository] ${operation} ${entity} failed:`, error);
  }
}
```

---

**重要原则**:
1. 所有数据操作必须通过仓库
2. 保持仓库接口的一致性
3. 实现适当的缓存策略
4. 处理并发访问和数据一致性
5. 提供详细的错误信息和日志
description:
globs:
alwaysApply: false
---
