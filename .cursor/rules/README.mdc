---
description:
globs:
alwaysApply: false
---
# Obsidian 超级任务管理器 - Cursor 开发规则

## 项目概览

这是一个功能全面的 Obsidian 项目任务管理插件开发规则集，支持敏捷开发方法论并与 Tasks 插件完全兼容。

## 规则文件结构

```
.cursor/rules/
├── README.mdc                  # 本文件 - 总体概述和指引
├── architecture.mdc            # 系统架构和模块划分规范
├── frontend/
│   ├── component.mdc          # React 组件开发规范
│   ├── styling.mdc            # 样式和 Obsidian 主题集成规范
│   └── state.mdc              # 状态管理和数据流规范
├── data-layer/
│   ├── repository.mdc         # 仓库模式和数据访问规范
│   └── file-format.mdc        # PTM 文件格式和数据结构规范
├── integration/
│   ├── obsidian-api.mdc       # Obsidian API 使用规范
│   └── tasks-plugin.mdc       # Tasks 插件兼容性规范
├── workflow/
│   └── git.mdc                # Git 版本控制规范
└── code-quality/
    ├── naming.mdc             # 命名约定和编码规范
    ├── testing.mdc            # 测试策略和规范
    └── performance.mdc        # 性能优化指导原则
```

## 核心开发原则

### 1. 设计一致性
- 遵循 Obsidian 官方设计语言
- 支持深色/浅色主题切换
- 保持响应式设计原则

### 2. 用户体验优先
- 与 Tasks 插件无缝兼容
- 保持简洁直观的用户界面
- 提供流畅的交互体验

### 3. 代码质量
- 使用 TypeScript 严格模式
- 遵循函数式编程范式
- 保持高测试覆盖率

### 4. 性能考虑
- 实现增量加载和虚拟滚动
- 使用缓存机制优化数据访问
- 避免阻塞主线程的操作

## 快速参考

### 开发模式
```bash
npm run dev        # 开发模式，自动部署到 Obsidian
npm run build      # 生产构建
npm run test       # 运行测试套件
```

### 关键约定
- 所有组件使用 React + TypeScript
- 数据存储使用 Repository 模式
- 文件使用 PTM 格式（JSON）
- UI 组件支持 Obsidian 主题系统

## 贡献指南

1. 阅读相关规则文件
2. 遵循命名约定和代码风格
3. 编写相应的测试
4. 确保与现有功能兼容
5. 更新相关文档

请在开发前仔细阅读相关的具体规则文件，确保代码符合项目标准。

