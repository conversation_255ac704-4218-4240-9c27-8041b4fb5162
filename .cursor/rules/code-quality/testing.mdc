# 测试策略和规范

## 测试金字塔策略

采用经典的测试金字塔模型，确保测试的全面性和效率：

```
        /\
       /  \
      / E2E \     端到端测试 (少量)
     /______\     - 用户场景测试
    /        \    - 插件集成测试
   /Integration\ 集成测试 (适量)
  /__________\   - 组件间交互
 /            \  - API 集成
/  Unit Tests  \ 单元测试 (大量)
/______________\ - 纯函数测试
                 - 组件单元测试
```

### 1. 测试覆盖率目标

```typescript
// 覆盖率目标
{
  statements: 80,    // 语句覆盖率 >= 80%
  branches: 75,      // 分支覆盖率 >= 75%
  functions: 85,     // 函数覆盖率 >= 85%
  lines: 80          // 行覆盖率 >= 80%
}

// 关键模块要求更高覆盖率
{
  // 数据层和业务逻辑
  'src/services/': 90,
  'src/models/': 85,
  
  // UI 组件
  'src/ui/components/': 75,
  
  // 工具函数
  'src/utils/': 95
}
```

## 单元测试规范

### 1. 测试文件组织

```
src/
├── services/
│   ├── ProjectManager.ts
│   ├── ProjectManager.test.ts        # 与源文件同目录
│   ├── TaskRepository.ts
│   └── TaskRepository.test.ts
├── ui/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx       # 组件测试
│   │   │   └── Button.stories.tsx    # Storybook 故事
│   │   └── TaskList/
│   │       ├── TaskList.tsx
│   │       ├── TaskList.test.tsx
│   │       └── __tests__/            # 复杂组件的测试目录
│   │           ├── TaskList.unit.test.tsx
│   │           └── TaskList.integration.test.tsx
└── __tests__/                        # 全局测试配置和工具
    ├── setup.ts
    ├── utils/
    │   ├── mockData.ts
    │   ├── testHelpers.ts
    │   └── renderWithProviders.tsx
    └── fixtures/
        ├── projects.json
        └── tasks.json
```

### 2. 业务逻辑测试

```typescript
// ProjectManager.test.ts
import { ProjectManager } from './ProjectManager';
import { ProjectRepository } from './ProjectRepository';
import { TaskRepository } from './TaskRepository';

// Mock 依赖
jest.mock('./ProjectRepository');
jest.mock('./TaskRepository');

describe('ProjectManager', () => {
  let projectManager: ProjectManager;
  let mockProjectRepo: jest.Mocked<ProjectRepository>;
  let mockTaskRepo: jest.Mocked<TaskRepository>;

  beforeEach(() => {
    mockProjectRepo = new ProjectRepository({} as any) as jest.Mocked<ProjectRepository>;
    mockTaskRepo = new TaskRepository({} as any) as jest.Mocked<TaskRepository>;
    projectManager = new ProjectManager({} as any, mockProjectRepo, mockTaskRepo);
  });

  describe('createProject', () => {
    it('should create a project with valid data', async () => {
      // Arrange
      const projectData = {
        name: 'Test Project',
        description: 'Test Description'
      };
      const expectedProject = {
        id: 'project_123',
        ...projectData,
        status: 'ACTIVE',
        createdAt: '2024-01-01T00:00:00.000Z'
      };

      mockProjectRepo.create.mockResolvedValue(expectedProject);

      // Act
      const result = await projectManager.createProject(projectData);

      // Assert
      expect(result).toEqual(expectedProject);
      expect(mockProjectRepo.create).toHaveBeenCalledWith(
        expect.objectContaining(projectData)
      );
    });

    it('should throw error when project name is empty', async () => {
      // Arrange
      const invalidData = { name: '', description: 'Test' };

      // Act & Assert
      await expect(projectManager.createProject(invalidData))
        .rejects
        .toThrow('项目名称不能为空');
    });

    it('should throw error when project name already exists', async () => {
      // Arrange
      const projectData = { name: 'Existing Project' };
      mockProjectRepo.findByName.mockResolvedValue({} as any);

      // Act & Assert
      await expect(projectManager.createProject(projectData))
        .rejects
        .toThrow('项目名称已存在');
    });
  });

  describe('deleteProject', () => {
    it('should delete project and handle associated tasks', async () => {
      // Arrange
      const projectId = 'project_123';
      const associatedTasks = [
        { id: 'task_1', projectId },
        { id: 'task_2', projectId }
      ];

      mockProjectRepo.findById.mockResolvedValue({ id: projectId } as any);
      mockTaskRepo.findByProject.mockResolvedValue(associatedTasks as any);
      mockProjectRepo.delete.mockResolvedValue(true);

      // Act
      await projectManager.deleteProject(projectId, { handleTasks: 'delete' });

      // Assert
      expect(mockTaskRepo.delete).toHaveBeenCalledTimes(2);
      expect(mockProjectRepo.delete).toHaveBeenCalledWith(projectId);
    });
  });
});
```

### 3. 数据仓库测试

```typescript
// TaskRepository.test.ts
import { TaskRepository } from './TaskRepository';
import { DataManager } from './DataManager';

describe('TaskRepository', () => {
  let repository: TaskRepository;
  let mockDataManager: jest.Mocked<DataManager>;

  beforeEach(() => {
    mockDataManager = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      getCollection: jest.fn()
    } as any;

    repository = new TaskRepository(mockDataManager);
  });

  describe('create', () => {
    it('should create task with generated ID and timestamps', async () => {
      // Arrange
      const taskData = {
        title: 'Test Task',
        status: 'TODO' as const,
        projectId: 'project_123'
      };

      mockDataManager.set.mockResolvedValue(undefined);

      // Act
      const result = await repository.create(taskData);

      // Assert
      expect(result).toMatchObject({
        title: 'Test Task',
        status: 'TODO',
        projectId: 'project_123'
      });
      expect(result.id).toMatch(/^task_\d+_\w+$/);
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
      expect(mockDataManager.set).toHaveBeenCalledWith(
        `tasks.${result.id}`,
        result
      );
    });

    it('should validate required fields', async () => {
      // Arrange
      const invalidData = { title: '' } as any;

      // Act & Assert
      await expect(repository.create(invalidData))
        .rejects
        .toThrow('任务标题不能为空');
    });
  });

  describe('updateTaskStatus', () => {
    it('should update task status and handle subtask cascading', async () => {
      // Arrange
      const parentTask = {
        id: 'parent_task',
        title: 'Parent Task',
        status: 'IN_PROGRESS' as const,
        subtasks: ['child_1', 'child_2']
      };

      const childTasks = [
        { id: 'child_1', status: 'TODO' as const },
        { id: 'child_2', status: 'TODO' as const }
      ];

      mockDataManager.get
        .mockResolvedValueOnce(parentTask)  // findById for parent
        .mockResolvedValueOnce(childTasks[0]) // findById for child_1
        .mockResolvedValueOnce(childTasks[1]); // findById for child_2

      jest.spyOn(repository, 'findSubTasks').mockResolvedValue(childTasks as any);
      jest.spyOn(repository, 'update').mockImplementation(async (id, updates) => {
        return { ...parentTask, ...updates } as any;
      });

      // Act
      await repository.updateTaskStatus('parent_task', 'COMPLETED');

      // Assert
      expect(repository.update).toHaveBeenCalledWith(
        'parent_task',
        expect.objectContaining({ status: 'COMPLETED' })
      );
      // 验证子任务也被更新
      expect(repository.updateTaskStatus).toHaveBeenCalledWith('child_1', 'COMPLETED');
      expect(repository.updateTaskStatus).toHaveBeenCalledWith('child_2', 'COMPLETED');
    });
  });
});
```

## React 组件测试

### 1. 基础组件测试

```typescript
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('applies correct variant class', () => {
    render(<Button variant="primary">Primary Button</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('ptm-button--primary');
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('disables button when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('ptm-button--disabled');
  });

  it('shows loading state', () => {
    render(<Button loading>Loading Button</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
```

### 2. 复杂组件测试

```typescript
// TaskList.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TaskList } from './TaskList';
import { renderWithProviders } from '../../__tests__/utils/renderWithProviders';
import { mockTasks } from '../../__tests__/utils/mockData';

// 测试工具函数
const renderTaskList = (props = {}) => {
  const defaultProps = {
    tasks: mockTasks,
    onTaskSelect: jest.fn(),
    onTaskUpdate: jest.fn(),
    loading: false
  };

  return renderWithProviders(<TaskList {...defaultProps} {...props} />);
};

describe('TaskList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders task list correctly', () => {
    renderTaskList();
    
    expect(screen.getByText('Task List')).toBeInTheDocument();
    expect(screen.getAllByTestId('task-item')).toHaveLength(mockTasks.length);
  });

  it('shows loading state', () => {
    renderTaskList({ loading: true });
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.queryByTestId('task-item')).not.toBeInTheDocument();
  });

  it('shows empty state when no tasks', () => {
    renderTaskList({ tasks: [] });
    
    expect(screen.getByText('No tasks found')).toBeInTheDocument();
    expect(screen.getByTestId('empty-state')).toBeInTheDocument();
  });

  it('handles task selection', () => {
    const onTaskSelect = jest.fn();
    renderTaskList({ onTaskSelect });
    
    const firstTask = screen.getAllByTestId('task-item')[0];
    fireEvent.click(firstTask);
    
    expect(onTaskSelect).toHaveBeenCalledWith(mockTasks[0]);
  });

  it('filters tasks based on search input', async () => {
    renderTaskList();
    
    const searchInput = screen.getByPlaceholderText('Search tasks...');
    fireEvent.change(searchInput, { target: { value: 'important' } });
    
    await waitFor(() => {
      const visibleTasks = screen.getAllByTestId('task-item');
      expect(visibleTasks).toHaveLength(1);
      expect(screen.getByText('Important Task')).toBeInTheDocument();
    });
  });

  it('handles task status updates', async () => {
    const onTaskUpdate = jest.fn();
    renderTaskList({ onTaskUpdate });
    
    const statusButton = screen.getByTestId('task-status-button-1');
    fireEvent.click(statusButton);
    
    const completedOption = screen.getByText('Completed');
    fireEvent.click(completedOption);
    
    await waitFor(() => {
      expect(onTaskUpdate).toHaveBeenCalledWith(
        'task_1',
        expect.objectContaining({ status: 'COMPLETED' })
      );
    });
  });

  it('supports keyboard navigation', () => {
    renderTaskList();
    
    const firstTask = screen.getAllByTestId('task-item')[0];
    firstTask.focus();
    
    fireEvent.keyDown(firstTask, { key: 'ArrowDown' });
    
    const secondTask = screen.getAllByTestId('task-item')[1];
    expect(secondTask).toHaveFocus();
  });
});
```

### 3. Hook 测试

```typescript
// useTaskData.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { useTaskData } from './useTaskData';
import { TaskRepository } from '../services/TaskRepository';

// Mock 仓库
jest.mock('../services/TaskRepository');

describe('useTaskData', () => {
  let mockTaskRepo: jest.Mocked<TaskRepository>;

  beforeEach(() => {
    mockTaskRepo = new TaskRepository({} as any) as jest.Mocked<TaskRepository>;
    // 提供 mock 实现
    (TaskRepository as jest.Mock).mockImplementation(() => mockTaskRepo);
  });

  it('should load tasks on mount', async () => {
    // Arrange
    const mockTasks = [
      { id: 'task_1', title: 'Task 1' },
      { id: 'task_2', title: 'Task 2' }
    ];
    mockTaskRepo.findByProject.mockResolvedValue(mockTasks as any);

    // Act
    const { result } = renderHook(() => useTaskData('project_123'));

    // Assert
    expect(result.current.loading).toBe(true);
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.tasks).toEqual(mockTasks);
    });
  });

  it('should handle errors gracefully', async () => {
    // Arrange
    const errorMessage = 'Failed to load tasks';
    mockTaskRepo.findByProject.mockRejectedValue(new Error(errorMessage));

    // Act
    const { result } = renderHook(() => useTaskData('project_123'));

    // Assert
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.tasks).toEqual([]);
    });
  });

  it('should refetch data when called', async () => {
    // Arrange
    const initialTasks = [{ id: 'task_1' }];
    const updatedTasks = [{ id: 'task_1' }, { id: 'task_2' }];
    
    mockTaskRepo.findByProject
      .mockResolvedValueOnce(initialTasks as any)
      .mockResolvedValueOnce(updatedTasks as any);

    // Act
    const { result } = renderHook(() => useTaskData('project_123'));

    await waitFor(() => {
      expect(result.current.tasks).toEqual(initialTasks);
    });

    // Refetch
    await result.current.refetch();

    // Assert
    await waitFor(() => {
      expect(result.current.tasks).toEqual(updatedTasks);
    });
    expect(mockTaskRepo.findByProject).toHaveBeenCalledTimes(2);
  });
});
```

## 集成测试

### 1. 组件集成测试

```typescript
// ProjectDashboard.integration.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProjectDashboard } from './ProjectDashboard';
import { renderWithProviders } from '../../__tests__/utils/renderWithProviders';
import { PTMManager } from '../../services/PTMManager';

// Mock PTMManager
jest.mock('../../services/PTMManager');

describe('ProjectDashboard Integration', () => {
  let mockPTMManager: jest.Mocked<PTMManager>;

  beforeEach(() => {
    mockPTMManager = {
      getProjects: jest.fn(),
      createProject: jest.fn(),
      deleteProject: jest.fn(),
      getProjectStatistics: jest.fn()
    } as any;
  });

  it('should load and display projects on mount', async () => {
    // Arrange
    const mockProjects = [
      { id: '1', name: 'Project 1', status: 'ACTIVE' },
      { id: '2', name: 'Project 2', status: 'COMPLETED' }
    ];
    mockPTMManager.getProjects.mockResolvedValue(mockProjects);

    // Act
    renderWithProviders(
      <ProjectDashboard ptmManager={mockPTMManager} />
    );

    // Assert
    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.getByText('Project 2')).toBeInTheDocument();
    });
  });

  it('should create new project through modal', async () => {
    // Arrange
    const newProject = { id: '3', name: 'New Project', status: 'ACTIVE' };
    mockPTMManager.getProjects.mockResolvedValue([]);
    mockPTMManager.createProject.mockResolvedValue(newProject);

    renderWithProviders(
      <ProjectDashboard ptmManager={mockPTMManager} />
    );

    // Act
    const createButton = screen.getByText('Create Project');
    fireEvent.click(createButton);

    const nameInput = screen.getByLabelText('Project Name');
    fireEvent.change(nameInput, { target: { value: 'New Project' } });

    const submitButton = screen.getByText('Create');
    fireEvent.click(submitButton);

    // Assert
    await waitFor(() => {
      expect(mockPTMManager.createProject).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'New Project' })
      );
    });
  });

  it('should handle project deletion with confirmation', async () => {
    // Arrange
    const mockProjects = [
      { id: '1', name: 'Project 1', status: 'ACTIVE' }
    ];
    mockPTMManager.getProjects.mockResolvedValue(mockProjects);
    mockPTMManager.deleteProject.mockResolvedValue(true);

    renderWithProviders(
      <ProjectDashboard ptmManager={mockPTMManager} />
    );

    await waitFor(() => {
      expect(screen.getByText('Project 1')).toBeInTheDocument();
    });

    // Act
    const deleteButton = screen.getByTestId('delete-project-1');
    fireEvent.click(deleteButton);

    const confirmButton = screen.getByText('Confirm Delete');
    fireEvent.click(confirmButton);

    // Assert
    await waitFor(() => {
      expect(mockPTMManager.deleteProject).toHaveBeenCalledWith('1');
    });
  });
});
```

### 2. 数据流集成测试

```typescript
// dataFlow.integration.test.ts
import { PTMManager } from '../services/PTMManager';
import { DataManager } from '../services/DataManager';
import { ProjectRepository } from '../services/ProjectRepository';
import { TaskRepository } from '../services/TaskRepository';

describe('Data Flow Integration', () => {
  let ptmManager: PTMManager;
  let dataManager: DataManager;

  beforeEach(async () => {
    // 使用内存存储进行测试
    dataManager = new DataManager({} as any, {
      enableCaching: true,
      maxCacheSize: 100,
      dataFileName: 'test-data.json'
    });

    ptmManager = new PTMManager({} as any, { dataManagerOptions: {} });
    await ptmManager.initialize();
  });

  it('should maintain data consistency across operations', async () => {
    // 1. 创建项目
    const project = await ptmManager.createProject({
      name: 'Integration Test Project',
      description: 'Test project for integration testing'
    });

    expect(project.id).toBeDefined();
    expect(project.name).toBe('Integration Test Project');

    // 2. 在项目中创建任务
    const task = await ptmManager.createTask({
      title: 'Integration Test Task',
      projectId: project.id,
      status: 'TODO'
    });

    expect(task.projectId).toBe(project.id);

    // 3. 验证数据关联性
    const projectTasks = await ptmManager.getProjectTasks(project.id);
    expect(projectTasks).toHaveLength(1);
    expect(projectTasks[0].id).toBe(task.id);

    // 4. 更新任务状态
    await ptmManager.updateTaskStatus(task.id, 'COMPLETED');

    // 5. 验证项目进度更新
    const updatedProject = await ptmManager.getProject(project.id);
    expect(updatedProject.progress).toBe(100);
  });

  it('should handle cascading deletes correctly', async () => {
    // 创建项目和多个任务
    const project = await ptmManager.createProject({
      name: 'Delete Test Project'
    });

    const tasks = await Promise.all([
      ptmManager.createTask({ title: 'Task 1', projectId: project.id }),
      ptmManager.createTask({ title: 'Task 2', projectId: project.id }),
      ptmManager.createTask({ title: 'Task 3', projectId: project.id })
    ]);

    // 删除项目
    await ptmManager.deleteProject(project.id, { handleTasks: 'delete' });

    // 验证项目和任务都被删除
    const deletedProject = await ptmManager.getProject(project.id);
    expect(deletedProject).toBeNull();

    for (const task of tasks) {
      const deletedTask = await ptmManager.getTask(task.id);
      expect(deletedTask).toBeNull();
    }
  });
});
```

## 端到端测试

### 1. 用户场景测试

```typescript
// e2e/projectManagement.test.ts
import { test, expect } from '@playwright/test';

test.describe('Project Management E2E', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到 Obsidian 并加载插件
    await page.goto('obsidian://vault/test-vault');
    await page.waitForSelector('.workspace');
    
    // 打开插件仪表板
    await page.keyboard.press('Control+P');
    await page.fill('.prompt-input', 'Open Project Dashboard');
    await page.press('.prompt-input', 'Enter');
  });

  test('complete project creation workflow', async ({ page }) => {
    // 1. 点击创建项目按钮
    await page.click('[data-testid="create-project-button"]');
    
    // 2. 填写项目信息
    await page.fill('[data-testid="project-name-input"]', 'E2E Test Project');
    await page.fill('[data-testid="project-description-input"]', 'Created via E2E test');
    
    // 3. 选择项目状态
    await page.click('[data-testid="project-status-select"]');
    await page.click('[data-testid="status-option-active"]');
    
    // 4. 提交表单
    await page.click('[data-testid="create-project-submit"]');
    
    // 5. 验证项目创建成功
    await expect(page.locator('[data-testid="project-card"]')).toContainText('E2E Test Project');
    await expect(page.locator('.success-message')).toContainText('Project created successfully');
  });

  test('task management within project', async ({ page }) => {
    // 前置条件：创建项目
    await createTestProject(page, 'Task Management Test');
    
    // 1. 进入项目详情
    await page.click('[data-testid="project-card"]:has-text("Task Management Test")');
    
    // 2. 创建任务
    await page.click('[data-testid="add-task-button"]');
    await page.fill('[data-testid="task-title-input"]', 'E2E Test Task');
    await page.click('[data-testid="task-priority-high"]');
    await page.click('[data-testid="create-task-submit"]');
    
    // 3. 验证任务出现在列表中
    await expect(page.locator('[data-testid="task-item"]')).toContainText('E2E Test Task');
    
    // 4. 更新任务状态
    await page.click('[data-testid="task-status-button"]');
    await page.click('[data-testid="status-in-progress"]');
    
    // 5. 验证状态更新
    await expect(page.locator('[data-testid="task-item"]')).toHaveClass(/in-progress/);
  });

  test('integration with Obsidian markdown files', async ({ page }) => {
    // 1. 创建新的 markdown 文件
    await page.keyboard.press('Control+N');
    
    // 2. 添加任务语法
    await page.fill('.cm-editor', `# My Project Tasks

- [ ] Task from markdown #task
- [x] Completed task #task  
- [/] In progress task #task`);
    
    // 3. 保存文件
    await page.keyboard.press('Control+S');
    await page.fill('[data-testid="file-name-input"]', 'test-tasks.md');
    await page.press('[data-testid="file-name-input"]', 'Enter');
    
    // 4. 检查任务是否同步到插件
    await page.click('[data-testid="ptm-ribbon-icon"]');
    await expect(page.locator('[data-testid="task-item"]')).toContainText('Task from markdown');
  });
});

// 测试工具函数
async function createTestProject(page: any, name: string) {
  await page.click('[data-testid="create-project-button"]');
  await page.fill('[data-testid="project-name-input"]', name);
  await page.click('[data-testid="create-project-submit"]');
  await page.waitForSelector(`[data-testid="project-card"]:has-text("${name}")`);
}
```

## 测试工具和配置

### 1. Jest 配置

```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.(ts|tsx)',
    '**/*.(test|spec).(ts|tsx)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  collectCoverageFrom: [
    'src/**/*.(ts|tsx)',
    '!src/**/*.d.ts',
    '!src/__tests__/**/*',
    '!src/**/index.ts'
  ],
  coverageThreshold: {
    global: {
      statements: 80,
      branches: 75,
      functions: 85,
      lines: 80
    },
    './src/services/': {
      statements: 90,
      branches: 85,
      functions: 95,
      lines: 90
    }
  }
};
```

### 2. 测试工具函数

```typescript
// src/__tests__/utils/renderWithProviders.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { ThemeProvider } from '../ui/utils/ThemeProvider';

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  theme?: 'light' | 'dark';
  initialData?: any;
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  const { theme = 'light', initialData, ...renderOptions } = options;

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <ThemeProvider theme={theme} initialData={initialData}>
        {children}
      </ThemeProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// 导出常用的测试工具
export * from '@testing-library/react';
export { renderWithProviders as render };
```

```typescript
// src/__tests__/utils/mockData.ts
export const mockProjects = [
  {
    id: 'project_1',
    name: 'Test Project 1',
    description: 'First test project',
    status: 'ACTIVE',
    priority: 'HIGH',
    tags: ['test', 'important'],
    progress: 45,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  // ... 更多模拟数据
];

export const mockTasks = [
  {
    id: 'task_1',
    title: 'Important Task',
    description: 'This is an important task',
    status: 'TODO',
    priority: 'HIGH',
    projectId: 'project_1',
    tags: ['urgent'],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  // ... 更多模拟数据
];

// 工厂函数
export function createMockProject(overrides: Partial<Project> = {}): Project {
  return {
    id: 'mock_project',
    name: 'Mock Project',
    status: 'ACTIVE',
    priority: 'MEDIUM',
    tags: [],
    progress: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  };
}
```

### 3. 测试脚本

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:unit": "jest --testPathPattern=unit",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "playwright test",
    "test:e2e:headed": "playwright test --headed"
  }
}
```

---

**重要原则**:
1. 测试应该快速、可靠、独立
2. 优先测试用户行为而非实现细节
3. 保持测试代码的简洁和可读性
4. 使用有意义的测试描述和断言
5. 定期维护和更新测试用例
description:
globs:
alwaysApply: false
---
