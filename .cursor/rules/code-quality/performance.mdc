# 性能优化指导原则

## 性能目标和指标

### 1. 关键性能指标 (KPIs)

```typescript
// 插件性能目标
const PERFORMANCE_TARGETS = {
  // 启动性能
  pluginInitialization: 500,    // 插件初始化 < 500ms
  firstRender: 300,             // 首次渲染 < 300ms
  
  // 运行时性能
  taskListRender: 100,          // 任务列表渲染 < 100ms
  searchResponse: 50,           // 搜索响应 < 50ms
  statusUpdate: 30,             // 状态更新 < 30ms
  
  // 内存使用
  maxMemoryUsage: 50 * 1024 * 1024,  // 最大内存使用 < 50MB
  memoryLeakThreshold: 5 * 1024 * 1024, // 内存泄漏阈值 < 5MB
  
  // 存储性能
  dataLoad: 200,                // 数据加载 < 200ms
  dataSave: 100,                // 数据保存 < 100ms
  cacheHitRate: 0.85           // 缓存命中率 > 85%
};
```

### 2. 性能监控

```typescript
// 性能监控工具类
export class PerformanceMonitor {
  private static metrics: Map<string, PerformanceEntry[]> = new Map();
  
  static startTiming(label: string): void {
    performance.mark(`${label}-start`);
  }
  
  static endTiming(label: string): number {
    performance.mark(`${label}-end`);
    performance.measure(label, `${label}-start`, `${label}-end`);
    
    const measure = performance.getEntriesByName(label)[0];
    const duration = measure.duration;
    
    // 记录指标
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    this.metrics.get(label)!.push(measure);
    
    // 性能警告
    if (duration > this.getThreshold(label)) {
      console.warn(`Performance issue: ${label} took ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }
  
  private static getThreshold(label: string): number {
    const thresholds: Record<string, number> = {
      'task-list-render': 100,
      'project-load': 200,
      'search-query': 50
    };
    return thresholds[label] || 1000;
  }
  
  static getMetrics(): Record<string, PerformanceStats> {
    const result: Record<string, PerformanceStats> = {};
    
    for (const [label, entries] of this.metrics) {
      const durations = entries.map(entry => entry.duration);
      result[label] = {
        count: durations.length,
        average: durations.reduce((a, b) => a + b, 0) / durations.length,
        min: Math.min(...durations),
        max: Math.max(...durations),
        p95: this.percentile(durations, 0.95)
      };
    }
    
    return result;
  }
  
  private static percentile(values: number[], p: number): number {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * p) - 1;
    return sorted[index];
  }
}

interface PerformanceStats {
  count: number;
  average: number;
  min: number;
  max: number;
  p95: number;
}

// 使用装饰器简化性能监控
function measurePerformance(label?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const measureLabel = label || `${target.constructor.name}.${propertyKey}`;
    
    descriptor.value = async function (...args: any[]) {
      PerformanceMonitor.startTiming(measureLabel);
      try {
        const result = await originalMethod.apply(this, args);
        return result;
      } finally {
        PerformanceMonitor.endTiming(measureLabel);
      }
    };
    
    return descriptor;
  };
}
```

## 数据层性能优化

### 1. 缓存策略

```typescript
// 多级缓存系统
export class CacheManager {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private persistentCache: Map<string, any> = new Map();
  private config: CacheConfig;
  
  constructor(config: CacheConfig) {
    this.config = config;
    this.setupAutoCleanup();
  }
  
  // L1: 内存缓存 (最快)
  getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry || this.isExpired(entry)) {
      this.memoryCache.delete(key);
      return null;
    }
    
    // 更新 LRU 时间戳
    entry.lastAccessed = Date.now();
    return entry.value as T;
  }
  
  setToMemory<T>(key: string, value: T, ttl?: number): void {
    // 检查内存限制
    if (this.memoryCache.size >= this.config.maxMemoryEntries) {
      this.evictLRU();
    }
    
    this.memoryCache.set(key, {
      value,
      created: Date.now(),
      lastAccessed: Date.now(),
      ttl: ttl || this.config.defaultTTL
    });
  }
  
  // L2: 持久化缓存 (中等速度)
  async getFromPersistent<T>(key: string): Promise<T | null> {
    // 检查内存缓存
    const memoryResult = this.getFromMemory<T>(key);
    if (memoryResult) {
      return memoryResult;
    }
    
    // 从持久化存储读取
    const data = this.persistentCache.get(key);
    if (data && !this.isExpired(data)) {
      // 提升到内存缓存
      this.setToMemory(key, data.value);
      return data.value;
    }
    
    return null;
  }
  
  // 智能预取
  async prefetch(keys: string[]): Promise<void> {
    const missingKeys = keys.filter(key => !this.memoryCache.has(key));
    
    if (missingKeys.length === 0) return;
    
    // 批量加载
    const results = await this.batchLoad(missingKeys);
    
    // 缓存结果
    for (const [key, value] of Object.entries(results)) {
      this.setToMemory(key, value);
    }
  }
  
  private async batchLoad(ids: string[]): Promise<Record<string, any>> {
    // 实现批量加载逻辑
    return {};
  }
  
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.created > entry.ttl;
  }
  
  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, entry] of this.memoryCache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }
  
  private setupAutoCleanup(): void {
    setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }
  
  private cleanup(): void {
    const now = Date.now();
    
    for (const [key, entry] of this.memoryCache) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
      }
    }
  }
}

interface CacheEntry {
  value: any;
  created: number;
  lastAccessed: number;
  ttl: number;
}

interface CacheConfig {
  maxMemoryEntries: number;
  defaultTTL: number;
  cleanupInterval: number;
}
```

### 2. 数据访问优化

```typescript
// 数据加载优化
export class OptimizedRepository<T extends { id: string }> {
  private cache: CacheManager;
  private loadingPromises: Map<string, Promise<T | null>> = new Map();
  
  constructor(private dataManager: DataManager, cache: CacheManager) {
    this.cache = cache;
  }
  
  // 防抖动的数据加载
  async findById(id: string): Promise<T | null> {
    // 检查缓存
    const cached = this.cache.getFromMemory<T>(`entity:${id}`);
    if (cached) {
      return cached;
    }
    
    // 防止重复请求
    if (this.loadingPromises.has(id)) {
      return this.loadingPromises.get(id)!;
    }
    
    const loadPromise = this.loadEntity(id);
    this.loadingPromises.set(id, loadPromise);
    
    try {
      const result = await loadPromise;
      if (result) {
        this.cache.setToMemory(`entity:${id}`, result);
      }
      return result;
    } finally {
      this.loadingPromises.delete(id);
    }
  }
  
  // 批量加载
  async findByIds(ids: string[]): Promise<T[]> {
    const results: T[] = [];
    const missingIds: string[] = [];
    
    // 检查缓存
    for (const id of ids) {
      const cached = this.cache.getFromMemory<T>(`entity:${id}`);
      if (cached) {
        results.push(cached);
      } else {
        missingIds.push(id);
      }
    }
    
    // 批量加载缺失的数据
    if (missingIds.length > 0) {
      const loaded = await this.batchLoad(missingIds);
      results.push(...loaded);
      
      // 缓存新加载的数据
      loaded.forEach(entity => {
        this.cache.setToMemory(`entity:${entity.id}`, entity);
      });
    }
    
    return results;
  }
  
  // 分页加载
  async findWithPagination(
    criteria: any, 
    page: number, 
    pageSize: number
  ): Promise<PaginatedResult<T>> {
    const cacheKey = `page:${JSON.stringify(criteria)}:${page}:${pageSize}`;
    
    // 检查页面缓存
    const cached = this.cache.getFromMemory<PaginatedResult<T>>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const offset = (page - 1) * pageSize;
    const allItems = await this.findByCriteria(criteria);
    const items = allItems.slice(offset, offset + pageSize);
    
    const result: PaginatedResult<T> = {
      items,
      total: allItems.length,
      page,
      pageSize,
      hasMore: offset + pageSize < allItems.length
    };
    
    // 缓存结果（较短的 TTL）
    this.cache.setToMemory(cacheKey, result, 30000); // 30 秒
    
    return result;
  }
  
  private async loadEntity(id: string): Promise<T | null> {
    return this.dataManager.get<T>(`${this.collectionName}.${id}`);
  }
  
  private async batchLoad(ids: string[]): Promise<T[]> {
    const promises = ids.map(id => this.loadEntity(id));
    const results = await Promise.all(promises);
    return results.filter(Boolean) as T[];
  }
}

interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}
```

### 3. 查询优化

```typescript
// 索引管理
export class IndexManager {
  private indexes: Map<string, Map<any, string[]>> = new Map();
  
  // 创建索引
  createIndex<T>(
    collectionName: string, 
    indexName: string, 
    keySelector: (item: T) => any,
    items: T[]
  ): void {
    const indexKey = `${collectionName}:${indexName}`;
    const index = new Map<any, string[]>();
    
    for (const item of items) {
      const key = keySelector(item);
      if (!index.has(key)) {
        index.set(key, []);
      }
      index.get(key)!.push((item as any).id);
    }
    
    this.indexes.set(indexKey, index);
  }
  
  // 使用索引查询
  findByIndex(collectionName: string, indexName: string, key: any): string[] {
    const indexKey = `${collectionName}:${indexName}`;
    const index = this.indexes.get(indexKey);
    return index?.get(key) || [];
  }
  
  // 更新索引
  updateIndex<T>(
    collectionName: string, 
    indexName: string, 
    keySelector: (item: T) => any,
    item: T,
    oldKey?: any
  ): void {
    const indexKey = `${collectionName}:${indexName}`;
    const index = this.indexes.get(indexKey);
    
    if (!index) return;
    
    const itemId = (item as any).id;
    const newKey = keySelector(item);
    
    // 从旧键中移除
    if (oldKey !== undefined && oldKey !== newKey) {
      const oldList = index.get(oldKey);
      if (oldList) {
        const filteredList = oldList.filter(id => id !== itemId);
        if (filteredList.length === 0) {
          index.delete(oldKey);
        } else {
          index.set(oldKey, filteredList);
        }
      }
    }
    
    // 添加到新键
    if (!index.has(newKey)) {
      index.set(newKey, []);
    }
    const newList = index.get(newKey)!;
    if (!newList.includes(itemId)) {
      newList.push(itemId);
    }
  }
}

// 查询优化器
export class QueryOptimizer {
  constructor(private indexManager: IndexManager) {}
  
  // 优化查询计划
  optimizeQuery<T>(
    collectionName: string,
    criteria: QueryCriteria<T>
  ): QueryPlan {
    const plan: QueryPlan = {
      useIndex: false,
      indexName: '',
      estimatedCost: 0,
      operations: []
    };
    
    // 检查是否可以使用索引
    const indexableFields = this.findIndexableFields(collectionName, criteria);
    
    if (indexableFields.length > 0) {
      // 选择最佳索引
      const bestIndex = this.selectBestIndex(indexableFields);
      plan.useIndex = true;
      plan.indexName = bestIndex.name;
      plan.estimatedCost = bestIndex.cost;
      plan.operations.push({
        type: 'index-lookup',
        field: bestIndex.field,
        value: criteria[bestIndex.field]
      });
    } else {
      // 全表扫描
      plan.operations.push({ type: 'full-scan' });
      plan.estimatedCost = 1000; // 高成本
    }
    
    return plan;
  }
  
  private findIndexableFields<T>(
    collectionName: string, 
    criteria: QueryCriteria<T>
  ): IndexInfo[] {
    const indexableFields: IndexInfo[] = [];
    
    for (const [field, value] of Object.entries(criteria)) {
      // 检查是否存在该字段的索引
      const indexExists = this.indexManager.findByIndex(
        collectionName, 
        field, 
        value
      ).length >= 0;
      
      if (indexExists) {
        indexableFields.push({
          name: field,
          field,
          cost: this.estimateIndexCost(collectionName, field, value)
        });
      }
    }
    
    return indexableFields;
  }
  
  private selectBestIndex(indexes: IndexInfo[]): IndexInfo {
    return indexes.reduce((best, current) => 
      current.cost < best.cost ? current : best
    );
  }
  
  private estimateIndexCost(collectionName: string, field: string, value: any): number {
    const matchingIds = this.indexManager.findByIndex(collectionName, field, value);
    return matchingIds.length; // 返回匹配项数量作为成本估算
  }
}

interface QueryCriteria<T> {
  [K in keyof T]?: T[K];
}

interface QueryPlan {
  useIndex: boolean;
  indexName: string;
  estimatedCost: number;
  operations: QueryOperation[];
}

interface QueryOperation {
  type: 'index-lookup' | 'full-scan' | 'filter' | 'sort';
  field?: string;
  value?: any;
}

interface IndexInfo {
  name: string;
  field: string;
  cost: number;
}
```

## React 组件性能优化

### 1. 渲染优化

```typescript
// 智能组件记忆化
const TaskItem = React.memo<TaskItemProps>(({ task, onUpdate, onSelect }) => {
  const handleStatusChange = useCallback((newStatus: TaskStatus) => {
    onUpdate(task.id, { status: newStatus });
  }, [task.id, onUpdate]);
  
  const handleClick = useCallback(() => {
    onSelect(task);
  }, [task, onSelect]);
  
  // 只在关键属性变化时重新渲染
  return (
    <div 
      className={`task-item task-item--${task.status}`}
      onClick={handleClick}
    >
      <TaskStatusButton 
        status={task.status} 
        onChange={handleStatusChange} 
      />
      <span className="task-title">{task.title}</span>
      <TaskPriority priority={task.priority} />
    </div>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.title === nextProps.task.title &&
    prevProps.task.status === nextProps.task.status &&
    prevProps.task.priority === nextProps.task.priority &&
    prevProps.onUpdate === nextProps.onUpdate &&
    prevProps.onSelect === nextProps.onSelect
  );
});

// 虚拟滚动优化
const VirtualizedTaskList: React.FC<VirtualizedTaskListProps> = ({ 
  tasks, 
  itemHeight = 60,
  containerHeight = 400
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      tasks.length
    );
    
    return { start: startIndex, end: endIndex };
  }, [scrollTop, itemHeight, containerHeight, tasks.length]);
  
  const visibleTasks = useMemo(() => {
    return tasks.slice(visibleRange.start, visibleRange.end);
  }, [tasks, visibleRange]);
  
  const totalHeight = tasks.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;
  
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);
  
  return (
    <div 
      className="virtualized-list"
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div 
          style={{ 
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleTasks.map((task, index) => (
            <TaskItem
              key={task.id}
              task={task}
              style={{ height: itemHeight }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// 防抖搜索优化
const useDebounced = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

const SearchableTaskList: React.FC<SearchableTaskListProps> = ({ tasks }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounced(searchTerm, 300);
  
  const filteredTasks = useMemo(() => {
    if (!debouncedSearchTerm) return tasks;
    
    return tasks.filter(task =>
      task.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      task.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [tasks, debouncedSearchTerm]);
  
  return (
    <div>
      <input
        type="text"
        placeholder="Search tasks..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      <VirtualizedTaskList tasks={filteredTasks} />
    </div>
  );
};
```

### 2. 状态管理优化

```typescript
// 状态选择器优化
const useTaskSelector = <T,>(
  selector: (tasks: Task[]) => T,
  equalityFn?: (prev: T, next: T) => boolean
) => {
  const tasks = useSelector(state => state.tasks);
  
  return useMemo(() => selector(tasks), [tasks, selector]);
};

// 分片状态更新
const useTaskUpdater = () => {
  const dispatch = useDispatch();
  
  const updateTask = useCallback((taskId: string, updates: Partial<Task>) => {
    // 批量更新，避免频繁渲染
    dispatch(batchUpdateTasks([{ id: taskId, updates }]));
  }, [dispatch]);
  
  const updateTasks = useCallback((updates: Array<{ id: string; updates: Partial<Task> }>) => {
    dispatch(batchUpdateTasks(updates));
  }, [dispatch]);
  
  return { updateTask, updateTasks };
};

// 状态规范化
interface NormalizedTaskState {
  tasks: {
    byId: Record<string, Task>;
    allIds: string[];
    byProject: Record<string, string[]>;
    byStatus: Record<TaskStatus, string[]>;
  };
  loading: boolean;
  error: string | null;
}

const tasksSlice = createSlice({
  name: 'tasks',
  initialState: {
    tasks: {
      byId: {},
      allIds: [],
      byProject: {},
      byStatus: {}
    },
    loading: false,
    error: null
  } as NormalizedTaskState,
  reducers: {
    addTask: (state, action: PayloadAction<Task>) => {
      const task = action.payload;
      
      // 添加到主索引
      state.tasks.byId[task.id] = task;
      state.tasks.allIds.push(task.id);
      
      // 更新项目索引
      if (!state.tasks.byProject[task.projectId]) {
        state.tasks.byProject[task.projectId] = [];
      }
      state.tasks.byProject[task.projectId].push(task.id);
      
      // 更新状态索引
      if (!state.tasks.byStatus[task.status]) {
        state.tasks.byStatus[task.status] = [];
      }
      state.tasks.byStatus[task.status].push(task.id);
    },
    
    updateTask: (state, action: PayloadAction<{ id: string; updates: Partial<Task> }>) => {
      const { id, updates } = action.payload;
      const existingTask = state.tasks.byId[id];
      
      if (!existingTask) return;
      
      // 如果状态发生变化，更新状态索引
      if (updates.status && updates.status !== existingTask.status) {
        // 从旧状态中移除
        const oldStatusTasks = state.tasks.byStatus[existingTask.status];
        if (oldStatusTasks) {
          const index = oldStatusTasks.indexOf(id);
          if (index > -1) {
            oldStatusTasks.splice(index, 1);
          }
        }
        
        // 添加到新状态
        if (!state.tasks.byStatus[updates.status]) {
          state.tasks.byStatus[updates.status] = [];
        }
        state.tasks.byStatus[updates.status].push(id);
      }
      
      // 更新任务数据
      state.tasks.byId[id] = { ...existingTask, ...updates };
    }
  }
});

// 选择器优化
export const selectTasksByProject = createSelector(
  [
    (state: RootState) => state.tasks.tasks.byId,
    (state: RootState) => state.tasks.tasks.byProject,
    (state: RootState, projectId: string) => projectId
  ],
  (tasksById, tasksByProject, projectId) => {
    const taskIds = tasksByProject[projectId] || [];
    return taskIds.map(id => tasksById[id]);
  }
);

export const selectTasksByStatus = createSelector(
  [
    (state: RootState) => state.tasks.tasks.byId,
    (state: RootState) => state.tasks.tasks.byStatus,
    (state: RootState, status: TaskStatus) => status
  ],
  (tasksById, tasksByStatus, status) => {
    const taskIds = tasksByStatus[status] || [];
    return taskIds.map(id => tasksById[id]);
  }
);
```

## 内存管理优化

### 1. 内存泄漏预防

```typescript
// 内存监控工具
export class MemoryMonitor {
  private static intervalId: number | null = null;
  private static baseline: number = 0;
  
  static startMonitoring(): void {
    this.baseline = this.getCurrentMemoryUsage();
    
    this.intervalId = window.setInterval(() => {
      const current = this.getCurrentMemoryUsage();
      const growth = current - this.baseline;
      
      if (growth > 10 * 1024 * 1024) { // 10MB 增长警告
        console.warn(`Memory usage increased by ${growth / 1024 / 1024}MB`);
        
        if (growth > 50 * 1024 * 1024) { // 50MB 严重警告
          console.error('Potential memory leak detected!');
          this.logMemoryUsage();
        }
      }
    }, 30000); // 每30秒检查一次
  }
  
  static stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
  
  private static getCurrentMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
  
  private static logMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.table({
        'Used': `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        'Total': `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        'Limit': `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
      });
    }
  }
}

// 自动清理 Hook
const useCleanup = (cleanupFn: () => void, deps: any[] = []) => {
  const cleanupRef = useRef(cleanupFn);
  cleanupRef.current = cleanupFn;
  
  useEffect(() => {
    return () => {
      cleanupRef.current();
    };
  }, deps);
};

// 弱引用缓存
export class WeakCache<K extends object, V> {
  private cache = new WeakMap<K, V>();
  
  get(key: K): V | undefined {
    return this.cache.get(key);
  }
  
  set(key: K, value: V): void {
    this.cache.set(key, value);
  }
  
  has(key: K): boolean {
    return this.cache.has(key);
  }
  
  delete(key: K): boolean {
    return this.cache.delete(key);
  }
}

// 对象池模式
export class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // 预填充对象池
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn());
    }
  }
  
  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
  
  size(): number {
    return this.pool.length;
  }
}
```

### 2. 资源管理

```typescript
// 资源管理器
export class ResourceManager {
  private resources: Map<string, Resource> = new Map();
  private cleanup: (() => void)[] = [];
  
  register<T>(id: string, resource: T, cleanup?: (resource: T) => void): T {
    // 如果资源已存在，先清理
    if (this.resources.has(id)) {
      this.unregister(id);
    }
    
    this.resources.set(id, {
      value: resource,
      cleanup: cleanup ? () => cleanup(resource) : undefined,
      timestamp: Date.now()
    });
    
    return resource;
  }
  
  unregister(id: string): boolean {
    const resource = this.resources.get(id);
    if (resource) {
      resource.cleanup?.();
      this.resources.delete(id);
      return true;
    }
    return false;
  }
  
  get<T>(id: string): T | undefined {
    const resource = this.resources.get(id);
    return resource?.value as T;
  }
  
  addCleanup(cleanupFn: () => void): void {
    this.cleanup.push(cleanupFn);
  }
  
  cleanup(): void {
    // 清理所有注册的资源
    for (const [id] of this.resources) {
      this.unregister(id);
    }
    
    // 执行额外的清理函数
    this.cleanup.forEach(fn => {
      try {
        fn();
      } catch (error) {
        console.error('Cleanup error:', error);
      }
    });
    
    this.cleanup.length = 0;
  }
  
  // 自动清理过期资源
  cleanupExpired(maxAge: number = 5 * 60 * 1000): void {
    const now = Date.now();
    
    for (const [id, resource] of this.resources) {
      if (now - resource.timestamp > maxAge) {
        this.unregister(id);
      }
    }
  }
}

interface Resource {
  value: any;
  cleanup?: () => void;
  timestamp: number;
}

// React 中的资源管理
const useResourceManager = () => {
  const resourceManager = useRef(new ResourceManager());
  
  useEffect(() => {
    const manager = resourceManager.current;
    
    // 设置定期清理
    const cleanupInterval = setInterval(() => {
      manager.cleanupExpired();
    }, 60000); // 每分钟清理一次
    
    return () => {
      clearInterval(cleanupInterval);
      manager.cleanup();
    };
  }, []);
  
  return resourceManager.current;
};
```

## 构建和部署优化

### 1. 代码分割

```typescript
// 动态导入组件
const LazyTaskList = React.lazy(() => import('./TaskList'));
const LazyKanbanBoard = React.lazy(() => import('./KanbanBoard'));
const LazyGanttChart = React.lazy(() => import('./GanttChart'));

// 路由级别的代码分割
const AppRouter: React.FC = () => {
  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/dashboard" element={<ProjectDashboard />} />
          <Route path="/tasks" element={<LazyTaskList />} />
          <Route path="/kanban" element={<LazyKanbanBoard />} />
          <Route path="/gantt" element={<LazyGanttChart />} />
        </Routes>
      </Suspense>
    </Router>
  );
};

// 特性检测和动态加载
const useConditionalImport = (condition: boolean, importFn: () => Promise<any>) => {
  const [module, setModule] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    if (condition && !module && !loading) {
      setLoading(true);
      importFn()
        .then(setModule)
        .catch(console.error)
        .finally(() => setLoading(false));
    }
  }, [condition, module, loading, importFn]);
  
  return { module, loading };
};
```
description:
globs:
alwaysApply: false
---
