# 命名约定和编码规范

## 文件和目录命名

### 1. 目录结构命名

```
src/
├── models/              # 数据模型 - kebab-case
├── services/            # 业务服务 - kebab-case  
├── ui/
│   ├── components/      # React 组件
│   │   ├── common/      # 通用组件
│   │   ├── dashboard/   # 功能模块组件
│   │   └── layout/      # 布局组件
│   └── utils/           # UI 工具函数
└── __tests__/           # 测试文件
```

### 2. 文件命名规范

#### TypeScript/JavaScript 文件
```typescript
// ✅ React 组件文件 - PascalCase
ProjectDashboard.tsx
TaskListView.tsx
Button.tsx

// ✅ 服务类和工具类 - PascalCase
DataManager.ts
ProjectRepository.ts
TasksPluginBridge.ts

// ✅ 接口和类型定义 - PascalCase
Project.ts
Task.ts
ValidationSchemas.ts

// ✅ 工具函数和常量 - camelCase 或 kebab-case
themeUtils.ts
dateHelpers.ts
constants.ts

// ✅ 测试文件 - 与被测试文件同名 + .test
Button.test.tsx
ProjectManager.test.ts
```

#### 样式文件
```css
/* ✅ 全局样式 */
styles.css
global.css

/* ✅ 组件样式 - kebab-case */
button.css
task-list.css
project-dashboard.css

/* ✅ 主题和工具样式 */
theme-variables.css
utility-classes.css
```

#### 配置和文档文件
```bash
# ✅ 配置文件 - kebab-case
tsconfig.json
esbuild.config.mjs
package.json
.gitignore

# ✅ 文档文件 - kebab-case 或 snake_case
README.md
CHANGELOG.md
api-documentation.md
development-guide.md
```

## TypeScript 命名规范

### 1. 变量和函数命名

```typescript
// ✅ 变量 - camelCase
const projectId = 'project_123';
const isTaskCompleted = true;
const taskListData = [];

// ✅ 常量 - SCREAMING_SNAKE_CASE
const MAX_CACHE_SIZE = 1000;
const DEFAULT_PROJECT_STATUS = 'ACTIVE';
const API_ENDPOINTS = {
  PROJECTS: '/api/projects',
  TASKS: '/api/tasks'
};

// ✅ 函数 - camelCase，动词开头
function createProject(name: string): Project { }
function validateTaskData(task: Task): boolean { }
function calculateProgress(tasks: Task[]): number { }

// ✅ 异步函数 - async 前缀可选但推荐
async function loadProjectData(id: string): Promise<Project> { }
async function saveTaskToFile(task: Task): Promise<void> { }

// ✅ 布尔值 - is/has/can/should 前缀
const isLoading = false;
const hasPermission = true;
const canEdit = user.role === 'admin';
const shouldValidate = config.enableValidation;

// ❌ 避免使用
const data = [];              // 太泛型
const temp = 'value';         // 临时变量应该有意义的名称
const flag = true;            // flag 应该说明具体含义
```

### 2. 类和接口命名

```typescript
// ✅ 类 - PascalCase，名词
class ProjectManager {
  private projectRepository: ProjectRepository;
  
  constructor(repository: ProjectRepository) {
    this.projectRepository = repository;
  }
  
  async createProject(data: CreateProjectData): Promise<Project> {
    // 实现
  }
}

class TaskValidator {
  static validateTask(task: Task): ValidationResult {
    // 实现
  }
}

// ✅ 接口 - PascalCase，可选 I 前缀
interface Project {
  id: string;
  name: string;
  status: ProjectStatus;
}

interface IRepository<T> {
  create(entity: T): Promise<T>;
  findById(id: string): Promise<T | null>;
}

// ✅ 类型别名 - PascalCase
type TaskStatus = 'TODO' | 'IN_PROGRESS' | 'COMPLETED';
type EventHandler<T> = (event: T) => void;
type APIResponse<T> = {
  data: T;
  status: number;
  message?: string;
};

// ✅ 枚举 - PascalCase
enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}
```

### 3. 泛型命名

```typescript
// ✅ 泛型参数 - 单个大写字母，从 T 开始
interface Repository<T> {
  create(entity: T): Promise<T>;
  update(id: string, updates: Partial<T>): Promise<T>;
}

interface ApiResponse<TData, TError = string> {
  data?: TData;
  error?: TError;
  success: boolean;
}

// ✅ 更具描述性的泛型名称（复杂场景）
interface EventEmitter<TEventMap extends Record<string, any>> {
  on<K extends keyof TEventMap>(event: K, handler: TEventMap[K]): void;
  emit<K extends keyof TEventMap>(event: K, ...args: Parameters<TEventMap[K]>): void;
}

// ✅ 约束泛型
interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

class BaseRepository<T extends BaseEntity> {
  async save(entity: T): Promise<T> {
    // 实现
  }
}
```

## React 组件命名

### 1. 组件和 Props

```typescript
// ✅ 组件 - PascalCase，描述性名词
export const TaskListView: React.FC<TaskListViewProps> = ({ 
  tasks, 
  onTaskSelect, 
  loading 
}) => {
  return (
    <div className="task-list-view">
      {/* 组件内容 */}
    </div>
  );
};

// ✅ Props 接口 - 组件名 + Props
interface TaskListViewProps {
  tasks: Task[];
  selectedTaskId?: string;
  loading?: boolean;
  onTaskSelect?: (task: Task) => void;
  onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void;
}

// ✅ 组件状态 - camelCase
const [isLoading, setIsLoading] = useState(false);
const [selectedTask, setSelectedTask] = useState<Task | null>(null);
const [formData, setFormData] = useState<FormData>({});

// ✅ 事件处理函数 - handle 前缀
const handleTaskClick = useCallback((task: Task) => {
  setSelectedTask(task);
  onTaskSelect?.(task);
}, [onTaskSelect]);

const handleFormSubmit = useCallback(async (event: React.FormEvent) => {
  event.preventDefault();
  // 处理表单提交
}, []);

// ✅ 自定义 Hook - use 前缀
function useTaskData(projectId: string) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Hook 实现
  
  return { tasks, loading, refetch };
}

function useLocalStorage<T>(key: string, defaultValue: T) {
  // Hook 实现
}
```

### 2. 组件文件组织

```typescript
// ✅ 组件导出模式
// TaskList.tsx
export interface TaskListProps {
  // Props 定义
}

export const TaskList: React.FC<TaskListProps> = (props) => {
  // 组件实现
};

export default TaskList;

// ✅ 复杂组件的子组件
// TaskList/TaskList.tsx - 主组件
// TaskList/TaskItem.tsx - 子组件
// TaskList/hooks/useTaskList.ts - 专用 Hook
// TaskList/index.ts - 统一导出

// TaskList/index.ts
export { TaskList as default } from './TaskList';
export type { TaskListProps } from './TaskList';
export { TaskItem } from './TaskItem';
```

## CSS 和样式命名

### 1. CSS 类命名 (BEM 变体)

```css
/* ✅ 组件基础类 - 项目前缀 + 组件名 */
.ptm-button { }
.ptm-task-list { }
.ptm-project-dashboard { }

/* ✅ 组件元素 - __ 分隔 */
.ptm-task-list__item { }
.ptm-task-list__header { }
.ptm-button__icon { }

/* ✅ 组件修饰符 - -- 分隔 */
.ptm-button--primary { }
.ptm-button--disabled { }
.ptm-task-list--compact { }

/* ✅ 状态类 - is/has 前缀 */
.ptm-task-list__item.is-selected { }
.ptm-button.is-loading { }
.ptm-project-card.has-error { }

/* ✅ 工具类 - 描述性 */
.ptm-visually-hidden { }
.ptm-truncate-text { }
.ptm-fade-in { }
```

### 2. CSS 变量命名

```css
/* ✅ Obsidian 主题变量（不修改） */
:root {
  /* 使用 Obsidian 提供的变量 */
  --background-primary: var(--background-primary);
  --text-normal: var(--text-normal);
}

/* ✅ 插件特定变量 */
:root {
  /* 间距 */
  --ptm-spacing-xs: 0.25rem;
  --ptm-spacing-sm: 0.5rem;
  --ptm-spacing-md: 1rem;
  --ptm-spacing-lg: 1.5rem;
  --ptm-spacing-xl: 2rem;
  
  /* 组件尺寸 */
  --ptm-button-height: 2.5rem;
  --ptm-input-height: 2.25rem;
  --ptm-header-height: 3rem;
  
  /* 动画 */
  --ptm-transition-fast: 0.15s ease;
  --ptm-transition-normal: 0.25s ease;
  --ptm-transition-slow: 0.35s ease;
  
  /* 层级 */
  --ptm-z-dropdown: 1000;
  --ptm-z-modal: 2000;
  --ptm-z-tooltip: 3000;
}
```

## 注释和文档规范

### 1. JSDoc 注释

```typescript
/**
 * 管理项目的核心服务类
 * 提供项目的 CRUD 操作和业务逻辑处理
 * 
 * @example
 * ```typescript
 * const projectManager = new ProjectManager(app, repository);
 * const project = await projectManager.createProject({
 *   name: "新项目",
 *   description: "项目描述"
 * });
 * ```
 */
export class ProjectManager {
  /**
   * 创建新项目
   * 
   * @param data - 项目创建数据
   * @param options - 创建选项
   * @returns 创建的项目对象
   * 
   * @throws {ValidationError} 当项目数据验证失败时
   * @throws {DuplicateError} 当项目名称已存在时
   * 
   * @example
   * ```typescript
   * const project = await projectManager.createProject(
   *   { name: "项目名称", description: "描述" },
   *   { validateName: true }
   * );
   * ```
   */
  async createProject(
    data: CreateProjectData, 
    options: CreateProjectOptions = {}
  ): Promise<Project> {
    // 实现
  }

  /**
   * 根据 ID 查找项目
   * 
   * @param id - 项目 ID
   * @returns 项目对象，如果未找到则返回 null
   */
  async getProject(id: string): Promise<Project | null> {
    // 实现
  }
}

/**
 * 项目创建数据接口
 */
interface CreateProjectData {
  /** 项目名称，必须唯一 */
  name: string;
  
  /** 项目描述，可选 */
  description?: string;
  
  /** 项目开始日期，ISO 8601 格式 */
  startDate?: string;
  
  /** 项目标签列表 */
  tags?: string[];
}
```

### 2. 内联注释

```typescript
export class TaskManager {
  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task> {
    // 验证任务存在性
    const task = await this.taskRepository.findById(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    // 检查状态转换是否合法
    if (!this.isValidStatusTransition(task.status, status)) {
      throw new Error(`Invalid status transition: ${task.status} -> ${status}`);
    }

    // 更新任务状态
    const updatedTask = await this.taskRepository.update(taskId, { 
      status,
      updatedAt: new Date().toISOString()
    });

    // 处理子任务状态联动
    // 当父任务完成时，所有子任务也应该标记为完成
    if (status === 'COMPLETED' && task.subtasks?.length > 0) {
      await this.updateSubTasksStatus(task.subtasks, status);
    }

    // 触发状态变更事件
    this.eventEmitter.emit('task:status-changed', updatedTask, task.status);

    return updatedTask;
  }

  /**
   * 检查状态转换是否合法
   * 
   * 业务规则：
   * - TODO 可以转换为任何状态
   * - COMPLETED 不能转换为其他状态
   * - CANCELLED 不能转换为其他状态
   */
  private isValidStatusTransition(from: TaskStatus, to: TaskStatus): boolean {
    // TODO 可以转换为任何状态
    if (from === 'TODO') return true;
    
    // 完成和取消的任务不能再变更状态
    if (from === 'COMPLETED' || from === 'CANCELLED') return false;
    
    // 其他状态之间可以自由转换
    return true;
  }
}
```

### 3. README 和文档注释

```typescript
/**
 * @fileoverview 项目任务管理器 - 数据模型定义
 * 
 * 这个文件定义了项目管理系统中的核心数据模型，包括：
 * - Project: 项目实体
 * - Task: 任务实体  
 * - Sprint: Sprint 实体
 * - 相关的枚举和类型定义
 * 
 * <AUTHOR> Task Manager Team
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * 任务状态枚举
 * 
 * 定义了任务在生命周期中的各种状态
 * 与 Tasks 插件的 emoji 语法保持兼容
 */
export enum TaskStatus {
  /** 待办：任务已创建但尚未开始 */
  TODO = 'todo',
  
  /** 进行中：任务正在执行 */
  IN_PROGRESS = 'in_progress',
  
  /** 被阻塞：任务因外部因素无法继续 */
  BLOCKED = 'blocked',
  
  /** 审核中：任务已完成，等待审核 */
  REVIEW = 'review',
  
  /** 已完成：任务已成功完成 */
  COMPLETED = 'completed',
  
  /** 已取消：任务被取消，不再执行 */
  CANCELLED = 'cancelled'
}
```

---

**重要原则**:
1. 保持命名的一致性和可预测性
2. 使用描述性的名称，避免缩写和简写
3. 遵循语言和框架的惯例
4. 优先考虑可读性而非简洁性
5. 在团队中保持统一的命名风格
description:
globs:
alwaysApply: false
---
