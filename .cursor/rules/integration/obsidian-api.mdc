# Obsidian API 使用规范

## Obsidian 插件开发基础

### 1. 插件生命周期管理

```typescript
export default class ProjectTaskManagerPlugin extends Plugin {
  settings: ProjectTaskManagerSettings;
  private services: ServiceContainer;

  async onload() {
    console.log('Loading Project Task Manager plugin...');
    
    try {
      // 1. 加载设置
      await this.loadSettings();
      
      // 2. 初始化核心服务
      await this.initializeServices();
      
      // 3. 注册视图和UI组件
      this.registerViews();
      
      // 4. 注册命令
      this.registerCommands();
      
      // 5. 设置事件监听器
      this.setupEventListeners();
      
      // 6. 添加设置面板
      this.addSettingTab(new ProjectTaskManagerSettingTab(this.app, this));
      
      console.log('Project Task Manager plugin loaded successfully');
    } catch (error) {
      console.error('Plugin initialization failed:', error);
      new Notice('插件初始化失败，请检查控制台错误信息');
    }
  }

  async onunload() {
    console.log('Unloading Project Task Manager plugin...');
    
    // 清理资源
    await this.cleanupServices();
    
    // 移除事件监听器
    this.removeEventListeners();
    
    // 保存设置
    await this.saveSettings();
    
    console.log('Project Task Manager plugin unloaded');
  }

  private async initializeServices(): Promise<void> {
    this.services = new ServiceContainer(this.app, this.settings);
    await this.services.initialize();
  }

  private async cleanupServices(): Promise<void> {
    if (this.services) {
      await this.services.cleanup();
    }
  }
}
```

### 2. 设置管理规范

```typescript
interface ProjectTaskManagerSettings {
  // 基础设置
  enableProjectMode: boolean;
  enableIndependentTaskMode: boolean;
  defaultView: ViewType;
  
  // Tasks 插件集成
  enableTasksPluginSync: boolean;
  tasksPluginCompatibility: boolean;
  
  // 性能设置
  enableCaching: boolean;
  maxCacheSize: number;
  enableVirtualScrolling: boolean;
  
  // UI 设置
  showStatusBar: boolean;
  showRibbonIcon: boolean;
  compactMode: boolean;
  
  // 自定义设置
  customFields: Record<string, any>;
}

const DEFAULT_SETTINGS: ProjectTaskManagerSettings = {
  enableProjectMode: true,
  enableIndependentTaskMode: true,
  defaultView: 'dashboard',
  enableTasksPluginSync: true,
  tasksPluginCompatibility: true,
  enableCaching: true,
  maxCacheSize: 1000,
  enableVirtualScrolling: true,
  showStatusBar: true,
  showRibbonIcon: true,
  compactMode: false,
  customFields: {}
};

// 设置加载和保存
class SettingsManager {
  constructor(private plugin: Plugin) {}

  async loadSettings(): Promise<ProjectTaskManagerSettings> {
    const loadedSettings = await this.plugin.loadData();
    return { ...DEFAULT_SETTINGS, ...loadedSettings };
  }

  async saveSettings(settings: ProjectTaskManagerSettings): Promise<void> {
    await this.plugin.saveData(settings);
  }

  validateSettings(settings: any): boolean {
    // 验证设置的有效性
    return true;
  }
}
```

## 文件系统操作规范

### 1. 文件操作

```typescript
export class ObsidianFileManager {
  constructor(private app: App) {}

  // 创建文件
  async createFile(path: string, content: string): Promise<TFile> {
    try {
      return await this.app.vault.create(path, content);
    } catch (error) {
      if (error.message.includes('already exists')) {
        throw new Error(`文件已存在: ${path}`);
      }
      throw new Error(`创建文件失败: ${error.message}`);
    }
  }

  // 读取文件
  async readFile(file: TFile): Promise<string> {
    try {
      return await this.app.vault.read(file);
    } catch (error) {
      throw new Error(`读取文件失败: ${error.message}`);
    }
  }

  // 修改文件
  async modifyFile(file: TFile, content: string): Promise<void> {
    try {
      await this.app.vault.modify(file, content);
    } catch (error) {
      throw new Error(`修改文件失败: ${error.message}`);
    }
  }

  // 删除文件
  async deleteFile(file: TFile): Promise<void> {
    try {
      await this.app.vault.delete(file);
    } catch (error) {
      throw new Error(`删除文件失败: ${error.message}`);
    }
  }

  // 获取文件
  getFileByPath(path: string): TFile | null {
    return this.app.vault.getAbstractFileByPath(path) as TFile;
  }

  // 确保目录存在
  async ensureDirectoryExists(dirPath: string): Promise<void> {
    const normalizedPath = this.normalizePath(dirPath);
    const dir = this.app.vault.getAbstractFileByPath(normalizedPath);
    
    if (!dir) {
      await this.app.vault.createFolder(normalizedPath);
    }
  }

  // 路径规范化
  private normalizePath(path: string): string {
    return this.app.vault.adapter.path.normalize(path);
  }
}
```

### 2. 文件监听

```typescript
export class FileWatcher {
  private listeners: Map<string, (file: TFile) => void> = new Map();

  constructor(private app: App) {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // 文件创建
    this.app.vault.on('create', (file) => {
      if (file instanceof TFile) {
        this.handleFileCreate(file);
      }
    });

    // 文件修改
    this.app.vault.on('modify', (file) => {
      if (file instanceof TFile) {
        this.handleFileModify(file);
      }
    });

    // 文件删除
    this.app.vault.on('delete', (file) => {
      if (file instanceof TFile) {
        this.handleFileDelete(file);
      }
    });

    // 文件重命名
    this.app.vault.on('rename', (file, oldPath) => {
      if (file instanceof TFile) {
        this.handleFileRename(file, oldPath);
      }
    });
  }

  private handleFileCreate(file: TFile): void {
    if (this.shouldWatchFile(file)) {
      console.log('File created:', file.path);
      this.notifyListeners('create', file);
    }
  }

  private handleFileModify(file: TFile): void {
    if (this.shouldWatchFile(file)) {
      console.log('File modified:', file.path);
      this.notifyListeners('modify', file);
    }
  }

  private handleFileDelete(file: TFile): void {
    if (this.shouldWatchFile(file)) {
      console.log('File deleted:', file.path);
      this.notifyListeners('delete', file);
    }
  }

  private handleFileRename(file: TFile, oldPath: string): void {
    if (this.shouldWatchFile(file)) {
      console.log('File renamed:', oldPath, '->', file.path);
      this.notifyListeners('rename', file);
    }
  }

  private shouldWatchFile(file: TFile): boolean {
    // 监听 .ptm 文件和 markdown 文件
    return file.extension === 'ptm' || file.extension === 'md';
  }

  private notifyListeners(event: string, file: TFile): void {
    const listener = this.listeners.get(event);
    if (listener) {
      listener(file);
    }
  }

  // 注册监听器
  addListener(event: string, callback: (file: TFile) => void): void {
    this.listeners.set(event, callback);
  }

  // 移除监听器
  removeListener(event: string): void {
    this.listeners.delete(event);
  }
}
```

## 视图管理规范

### 1. 自定义视图

```typescript
export class ProjectDashboardView extends ItemView {
  private reactComponent: ReactRenderer;

  getViewType(): string {
    return 'project-dashboard';
  }

  getDisplayText(): string {
    return '项目仪表板';
  }

  getIcon(): string {
    return 'dashboard';
  }

  async onOpen(): Promise<void> {
    const container = this.containerEl.children[1];
    container.empty();
    
    // 创建 React 渲染器
    this.reactComponent = new ReactRenderer(
      container as HTMLElement,
      ProjectDashboard,
      {
        app: this.app,
        ptmManager: this.getPlugin().ptmManager
      }
    );

    this.reactComponent.render();
  }

  async onClose(): Promise<void> {
    if (this.reactComponent) {
      this.reactComponent.unmount();
    }
  }

  private getPlugin(): ProjectTaskManagerPlugin {
    return this.app.plugins.plugins['obsidian-project-task-manager'];
  }
}

// 视图注册
export class ViewManager {
  constructor(private plugin: ProjectTaskManagerPlugin) {}

  registerViews(): void {
    // 注册项目仪表板视图
    this.plugin.registerView(
      'project-dashboard',
      (leaf) => new ProjectDashboardView(leaf)
    );

    // 注册任务列表视图
    this.plugin.registerView(
      'task-list',
      (leaf) => new TaskListView(leaf)
    );

    // 注册看板视图
    this.plugin.registerView(
      'kanban-board',
      (leaf) => new KanbanBoardView(leaf)
    );
  }

  async openView(viewType: string, mode: 'tab' | 'split' = 'tab'): Promise<void> {
    const workspace = this.plugin.app.workspace;
    
    let leaf: WorkspaceLeaf;
    
    if (mode === 'split') {
      leaf = workspace.splitActiveLeaf();
    } else {
      // 检查是否已有相同类型的视图
      const existingLeaf = workspace.getLeavesOfType(viewType)[0];
      if (existingLeaf) {
        workspace.revealLeaf(existingLeaf);
        return;
      }
      leaf = workspace.getUnpinnedLeaf();
    }

    await leaf.setViewState({ type: viewType });
    workspace.revealLeaf(leaf);
  }
}
```

### 2. React 组件集成

```typescript
export class ReactRenderer {
  private root: Root | null = null;

  constructor(
    private container: HTMLElement,
    private component: React.ComponentType<any>,
    private props: any = {}
  ) {}

  render(): void {
    if (!this.root) {
      this.root = createRoot(this.container);
    }

    const WrappedComponent = () => {
      const [theme, setTheme] = useState<'light' | 'dark'>('light');

      useEffect(() => {
        const themeManager = new ThemeManager(this.props.app);
        setTheme(themeManager.getCurrentTheme());

        const cleanup = themeManager.onThemeChange(setTheme);
        return cleanup;
      }, []);

      return React.createElement(this.component, {
        ...this.props,
        theme
      });
    };

    this.root.render(React.createElement(WrappedComponent));
  }

  unmount(): void {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
  }

  updateProps(newProps: any): void {
    this.props = { ...this.props, ...newProps };
    this.render();
  }
}
```

## 命令系统规范

### 1. 命令注册

```typescript
export class CommandManager {
  constructor(private plugin: ProjectTaskManagerPlugin) {}

  registerCommands(): void {
    // 项目管理命令
    this.registerProjectCommands();
    
    // 任务管理命令
    this.registerTaskCommands();
    
    // 视图切换命令
    this.registerViewCommands();
    
    // PTM 文件操作命令
    this.registerPTMCommands();
  }

  private registerProjectCommands(): void {
    this.plugin.addCommand({
      id: 'open-project-dashboard',
      name: '打开项目仪表板',
      icon: 'dashboard',
      callback: () => {
        this.plugin.viewManager.openView('project-dashboard');
      }
    });

    this.plugin.addCommand({
      id: 'create-new-project',
      name: '创建新项目',
      icon: 'plus',
      callback: () => {
        this.openProjectCreationModal();
      }
    });
  }

  private registerTaskCommands(): void {
    this.plugin.addCommand({
      id: 'create-task-from-selection',
      name: '从选中文本创建任务',
      editorCallback: (editor: Editor, ctx: MarkdownView | MarkdownFileInfo) => {
        if (ctx instanceof MarkdownView) {
          this.createTaskFromSelection(editor, ctx);
        }
      }
    });

    this.plugin.addCommand({
      id: 'open-task-list',
      name: '打开任务列表',
      callback: () => {
        this.plugin.viewManager.openView('task-list');
      }
    });
  }

  private async createTaskFromSelection(editor: Editor, view: MarkdownView): Promise<void> {
    const selection = editor.getSelection();
    if (!selection) {
      new Notice('请先选择要转换为任务的文本');
      return;
    }

    try {
      const task = await this.plugin.services.taskManager.createTaskFromText(
        selection,
        view.file?.path
      );
      
      new Notice(`任务已创建: ${task.title}`);
    } catch (error) {
      new Notice(`创建任务失败: ${error.message}`);
    }
  }
}
```

### 2. 模态对话框

```typescript
export class ProjectCreationModal extends Modal {
  private form: ProjectForm;

  constructor(app: App, private onSubmit: (project: Partial<Project>) => void) {
    super(app);
  }

  onOpen(): void {
    const { contentEl } = this;
    contentEl.empty();

    contentEl.createEl('h2', { text: '创建新项目' });

    // 创建表单容器
    const formContainer = contentEl.createEl('div', { cls: 'ptm-form-container' });
    
    // 渲染 React 表单组件
    this.form = new ReactRenderer(
      formContainer,
      ProjectForm,
      {
        onSubmit: (data: Partial<Project>) => {
          this.onSubmit(data);
          this.close();
        },
        onCancel: () => {
          this.close();
        }
      }
    );

    this.form.render();
  }

  onClose(): void {
    if (this.form) {
      this.form.unmount();
    }
  }
}

// 使用示例
const openProjectCreationModal = () => {
  new ProjectCreationModal(
    this.app,
    async (projectData) => {
      try {
        const project = await this.ptmManager.createProject(projectData);
        new Notice(`项目已创建: ${project.name}`);
      } catch (error) {
        new Notice(`创建项目失败: ${error.message}`);
      }
    }
  ).open();
};
```

## 事件系统规范

### 1. 自定义事件

```typescript
export class PTMEventManager {
  private events: Component = new Component();

  // 项目事件
  onProjectCreated(callback: (project: Project) => void): () => void {
    return this.addEventListener('project:created', callback);
  }

  onProjectUpdated(callback: (project: Project) => void): () => void {
    return this.addEventListener('project:updated', callback);
  }

  onProjectDeleted(callback: (projectId: string) => void): () => void {
    return this.addEventListener('project:deleted', callback);
  }

  // 任务事件
  onTaskCreated(callback: (task: Task) => void): () => void {
    return this.addEventListener('task:created', callback);
  }

  onTaskUpdated(callback: (task: Task) => void): () => void {
    return this.addEventListener('task:updated', callback);
  }

  onTaskStatusChanged(callback: (task: Task, oldStatus: TaskStatus) => void): () => void {
    return this.addEventListener('task:status-changed', callback);
  }

  // 触发事件
  emitProjectCreated(project: Project): void {
    this.events.trigger('project:created', project);
  }

  emitTaskStatusChanged(task: Task, oldStatus: TaskStatus): void {
    this.events.trigger('task:status-changed', task, oldStatus);
  }

  private addEventListener(event: string, callback: (...args: any[]) => void): () => void {
    this.events.on(event, callback);
    return () => this.events.off(event, callback);
  }

  cleanup(): void {
    this.events.unload();
  }
}
```

### 2. Obsidian 事件监听

```typescript
export class ObsidianEventManager {
  private registeredEvents: EventRef[] = [];

  constructor(private app: App) {}

  setupEventListeners(): void {
    // 工作区事件
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', (leaf) => {
        this.handleActiveLeafChange(leaf);
      })
    );

    // 文件事件
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file instanceof TFile && file.extension === 'md') {
          this.handleMarkdownFileModify(file);
        }
      })
    );

    // 元数据缓存事件
    this.registerEvent(
      this.app.metadataCache.on('resolved', () => {
        this.handleMetadataResolved();
      })
    );
  }

  private registerEvent(eventRef: EventRef): void {
    this.registeredEvents.push(eventRef);
  }

  private handleActiveLeafChange(leaf: WorkspaceLeaf | null): void {
    // 处理活动页面变更
  }

  private async handleMarkdownFileModify(file: TFile): Promise<void> {
    // 检查是否包含任务语法
    const content = await this.app.vault.read(file);
    if (this.containsTaskSyntax(content)) {
      // 通知 Tasks 插件桥接器
      this.app.trigger('ptm:markdown-tasks-changed', file, content);
    }
  }

  private containsTaskSyntax(content: string): boolean {
    return /^[\s]*-\s*\[[ x\/\-\>\<\?]\]/m.test(content);
  }

  private handleMetadataResolved(): void {
    // 元数据缓存解析完成
  }

  cleanup(): void {
    this.registeredEvents.forEach(eventRef => {
      this.app.vault.offref(eventRef);
    });
    this.registeredEvents = [];
  }
}
```

## 状态栏和功能区集成

### 1. 状态栏管理

```typescript
export class StatusBarManager {
  private statusBarItem: HTMLElement | null = null;

  constructor(private plugin: ProjectTaskManagerPlugin) {}

  addStatusBar(): void {
    if (this.plugin.settings.showStatusBar) {
      this.statusBarItem = this.plugin.addStatusBarItem();
      this.updateStatusBar();
    }
  }

  updateStatusBar(): void {
    if (!this.statusBarItem) return;

    // 显示当前项目信息
    const currentProject = this.getCurrentProject();
    if (currentProject) {
      this.statusBarItem.setText(`PTM: ${currentProject.name}`);
      this.statusBarItem.title = `当前项目: ${currentProject.name}`;
    } else {
      this.statusBarItem.setText('PTM: 无项目');
      this.statusBarItem.title = 'Project Task Manager - 未选择项目';
    }
  }

  removeStatusBar(): void {
    if (this.statusBarItem) {
      this.statusBarItem.remove();
      this.statusBarItem = null;
    }
  }

  private getCurrentProject(): Project | null {
    // 获取当前活动项目
    return null;
  }
}
```

### 2. 功能区图标

```typescript
export class RibbonManager {
  private ribbonIcon: HTMLElement | null = null;

  constructor(private plugin: ProjectTaskManagerPlugin) {}

  addRibbonIcon(): void {
    if (this.plugin.settings.showRibbonIcon) {
      this.ribbonIcon = this.plugin.addRibbonIcon(
        'project-diagram',
        'Project Task Manager',
        (evt: MouseEvent) => {
          this.handleRibbonClick(evt);
        }
      );
      
      this.ribbonIcon.addClass('ptm-ribbon-icon');
    }
  }

  private handleRibbonClick(evt: MouseEvent): void {
    if (evt.button === 0) { // 左键点击
      this.plugin.viewManager.openView('project-dashboard');
    } else if (evt.button === 2) { // 右键点击
      this.showRibbonMenu(evt);
    }
  }

  private showRibbonMenu(evt: MouseEvent): void {
    const menu = new Menu();

    menu.addItem((item) =>
      item
        .setTitle('打开项目仪表板')
        .setIcon('dashboard')
        .onClick(() => {
          this.plugin.viewManager.openView('project-dashboard');
        })
    );

    menu.addItem((item) =>
      item
        .setTitle('打开任务列表')
        .setIcon('list')
        .onClick(() => {
          this.plugin.viewManager.openView('task-list');
        })
    );

    menu.addSeparator();

    menu.addItem((item) =>
      item
        .setTitle('创建新项目')
        .setIcon('plus')
        .onClick(() => {
          this.plugin.commandManager.openProjectCreationModal();
        })
    );

    menu.showAtMouseEvent(evt);
  }

  removeRibbonIcon(): void {
    if (this.ribbonIcon) {
      this.ribbonIcon.remove();
      this.ribbonIcon = null;
    }
  }
}
```

---

**重要原则**:
1. 所有 API 调用必须包含错误处理
2. 正确管理组件生命周期
3. 使用 TypeScript 类型安全
4. 遵循 Obsidian 插件开发最佳实践
5. 实现适当的资源清理机制
description:
globs:
alwaysApply: false
---
