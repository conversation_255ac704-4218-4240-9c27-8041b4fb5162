# Tasks 插件兼容性规范

## Tasks 插件集成概述

确保与 Obsidian Tasks 插件的完全兼容性，实现双向同步，支持现有 Tasks 插件用户无缝迁移。

## Emoji 语法映射

### 1. 状态映射表

```typescript
// Tasks 插件 emoji 到 PTM 状态的映射
export const TASKS_EMOJI_MAPPING: Record<string, TaskStatus> = {
  '[ ]': 'TODO',          // 待办
  '[x]': 'COMPLETED',     // 已完成
  '[X]': 'COMPLETED',     // 已完成（大写）
  '[/]': 'IN_PROGRESS',   // 进行中
  '[>]': 'IN_PROGRESS',   // 转发/进行中
  '[-]': 'CANCELLED',     // 已取消
  '[<]': 'BLOCKED',       // 被阻塞
  '[?]': 'REVIEW',        // 审核中
  '[!]': 'HIGH_PRIORITY', // 高优先级（扩展）
  '[i]': 'INFO',          // 信息（扩展）
  '[b]': 'BOOKMARK',      // 书签（扩展）
  '[p]': 'PROS',          // 优点（扩展）
  '[c]': 'CONS',          // 缺点（扩展）
  '[f]': 'FIRE',          // 紧急（扩展）
  '[k]': 'KEY',           // 关键（扩展）
  '[w]': 'WIN',           // 胜利（扩展）
  '[u]': 'UP',            // 向上（扩展）
  '[d]': 'DOWN'           // 向下（扩展）
};

// PTM 状态到 Tasks 插件 emoji 的反向映射
export const PTM_STATUS_TO_EMOJI: Record<TaskStatus, string> = {
  'TODO': '[ ]',
  'IN_PROGRESS': '[/]',
  'BLOCKED': '[<]',
  'REVIEW': '[?]',
  'COMPLETED': '[x]',
  'CANCELLED': '[-]'
};
```

### 2. 优先级映射

```typescript
// Tasks 插件优先级 emoji 映射
export const PRIORITY_EMOJI_MAPPING: Record<string, Priority> = {
  '⏫': 'CRITICAL',    // 最高优先级
  '🔼': 'HIGH',        // 高优先级
  '🔽': 'LOW',         // 低优先级
  '🔥': 'CRITICAL',    // 紧急
  '⭐': 'HIGH',        // 重要
  '❗': 'HIGH',        // 感叹号
  '❓': 'MEDIUM'       // 问号
};

export const PTM_PRIORITY_TO_EMOJI: Record<Priority, string> = {
  'LOW': '🔽',
  'MEDIUM': '',        // 无 emoji 表示中等优先级
  'HIGH': '🔼',
  'CRITICAL': '⏫'
};
```

## 任务解析器

### 1. Tasks 语法解析

```typescript
export class TasksPluginParser {
  // 任务行的正则表达式
  private static readonly TASK_REGEX = /^(\s*)-\s*(\[(.)\])\s*(.*)$/;
  
  // 日期相关的正则表达式
  private static readonly DATE_PATTERNS = {
    due: /📅\s*(\d{4}-\d{2}-\d{2})/,
    scheduled: /🗓️\s*(\d{4}-\d{2}-\d{2})/,
    start: /🛫\s*(\d{4}-\d{2}-\d{2})/,
    completion: /✅\s*(\d{4}-\d{2}-\d{2})/,
    created: /➕\s*(\d{4}-\d{2}-\d{2})/
  };

  // 优先级正则表达式
  private static readonly PRIORITY_REGEX = /[⏫🔼🔽🔥⭐❗❓]/;

  // 标签正则表达式
  private static readonly TAG_REGEX = /#[\w\-\/]+/g;

  // 解析任务行
  static parseTaskLine(line: string, filePath: string, lineNumber: number): ParsedTask | null {
    const match = line.match(this.TASK_REGEX);
    if (!match) {
      return null;
    }

    const [fullMatch, indent, checkbox, statusChar, content] = match;
    
    const task: ParsedTask = {
      originalText: line,
      filePath,
      lineNumber,
      indent: indent.length,
      status: this.parseStatus(statusChar),
      title: this.extractTitle(content),
      description: this.extractDescription(content),
      priority: this.extractPriority(content),
      tags: this.extractTags(content),
      dates: this.extractDates(content),
      hasTasksPluginSyntax: true
    };

    return task;
  }

  private static parseStatus(statusChar: string): TaskStatus {
    const emoji = `[${statusChar}]`;
    return TASKS_EMOJI_MAPPING[emoji] || 'TODO';
  }

  private static extractTitle(content: string): string {
    // 移除所有 Tasks 插件特殊语法，提取纯标题
    let title = content
      .replace(this.PRIORITY_REGEX, '') // 移除优先级 emoji
      .replace(/📅\s*\d{4}-\d{2}-\d{2}/, '') // 移除截止日期
      .replace(/🗓️\s*\d{4}-\d{2}-\d{2}/, '') // 移除计划日期
      .replace(/🛫\s*\d{4}-\d{2}-\d{2}/, '') // 移除开始日期
      .replace(/✅\s*\d{4}-\d{2}-\d{2}/, '') // 移除完成日期
      .replace(/➕\s*\d{4}-\d{2}-\d{2}/, '') // 移除创建日期
      .replace(this.TAG_REGEX, '') // 移除标签
      .trim();

    return title;
  }

  private static extractDescription(content: string): string | undefined {
    // 如果内容包含换行或特殊标记，提取描述
    const lines = content.split('\n');
    if (lines.length > 1) {
      return lines.slice(1).join('\n').trim();
    }
    return undefined;
  }

  private static extractPriority(content: string): Priority {
    const priorityMatch = content.match(this.PRIORITY_REGEX);
    if (priorityMatch) {
      return PRIORITY_EMOJI_MAPPING[priorityMatch[0]] || 'MEDIUM';
    }
    return 'MEDIUM';
  }

  private static extractTags(content: string): string[] {
    const tagMatches = content.match(this.TAG_REGEX);
    return tagMatches ? tagMatches.map(tag => tag.substring(1)) : [];
  }

  private static extractDates(content: string): TaskDates {
    const dates: TaskDates = {};

    for (const [dateType, pattern] of Object.entries(this.DATE_PATTERNS)) {
      const match = content.match(pattern);
      if (match) {
        dates[dateType as keyof TaskDates] = match[1];
      }
    }

    return dates;
  }
}

interface ParsedTask {
  originalText: string;
  filePath: string;
  lineNumber: number;
  indent: number;
  status: TaskStatus;
  title: string;
  description?: string;
  priority: Priority;
  tags: string[];
  dates: TaskDates;
  hasTasksPluginSyntax: boolean;
}

interface TaskDates {
  due?: string;
  scheduled?: string;
  start?: string;
  completion?: string;
  created?: string;
}
```

### 2. 任务格式化器

```typescript
export class TasksPluginFormatter {
  // 将 PTM 任务转换为 Tasks 插件格式
  static formatTaskForTasks(task: Task): string {
    const components: string[] = [];

    // 状态 checkbox
    const emoji = PTM_STATUS_TO_EMOJI[task.status] || '[ ]';
    components.push(`- ${emoji}`);

    // 优先级 emoji
    if (task.priority && task.priority !== 'MEDIUM') {
      const priorityEmoji = PTM_PRIORITY_TO_EMOJI[task.priority];
      if (priorityEmoji) {
        components.push(priorityEmoji);
      }
    }

    // 任务标题
    components.push(task.title);

    // 标签
    if (task.tags && task.tags.length > 0) {
      const tagStr = task.tags.map(tag => `#${tag}`).join(' ');
      components.push(tagStr);
    }

    // 日期信息
    if (task.dueDate) {
      components.push(`📅 ${this.formatDate(task.dueDate)}`);
    }

    if (task.startDate) {
      components.push(`🛫 ${this.formatDate(task.startDate)}`);
    }

    if (task.status === 'COMPLETED' && task.updatedAt) {
      components.push(`✅ ${this.formatDate(task.updatedAt)}`);
    }

    return components.join(' ');
  }

  // 更新现有任务行
  static updateTaskLine(
    originalLine: string, 
    task: Task, 
    preserveIndentation: boolean = true
  ): string {
    const match = originalLine.match(/^(\s*)-\s*(\[(.)\])\s*(.*)$/);
    if (!match) {
      return this.formatTaskForTasks(task);
    }

    const [, indent, , , content] = match;
    const newTaskLine = this.formatTaskForTasks(task);
    
    if (preserveIndentation) {
      // 保持原有缩进
      return `${indent}${newTaskLine.substring(1)}`;
    }

    return newTaskLine;
  }

  private static formatDate(dateString: string): string {
    // 确保日期格式为 YYYY-MM-DD
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }
}
```

## 双向同步机制

### 1. 同步协调器

```typescript
export class TasksPluginBridge {
  private syncInProgress = false;
  private lastSyncTimestamp = 0;
  private fileWatcher: FileWatcher;
  private syncInterval: number;

  constructor(
    private app: App,
    private taskRepository: TaskRepository,
    private taskManager: TaskManager,
    private options: TasksPluginBridgeOptions = {}
  ) {
    this.syncInterval = options.syncInterval || 5000; // 5秒同步间隔
    this.fileWatcher = new FileWatcher(app);
    this.setupEventListeners();
  }

  async initialize(): Promise<void> {
    if (this.options.enableSync) {
      await this.performInitialSync();
      this.startPeriodicSync();
    }
  }

  private setupEventListeners(): void {
    // 监听文件修改
    this.fileWatcher.addListener('modify', (file) => {
      this.handleFileModification(file);
    });

    // 监听 PTM 任务变更
    this.app.on('ptm:task-updated', (task: Task) => {
      this.handlePTMTaskUpdate(task);
    });

    // 监听 PTM 任务创建
    this.app.on('ptm:task-created', (task: Task) => {
      this.handlePTMTaskCreation(task);
    });
  }

  private async performInitialSync(): Promise<void> {
    console.log('开始初始同步...');
    
    try {
      // 扫描所有 markdown 文件中的任务
      const markdownFiles = this.app.vault.getMarkdownFiles();
      
      for (const file of markdownFiles) {
        await this.syncFileTasksToPTM(file);
      }

      console.log('初始同步完成');
    } catch (error) {
      console.error('初始同步失败:', error);
    }
  }

  private async syncFileTasksToPTM(file: TFile): Promise<void> {
    if (this.syncInProgress) return;

    try {
      this.syncInProgress = true;

      const content = await this.app.vault.read(file);
      const lines = content.split('\n');
      const tasksInFile: ParsedTask[] = [];

      // 解析文件中的所有任务
      for (let i = 0; i < lines.length; i++) {
        const parsedTask = TasksPluginParser.parseTaskLine(lines[i], file.path, i + 1);
        if (parsedTask) {
          tasksInFile.push(parsedTask);
        }
      }

      // 同步到 PTM
      for (const parsedTask of tasksInFile) {
        await this.syncParsedTaskToPTM(parsedTask);
      }

    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncParsedTaskToPTM(parsedTask: ParsedTask): Promise<void> {
    // 检查任务是否应该被 PTM 管理（包含 #task 标签）
    if (!this.shouldManageTask(parsedTask)) {
      return;
    }

    // 查找现有任务
    const existingTask = await this.findExistingTask(parsedTask);

    if (existingTask) {
      // 更新现有任务
      await this.updateExistingTask(existingTask, parsedTask);
    } else {
      // 创建新任务
      await this.createNewTaskFromParsed(parsedTask);
    }
  }

  private shouldManageTask(parsedTask: ParsedTask): boolean {
    // 检查是否包含 #task 标签或在项目文件中
    return parsedTask.tags.includes('task') || 
           this.isInProjectFile(parsedTask.filePath);
  }

  private isInProjectFile(filePath: string): boolean {
    // 检查文件是否在项目目录中或包含项目相关标记
    return filePath.includes('projects/') || 
           filePath.includes('project-') ||
           filePath.endsWith('.ptm');
  }

  private async findExistingTask(parsedTask: ParsedTask): Promise<Task | null> {
    // 通过文件路径和行号查找现有任务
    const tasks = await this.taskRepository.findAll();
    
    return tasks.find(task => 
      task.tasksPluginData?.filePath === parsedTask.filePath &&
      task.tasksPluginData?.lineNumber === parsedTask.lineNumber
    ) || null;
  }

  private async updateExistingTask(existingTask: Task, parsedTask: ParsedTask): Promise<void> {
    // 检查是否需要更新
    if (this.isTaskSyncRequired(existingTask, parsedTask)) {
      const updates: Partial<Task> = {
        title: parsedTask.title,
        status: parsedTask.status,
        priority: parsedTask.priority,
        tags: [...new Set([...existingTask.tags, ...parsedTask.tags])],
        tasksPluginData: {
          originalText: parsedTask.originalText,
          filePath: parsedTask.filePath,
          lineNumber: parsedTask.lineNumber,
          emoji: PTM_STATUS_TO_EMOJI[parsedTask.status]
        }
      };

      // 更新日期信息
      if (parsedTask.dates.due) {
        updates.dueDate = parsedTask.dates.due;
      }
      if (parsedTask.dates.start) {
        updates.startDate = parsedTask.dates.start;
      }

      await this.taskRepository.update(existingTask.id, updates);
    }
  }

  private async createNewTaskFromParsed(parsedTask: ParsedTask): Promise<void> {
    // 尝试找到合适的项目
    const projectId = await this.findOrCreateProjectForTask(parsedTask);

    const newTask: Omit<Task, 'id'> = {
      title: parsedTask.title,
      description: parsedTask.description,
      status: parsedTask.status,
      priority: parsedTask.priority,
      projectId,
      tags: parsedTask.tags,
      dueDate: parsedTask.dates.due,
      startDate: parsedTask.dates.start,
      tasksPluginData: {
        originalText: parsedTask.originalText,
        filePath: parsedTask.filePath,
        lineNumber: parsedTask.lineNumber,
        emoji: PTM_STATUS_TO_EMOJI[parsedTask.status]
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await this.taskRepository.create(newTask);
  }

  private async findOrCreateProjectForTask(parsedTask: ParsedTask): Promise<string> {
    // 从文件路径或标签推断项目
    const projectName = this.inferProjectName(parsedTask);
    
    // 查找现有项目
    const projectRepo = new ProjectRepository(this.taskRepository.dataManager);
    let project = await projectRepo.findByName(projectName);

    if (!project) {
      // 创建新项目
      project = await projectRepo.create({
        name: projectName,
        description: `从 ${parsedTask.filePath} 自动创建`,
        status: 'ACTIVE' as ProjectStatus,
        priority: 'MEDIUM' as Priority,
        tags: ['auto-generated'],
        settings: {},
        progress: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    return project.id;
  }

  private inferProjectName(parsedTask: ParsedTask): string {
    // 从文件路径推断项目名称
    const pathParts = parsedTask.filePath.split('/');
    const fileName = pathParts[pathParts.length - 1];
    
    // 项目标签
    const projectTags = parsedTask.tags.filter(tag => tag.startsWith('project-'));
    if (projectTags.length > 0) {
      return projectTags[0].replace('project-', '');
    }

    // 文件名（去除扩展名）
    return fileName.replace(/\.(md|txt)$/, '') || 'Inbox';
  }

  private isTaskSyncRequired(existingTask: Task, parsedTask: ParsedTask): boolean {
    // 比较关键字段判断是否需要同步
    return (
      existingTask.title !== parsedTask.title ||
      existingTask.status !== parsedTask.status ||
      existingTask.priority !== parsedTask.priority ||
      existingTask.tasksPluginData?.originalText !== parsedTask.originalText
    );
  }

  // PTM 到 Tasks 插件的同步
  private async handlePTMTaskUpdate(task: Task): Promise<void> {
    if (!task.tasksPluginData || this.syncInProgress) {
      return;
    }

    try {
      const file = this.app.vault.getAbstractFileByPath(task.tasksPluginData.filePath) as TFile;
      if (!file) {
        console.warn(`文件不存在: ${task.tasksPluginData.filePath}`);
        return;
      }

      await this.updateTaskInFile(file, task);
    } catch (error) {
      console.error('更新文件中的任务失败:', error);
    }
  }

  private async updateTaskInFile(file: TFile, task: Task): Promise<void> {
    const content = await this.app.vault.read(file);
    const lines = content.split('\n');
    
    if (task.tasksPluginData && task.tasksPluginData.lineNumber <= lines.length) {
      const lineIndex = task.tasksPluginData.lineNumber - 1;
      const newTaskLine = TasksPluginFormatter.updateTaskLine(lines[lineIndex], task);
      
      lines[lineIndex] = newTaskLine;
      
      const newContent = lines.join('\n');
      await this.app.vault.modify(file, newContent);
      
      // 更新任务的 Tasks 插件数据
      await this.taskRepository.update(task.id, {
        tasksPluginData: {
          ...task.tasksPluginData,
          originalText: newTaskLine
        }
      });
    }
  }

  private startPeriodicSync(): void {
    setInterval(() => {
      if (!this.syncInProgress) {
        this.performIncrementalSync();
      }
    }, this.syncInterval);
  }

  private async performIncrementalSync(): Promise<void> {
    // 增量同步：只同步自上次同步以来修改的文件
    const now = Date.now();
    const modifiedFiles = this.getModifiedFilesSince(this.lastSyncTimestamp);
    
    for (const file of modifiedFiles) {
      await this.syncFileTasksToPTM(file);
    }
    
    this.lastSyncTimestamp = now;
  }

  private getModifiedFilesSince(timestamp: number): TFile[] {
    return this.app.vault.getMarkdownFiles().filter(file => {
      return file.stat.mtime > timestamp;
    });
  }

  async cleanup(): Promise<void> {
    // 清理资源
    this.fileWatcher.removeListener('modify');
  }
}

interface TasksPluginBridgeOptions {
  enableSync?: boolean;
  syncInterval?: number;
  preserveTasksPluginFormat?: boolean;
  autoDetectTasks?: boolean;
  defaultProjectName?: string;
}
```

## 冲突解决机制

### 1. 冲突检测

```typescript
export class ConflictResolver {
  static detectConflicts(ptmTask: Task, parsedTask: ParsedTask): TaskConflict[] {
    const conflicts: TaskConflict[] = [];

    // 标题冲突
    if (ptmTask.title !== parsedTask.title) {
      conflicts.push({
        type: 'title',
        ptmValue: ptmTask.title,
        tasksValue: parsedTask.title,
        lastModified: {
          ptm: ptmTask.updatedAt,
          tasks: Date.now() // 文件修改时间
        }
      });
    }

    // 状态冲突
    if (ptmTask.status !== parsedTask.status) {
      conflicts.push({
        type: 'status',
        ptmValue: ptmTask.status,
        tasksValue: parsedTask.status,
        lastModified: {
          ptm: ptmTask.updatedAt,
          tasks: Date.now()
        }
      });
    }

    return conflicts;
  }

  static async resolveConflicts(
    conflicts: TaskConflict[], 
    strategy: ConflictResolutionStrategy = 'last-modified-wins'
  ): Promise<ConflictResolution[]> {
    const resolutions: ConflictResolution[] = [];

    for (const conflict of conflicts) {
      let resolution: ConflictResolution;

      switch (strategy) {
        case 'last-modified-wins':
          resolution = this.resolveByLastModified(conflict);
          break;
        case 'ptm-wins':
          resolution = this.resolvePTMWins(conflict);
          break;
        case 'tasks-wins':
          resolution = this.resolveTasksWins(conflict);
          break;
        case 'manual':
          resolution = this.requireManualResolution(conflict);
          break;
        default:
          resolution = this.resolveByLastModified(conflict);
      }

      resolutions.push(resolution);
    }

    return resolutions;
  }

  private static resolveByLastModified(conflict: TaskConflict): ConflictResolution {
    const ptmTime = new Date(conflict.lastModified.ptm).getTime();
    const tasksTime = conflict.lastModified.tasks;

    return {
      conflict,
      resolution: ptmTime > tasksTime ? 'use-ptm' : 'use-tasks',
      automatic: true
    };
  }
}

interface TaskConflict {
  type: 'title' | 'status' | 'priority' | 'dates' | 'tags';
  ptmValue: any;
  tasksValue: any;
  lastModified: {
    ptm: string;
    tasks: number;
  };
}

interface ConflictResolution {
  conflict: TaskConflict;
  resolution: 'use-ptm' | 'use-tasks' | 'merge' | 'manual';
  automatic: boolean;
}

type ConflictResolutionStrategy = 
  | 'last-modified-wins' 
  | 'ptm-wins' 
  | 'tasks-wins' 
  | 'manual';
```

### 2. 用户界面集成

```typescript
export class ConflictResolutionModal extends Modal {
  constructor(
    app: App, 
    private conflicts: TaskConflict[],
    private onResolve: (resolutions: ConflictResolution[]) => void
  ) {
    super(app);
  }

  onOpen(): void {
    const { contentEl } = this;
    contentEl.empty();

    contentEl.createEl('h2', { text: '任务同步冲突' });
    contentEl.createEl('p', { 
      text: '检测到 PTM 和 Tasks 插件之间的数据冲突，请选择解决方案：' 
    });

    const resolutionContainer = contentEl.createEl('div', { 
      cls: 'conflict-resolution-container' 
    });

    this.renderConflicts(resolutionContainer);
    this.renderButtons(contentEl);
  }

  private renderConflicts(container: HTMLElement): void {
    for (const [index, conflict] of this.conflicts.entries()) {
      const conflictEl = container.createEl('div', { 
        cls: 'conflict-item' 
      });

      conflictEl.createEl('h3', { text: `冲突 ${index + 1}: ${conflict.type}` });
      
      const ptmOption = conflictEl.createEl('label');
      ptmOption.createEl('input', { 
        type: 'radio', 
        attr: { name: `conflict-${index}`, value: 'ptm' }
      });
      ptmOption.createSpan({ text: `PTM: ${conflict.ptmValue}` });

      const tasksOption = conflictEl.createEl('label');
      tasksOption.createEl('input', { 
        type: 'radio', 
        attr: { name: `conflict-${index}`, value: 'tasks' }
      });
      tasksOption.createSpan({ text: `Tasks: ${conflict.tasksValue}` });
    }
  }

  private renderButtons(container: HTMLElement): void {
    const buttonContainer = container.createEl('div', { 
      cls: 'conflict-resolution-buttons' 
    });

    buttonContainer.createEl('button', { text: '应用解决方案' })
      .addEventListener('click', () => {
        const resolutions = this.collectResolutions();
        this.onResolve(resolutions);
        this.close();
      });

    buttonContainer.createEl('button', { text: '取消' })
      .addEventListener('click', () => {
        this.close();
      });
  }

  private collectResolutions(): ConflictResolution[] {
    // 收集用户选择的解决方案
    return [];
  }
}
```

---

**重要原则**:
1. 保持与 Tasks 插件的完全兼容性
2. 实现可靠的双向同步机制
3. 提供清晰的冲突解决策略
4. 尊重用户的现有工作流程
5. 确保数据一致性和完整性
description:
globs:
alwaysApply: false
---
