{"compilerOptions": {"baseUrl": ".", "inlineSourceMap": true, "inlineSources": true, "module": "ESNext", "target": "ES6", "allowJs": true, "noImplicitAny": false, "moduleResolution": "node", "importHelpers": true, "declaration": true, "outDir": "lib", "typeRoots": ["node_modules/@types"], "lib": ["DOM", "ES6"], "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx"]}