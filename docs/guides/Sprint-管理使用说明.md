# Sprint 管理功能使用说明

## 🏃‍♂️ 如何打开 Sprint 管理界面

### 方法一：使用命令面板
1. 按 `Ctrl+P` (Windows/Linux) 或 `Cmd+P` (Mac) 打开命令面板
2. 输入 "Open Sprint Board" 或 "sprint"
3. 选择 "Open Sprint Board" 命令
4. Sprint 管理面板将在新标签页中打开

### 方法二：使用功能区图标
- 点击左侧功能区的项目管理图标
- 这将打开项目仪表板，从中可以导航到 Sprint 管理

## 📋 Sprint 管理功能特性

### ✅ Sprint 创建和管理
- **创建 Sprint**: 点击"创建 Sprint"按钮，填写名称、目标、开始和结束日期
- **编辑 Sprint**: 点击 Sprint 卡片上的"编辑"按钮
- **删除 Sprint**: 点击"删除"按钮（谨慎操作）
- **Sprint 状态**: 支持计划中、进行中、已完成、已取消四种状态

### 📋 任务分配功能
- **添加任务**: 点击"任务分配"按钮，从可用任务列表中选择任务添加到 Sprint
- **移除任务**: 在任务分配界面中点击"移除"按钮
- **任务搜索**: 使用搜索框快速找到特定任务
- **任务状态**: 显示任务的当前状态（待办、进行中、已完成）

### 📊 燃尽图显示
- **查看燃尽图**: 在活跃的 Sprint 中点击"燃尽图"按钮
- **理想线**: 显示理想的工作完成进度
- **实际线**: 显示实际的工作完成情况
- **数据表格**: 查看每日的详细燃尽数据
- **进度分析**: 显示是否超前或滞后于计划

### 🔄 Sprint 回顾和统计
- **Sprint 统计**: 显示任务完成率、工时统计等关键指标
- **完成 Sprint**: 点击"完成"按钮结束 Sprint 并自动打开回顾界面
- **回顾记录**: 记录做得好的地方、需要改进的地方和行动项
- **团队速度**: 计算和调整团队的开发速度

## 🎯 使用流程建议

### 1. 创建项目和任务
- 首先确保已经创建了项目和相关任务
- 任务应该有明确的描述和预估工时

### 2. 规划 Sprint
- 创建新的 Sprint，设置合理的时间范围（通常 1-4 周）
- 设定明确的 Sprint 目标
- 从待办任务中选择适量的任务加入 Sprint

### 3. 执行 Sprint
- 开始 Sprint（状态变为"进行中"）
- 团队成员更新任务状态
- 定期查看燃尽图了解进度

### 4. 完成 Sprint
- Sprint 结束时点击"完成"按钮
- 填写 Sprint 回顾，总结经验教训
- 记录团队速度用于下次 Sprint 规划

## 🔧 技术信息

### 数据存储
- Sprint 数据存储在插件的数据文件中
- 支持数据备份和恢复
- 与项目和任务数据保持同步

### 集成功能
- 与项目管理功能完全集成
- 支持任务状态同步
- 可与看板视图配合使用

### 快捷键和命令
- `Open Sprint Board`: 打开 Sprint 管理面板
- `Create New Sprint`: 快速创建新 Sprint

## 🐛 故障排除

### 如果 Sprint 界面无法打开
1. 检查插件是否正确安装和启用
2. 查看开发者控制台是否有错误信息
3. 尝试重新加载 Obsidian

### 如果数据不显示
1. 确保已经创建了项目和任务
2. 检查项目选择器是否选择了正确的项目
3. 尝试刷新界面

### 如果燃尽图不准确
1. 确保任务有正确的预估工时
2. 检查任务的完成日期是否正确设置
3. 验证 Sprint 的开始和结束日期

## 📞 获取帮助

如果遇到问题或需要更多功能，请：
1. 查看插件的日志信息
2. 检查 GitHub 仓库的文档
3. 提交 Issue 或功能请求

---

**提示**: Sprint 管理功能是敏捷开发方法的核心工具，建议结合团队的实际情况灵活使用。