# 文档目录说明

本目录用于存放项目相关的文档，按照功能和类型进行分类管理。

## 目录结构

```
docs/
├── README.md           # 本说明文件
├── development/        # 开发相关文档
├── summaries/         # 开发总结和修复记录
├── guides/            # 使用指南和操作手册
└── archive/           # 归档文档
```

## 各目录说明

### development/ - 开发文档
存放开发过程中的技术文档、设计文档、API文档等。

**适用文件类型：**
- 技术设计文档
- API文档
- 架构说明
- 开发演示文档
- 技术调研报告

**命名规范：**
- 使用小写字母和连字符：`api-design.md`
- 包含版本信息：`architecture-v2.md`
- 描述性命名：`component-structure.md`

### summaries/ - 开发总结
存放各种开发过程中的总结文档、问题修复记录、优化记录等。

**适用文件类型：**
- Bug修复总结
- 功能开发总结
- 性能优化记录
- 重构记录
- 问题排查记录

**命名规范：**
- 功能-操作-总结：`gantt-fix-summary.md`
- 包含日期：`performance-optimization-2024-01.md`
- 问题描述：`memory-leak-fix-summary.md`

### guides/ - 使用指南
存放用户使用指南、操作手册、配置说明等面向用户的文档。

**适用文件类型：**
- 用户使用指南
- 功能操作手册
- 配置说明文档
- 快速开始指南
- 常见问题解答

**命名规范：**
- 功能-使用说明：`Sprint-管理使用说明.md`
- 操作手册：`task-management-guide.md`
- 配置指南：`plugin-configuration.md`

### archive/ - 归档文档
存放过时的、不再使用的文档，但可能具有历史参考价值。

**适用文件类型：**
- 废弃的设计文档
- 旧版本的使用指南
- 历史版本的API文档
- 已完成项目的总结

**命名规范：**
- 添加归档日期：`old-api-design-archived-2024-01.md`
- 标明版本：`user-guide-v1-archived.md`

## 文档编写规范

### 1. 文件命名
- 使用中文或英文，保持一致性
- 使用连字符分隔单词
- 避免特殊字符和空格
- 文件名要具有描述性

### 2. 文档结构
每个文档应包含以下基本结构：
```markdown
# 文档标题

## 概述
简要说明文档的目的和内容

## 详细内容
...

## 相关链接
- 相关文档链接
- 参考资料链接

## 更新记录
- 2024-01-01: 创建文档
- 2024-01-15: 更新内容
```

### 3. 内容要求
- 使用中文编写，技术术语可保留英文
- 内容要清晰、准确、完整
- 包含必要的代码示例和截图
- 及时更新过时信息

### 4. 维护原则
- 定期检查文档的时效性
- 及时归档过时文档
- 保持目录结构的整洁
- 遵循统一的命名规范

## 文档管理流程

### 新增文档
1. 确定文档类型和归属目录
2. 按照命名规范创建文件
3. 使用标准文档结构编写内容
4. 在相关文档中添加交叉引用

### 更新文档
1. 在文档末尾记录更新信息
2. 更新相关的交叉引用
3. 检查是否需要通知相关人员

### 归档文档
1. 将过时文档移动到archive目录
2. 在原位置添加重定向说明
3. 更新相关文档的引用链接

## 注意事项

- 所有文档都应该是自包含的，避免过度依赖外部链接
- 重要的技术决策和变更应该有对应的文档记录
- 定期清理和整理文档，保持目录结构清晰
- 文档应该面向目标读者编写，考虑读者的技术背景

---

*最后更新：2024-07-19*
*维护者：项目开发团队*