# 变更日志

本文档记录了 Obsidian Project Task Manager 插件的所有重要变更。

## [未发布] - 2025-07-26

### 移除的功能
- **移除开发演示命令**: 从命令面板中移除了 "Open Development Demo" 命令
  - **影响**: 用户无法再通过命令面板直接打开开发演示页面
  - **替代方案**: 
    - 使用 Ribbon 图标访问项目仪表板
    - 开发演示功能已集成到主要的项目管理界面中
  - **技术细节**: 
    - 移除了 `main.ts` 中的 `open-development-demo` 命令注册
    - `openDevelopmentDemo()` 方法实现已完全移除
    - 开发演示功能已整合到主要的项目管理界面中

### 优化改进
- **简化命令面板**: 减少了命令面板中的开发相关命令，提升用户体验
- **统一访问入口**: 将开发演示功能整合到主要的项目管理工作流中

### 文档更新
- 更新 README.md 中的使用指南和命令列表
- 更新开发文档中的访问方式说明
- 添加变更日志文档

## [开发中] - 2025-07-25

### 新增功能
- ✅ 完成核心UI组件开发
  - StatusIndicator - 状态指示器组件
  - ProgressBar - 进度条组件
  - Avatar - 头像组件
  - SearchBar - 搜索栏组件
  - NavigationItem - 导航项组件
  - NavigationSection - 导航分组组件
  - PageTabs - 页面标签组件

### 技术改进
- ✅ 实现完整的样式隔离系统
- ✅ 建立组件测试框架
- ✅ 创建开发演示插件系统
- ✅ 完善文档组织结构

### 开发工具
- ✅ 样式隔离检查器
- ✅ 自动化测试套件
- ✅ 开发文档系统

## 计划中的功能

### 即将发布
- 🚧 PTM 文件格式完整支持
- 🚧 项目仪表板界面集成
- 🚧 任务管理核心功能
- 🚧 看板视图实现

### 未来版本
- ⏳ 甘特图功能
- ⏳ Sprint 管理
- ⏳ Tasks 插件完整兼容
- ⏳ 高级报表功能

## 技术债务和已知问题

### 当前限制
- 开发演示功能仅通过 Ribbon 图标访问
- 部分UI组件尚未集成到实际业务流程中
- 测试覆盖率需要进一步提升

### 计划修复
- 完善组件集成测试
- 优化样式隔离性能
- 改进错误处理机制

## 迁移指南

### 从开发版本升级

如果您之前使用过开发演示命令，请注意：

1. **命令面板访问**: `Open Development Demo` 命令已移除
2. **新的访问方式**: 使用左侧工具栏的 Ribbon 图标
3. **功能保留**: 所有演示功能仍然可用，只是访问方式有所变化

### 开发者注意事项

- `openDevelopmentDemo()` 方法仍然存在于代码中
- 如需重新启用命令，可以在 `registerCommands()` 方法中添加相应的命令注册
- 相关的视图和组件代码未受影响

---

**注意**: 本插件目前处于积极开发阶段，API 和功能可能会发生变化。建议在非生产环境中进行测试。