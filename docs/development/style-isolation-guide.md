# CSS样式隔离指南

本文档详细说明了如何在Obsidian Project Task Manager插件中实现CSS样式隔离，避免与Obsidian主题产生冲突。

## 为什么需要样式隔离？

1. **避免主题冲突**：Obsidian有丰富的主题生态，我们的插件需要在各种主题下都能正常显示
2. **保持一致性**：确保插件在不同环境下的视觉表现一致
3. **防止样式污染**：避免插件样式影响Obsidian的其他部分
4. **提升用户体验**：用户切换主题时插件功能不受影响

## 样式隔离策略

### 1. 项目前缀系统

所有CSS类名都必须使用 `ptm-` 前缀：

```css
/* ✅ 正确 */
.ptm-button {
  background: var(--ptm-primary);
}

.ptm-card {
  border: 1px solid var(--ptm-border-light);
}

/* ❌ 错误 */
.button {
  background: blue;
}

.card {
  border: 1px solid #ccc;
}
```

### 2. CSS变量隔离

使用 `--ptm-` 前缀的CSS变量：

```css
/* ✅ 正确 */
:root {
  --ptm-primary: #667eea;
  --ptm-text-primary: #1f2937;
  --ptm-bg-secondary: #f7fafc;
}

.ptm-component {
  color: var(--ptm-text-primary);
  background: var(--ptm-bg-secondary);
}

/* ❌ 错误 */
.component {
  color: var(--text-normal); /* Obsidian变量 */
  background: white;
}
```

### 3. 容器隔离

所有组件都必须在 `.ptm-app-container` 内：

```tsx
// ✅ 正确
export const MyComponent: React.FC = () => {
  return (
    <AppContainer>
      <div className="ptm-my-component">
        内容
      </div>
    </AppContainer>
  );
};

// ❌ 错误
export const MyComponent: React.FC = () => {
  return (
    <div className="my-component">
      内容
    </div>
  );
};
```

### 4. 通用元素重置

对可能被Obsidian主题影响的通用元素进行样式重置：

```css
.ptm-app-container h1,
.ptm-app-container h2,
.ptm-app-container h3,
.ptm-app-container h4,
.ptm-app-container h5,
.ptm-app-container h6 {
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: 600 !important;
}

.ptm-app-container button {
  font-family: var(--ptm-font-family) !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ptm-app-container input,
.ptm-app-container textarea,
.ptm-app-container select {
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-base) !important;
  color: var(--ptm-text-primary) !important;
}
```

## 开发工具和最佳实践

### 1. 使用样式工具函数

```tsx
import { cn, classNames, createContainer } from '../utils/styles';

// 单个类名
const className = cn('button');
// 结果: 'ptm-button'

// 多个类名
const className = classNames('button', 'primary', isActive && 'active');
// 结果: 'ptm-button ptm-primary ptm-active'

// 容器类名
const containerClass = createContainer('dashboard');
// 结果: 'ptm-dashboard-container'
```

### 2. 样式隔离检查

使用内置的样式隔离检查工具：

```tsx
import { checkStyleIsolation } from '../utils/styleIsolationChecker';

// 在开发环境中检查元素
const element = document.querySelector('.my-component');
const report = checkStyleIsolation(element);

if (!report.passed) {
  console.warn('样式隔离检查失败:', report.suggestions);
}
```

### 3. 自动化测试

为组件编写样式隔离测试：

```tsx
import { render } from '@testing-library/react';
import { MyComponent } from '../MyComponent';

describe('MyComponent', () => {
  it('应该应用正确的样式隔离', () => {
    const { container } = render(<MyComponent />);
    const element = container.firstChild as HTMLElement;
    
    expect(element).toHaveClass('ptm-my-component');
    expect(element.className).toMatch(/ptm-/);
  });
});
```

## 常见问题和解决方案

### 1. 样式被Obsidian主题覆盖

**问题**：插件样式被Obsidian主题样式覆盖

**解决方案**：
- 使用更具体的选择器
- 适当使用 `!important`
- 确保在容器内应用样式

```css
/* ✅ 正确 */
.ptm-app-container .ptm-button {
  background: var(--ptm-primary) !important;
}

/* ❌ 错误 */
.button {
  background: blue;
}
```

### 2. CSS变量冲突

**问题**：CSS变量名与Obsidian冲突

**解决方案**：
- 始终使用 `--ptm-` 前缀
- 避免使用Obsidian的CSS变量

```css
/* ✅ 正确 */
:root {
  --ptm-text-color: #333;
}

/* ❌ 错误 */
:root {
  --text-normal: #333; /* 可能与Obsidian冲突 */
}
```

### 3. 响应式布局问题

**问题**：在不同屏幕尺寸下样式表现不一致

**解决方案**：
- 使用项目内的响应式断点
- 确保媒体查询也有前缀

```css
/* ✅ 正确 */
@media (max-width: 768px) {
  .ptm-app-container.ptm-responsive-mobile {
    flex-direction: column;
  }
}

/* ❌ 错误 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
}
```

## 样式隔离检查清单

在提交代码前，请确保：

- [ ] 所有CSS类名都有 `ptm-` 前缀
- [ ] 所有CSS变量都有 `--ptm-` 前缀
- [ ] 组件被 `AppContainer` 包裹
- [ ] 通用元素（h1-h6, button, input等）有样式重置
- [ ] 使用了项目的设计令牌系统
- [ ] 编写了样式隔离测试
- [ ] 在不同Obsidian主题下测试过

## 工具和命令

### 样式隔离检查命令

```bash
# 运行样式隔离测试
npm test -- --testNamePattern="样式隔离"

# 检查特定组件
npm test -- src/ui/components/layout/__tests__/AppContainer.test.tsx
```

### 开发时自动检查

在开发环境中，样式隔离检查器会自动运行并在控制台输出警告。

### 构建时验证

构建过程中会验证所有CSS文件是否符合样式隔离规范。

## 参考资源

- [设计令牌系统](../ui/styles/tokens.ts)
- [样式工具函数](../ui/utils/styles.ts)
- [样式隔离检查器](../ui/utils/styleIsolationChecker.ts)
- [AppContainer组件](../ui/components/layout/AppContainer.tsx)

## 更新日志

- 2025-07-25: 创建样式隔离指南
- 2025-07-25: 添加自动化检查工具
- 2025-07-25: 完善测试覆盖