# 样式隔离使用指南

本文档详细说明如何在开发中正确使用样式隔离工具，确保组件与Obsidian主题完全隔离。

## 快速开始

### 1. 基础类名生成

```tsx
import { cn, classNames } from '../utils/styles';

// 单个类名
const buttonClass = cn('button'); // 结果: 'ptm-button'

// 多个类名
const cardClass = classNames('card', 'primary', isActive && 'active');
// 结果: 'ptm-card ptm-primary ptm-active'

// 在组件中使用
export const Button: React.FC = ({ className, active, ...props }) => {
  return (
    <button 
      className={classNames('button', active && 'active', className)}
      {...props}
    />
  );
};
```

### 2. 容器隔离

所有组件都必须在PTM容器内：

```tsx
import { AppContainer } from '../components/layout/AppContainer';

export const MyView: React.FC = () => {
  return (
    <AppContainer>
      <div className={cn('my-view')}>
        {/* 组件内容 */}
      </div>
    </AppContainer>
  );
};
```

### 3. CSS变量使用

```tsx
import { getCSSVar } from '../utils/styles';

// 在样式对象中使用
const styles = {
  color: getCSSVar('text-primary'),
  background: getCSSVar('bg-secondary'),
  padding: getCSSVar('spacing-4'),
};

// 在CSS文件中使用
.ptm-component {
  color: var(--ptm-text-primary);
  background: var(--ptm-bg-secondary);
  padding: var(--ptm-spacing-4);
}
```

## 高级用法

### 1. BEM风格类名

```tsx
import { bem } from '../utils/styles';

// BEM类名生成
const blockClass = bem('card'); // 'ptm-card'
const elementClass = bem('card', 'header'); // 'ptm-card__header'
const modifierClass = bem('card', 'header', 'large'); // 'ptm-card__header--large'

// 在组件中使用
export const Card: React.FC<{ size?: 'small' | 'large' }> = ({ size, children }) => {
  return (
    <div className={bem('card')}>
      <div className={bem('card', 'header', size)}>
        Header
      </div>
      <div className={bem('card', 'content')}>
        {children}
      </div>
    </div>
  );
};
```

### 2. 条件类名

```tsx
import { conditionalClass } from '../utils/styles';

export const Button: React.FC<ButtonProps> = ({ 
  variant, 
  size, 
  disabled, 
  loading 
}) => {
  const className = conditionalClass('button', {
    [`variant-${variant}`]: !!variant,
    [`size-${size}`]: !!size,
    'disabled': disabled,
    'loading': loading,
  });

  return <button className={className} />;
};
```

### 3. 响应式类名

```tsx
import { responsive } from '../utils/styles';

const className = responsive('grid', {
  mobile: 'grid-cols-1',
  tablet: 'grid-cols-2', 
  desktop: 'grid-cols-3',
  wide: 'grid-cols-4',
});
// 结果: 'ptm-grid ptm-mobile:grid-cols-1 ptm-tablet:grid-cols-2 ...'
```

### 4. 主题相关类名

```tsx
import { themeClass } from '../utils/styles';

const className = themeClass('bg-white', 'bg-gray-900');
// 结果: 'ptm-light:bg-white ptm-dark:bg-gray-900'
```

## 样式重置

### 1. 自动样式重置

```tsx
import { injectStyleReset } from '../utils/styleReset';

// 在应用启动时注入全局样式重置
export const initializeApp = () => {
  injectStyleReset('.ptm-app-container');
};
```

### 2. 元素特定重置

```tsx
import { elementClass, generateElementReset } from '../utils/styles';

// 为特定元素生成隔离类名
const titleClass = elementClass('h1', 'primary');
// 结果: 'ptm-element-h1 ptm-element-h1--primary'

// 生成元素特定的重置CSS
const resetCSS = generateElementReset('button');
```

### 3. 检查元素是否需要重置

```tsx
import { needsStyleReset } from '../utils/styleReset';

const element = document.querySelector('h1');
if (needsStyleReset(element)) {
  element.classList.add(elementClass('h1'));
}
```

## 开发时检查

### 1. 样式隔离检查

```tsx
import { checkStyleIsolation, devStyleWarning } from '../utils/styles';

// 在组件挂载后检查
useEffect(() => {
  const element = ref.current;
  if (element) {
    const isIsolated = checkStyleIsolation(element);
    if (!isIsolated) {
      devStyleWarning(element, 'MyComponent');
    }
  }
}, []);
```

### 2. 自动化检查

```tsx
import { useStyleIsolationCheck } from '../hooks/useStyleIsolationCheck';

export const MyComponent: React.FC = () => {
  const ref = useStyleIsolationCheck('MyComponent');
  
  return (
    <div ref={ref} className={cn('my-component')}>
      {/* 组件内容 */}
    </div>
  );
};
```

## CSS编写规范

### 1. CSS模块文件

```css
/* MyComponent.module.css */

/* 主容器 - 使用双重选择器确保优先级 */
.ptm-app-container .myComponent,
.myComponent {
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-primary) !important;
  padding: var(--ptm-spacing-4) !important;
  border-radius: var(--ptm-radius-base) !important;
  box-sizing: border-box !important;
}

/* 子元素重置 */
.ptm-app-container .myComponent h1,
.myComponent h1 {
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-2xl) !important;
  font-weight: var(--ptm-font-bold) !important;
  color: var(--ptm-text-primary) !important;
  margin: 0 0 var(--ptm-spacing-4) 0 !important;
  padding: 0 !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

/* 状态变体 */
.ptm-app-container .myComponent.active,
.myComponent.active {
  background: var(--ptm-primary) !important;
  color: var(--ptm-text-inverse) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ptm-app-container .myComponent,
  .myComponent {
    padding: var(--ptm-spacing-2) !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-app-container .myComponent,
  .myComponent {
    border: 2px solid var(--ptm-border-dark) !important;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .ptm-app-container .myComponent,
  .myComponent {
    transition: none !important;
  }
}
```

### 2. 全局样式文件

```css
/* globals.css */

/* 容器基础样式 */
.ptm-app-container {
  /* CSS变量定义 */
  --ptm-primary: #667eea;
  --ptm-text-primary: #1f2937;
  --ptm-bg-primary: #ffffff;
  --ptm-spacing-4: 16px;
  --ptm-radius-base: 8px;
  --ptm-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* 基础重置 */
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-primary) !important;
  box-sizing: border-box !important;
}

.ptm-app-container *,
.ptm-app-container *::before,
.ptm-app-container *::after {
  box-sizing: border-box !important;
}
```

## 测试样式隔离

### 1. 单元测试

```tsx
import { render } from '@testing-library/react';
import { checkStyleIsolation } from '../utils/styles';
import { MyComponent } from './MyComponent';

describe('MyComponent样式隔离', () => {
  it('应该正确应用样式隔离', () => {
    const { container } = render(
      <div className="ptm-app-container">
        <MyComponent />
      </div>
    );
    
    const component = container.querySelector('.ptm-my-component');
    expect(component).not.toBeNull();
    
    const isIsolated = checkStyleIsolation(component as HTMLElement);
    expect(isIsolated).toBe(true);
  });
  
  it('应该有正确的PTM类名', () => {
    const { container } = render(<MyComponent />);
    
    const component = container.firstChild as HTMLElement;
    expect(component.className).toMatch(/ptm-/);
    expect(component).toHaveClass('ptm-my-component');
  });
});
```

### 2. 集成测试

```tsx
import { checkComponentTreeIsolation } from '../utils/styleIsolationChecker';

describe('应用样式隔离集成测试', () => {
  it('整个组件树应该通过样式隔离检查', () => {
    const { container } = render(
      <AppContainer>
        <MyComplexComponent />
      </AppContainer>
    );
    
    const report = checkComponentTreeIsolation(container.firstChild as HTMLElement);
    
    expect(report.overallPassed).toBe(true);
    expect(report.failedElements).toBe(0);
    expect(report.commonIssues).toHaveLength(0);
  });
});
```

## 常见问题解决

### 1. 样式被Obsidian覆盖

**问题**：组件样式被Obsidian主题覆盖

**解决方案**：
```css
/* 使用更高的优先级 */
.ptm-app-container .ptm-component {
  color: var(--ptm-text-primary) !important;
}

/* 或使用双重选择器 */
.ptm-app-container .ptm-app-container .ptm-component {
  color: var(--ptm-text-primary);
}
```

### 2. CSS变量未定义

**问题**：CSS变量显示为初始值

**解决方案**：
```tsx
// 确保在应用根部定义CSS变量
import { injectDesignTokens } from '../utils/designTokens';

useEffect(() => {
  injectDesignTokens();
}, []);
```

### 3. 响应式断点不生效

**问题**：响应式样式在Obsidian中不工作

**解决方案**：
```css
/* 使用容器查询而不是媒体查询 */
@container ptm-app (max-width: 768px) {
  .ptm-component {
    flex-direction: column;
  }
}
```

## 最佳实践总结

1. **始终使用工具函数**：使用`cn()`、`classNames()`等工具生成类名
2. **容器隔离**：所有组件都要在`AppContainer`内
3. **CSS变量优先**：使用项目CSS变量而不是硬编码值
4. **双重选择器**：在CSS中使用双重选择器提高优先级
5. **!important适度使用**：在关键样式上使用`!important`
6. **开发时检查**：使用开发工具检查样式隔离
7. **充分测试**：为样式隔离编写测试用例
8. **文档更新**：及时更新样式隔离相关文档

## 工具链支持

- **开发时检查**：自动检测样式隔离问题
- **构建时验证**：确保所有组件都有正确的样式隔离
- **测试覆盖**：样式隔离的自动化测试
- **文档生成**：自动生成样式隔离报告

通过遵循这些指南，可以确保插件在各种Obsidian主题下都能保持一致的视觉表现。