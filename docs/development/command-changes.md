# 命令变更说明

## 概述

本文档记录了插件命令系统的重要变更，特别是开发演示命令的移除及其影响。

## 变更详情

### 移除的命令 (2025-07-26)

#### `open-development-demo` 命令

**变更类型**: 移除  
**影响范围**: 开发者和测试用户  
**变更原因**: 简化命令面板，减少开发相关命令的暴露

**技术细节**:
```typescript
// 已移除的代码
this.addCommand({
    id: 'open-development-demo',
    name: 'Open Development Demo',
    callback: () => {
        this.openDevelopmentDemo();
    }
});
```

**保留的功能**:
- `openDevelopmentDemo()` 方法实现仍然存在
- 相关的视图注册未受影响
- 演示页面功能完全保留

## 当前可用命令

### 项目管理命令

1. **`open-project-dashboard`**
   - 名称: "Open Project Dashboard"
   - 功能: 打开项目仪表板
   - 访问: 命令面板 + Ribbon图标

2. **`create-new-project`**
   - 名称: "Create New Project"
   - 功能: 创建新项目
   - 访问: 命令面板

### 任务管理命令

3. **`create-task-from-selection`**
   - 名称: "Create Task from Selection"
   - 功能: 从选中文本创建任务
   - 访问: 命令面板（编辑器上下文）

4. **`create-new-task`**
   - 名称: "Create New Task"
   - 功能: 创建新任务
   - 访问: 命令面板

5. **`open-task-list`**
   - 名称: "Open Task List"
   - 功能: 打开任务列表视图
   - 访问: 命令面板

### 视图管理命令

6. **`open-kanban-board`**
   - 名称: "Open Kanban Board"
   - 功能: 打开看板视图
   - 访问: 命令面板

7. **`open-gantt-chart`**
   - 名称: "Open Gantt Chart"
   - 功能: 打开甘特图
   - 访问: 命令面板

8. **`open-sprint-board`**
   - 名称: "Open Sprint Board"
   - 功能: 打开Sprint管理面板
   - 访问: 命令面板

### Sprint管理命令

9. **`create-new-sprint`**
   - 名称: "Create New Sprint"
   - 功能: 创建新Sprint
   - 访问: 命令面板

### PTM文件命令

10. **`create-ptm-project`**
    - 名称: "Create PTM Project File"
    - 功能: 创建PTM项目文件
    - 访问: 命令面板

11. **`load-ptm-project`**
    - 名称: "Load PTM Project File"
    - 功能: 加载PTM项目文件
    - 访问: 命令面板

12. **`backup-ptm-project`**
    - 名称: "Backup PTM Project"
    - 功能: 备份PTM项目
    - 访问: 命令面板

### 维护和调试命令

13. **`clear-auto-synced-tasks`**
    - 名称: "Clear Auto-synced Tasks (Emergency Cleanup)"
    - 功能: 清理自动同步的任务
    - 访问: 命令面板

14. **`clear-duplicate-test-data`**
    - 名称: "Clear Duplicate Test Data (Cleanup)"
    - 功能: 清理重复的测试数据
    - 访问: 命令面板

### 同步管理命令

15. **`sync-tasks-from-vault`**
    - 名称: "Sync Tasks from Vault (Manual)"
    - 功能: 手动从库中同步任务
    - 访问: 命令面板

16. **`sync-tasks-to-vault`**
    - 名称: "Sync Tasks to Vault (Manual)"
    - 功能: 手动将任务同步到库
    - 访问: 命令面板

17. **`enable-auto-sync`**
    - 名称: "Enable Auto Sync (Careful!)"
    - 功能: 启用自动同步
    - 访问: 命令面板

18. **`disable-auto-sync`**
    - 名称: "Disable Auto Sync"
    - 功能: 禁用自动同步
    - 访问: 命令面板

### 调试信息命令

19. **`show-task-data-info`**
    - 名称: "Show Task Data Info (Debug)"
    - 功能: 显示任务数据信息
    - 访问: 命令面板

20. **`analyze-sync-differences`**
    - 名称: "Analyze Sync Differences (Debug)"
    - 功能: 分析同步差异
    - 访问: 命令面板

### 模式切换命令

21. **`toggle-project-mode`**
    - 名称: "Toggle Project Mode"
    - 功能: 切换项目模式
    - 访问: 命令面板

## 替代访问方式

### 开发演示功能

由于 `open-development-demo` 命令已移除，请使用以下方式访问：

1. **Ribbon图标**: 点击左侧工具栏的项目管理图标
2. **项目仪表板**: 通过 `open-project-dashboard` 命令访问主界面
3. **直接调用**: 开发环境下可以直接调用 `openDevelopmentDemo()` 方法

### 开发者调试

由于方法已完全移除，开发者需要通过以下方式访问相关功能：
- 使用项目仪表板的组件展示功能
- 直接访问各个组件的测试页面
- 使用开发环境的热重载功能

## 迁移指南

### 对用户的影响

- **最小影响**: 大多数用户不会受到影响，因为开发演示主要面向开发者
- **访问变更**: 需要通过Ribbon图标而非命令面板访问演示功能
- **功能保留**: 所有演示功能仍然可用

### 对开发者的影响

- **工作流调整**: 需要更新开发文档和测试流程
- **代码保留**: 相关代码仍然存在，可以随时重新启用
- **测试影响**: 自动化测试需要更新访问方式

### 重新启用命令

如果需要重新启用开发演示命令，需要：

1. 重新实现 `openDevelopmentDemo()` 方法
2. 在 `registerCommands()` 方法中添加命令注册
3. 确保相关的视图和组件正确注册

```typescript
// 需要重新实现的方法
private async openDevelopmentDemo() {
    // 实现开发演示页面打开逻辑
}

// 在 registerCommands() 中添加
this.addCommand({
    id: 'open-development-demo',
    name: 'Open Development Demo',
    callback: () => {
        this.openDevelopmentDemo();
    }
});
```

## 未来计划

### 短期计划

- 监控用户反馈，评估是否需要重新启用命令
- 完善Ribbon图标的用户体验
- 优化项目仪表板的演示功能集成

### 长期计划

- 考虑将演示功能完全集成到主界面
- 开发更直观的组件展示方式
- 建立更完善的开发者工具链

## 反馈和建议

如果您对这个变更有任何意见或建议，请通过以下方式联系我们：

- GitHub Issues: 报告问题或提出功能请求
- GitHub Discussions: 参与讨论和分享想法
- 开发者邮件: 直接联系开发团队

---

**注意**: 本文档将随着插件的发展持续更新。建议开发者定期查看以获取最新的命令变更信息。