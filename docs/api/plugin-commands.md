# 插件命令 API 文档

## 概述

本文档描述了 Obsidian Project Task Manager 插件的命令系统API，包括所有可用命令及其使用方法。

## 命令注册架构

### 基础结构

```typescript
export default class ProjectTaskManagerPlugin extends Plugin {
    private registerCommands() {
        this.addCommand({
            id: 'command-id',
            name: 'Command Display Name',
            callback: () => {
                this.commandHandler();
            }
        });
    }
}
```

### 命令类型

插件支持以下类型的命令：

1. **普通命令**: 基础的回调函数命令
2. **编辑器命令**: 需要编辑器上下文的命令
3. **条件命令**: 根据状态动态启用/禁用的命令

## 当前命令列表

### 项目管理

#### `open-project-dashboard`
- **显示名称**: "Open Project Dashboard"
- **功能**: 打开项目仪表板视图
- **实现**: `openProjectDashboard()`
- **访问方式**: 命令面板 + Ribbon图标

#### `create-new-project`
- **显示名称**: "Create New Project"
- **功能**: 打开项目创建模态框
- **实现**: `createNewProject()`
- **访问方式**: 命令面板

### 任务管理

#### `create-task-from-selection`
- **显示名称**: "Create Task from Selection"
- **类型**: 编辑器命令
- **功能**: 从选中文本创建任务
- **实现**: `createTaskFromSelection(editor, view)`
- **访问方式**: 命令面板（编辑器上下文）

#### `create-new-task`
- **显示名称**: "Create New Task"
- **功能**: 打开任务创建模态框
- **实现**: `createNewTask()`
- **访问方式**: 命令面板

#### `open-task-list`
- **显示名称**: "Open Task List"
- **功能**: 打开任务列表视图
- **实现**: `openTaskList()`
- **访问方式**: 命令面板

### 视图管理

#### `open-kanban-board`
- **显示名称**: "Open Kanban Board"
- **功能**: 打开看板视图
- **实现**: `openKanbanBoard()`
- **访问方式**: 命令面板

#### `open-gantt-chart`
- **显示名称**: "Open Gantt Chart"
- **功能**: 打开甘特图视图
- **实现**: `openGanttChart()`
- **访问方式**: 命令面板

#### `open-sprint-board`
- **显示名称**: "Open Sprint Board"
- **功能**: 打开Sprint管理面板
- **实现**: `openSprintBoard()`
- **访问方式**: 命令面板

## 已移除的命令

### `open-development-demo` (移除于 2025-07-26)

**原始定义**:
```typescript
this.addCommand({
    id: 'open-development-demo',
    name: 'Open Development Demo',
    callback: () => {
        this.openDevelopmentDemo();
    }
});
```

**移除原因**:
- 简化命令面板界面
- 减少开发相关命令的暴露
- 统一访问入口到Ribbon图标

**替代方案**:
- 使用Ribbon图标访问
- 通过项目仪表板访问相关功能
- 开发环境下直接调用方法

**完全移除**:
方法实现已完全从代码库中移除，开发演示功能已整合到主要的项目管理界面中。

## 命令实现模式

### 标准命令模式

```typescript
private async openSomeView() {
    try {
        const leaf = this.app.workspace.getLeaf(false);
        await leaf.setViewState({
            type: 'view-type-id',
            active: true
        });
        
        this.app.workspace.revealLeaf(leaf);
        new Notice('视图已打开');
    } catch (error) {
        console.error('Error opening view:', error);
        new Notice('打开视图失败');
    }
}
```

### 模态框命令模式

```typescript
private async createSomething() {
    try {
        if (!this.ptmManager) {
            new Notice('PTM管理器未初始化');
            return;
        }

        const { SomeModal } = await import('./src/ui/components/common/SomeModal');
        const modal = new SomeModal(this.app, this.ptmManager);
        modal.open();
    } catch (error) {
        console.error('Error opening modal:', error);
        new Notice('打开对话框失败');
    }
}
```

### 编辑器命令模式

```typescript
private async doSomethingWithEditor(editor: Editor, view: MarkdownView) {
    try {
        if (!this.ptmManager) {
            new Notice('PTM管理器未初始化');
            return;
        }

        const selection = editor.getSelection();
        // 处理选中内容
        
    } catch (error) {
        console.error('Error processing editor content:', error);
        new Notice('处理编辑器内容失败');
    }
}
```

## 错误处理

### 标准错误处理模式

所有命令都应该遵循以下错误处理模式：

```typescript
private async someCommand() {
    try {
        // 检查前置条件
        if (!this.ptmManager) {
            new Notice('PTM管理器未初始化');
            return;
        }

        // 执行主要逻辑
        await this.doSomething();
        
        // 成功反馈
        new Notice('操作成功');
    } catch (error) {
        // 错误日志
        console.error('Error in someCommand:', error);
        
        // 用户反馈
        new Notice(`操作失败: ${error instanceof Error ? error.message : String(error)}`);
    }
}
```

## 扩展命令系统

### 添加新命令

1. **在 `registerCommands()` 中注册**:
```typescript
this.addCommand({
    id: 'new-command-id',
    name: 'New Command Name',
    callback: () => {
        this.newCommandHandler();
    }
});
```

2. **实现命令处理函数**:
```typescript
private async newCommandHandler() {
    // 实现命令逻辑
}
```

3. **更新文档**:
- 在本文档中添加命令描述
- 更新用户指南
- 添加相关测试

### 命令分组建议

建议按功能对命令进行分组：

- **项目管理**: 项目相关的CRUD操作
- **任务管理**: 任务相关的操作
- **视图管理**: 不同视图的打开和切换
- **数据管理**: 导入、导出、同步等
- **调试工具**: 开发和调试相关的命令

## 最佳实践

### 命令命名

- **ID**: 使用kebab-case，描述性强
- **显示名称**: 使用Title Case，简洁明了
- **分组**: 相关命令使用相同前缀

### 性能考虑

- 延迟加载重型组件
- 使用异步操作避免阻塞
- 适当的错误处理和用户反馈

### 用户体验

- 提供清晰的成功/失败反馈
- 使用一致的错误消息格式
- 考虑命令的可发现性

## 测试

### 命令测试示例

```typescript
describe('Plugin Commands', () => {
    let plugin: ProjectTaskManagerPlugin;
    
    beforeEach(() => {
        plugin = new ProjectTaskManagerPlugin(app, manifest);
    });
    
    test('should register all commands', () => {
        const spy = jest.spyOn(plugin, 'addCommand');
        plugin.registerCommands();
        
        expect(spy).toHaveBeenCalledWith(
            expect.objectContaining({
                id: 'open-project-dashboard',
                name: 'Open Project Dashboard'
            })
        );
    });
});
```

---

**注意**: 此API文档将随着插件的发展持续更新。建议开发者在添加新命令时同步更新此文档。