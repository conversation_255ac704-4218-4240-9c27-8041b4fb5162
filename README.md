# Obsidian 超级任务管理器

一个功能全面的 Obsidian 项目任务管理插件，支持敏捷开发方法论并与 Tasks 插件完全兼容。

## ✨ 核心特性

- **📋 项目管理**: 创建和管理具有层级任务结构的项目
- **✅ 任务管理**: 支持任务依赖关系、优先级和状态跟踪
- **🏃‍♂️ 敏捷支持**: Sprint 管理、看板视图和燃尽图
- **🔗 Tasks 插件兼容**: 与现有 Tasks 插件 emoji 语法无缝集成
- **📊 甘特图**: 可视化项目时间线和依赖关系管理
- **🔧 深度集成**: 与 Obsidian 笔记、标签和模板深度集成
- **🎨 组件展示**: 内置开发演示页面，展示所有UI组件效果

## 🚀 功能亮点

### 项目管理
- 创建多层级项目结构
- 项目状态跟踪（规划中、进行中、暂停、已完成、已取消）
- 项目进度可视化和统计分析
- 支持项目标签和自定义属性

### 任务管理
- 支持父子任务层级关系
- 任务依赖关系管理（完成-开始、开始-开始等）
- 多种任务状态（待办、进行中、阻塞、审核、已完成、已取消）
- 任务优先级设置（低、中、高、紧急）
- 任务时间跟踪和工时统计

### 敏捷开发
- Sprint 创建和管理
- 可拖拽的看板视图
- 燃尽图和速度跟踪
- 自定义工作流配置

### Tasks 插件兼容
- 完全兼容 Tasks 插件的 emoji 语法
- 自动识别和同步任务状态
- 支持现有任务数据迁移
- 双向同步机制

## 🛠️ 开发环境

### 环境要求

- Node.js (v16 或更高版本)
- npm 或 yarn
- Obsidian (v0.15.0 或更高版本)

### 开发设置

1. 克隆此仓库
   ```bash
   git clone https://github.com/your-repo/obsidian-super-task-master.git
   cd obsidian-super-task-master
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 开发模式（自动复制到 Obsidian）
   ```bash
   # 主插件开发模式
   npm run dev
   
   # 演示插件开发模式
   npm run dev:demo
   ```

4. 生产构建
   ```bash
   # 主插件构建
   npm run build
   
   # 演示插件构建
   npm run build:demo
   ```

### 组件开发工作流

1. **启动演示插件**
   ```bash
   npm run dev:demo
   ```

2. **在 Obsidian 中查看效果**
   - 启用演示插件
   - 点击 Ribbon 图标或使用命令面板
   - 实时查看组件开发效果

3. **运行测试**
   ```bash
   # 运行所有测试
   npm test
   
   # 运行样式隔离测试
   npm test -- --testNamePattern="样式隔离"
   
   # 运行特定组件测试
   npm test -- src/ui/components/common/__tests__/StatusIndicator.test.tsx
   ```

### 项目结构

```
├── main.ts                    # 插件入口文件
├── demo-main.ts               # 演示插件入口（开发用）
├── manifest.json              # 插件清单
├── package.json               # Node.js 依赖
├── tsconfig.json              # TypeScript 配置
├── esbuild.config.mjs         # 构建配置
├── styles.css                 # 插件样式
└── src/                       # 源代码目录
    ├── models/                # 数据模型
    │   ├── Project.ts         # 项目模型
    │   ├── Task.ts            # 任务模型
    │   ├── Sprint.ts          # Sprint 模型
    │   ├── Workflow.ts        # 工作流模型
    │   └── ValidationSchemas.ts # 数据验证
    ├── services/              # 业务逻辑
    │   ├── DataManager.ts     # 数据管理器
    │   ├── Repository.ts      # 仓库模式基类
    │   ├── ProjectRepository.ts # 项目仓库
    │   └── TaskRepository.ts  # 任务仓库
    └── ui/                    # 用户界面
        ├── components/        # React 组件
        ├── views/            # Obsidian 视图
        ├── utils/            # UI 工具函数
        └── styles/           # 样式系统
```

## 📦 安装方法

### 手动安装

1. 下载最新版本的发布包
2. 解压文件到你的 vault 目录下的 `.obsidian/plugins/obsidian-super-task-master/` 文件夹
3. 在 Obsidian 设置中启用插件

### 社区插件商店

*即将上架 Obsidian 社区插件商店*

## 📖 使用指南

### 基础设置

1. 在 Obsidian 设置中找到"超级任务管理器"
2. 根据需要配置以下选项：
   - **项目模式**: 启用基于项目的任务组织
   - **独立任务模式**: 允许不依赖项目的任务管理
   - **Tasks 插件同步**: 与 Tasks 插件保持同步
   - **性能设置**: 缓存和虚拟滚动配置

### 快速开始

1. **查看项目仪表板**: 点击左侧工具栏的仪表板图标或使用命令面板搜索"Open Project Dashboard"
2. **创建项目**: 使用命令面板搜索"创建新项目"
3. **添加任务**: 在项目中创建任务，支持层级结构
4. **管理 Sprint**: 创建 Sprint 并分配任务
5. **查看进度**: 使用仪表板、看板或甘特图查看项目进度

### 开发演示功能

插件包含一个专门的开发演示页面，展示所有已实现的UI组件：

#### 启动方式
- **Ribbon图标**: 点击左侧工具栏的仪表板图标
- **注意**: 开发演示命令已从命令面板中移除，请使用Ribbon图标或直接访问项目仪表板

#### 演示内容
- **页面标签组件**: 支持图标、徽章、关闭功能的标签页系统
- **搜索栏组件**: 带建议列表和关键词高亮的搜索输入框
- **状态指示器**: 成功、警告、错误、信息等多种状态的可视化指示器
- **进度条组件**: 支持动画效果、条纹样式和百分比显示的进度条
- **头像组件**: 多尺寸、多状态的用户头像显示
- **导航组件**: 可折叠的侧边栏导航系统
- **组合示例**: 展示多个组件协同工作的实际效果

### 命令列表

- `Ctrl/Cmd + P` 打开命令面板，搜索以下命令：
  - 打开项目仪表板
  - 创建新项目
  - 打开任务列表
  - 打开看板视图
  - 打开甘特图
  - 创建新 Sprint
  - 切换项目模式

## 🔧 开发状态

当前项目处于积极开发阶段，已完成的功能模块：

- ✅ 项目基础架构搭建
- ✅ 插件主入口和生命周期管理
- ✅ 核心数据模型定义
- ✅ 数据存储和仓库模式
- ✅ UI组件系统开发
- ✅ 样式隔离系统实现
- ✅ 开发演示插件
- 🚧 PTM 文件格式支持（进行中）
- 🚧 用户界面集成（进行中）
- ⏳ 高级功能开发

### 已完成的UI组件

- ✅ **StatusIndicator** - 状态指示器组件
- ✅ **ProgressBar** - 进度条组件  
- ✅ **Avatar** - 头像组件
- ✅ **SearchBar** - 搜索栏组件
- ✅ **NavigationItem** - 导航项组件
- ✅ **NavigationSection** - 导航分组组件
- ✅ **PageTabs** - 页面标签组件
- ✅ **DevelopmentHomepage** - 开发演示页面

### 开发工具

- ✅ **样式隔离检查器** - 自动检测样式冲突
- ✅ **组件测试套件** - 完整的单元测试覆盖
- ✅ **演示插件系统** - 独立的组件展示环境
- ✅ **开发文档系统** - 完整的API和使用文档

### 相关文档

- [组件开发演示系统](docs/development/DEMO.md) - 演示系统详细介绍
- [演示插件开发指南](docs/development/demo-plugin-guide.md) - 演示插件使用指南
- [演示插件API文档](docs/api/demo-plugin-api.md) - API接口文档
- [样式隔离指南](docs/development/style-isolation-guide.md) - 样式隔离最佳实践

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 此仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: 添加某个很棒的功能'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 提交规范

请使用以下格式提交代码：
- `feat: 新功能`
- `fix: 修复问题`
- `docs: 文档更新`
- `style: 代码格式调整`
- `refactor: 代码重构`
- `test: 测试相关`
- `chore: 构建过程或辅助工具的变动`

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢 Obsidian 团队提供优秀的插件 API
- 感谢 Tasks 插件团队的兼容性支持
- 感谢所有贡献者和测试用户

## 📞 联系我们

- 问题反馈: [GitHub Issues](https://github.com/your-repo/obsidian-super-task-master/issues)
- 功能建议: [GitHub Discussions](https://github.com/your-repo/obsidian-super-task-master/discussions)

---

**注意**: 此插件目前处于开发阶段，部分功能可能尚未完全实现。我们建议在非生产环境中进行测试。