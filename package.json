{"name": "obsidian-project-task-manager", "version": "1.0.0", "description": "A comprehensive project and task management plugin for Obsidian", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs dev", "watch": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test-build": "npm run build && node esbuild.config.mjs dev", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": ["obsidian", "plugin", "project-management", "task-management", "agile", "kanban", "gantt"], "author": "Project Task Manager Team", "license": "MIT", "dependencies": {"clsx": "^2.0.0", "gantt-task-react": "^0.3.9", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^16.11.6", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.5", "obsidian": "latest", "ts-jest": "^29.4.0", "tslib": "2.4.0", "typescript": "4.7.4"}}