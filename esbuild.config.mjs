import esbuild from "esbuild";
import process from "process";
import builtins from "builtin-modules";
import { copyFileSync, existsSync, mkdirSync } from "fs";
import path from "path";

const banner =
`/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/
`;

const prod = (process.argv[2] === 'production');
const dev = (process.argv[2] === 'dev');

// Obsidian plugin directory for testing
const OBSIDIAN_PLUGIN_DIR = '/Users/<USER>/Documents/010-NoteSpace/010-ObsidianNote/.obsidian/plugins/obsidian-super-task-master';

// Function to copy files to Obsidian plugin directory
function copyToObsidian() {
	try {
		// Create plugin directory if it doesn't exist
		if (!existsSync(OBSIDIAN_PLUGIN_DIR)) {
			mkdirSync(OBSIDIAN_PLUGIN_DIR, { recursive: true });
			console.log(`Created plugin directory: ${OBSIDIAN_PLUGIN_DIR}`);
		}

		// Copy main.js
		if (existsSync('main.js')) {
			copyFileSync('main.js', path.join(OBSIDIAN_PLUGIN_DIR, 'main.js'));
			console.log('✓ Copied main.js to Obsidian');
		}

		// Copy manifest.json
		if (existsSync('manifest.json')) {
			copyFileSync('manifest.json', path.join(OBSIDIAN_PLUGIN_DIR, 'manifest.json'));
			console.log('✓ Copied manifest.json to Obsidian');
		}

		// Copy styles.css
		if (existsSync('styles.css')) {
			copyFileSync('styles.css', path.join(OBSIDIAN_PLUGIN_DIR, 'styles.css'));
			console.log('✓ Copied styles.css to Obsidian');
		}

		console.log(`🚀 Plugin files copied to: ${OBSIDIAN_PLUGIN_DIR}`);
	} catch (error) {
		console.error('❌ Error copying files to Obsidian:', error.message);
	}
}

const context = await esbuild.context({
	banner: {
		js: banner,
	},
	entryPoints: ['main.ts'],
	bundle: true,
	external: [
		'obsidian',
		'electron',
		'@codemirror/autocomplete',
		'@codemirror/collab',
		'@codemirror/commands',
		'@codemirror/language',
		'@codemirror/lint',
		'@codemirror/search',
		'@codemirror/state',
		'@codemirror/view',
		'@lezer/common',
		'@lezer/highlight',
		'@lezer/lr',
		...builtins],
	format: 'cjs',
	target: 'es2018',
	logLevel: "info",
	sourcemap: prod ? false : 'inline',
	treeShaking: true,
	outfile: 'main.js',
	jsx: 'automatic',
	jsxImportSource: 'react',
	plugins: [
		{
			name: 'copy-to-obsidian',
			setup(build) {
				build.onEnd(() => {
					if (dev) {
						copyToObsidian();
					}
				});
			}
		}
	]
});

if (prod) {
	await context.rebuild();
	console.log('✓ Production build completed');
	
	// Copy files to Obsidian in production mode too
	copyToObsidian();
	
	process.exit(0);
} else if (dev) {
	console.log('🔄 Starting development mode with auto-copy to Obsidian...');
	console.log(`📁 Plugin directory: ${OBSIDIAN_PLUGIN_DIR}`);
	
	// Initial copy
	copyToObsidian();
	
	// Watch for changes
	await context.watch();
} else {
	await context.watch();
}