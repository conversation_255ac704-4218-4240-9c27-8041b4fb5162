// 看板视图相关的数据模型和接口

import { TaskStatus } from './Task';

/**
 * 看板列配置
 */
export interface KanbanColumn {
	id: string;
	title: string;
	status: TaskStatus;
	wipLimit?: number; // Work In Progress 限制
	color?: string;
	position: number;
	isCollapsed?: boolean;
}

/**
 * 泳道配置
 */
export interface KanbanSwimLane {
	id: string;
	title: string;
	type: SwimLaneType;
	value: string; // 用于分组的值（如优先级、负责人等）
	color?: string;
	position: number;
	isCollapsed?: boolean;
}

/**
 * 泳道类型
 */
export enum SwimLaneType {
	PRIORITY = 'priority',
	ASSIGNEE = 'assignee',
	PROJECT = 'project',
	TAG = 'tag',
	CUSTOM = 'custom'
}

/**
 * 看板配置
 */
export interface KanbanConfig {
	id: string;
	name: string;
	projectId?: string; // 如果为空则显示所有项目的任务
	columns: KanbanColumn[];
	swimLanes?: KanbanSwimLane[];
	enableSwimLanes: boolean;
	showTaskCount: boolean;
	showWipLimits: boolean;
	autoRefresh: boolean;
	refreshInterval: number; // 秒
	createdAt: Date;
	updatedAt: Date;
}

/**
 * 看板任务卡片数据
 */
export interface KanbanTaskCard {
	taskId: string;
	title: string;
	description?: string;
	status: TaskStatus;
	priority: string;
	assignee?: string;
	tags: string[];
	dueDate?: Date | string;
	estimatedHours?: number;
	actualHours?: number;
	isOverdue: boolean;
	hasChildren: boolean;
	childrenCount: number;
	dependenciesCount: number;
	linkedNotesCount: number;
}

/**
 * 拖拽操作数据
 */
export interface DragDropData {
	taskId: string;
	sourceColumnId: string;
	targetColumnId: string;
	sourceIndex: number;
	targetIndex: number;
	swimLaneId?: string;
}

/**
 * 看板统计信息
 */
export interface KanbanStats {
	totalTasks: number;
	tasksByColumn: Record<string, number>;
	tasksBySwimLane?: Record<string, number>;
	wipViolations: string[]; // 违反WIP限制的列ID
	overdueTasksCount: number;
	completionRate: number; // 完成率百分比
}

/**
 * 默认看板列配置
 */
export const DEFAULT_KANBAN_COLUMNS: KanbanColumn[] = [
	{
		id: 'todo',
		title: '待办',
		status: TaskStatus.TODO,
		position: 0,
		color: '#6b7280'
	},
	{
		id: 'in_progress',
		title: '进行中',
		status: TaskStatus.IN_PROGRESS,
		position: 1,
		wipLimit: 3,
		color: '#3b82f6'
	},
	{
		id: 'review',
		title: '待审核',
		status: TaskStatus.REVIEW,
		position: 2,
		wipLimit: 2,
		color: '#f59e0b'
	},
	{
		id: 'completed',
		title: '已完成',
		status: TaskStatus.COMPLETED,
		position: 3,
		color: '#10b981'
	}
];

/**
 * 看板工具类
 */
export class KanbanUtils {
	/**
	 * 创建默认看板配置
	 */
	static createDefaultConfig(name: string, projectId?: string): KanbanConfig {
		const now = new Date();
		return {
			id: this.generateId(),
			name,
			projectId,
			columns: [...DEFAULT_KANBAN_COLUMNS],
			swimLanes: [],
			enableSwimLanes: false,
			showTaskCount: true,
			showWipLimits: true,
			autoRefresh: false,
			refreshInterval: 30,
			createdAt: now,
			updatedAt: now
		};
	}

	/**
	 * 创建优先级泳道配置
	 */
	static createPrioritySwimLanes(): KanbanSwimLane[] {
		return [
			{
				id: 'critical',
				title: '紧急',
				type: SwimLaneType.PRIORITY,
				value: 'critical',
				color: '#ef4444',
				position: 0
			},
			{
				id: 'high',
				title: '高优先级',
				type: SwimLaneType.PRIORITY,
				value: 'high',
				color: '#f97316',
				position: 1
			},
			{
				id: 'medium',
				title: '中优先级',
				type: SwimLaneType.PRIORITY,
				value: 'medium',
				color: '#eab308',
				position: 2
			},
			{
				id: 'low',
				title: '低优先级',
				type: SwimLaneType.PRIORITY,
				value: 'low',
				color: '#6b7280',
				position: 3
			}
		];
	}

	/**
	 * 验证WIP限制
	 */
	static validateWipLimits(config: KanbanConfig, tasksByColumn: Record<string, number>): string[] {
		const violations: string[] = [];
		
		for (const column of config.columns) {
			if (column.wipLimit && column.wipLimit > 0) {
				const taskCount = tasksByColumn[column.id] || 0;
				if (taskCount > column.wipLimit) {
					violations.push(column.id);
				}
			}
		}
		
		return violations;
	}

	/**
	 * 计算看板统计信息
	 */
	static calculateStats(
		config: KanbanConfig, 
		tasks: KanbanTaskCard[]
	): KanbanStats {
		const tasksByColumn: Record<string, number> = {};
		const tasksBySwimLane: Record<string, number> = {};
		
		// 初始化计数器
		for (const column of config.columns) {
			tasksByColumn[column.id] = 0;
		}
		
		if (config.enableSwimLanes && config.swimLanes) {
			for (const swimLane of config.swimLanes) {
				tasksBySwimLane[swimLane.id] = 0;
			}
		}
		
		// 统计任务
		let overdueCount = 0;
		let completedCount = 0;
		
		for (const task of tasks) {
			// 按列统计
			const columnId = config.columns.find(col => col.status === task.status)?.id;
			if (columnId) {
				tasksByColumn[columnId]++;
			}
			
			// 按泳道统计
			if (config.enableSwimLanes && config.swimLanes) {
				for (const swimLane of config.swimLanes) {
					if (this.taskMatchesSwimLane(task, swimLane)) {
						tasksBySwimLane[swimLane.id]++;
						break;
					}
				}
			}
			
			// 统计逾期和完成任务
			if (task.isOverdue) {
				overdueCount++;
			}
			if (task.status === TaskStatus.COMPLETED) {
				completedCount++;
			}
		}
		
		// 检查WIP限制违规
		const wipViolations = this.validateWipLimits(config, tasksByColumn);
		
		// 计算完成率
		const completionRate = tasks.length > 0 ? 
			Math.round((completedCount / tasks.length) * 100) : 0;
		
		return {
			totalTasks: tasks.length,
			tasksByColumn,
			tasksBySwimLane: config.enableSwimLanes ? tasksBySwimLane : undefined,
			wipViolations,
			overdueTasksCount: overdueCount,
			completionRate
		};
	}

	/**
	 * 检查任务是否匹配泳道
	 */
	static taskMatchesSwimLane(task: KanbanTaskCard, swimLane: KanbanSwimLane): boolean {
		switch (swimLane.type) {
			case SwimLaneType.PRIORITY:
				return task.priority === swimLane.value;
			case SwimLaneType.ASSIGNEE:
				return task.assignee === swimLane.value;
			case SwimLaneType.TAG:
				return task.tags.includes(swimLane.value);
			case SwimLaneType.CUSTOM:
				// 自定义逻辑可以在这里实现
				return false;
			default:
				return false;
		}
	}

	/**
	 * 生成唯一ID
	 */
	private static generateId(): string {
		return 'kanban_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
	}
}