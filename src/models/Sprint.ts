// Sprint data model based on design document
// Import unified types from core types
import { SprintStatus, BaseEntity } from '../types/core';

// Re-export core types for convenience
export { SprintStatus, BaseEntity } from '../types/core';

export interface BurndownPoint {
	date: Date;
	remainingHours: number;
	completedTasks: number;
}

export interface Sprint {
	id: string;
	projectId: string;
	name: string;
	goal?: string;
	startDate: Date;
	endDate: Date;
	status: SprintStatus;
	taskIds: string[];
	velocity?: number;
	burndownData: BurndownPoint[];
	createdAt: Date;
	updatedAt: Date;
}

// Utility functions for sprint management
export class SprintUtils {
	static createSprint(name: string, projectId: string, startDate: Date, endDate: Date): Sprint {
		const now = new Date();
		return {
			id: this.generateId(),
			projectId,
			name,
			startDate,
			endDate,
			status: SprintStatus.PLANNING,
			taskIds: [],
			burndownData: [],
			createdAt: now,
			updatedAt: now
		};
	}

	static updateSprint(sprint: Sprint, updates: Partial<Sprint>): Sprint {
		return {
			...sprint,
			...updates,
			updatedAt: new Date()
		};
	}

	static addTask(sprint: Sprint, taskId: string): Sprint {
		if (sprint.taskIds.includes(taskId)) {
			return sprint; // Task already in sprint
		}
		
		return {
			...sprint,
			taskIds: [...sprint.taskIds, taskId],
			updatedAt: new Date()
		};
	}

	static removeTask(sprint: Sprint, taskId: string): Sprint {
		return {
			...sprint,
			taskIds: sprint.taskIds.filter(id => id !== taskId),
			updatedAt: new Date()
		};
	}

	static addBurndownPoint(sprint: Sprint, point: BurndownPoint): Sprint {
		// Remove existing point for the same date if it exists
		const filteredData = sprint.burndownData.filter(
			p => p.date.toDateString() !== point.date.toDateString()
		);
		
		return {
			...sprint,
			burndownData: [...filteredData, point].sort((a, b) => a.date.getTime() - b.date.getTime()),
			updatedAt: new Date()
		};
	}

	static calculateVelocity(sprint: Sprint, tasks: any[]): number {
		if (sprint.status !== SprintStatus.COMPLETED) {
			return 0; // Can only calculate velocity for completed sprints
		}

		const sprintTasks = tasks.filter(task => sprint.taskIds.includes(task.id));
		const completedTasks = sprintTasks.filter(task => task.status === 'completed');
		
		// Calculate story points or use task count as fallback
		const totalPoints = completedTasks.reduce((sum, task) => {
			return sum + (task.storyPoints || 1);
		}, 0);

		return totalPoints;
	}

	static getDaysRemaining(sprint: Sprint): number {
		const now = new Date();
		const endDate = new Date(sprint.endDate);
		const diffTime = endDate.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		return Math.max(0, diffDays);
	}

	static getDuration(sprint: Sprint): number {
		const startDate = new Date(sprint.startDate);
		const endDate = new Date(sprint.endDate);
		const diffTime = endDate.getTime() - startDate.getTime();
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	}

	static isActive(sprint: Sprint): boolean {
		const now = new Date();
		return sprint.status === SprintStatus.ACTIVE &&
			   now >= sprint.startDate &&
			   now <= sprint.endDate;
	}

	static isOverdue(sprint: Sprint): boolean {
		const now = new Date();
		return now > sprint.endDate && sprint.status === SprintStatus.ACTIVE;
	}

	static canStart(sprint: Sprint): boolean {
		return sprint.status === SprintStatus.PLANNING && sprint.taskIds.length > 0;
	}

	static canComplete(sprint: Sprint): boolean {
		return sprint.status === SprintStatus.ACTIVE;
	}

	private static generateId(): string {
		return 'sprint_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
	}
}