// 甘特图数据模型

import { Task, TaskDependency, DependencyType, TaskStatus } from './Task';

// 甘特图任务项接口
export interface GanttTask {
	id: string;
	name: string;
	start: Date;
	end: Date;
	progress: number; // 0-100
	type: GanttTaskType;
	project?: string;
	dependencies?: string[];
	hideChildren?: boolean;
	displayOrder?: number;
	styles?: {
		backgroundColor?: string;
		backgroundSelectedColor?: string;
		progressColor?: string;
		progressSelectedColor?: string;
	};
}

export enum GanttTaskType {
	TASK = 'task',
	MILESTONE = 'milestone',
	PROJECT = 'project'
}

// 甘特图视图配置
export interface GanttViewConfig {
	viewMode: GanttViewMode;
	columnWidth: number;
	listCellWidth: string;
	rowHeight: number;
	ganttHeight: number;
	barCornerRadius: number;
	handleWidth: number;
	fontFamily: string;
	fontSize: string;
	barFill: number;
	barProgressColor: string;
	barProgressSelectedColor: string;
	barBackgroundColor: string;
	barBackgroundSelectedColor: string;
	projectProgressColor: string;
	projectProgressSelectedColor: string;
	projectBackgroundColor: string;
	projectBackgroundSelectedColor: string;
	milestoneBackgroundColor: string;
	milestoneBackgroundSelectedColor: string;
	rtl: boolean;
}

export enum GanttViewMode {
	QUARTER_DAY = 'Quarter Day',
	HALF_DAY = 'Half Day',
	DAY = 'Day',
	WEEK = 'Week',
	MONTH = 'Month',
	YEAR = 'Year'
}

// 关键路径分析结果
export interface CriticalPathAnalysis {
	criticalTasks: string[]; // 关键任务ID列表
	totalDuration: number; // 总工期（天）
	earliestStart: Date;
	latestFinish: Date;
	slack: Map<string, number>; // 任务松弛时间
}

// 资源冲突检测结果
export interface ResourceConflict {
	taskId: string;
	conflictingTaskIds: string[];
	resource: string; // 冲突的资源（如负责人）
	conflictType: ConflictType;
	severity: ConflictSeverity;
}

export enum ConflictType {
	RESOURCE_OVERALLOCATION = 'resource_overallocation',
	TIME_OVERLAP = 'time_overlap',
	DEPENDENCY_VIOLATION = 'dependency_violation'
}

export enum ConflictSeverity {
	LOW = 'low',
	MEDIUM = 'medium',
	HIGH = 'high',
	CRITICAL = 'critical'
}

// 甘特图工具类
export class GanttUtils {
	/**
	 * 将Task转换为GanttTask
	 */
	static taskToGanttTask(task: Task): GanttTask {
		// 安全处理日期字段，确保始终有有效的Date对象
		const now = new Date();
		let start: Date;
		let end: Date;

		// 处理开始日期
		if (task.startDate) {
			if (typeof task.startDate === 'string') {
				start = new Date(task.startDate);
				// 检查日期是否有效
				if (isNaN(start.getTime())) {
					start = now;
				}
			} else if (task.startDate instanceof Date) {
				start = task.startDate;
				// 检查日期是否有效
				if (isNaN(start.getTime())) {
					start = now;
				}
			} else {
				start = now;
			}
		} else {
			start = now;
		}

		// 处理结束日期
		if (task.dueDate) {
			if (typeof task.dueDate === 'string') {
				end = new Date(task.dueDate);
				// 检查日期是否有效
				if (isNaN(end.getTime())) {
					end = new Date(start.getTime() + 24 * 60 * 60 * 1000);
				}
			} else if (task.dueDate instanceof Date) {
				end = task.dueDate;
				// 检查日期是否有效
				if (isNaN(end.getTime())) {
					end = new Date(start.getTime() + 24 * 60 * 60 * 1000);
				}
			} else {
				end = new Date(start.getTime() + 24 * 60 * 60 * 1000);
			}
		} else {
			end = new Date(start.getTime() + 24 * 60 * 60 * 1000); // 默认1天
		}

		// 确保结束时间不早于开始时间
		if (end.getTime() <= start.getTime()) {
			end = new Date(start.getTime() + 24 * 60 * 60 * 1000);
		}
		
		// 计算进度百分比
		let progress = 0;
		if (task.status === TaskStatus.COMPLETED) {
			progress = 100;
		} else if (task.status === TaskStatus.IN_PROGRESS) {
			progress = 50; // 默认进行中任务50%
		}

		return {
			id: task.id,
			name: task.title || '未命名任务',
			start,
			end,
			progress,
			type: (task.childTaskIds && task.childTaskIds.length > 0) ? GanttTaskType.PROJECT : GanttTaskType.TASK,
			dependencies: task.dependencies ? task.dependencies.map(dep => dep.taskId) : [],
			displayOrder: task.position || 0
		};
	}

	/**
	 * 批量转换任务为甘特图任务
	 */
	static tasksToGanttTasks(tasks: Task[]): GanttTask[] {
		return tasks
			.map(task => this.taskToGanttTask(task))
			.sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
	}

	/**
	 * 计算关键路径
	 */
	static calculateCriticalPath(tasks: Task[]): CriticalPathAnalysis {
		const taskMap = new Map(tasks.map(task => [task.id, task]));
		const criticalTasks: string[] = [];
		const slack = new Map<string, number>();
		
		// 简化的关键路径算法
		// 找到没有依赖的起始任务
		const startTasks = tasks.filter(task => task.dependencies.length === 0);
		
		// 找到没有后续任务的结束任务
		const endTasks = tasks.filter(task => {
			return !tasks.some(t => t.dependencies.some(dep => dep.taskId === task.id));
		});

		// 计算最早开始时间和最晚结束时间
		let earliestStart = new Date();
		let latestFinish = new Date();
		let totalDuration = 0;

		if (startTasks.length > 0) {
			earliestStart = new Date(Math.min(...startTasks.map(t => 
				(t.startDate || new Date()).getTime()
			)));
		}

		if (endTasks.length > 0) {
			latestFinish = new Date(Math.max(...endTasks.map(t => 
				(t.dueDate || new Date()).getTime()
			)));
		}

		totalDuration = Math.ceil((latestFinish.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24));

		// 简化处理：将所有有依赖关系的任务标记为关键任务
		tasks.forEach(task => {
			if (task.dependencies.length > 0 || 
				tasks.some(t => t.dependencies.some(dep => dep.taskId === task.id))) {
				criticalTasks.push(task.id);
				slack.set(task.id, 0); // 关键任务松弛时间为0
			} else {
				slack.set(task.id, 1); // 非关键任务给予1天松弛时间
			}
		});

		return {
			criticalTasks,
			totalDuration,
			earliestStart,
			latestFinish,
			slack
		};
	}

	/**
	 * 检测资源冲突
	 */
	static detectResourceConflicts(tasks: Task[]): ResourceConflict[] {
		const conflicts: ResourceConflict[] = [];
		const resourceMap = new Map<string, Task[]>();

		// 按负责人分组任务
		tasks.forEach(task => {
			if (task.assignee) {
				if (!resourceMap.has(task.assignee)) {
					resourceMap.set(task.assignee, []);
				}
				resourceMap.get(task.assignee)!.push(task);
			}
		});

		// 检测时间重叠
		resourceMap.forEach((assignedTasks, assignee) => {
			for (let i = 0; i < assignedTasks.length; i++) {
				for (let j = i + 1; j < assignedTasks.length; j++) {
					const task1 = assignedTasks[i];
					const task2 = assignedTasks[j];

					if (this.isTimeOverlap(task1, task2)) {
						conflicts.push({
							taskId: task1.id,
							conflictingTaskIds: [task2.id],
							resource: assignee,
							conflictType: ConflictType.TIME_OVERLAP,
							severity: ConflictSeverity.MEDIUM
						});
					}
				}
			}
		});

		return conflicts;
	}

	/**
	 * 检查两个任务是否时间重叠
	 */
	private static isTimeOverlap(task1: Task, task2: Task): boolean {
		const start1 = task1.startDate || new Date();
		const end1 = task1.dueDate || new Date(start1.getTime() + 24 * 60 * 60 * 1000);
		const start2 = task2.startDate || new Date();
		const end2 = task2.dueDate || new Date(start2.getTime() + 24 * 60 * 60 * 1000);

		const s1 = typeof start1 === 'string' ? new Date(start1) : start1;
		const e1 = typeof end1 === 'string' ? new Date(end1) : end1;
		const s2 = typeof start2 === 'string' ? new Date(start2) : start2;
		const e2 = typeof end2 === 'string' ? new Date(end2) : end2;

		return s1 < e2 && s2 < e1;
	}

	/**
	 * 获取默认甘特图配置
	 */
	static getDefaultConfig(): Partial<GanttViewConfig> {
		return {
			viewMode: GanttViewMode.DAY,
			columnWidth: 65,
			listCellWidth: '155px',
			rowHeight: 50,
			ganttHeight: 300,
			barCornerRadius: 3,
			handleWidth: 8,
			fontFamily: 'Arial, sans-serif',
			fontSize: '14px',
			barFill: 60,
			barProgressColor: '#a3a3ff',
			barProgressSelectedColor: '#8282f5',
			barBackgroundColor: '#b7b7b7',
			barBackgroundSelectedColor: '#aeb8c2',
			projectProgressColor: '#7db59a',
			projectProgressSelectedColor: '#59a985',
			projectBackgroundColor: '#fac465',
			projectBackgroundSelectedColor: '#f7bb53',
			milestoneBackgroundColor: '#f1c453',
			milestoneBackgroundSelectedColor: '#f29e4c',
			rtl: false
		};
	}
}