// Task data model based on design document
// Import unified types from core types
import { TaskStatus, Priority, BaseEntity } from '../types/core';

// Re-export core types for convenience
export { TaskStatus, Priority, BaseEntity } from '../types/core';

export enum DependencyType {
	FINISH_TO_START = 'fs',
	START_TO_START = 'ss',
	FINISH_TO_FINISH = 'ff',
	START_TO_FINISH = 'sf'
}

export interface TaskDependency {
	taskId: string;
	type: DependencyType;
}

export interface Task extends BaseEntity {
	projectId: string;
	title: string;
	description?: string;
	status: TaskStatus;
	priority: Priority;
	assignee?: string;
	parentTaskId?: string;
	childTaskIds: string[];
	dependencies: TaskDependency[];
	estimatedHours?: number;
	actualHours?: number;
	startDate?: Date;
	dueDate?: Date;
	completedDate?: Date;
	tags: string[];
	linkedNotes: string[]; // file paths
	sprintId?: string;
	position: number; // for kanban ordering
}

// Tasks plugin emoji mapping for compatibility
export const TASKS_EMOJI_MAP: Record<string, TaskStatus> = {
	'❌': TaskStatus.CANCELLED,
	'✅': TaskStatus.COMPLETED,
	'⏳': TaskStatus.IN_PROGRESS,
	'🔄': TaskStatus.IN_PROGRESS,
	'⏸️': TaskStatus.BLOCKED,
	'👁️': TaskStatus.REVIEW,
	'📝': TaskStatus.TODO,
	'⭐': TaskStatus.TODO, // high priority
	'🔥': TaskStatus.TODO, // urgent
	'📅': TaskStatus.TODO, // scheduled
	'📆': TaskStatus.TODO, // due date
	'🔁': TaskStatus.TODO, // recurring
};

export const STATUS_TO_EMOJI: Record<TaskStatus, string> = {
	[TaskStatus.TODO]: '📝',
	[TaskStatus.IN_PROGRESS]: '⏳',
	[TaskStatus.BLOCKED]: '⏸️',
	[TaskStatus.REVIEW]: '👁️',
	[TaskStatus.COMPLETED]: '✅',
	[TaskStatus.CANCELLED]: '❌'
};

// Utility functions for task management
export class TaskUtils {
	static createTask(title: string, projectId: string): Task {
		const now = new Date();
		return {
			id: this.generateId(),
			projectId,
			title,
			status: TaskStatus.TODO,
			priority: Priority.MEDIUM,
			childTaskIds: [],
			dependencies: [],
			tags: [],
			linkedNotes: [],
			position: 0,
			createdAt: now,
			updatedAt: now
		};
	}

	static updateTask(task: Task, updates: Partial<Task>): Task {
		return {
			...task,
			...updates,
			updatedAt: new Date()
		};
	}

	static addChildTask(parentTask: Task, childTaskId: string): Task {
		return {
			...parentTask,
			childTaskIds: [...parentTask.childTaskIds, childTaskId],
			updatedAt: new Date()
		};
	}

	static removeChildTask(parentTask: Task, childTaskId: string): Task {
		return {
			...parentTask,
			childTaskIds: parentTask.childTaskIds.filter(id => id !== childTaskId),
			updatedAt: new Date()
		};
	}

	static addDependency(task: Task, dependency: TaskDependency): Task {
		return {
			...task,
			dependencies: [...task.dependencies, dependency],
			updatedAt: new Date()
		};
	}

	static removeDependency(task: Task, taskId: string): Task {
		return {
			...task,
			dependencies: task.dependencies.filter(dep => dep.taskId !== taskId),
			updatedAt: new Date()
		};
	}

	static isOverdue(task: Task): boolean {
		if (!task.dueDate) return false;
		
		try {
			// 安全处理可能的字符串或Date对象
			const dueDate = typeof task.dueDate === 'string' ? new Date(task.dueDate) : task.dueDate;
			if (isNaN(dueDate.getTime())) return false;
			
			return new Date() > dueDate && task.status !== TaskStatus.COMPLETED;
		} catch (error) {
			console.warn('日期比较失败:', task.dueDate, error);
			return false;
		}
	}

	static canTransitionTo(currentStatus: TaskStatus, newStatus: TaskStatus): boolean {
		// Define valid status transitions
		const validTransitions: Record<TaskStatus, TaskStatus[]> = {
			[TaskStatus.TODO]: [TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
			[TaskStatus.IN_PROGRESS]: [TaskStatus.BLOCKED, TaskStatus.REVIEW, TaskStatus.COMPLETED, TaskStatus.TODO],
			[TaskStatus.BLOCKED]: [TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.CANCELLED],
			[TaskStatus.REVIEW]: [TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED, TaskStatus.TODO],
			[TaskStatus.COMPLETED]: [TaskStatus.TODO], // Allow reopening
			[TaskStatus.CANCELLED]: [TaskStatus.TODO] // Allow reactivation
		};

		return validTransitions[currentStatus]?.includes(newStatus) ?? false;
	}

	static parseTasksPluginEmoji(emoji: string): TaskStatus | null {
		return TASKS_EMOJI_MAP[emoji] || null;
	}

	static getEmojiForStatus(status: TaskStatus): string {
		return STATUS_TO_EMOJI[status] || '📝';
	}

	private static generateId(): string {
		return 'task_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
	}
}