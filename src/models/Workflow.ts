// Workflow engine interfaces and types

import { TaskStatus } from './Task';

export interface WorkflowTransition {
	from: TaskStatus;
	to: TaskStatus;
	conditions?: WorkflowCondition[];
	actions?: WorkflowAction[];
}

export interface WorkflowCondition {
	type: 'user_role' | 'task_property' | 'custom';
	property?: string;
	operator?: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
	value?: any;
	customFunction?: (task: any, context: any) => boolean;
}

export interface WorkflowAction {
	type: 'set_property' | 'send_notification' | 'create_task' | 'custom';
	property?: string;
	value?: any;
	customFunction?: (task: any, context: any) => void;
}

export interface WorkflowState {
	id: TaskStatus;
	name: string;
	description?: string;
	color?: string;
	isInitial?: boolean;
	isFinal?: boolean;
}

export interface Workflow {
	id: string;
	name: string;
	description?: string;
	states: WorkflowState[];
	transitions: WorkflowTransition[];
	createdAt: Date;
	updatedAt: Date;
}

// Default workflows
export const DEFAULT_WORKFLOW: Workflow = {
	id: 'default',
	name: 'Default Workflow',
	description: 'Standard task workflow with basic states',
	states: [
		{
			id: TaskStatus.TODO,
			name: 'To Do',
			description: 'Task is ready to be worked on',
			color: '#6b7280',
			isInitial: true
		},
		{
			id: TaskStatus.IN_PROGRESS,
			name: 'In Progress',
			description: 'Task is currently being worked on',
			color: '#3b82f6'
		},
		{
			id: TaskStatus.BLOCKED,
			name: 'Blocked',
			description: 'Task is blocked by external dependencies',
			color: '#ef4444'
		},
		{
			id: TaskStatus.REVIEW,
			name: 'Review',
			description: 'Task is ready for review',
			color: '#f59e0b'
		},
		{
			id: TaskStatus.COMPLETED,
			name: 'Completed',
			description: 'Task has been completed',
			color: '#10b981',
			isFinal: true
		},
		{
			id: TaskStatus.CANCELLED,
			name: 'Cancelled',
			description: 'Task has been cancelled',
			color: '#6b7280',
			isFinal: true
		}
	],
	transitions: [
		{ from: TaskStatus.TODO, to: TaskStatus.IN_PROGRESS },
		{ from: TaskStatus.TODO, to: TaskStatus.CANCELLED },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.BLOCKED },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.REVIEW },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.COMPLETED },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.TODO },
		{ from: TaskStatus.BLOCKED, to: TaskStatus.TODO },
		{ from: TaskStatus.BLOCKED, to: TaskStatus.IN_PROGRESS },
		{ from: TaskStatus.BLOCKED, to: TaskStatus.CANCELLED },
		{ from: TaskStatus.REVIEW, to: TaskStatus.IN_PROGRESS },
		{ from: TaskStatus.REVIEW, to: TaskStatus.COMPLETED },
		{ from: TaskStatus.REVIEW, to: TaskStatus.TODO },
		{ from: TaskStatus.COMPLETED, to: TaskStatus.TODO },
		{ from: TaskStatus.CANCELLED, to: TaskStatus.TODO }
	],
	createdAt: new Date(),
	updatedAt: new Date()
};

export const AGILE_WORKFLOW: Workflow = {
	id: 'agile',
	name: 'Agile Workflow',
	description: 'Agile development workflow with sprint-focused states',
	states: [
		{
			id: TaskStatus.TODO,
			name: 'Backlog',
			description: 'Task is in the product backlog',
			color: '#6b7280',
			isInitial: true
		},
		{
			id: TaskStatus.IN_PROGRESS,
			name: 'In Sprint',
			description: 'Task is in the current sprint and being worked on',
			color: '#3b82f6'
		},
		{
			id: TaskStatus.REVIEW,
			name: 'Code Review',
			description: 'Task is ready for code review',
			color: '#f59e0b'
		},
		{
			id: TaskStatus.COMPLETED,
			name: 'Done',
			description: 'Task meets definition of done',
			color: '#10b981',
			isFinal: true
		}
	],
	transitions: [
		{ from: TaskStatus.TODO, to: TaskStatus.IN_PROGRESS },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.REVIEW },
		{ from: TaskStatus.IN_PROGRESS, to: TaskStatus.TODO },
		{ from: TaskStatus.REVIEW, to: TaskStatus.COMPLETED },
		{ from: TaskStatus.REVIEW, to: TaskStatus.IN_PROGRESS },
		{ from: TaskStatus.COMPLETED, to: TaskStatus.TODO }
	],
	createdAt: new Date(),
	updatedAt: new Date()
};

// Utility functions for workflow management
export class WorkflowUtils {
	static validateWorkflow(workflow: Workflow): string[] {
		const errors: string[] = [];

		// Check for at least one initial state
		const initialStates = workflow.states.filter(s => s.isInitial);
		if (initialStates.length === 0) {
			errors.push('Workflow must have at least one initial state');
		}

		// Check for at least one final state
		const finalStates = workflow.states.filter(s => s.isFinal);
		if (finalStates.length === 0) {
			errors.push('Workflow must have at least one final state');
		}

		// Check that all transition states exist
		const stateIds = new Set(workflow.states.map(s => s.id));
		for (const transition of workflow.transitions) {
			if (!stateIds.has(transition.from)) {
				errors.push(`Transition references unknown 'from' state: ${transition.from}`);
			}
			if (!stateIds.has(transition.to)) {
				errors.push(`Transition references unknown 'to' state: ${transition.to}`);
			}
		}

		return errors;
	}

	static canTransition(workflow: Workflow, from: TaskStatus, to: TaskStatus): boolean {
		return workflow.transitions.some(t => t.from === from && t.to === to);
	}

	static getAvailableTransitions(workflow: Workflow, currentStatus: TaskStatus): TaskStatus[] {
		return workflow.transitions
			.filter(t => t.from === currentStatus)
			.map(t => t.to);
	}

	static getStateInfo(workflow: Workflow, status: TaskStatus): WorkflowState | undefined {
		return workflow.states.find(s => s.id === status);
	}

	static createCustomWorkflow(name: string, states: WorkflowState[], transitions: WorkflowTransition[]): Workflow {
		const now = new Date();
		return {
			id: 'custom_' + Date.now().toString(36),
			name,
			states,
			transitions,
			createdAt: now,
			updatedAt: now
		};
	}
}