// 报告数据模型

export enum ReportType {
	PROJECT_PROGRESS = 'project_progress',
	TIME_TRACKING = 'time_tracking',
	TEAM_PERFORMANCE = 'team_performance',
	TASK_COMPLETION = 'task_completion',
	SPRINT_SUMMARY = 'sprint_summary',
	CUSTOM = 'custom'
}

export enum ReportFormat {
	JSON = 'json',
	CSV = 'csv',
	MARKDOWN = 'markdown',
	HTML = 'html'
}

export interface ReportTemplate {
	id: string;
	name: string;
	description?: string;
	type: ReportType;
	sections: ReportSection[];
	filters: ReportFilter[];
	createdAt: Date;
	updatedAt: Date;
}

export interface ReportSection {
	id: string;
	title: string;
	type: 'chart' | 'table' | 'text' | 'metrics';
	config: any; // 具体配置根据类型而定
	order: number;
}

export interface ReportFilter {
	field: string;
	operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between';
	value: any;
}

export interface ProjectProgressReport {
	projectId: string;
	projectName: string;
	reportDate: Date;
	timeRange: {
		startDate: Date;
		endDate: Date;
	};
	
	// 基础统计
	totalTasks: number;
	completedTasks: number;
	inProgressTasks: number;
	blockedTasks: number;
	completionPercentage: number;
	
	// 时间分析
	plannedDuration?: number; // 计划工期（天）
	actualDuration?: number; // 实际工期（天）
	remainingDuration?: number; // 剩余工期（天）
	isOnSchedule: boolean;
	scheduleVariance: number; // 进度偏差（天）
	
	// 任务分析
	tasksByPriority: Record<string, number>;
	tasksByStatus: Record<string, number>;
	tasksByAssignee: Record<string, number>;
	overdueTasksCount: number;
	averageTaskDuration: number; // 平均任务完成时间（小时）
	
	// 趋势数据
	dailyProgress: DailyProgressPoint[];
	weeklyVelocity: WeeklyVelocityPoint[];
	
	// 里程碑
	milestones: MilestoneProgress[];
	
	// 风险和问题
	risks: ProjectRisk[];
	blockers: TaskBlocker[];
}

export interface DailyProgressPoint {
	date: Date;
	completedTasks: number;
	totalTasks: number;
	completionPercentage: number;
	hoursWorked?: number;
}

export interface WeeklyVelocityPoint {
	weekStart: Date;
	weekEnd: Date;
	tasksCompleted: number;
	storyPointsCompleted?: number;
	hoursWorked: number;
	velocity: number; // 任务完成速度
}

export interface MilestoneProgress {
	id: string;
	name: string;
	targetDate: Date;
	actualDate?: Date;
	status: 'pending' | 'completed' | 'overdue';
	progress: number; // 0-100
	associatedTasks: string[];
}

export interface ProjectRisk {
	id: string;
	description: string;
	severity: 'low' | 'medium' | 'high' | 'critical';
	probability: number; // 0-1
	impact: string;
	mitigation?: string;
	identifiedDate: Date;
}

export interface TaskBlocker {
	taskId: string;
	taskTitle: string;
	blockerDescription: string;
	blockedSince: Date;
	assignee?: string;
	priority: string;
}

export interface TimeTrackingReport {
	projectId: string;
	projectName: string;
	reportDate: Date;
	timeRange: {
		startDate: Date;
		endDate: Date;
	};
	
	// 总体时间统计
	totalEstimatedHours: number;
	totalActualHours: number;
	timeVariance: number; // 时间偏差
	timeVariancePercentage: number;
	
	// 按任务分析
	taskTimeAnalysis: TaskTimeAnalysis[];
	
	// 按人员分析
	teamTimeAnalysis: TeamMemberTimeAnalysis[];
	
	// 按时间段分析
	dailyTimeTracking: DailyTimePoint[];
	weeklyTimeTracking: WeeklyTimePoint[];
	
	// 效率分析
	productivityMetrics: ProductivityMetrics;
}

export interface TaskTimeAnalysis {
	taskId: string;
	taskTitle: string;
	estimatedHours: number;
	actualHours: number;
	timeVariance: number;
	efficiency: number; // 效率比率
	status: string;
	assignee?: string;
}

export interface TeamMemberTimeAnalysis {
	assignee: string;
	totalEstimatedHours: number;
	totalActualHours: number;
	tasksCompleted: number;
	averageTaskTime: number;
	efficiency: number;
	workload: number; // 工作负荷
}

export interface DailyTimePoint {
	date: Date;
	estimatedHours: number;
	actualHours: number;
	tasksWorked: number;
	productivity: number;
}

export interface WeeklyTimePoint {
	weekStart: Date;
	weekEnd: Date;
	totalHours: number;
	averageDailyHours: number;
	tasksCompleted: number;
	efficiency: number;
}

export interface ProductivityMetrics {
	overallEfficiency: number; // 整体效率
	timeAccuracy: number; // 时间估算准确性
	focusTime: number; // 专注时间比例
	multitaskingIndex: number; // 多任务处理指数
	peakProductivityHours: string[]; // 高效时间段
}

export interface TeamPerformanceReport {
	projectId: string;
	projectName: string;
	reportDate: Date;
	timeRange: {
		startDate: Date;
		endDate: Date;
	};
	
	// 团队概览
	teamSize: number;
	activeMembers: number;
	totalTasksAssigned: number;
	totalTasksCompleted: number;
	teamVelocity: number;
	
	// 个人绩效
	memberPerformance: MemberPerformance[];
	
	// 协作分析
	collaborationMetrics: CollaborationMetrics;
	
	// 质量指标
	qualityMetrics: QualityMetrics;
	
	// 团队健康度
	teamHealthMetrics: TeamHealthMetrics;
}

export interface MemberPerformance {
	assignee: string;
	tasksAssigned: number;
	tasksCompleted: number;
	completionRate: number;
	averageTaskTime: number;
	qualityScore: number;
	collaborationScore: number;
	onTimeDelivery: number; // 按时交付率
	workload: 'underloaded' | 'optimal' | 'overloaded';
}

export interface CollaborationMetrics {
	crossFunctionalTasks: number;
	sharedTasks: number;
	communicationFrequency: number;
	knowledgeSharing: number;
	teamCohesion: number;
}

export interface QualityMetrics {
	defectRate: number;
	reworkRate: number;
	reviewCoverage: number;
	testCoverage: number;
	customerSatisfaction: number;
}

export interface TeamHealthMetrics {
	burnoutRisk: number; // 0-1
	workLifeBalance: number;
	jobSatisfaction: number;
	skillDevelopment: number;
	retentionRisk: number;
}

// 报告生成配置
export interface ReportGenerationConfig {
	projectId: string;
	reportType: ReportType;
	timeRange: {
		startDate: Date;
		endDate: Date;
	};
	format: ReportFormat;
	template?: string; // 模板ID
	filters?: ReportFilter[];
	includeCharts?: boolean;
	includeTrends?: boolean;
	includeRecommendations?: boolean;
	outputPath?: string;
}

// 报告生成结果
export interface ReportGenerationResult {
	success: boolean;
	reportId: string;
	filePath?: string;
	data: any;
	generatedAt: Date;
	error?: string;
}

// 默认报告模板
export const DEFAULT_REPORT_TEMPLATES: ReportTemplate[] = [
	{
		id: 'project_progress_basic',
		name: '基础项目进度报告',
		description: '包含项目基本进度、任务统计和时间分析',
		type: ReportType.PROJECT_PROGRESS,
		sections: [
			{
				id: 'overview',
				title: '项目概览',
				type: 'metrics',
				config: { metrics: ['totalTasks', 'completionPercentage', 'remainingDuration'] },
				order: 1
			},
			{
				id: 'progress_chart',
				title: '进度趋势',
				type: 'chart',
				config: { chartType: 'line', dataSource: 'dailyProgress' },
				order: 2
			},
			{
				id: 'task_breakdown',
				title: '任务分解',
				type: 'table',
				config: { columns: ['title', 'status', 'assignee', 'dueDate'] },
				order: 3
			}
		],
		filters: [],
		createdAt: new Date(),
		updatedAt: new Date()
	}
];