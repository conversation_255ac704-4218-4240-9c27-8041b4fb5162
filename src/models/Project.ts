// Project data model based on design document
// Import unified types from core types
import { ProjectStatus, BaseEntity, Priority } from '../types/core';

// Re-export core types for convenience
export { ProjectStatus, BaseEntity, Priority } from '../types/core';

export interface ProjectSettings {
	workflowId: string;
	sprintDuration: number; // days
	autoProgressTracking: boolean;
	ganttViewEnabled: boolean;
}

export interface Project {
	id: string;
	name: string;
	description?: string;
	startDate: Date;
	endDate?: Date;
	status: ProjectStatus;
	priority: Priority;
	tags: string[];
	settings: ProjectSettings;
	createdAt: Date;
	updatedAt: Date;
}

// Default project settings
export const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {
	workflowId: 'default',
	sprintDuration: 14, // 2 weeks
	autoProgressTracking: true,
	ganttViewEnabled: true
};

// Utility functions for project management
export class ProjectUtils {
	static createProject(name: string, description?: string): Project {
		const now = new Date();
		return {
			id: this.generateId(),
			name,
			description,
			startDate: now,
			status: ProjectStatus.PLANNING,
			priority: Priority.MEDIUM,
			tags: [],
			settings: { ...DEFAULT_PROJECT_SETTINGS },
			createdAt: now,
			updatedAt: now
		};
	}

	static updateProject(project: Project, updates: Partial<Project>): Project {
		return {
			...project,
			...updates,
			updatedAt: new Date()
		};
	}

	static calculateProgress(project: Project, tasks: any[]): number {
		if (!tasks || tasks.length === 0) return 0;
		
		const completedTasks = tasks.filter(task => task.status === 'completed');
		return Math.round((completedTasks.length / tasks.length) * 100);
	}

	static isOverdue(project: Project): boolean {
		if (!project.endDate) return false;
		return new Date() > project.endDate && project.status !== ProjectStatus.COMPLETED;
	}

	private static generateId(): string {
		return 'proj_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
	}
}