// Data validation schemas for type safety and data integrity

import { Project, ProjectStatus } from './Project';
import { Priority } from '../types/core';
import { Task, TaskStatus, DependencyType } from './Task';
import { Sprint, SprintStatus } from './Sprint';
import { Workflow } from './Workflow';

// Validation result interface
export interface ValidationResult {
	isValid: boolean;
	errors: string[];
}

// Project validation
export class ProjectValidator {
	static validate(project: Partial<Project>): ValidationResult {
		const errors: string[] = [];

		// Required fields
		if (!project.id) {
			errors.push('Project ID is required');
		}
		if (!project.name || project.name.trim().length === 0) {
			errors.push('Project name is required');
		}
		if (!project.startDate) {
			errors.push('Project start date is required');
		}
		if (!project.status) {
			errors.push('Project status is required');
		}
		if (!project.priority) {
			errors.push('Project priority is required');
		}

		// Validate enums
		if (project.status && !Object.values(ProjectStatus).includes(project.status)) {
			errors.push('Invalid project status');
		}
		if (project.priority && !Object.values(Priority).includes(project.priority)) {
			errors.push('Invalid project priority');
		}

		// Date validation
		if (project.startDate && project.endDate) {
			if (project.startDate > project.endDate) {
				errors.push('Project start date must be before end date');
			}
		}

		// Name length validation
		if (project.name && project.name.length > 200) {
			errors.push('Project name must be less than 200 characters');
		}

		// Description length validation
		if (project.description && project.description.length > 1000) {
			errors.push('Project description must be less than 1000 characters');
		}

		// Tags validation
		if (project.tags) {
			if (!Array.isArray(project.tags)) {
				errors.push('Project tags must be an array');
			} else {
				for (const tag of project.tags) {
					if (typeof tag !== 'string' || tag.trim().length === 0) {
						errors.push('All project tags must be non-empty strings');
						break;
					}
				}
			}
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}
}

// Task validation
export class TaskValidator {
	static validate(task: Partial<Task>): ValidationResult {
		const errors: string[] = [];

		// Required fields
		if (!task.id) {
			errors.push('Task ID is required');
		}
		if (!task.projectId) {
			errors.push('Task project ID is required');
		}
		if (!task.title || task.title.trim().length === 0) {
			errors.push('Task title is required');
		}
		if (!task.status) {
			errors.push('Task status is required');
		}
		if (!task.priority) {
			errors.push('Task priority is required');
		}

		// Validate enums
		if (task.status && !Object.values(TaskStatus).includes(task.status)) {
			errors.push('Invalid task status');
		}
		if (task.priority && !Object.values(Priority).includes(task.priority)) {
			errors.push('Invalid task priority');
		}

		// Date validation
		if (task.startDate && task.dueDate) {
			if (task.startDate > task.dueDate) {
				errors.push('Task start date must be before due date');
			}
		}
		if (task.completedDate && task.status !== TaskStatus.COMPLETED) {
			errors.push('Completed date can only be set for completed tasks');
		}

		// Title length validation
		if (task.title && task.title.length > 300) {
			errors.push('Task title must be less than 300 characters');
		}

		// Description length validation
		if (task.description && task.description.length > 2000) {
			errors.push('Task description must be less than 2000 characters');
		}

		// Hours validation
		if (task.estimatedHours !== undefined && task.estimatedHours < 0) {
			errors.push('Estimated hours must be non-negative');
		}
		if (task.actualHours !== undefined && task.actualHours < 0) {
			errors.push('Actual hours must be non-negative');
		}

		// Position validation
		if (task.position !== undefined && task.position < 0) {
			errors.push('Task position must be non-negative');
		}

		// Arrays validation
		if (task.childTaskIds && !Array.isArray(task.childTaskIds)) {
			errors.push('Child task IDs must be an array');
		}
		if (task.dependencies && !Array.isArray(task.dependencies)) {
			errors.push('Task dependencies must be an array');
		}
		if (task.tags && !Array.isArray(task.tags)) {
			errors.push('Task tags must be an array');
		}
		if (task.linkedNotes && !Array.isArray(task.linkedNotes)) {
			errors.push('Linked notes must be an array');
		}

		// Dependencies validation
		if (task.dependencies) {
			for (const dep of task.dependencies) {
				if (!dep.taskId || typeof dep.taskId !== 'string') {
					errors.push('Dependency task ID must be a non-empty string');
					break;
				}
				if (!Object.values(DependencyType).includes(dep.type)) {
					errors.push('Invalid dependency type');
					break;
				}
			}
		}

		// Self-reference validation
		if (task.parentTaskId === task.id) {
			errors.push('Task cannot be its own parent');
		}
		if (task.childTaskIds && task.childTaskIds.includes(task.id!)) {
			errors.push('Task cannot be its own child');
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}
}

// Sprint validation
export class SprintValidator {
	static validate(sprint: Partial<Sprint>): ValidationResult {
		const errors: string[] = [];

		// Required fields
		if (!sprint.id) {
			errors.push('Sprint ID is required');
		}
		if (!sprint.projectId) {
			errors.push('Sprint project ID is required');
		}
		if (!sprint.name || sprint.name.trim().length === 0) {
			errors.push('Sprint name is required');
		}
		if (!sprint.startDate) {
			errors.push('Sprint start date is required');
		}
		if (!sprint.endDate) {
			errors.push('Sprint end date is required');
		}
		if (!sprint.status) {
			errors.push('Sprint status is required');
		}

		// Validate enums
		if (sprint.status && !Object.values(SprintStatus).includes(sprint.status)) {
			errors.push('Invalid sprint status');
		}

		// Date validation
		if (sprint.startDate && sprint.endDate) {
			if (sprint.startDate >= sprint.endDate) {
				errors.push('Sprint start date must be before end date');
			}
			
			// Sprint duration validation (typically 1-4 weeks)
			const duration = sprint.endDate.getTime() - sprint.startDate.getTime();
			const days = duration / (1000 * 60 * 60 * 24);
			if (days < 1) {
				errors.push('Sprint must be at least 1 day long');
			}
			if (days > 28) {
				errors.push('Sprint should not exceed 4 weeks');
			}
		}

		// Name length validation
		if (sprint.name && sprint.name.length > 100) {
			errors.push('Sprint name must be less than 100 characters');
		}

		// Goal length validation
		if (sprint.goal && sprint.goal.length > 500) {
			errors.push('Sprint goal must be less than 500 characters');
		}

		// Velocity validation
		if (sprint.velocity !== undefined && sprint.velocity < 0) {
			errors.push('Sprint velocity must be non-negative');
		}

		// Task IDs validation
		if (sprint.taskIds && !Array.isArray(sprint.taskIds)) {
			errors.push('Sprint task IDs must be an array');
		}

		// Burndown data validation
		if (sprint.burndownData && !Array.isArray(sprint.burndownData)) {
			errors.push('Sprint burndown data must be an array');
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}
}

// Workflow validation
export class WorkflowValidator {
	static validate(workflow: Partial<Workflow>): ValidationResult {
		const errors: string[] = [];

		// Required fields
		if (!workflow.id) {
			errors.push('Workflow ID is required');
		}
		if (!workflow.name || workflow.name.trim().length === 0) {
			errors.push('Workflow name is required');
		}
		if (!workflow.states || !Array.isArray(workflow.states)) {
			errors.push('Workflow states must be an array');
		}
		if (!workflow.transitions || !Array.isArray(workflow.transitions)) {
			errors.push('Workflow transitions must be an array');
		}

		// States validation
		if (workflow.states) {
			if (workflow.states.length === 0) {
				errors.push('Workflow must have at least one state');
			}

			const stateIds = new Set<string>();
			let hasInitial = false;
			let hasFinal = false;

			for (const state of workflow.states) {
				if (!state.id) {
					errors.push('All workflow states must have an ID');
					break;
				}
				if (stateIds.has(state.id)) {
					errors.push('Duplicate state ID found');
					break;
				}
				stateIds.add(state.id);

				if (!state.name || state.name.trim().length === 0) {
					errors.push('All workflow states must have a name');
					break;
				}

				if (state.isInitial) hasInitial = true;
				if (state.isFinal) hasFinal = true;
			}

			if (!hasInitial) {
				errors.push('Workflow must have at least one initial state');
			}
			if (!hasFinal) {
				errors.push('Workflow must have at least one final state');
			}
		}

		// Transitions validation
		if (workflow.transitions && workflow.states) {
			const stateIds = new Set(workflow.states.map(s => s.id));
			
			for (const transition of workflow.transitions) {
				if (!transition.from) {
					errors.push('All transitions must have a from state');
					break;
				}
				if (!transition.to) {
					errors.push('All transitions must have a to state');
					break;
				}
				if (!stateIds.has(transition.from)) {
					errors.push(`Transition references unknown from state: ${transition.from}`);
					break;
				}
				if (!stateIds.has(transition.to)) {
					errors.push(`Transition references unknown to state: ${transition.to}`);
					break;
				}
			}
		}

		return {
			isValid: errors.length === 0,
			errors
		};
	}
}

// General utility functions
export class ValidationUtils {
	static isValidId(id: string): boolean {
		return typeof id === 'string' && id.trim().length > 0 && id.length <= 100;
	}

	static isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	static isValidUrl(url: string): boolean {
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	static sanitizeString(input: string): string {
		return input.trim().replace(/\s+/g, ' ');
	}

	static validateDateRange(startDate: Date, endDate: Date): boolean {
		return startDate < endDate;
	}
}