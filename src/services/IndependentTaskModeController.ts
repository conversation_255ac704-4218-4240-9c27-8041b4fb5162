// 独立任务模式控制器 - 管理独立任务模式的核心逻辑

import { App, Notice } from 'obsidian';
import { Task, TaskStatus, Priority, TaskUtils } from '../models';
import { TaskRepository } from './TaskRepository';
import { DataManager } from './DataManager';

export enum TaskMode {
	PROJECT_BASED = 'project_based',
	INDEPENDENT = 'independent'
}

export interface IndependentTaskModeSettings {
	currentMode: TaskMode;
	independentProjectId: string; // 虚拟项目ID用于存储独立任务
	autoMigrationEnabled: boolean;
	defaultPriority: Priority;
	enableAdvancedFiltering: boolean;
	enableBatchOperations: boolean;
}

export interface TaskModeStats {
	totalTasks: number;
	completedTasks: number;
	inProgressTasks: number;
	overdueTasks: number;
	tasksByPriority: Record<Priority, number>;
	tasksByStatus: Record<TaskStatus, number>;
}

/**
 * 独立任务模式控制器
 * 负责管理独立任务模式的切换、数据管理和状态持久化
 */
export class IndependentTaskModeController {
	private app: App;
	private taskRepository: TaskRepository;
	private dataManager: DataManager;
	private settings: IndependentTaskModeSettings;
	private readonly SETTINGS_KEY = 'independent_task_mode_settings';
	private readonly INDEPENDENT_PROJECT_NAME = '__INDEPENDENT_TASKS__';

	constructor(
		app: App,
		taskRepository: TaskRepository,
		dataManager: DataManager
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.dataManager = dataManager;
		
		// 默认设置
		this.settings = {
			currentMode: TaskMode.PROJECT_BASED,
			independentProjectId: this.generateIndependentProjectId(),
			autoMigrationEnabled: false,
			defaultPriority: Priority.MEDIUM,
			enableAdvancedFiltering: true,
			enableBatchOperations: true
		};
	}

	/**
	 * 初始化控制器
	 */
	async initialize(): Promise<void> {
		try {
			await this.loadSettings();
			await this.ensureIndependentProjectExists();
			console.log('IndependentTaskModeController initialized successfully');
		} catch (error) {
			console.error('Failed to initialize IndependentTaskModeController:', error);
			throw error;
		}
	}

	/**
	 * 获取当前模式
	 */
	getCurrentMode(): TaskMode {
		return this.settings.currentMode;
	}

	/**
	 * 切换到独立任务模式
	 */
	async switchToIndependentMode(): Promise<void> {
		if (this.settings.currentMode === TaskMode.INDEPENDENT) {
			new Notice('已经处于独立任务模式');
			return;
		}

		try {
			this.settings.currentMode = TaskMode.INDEPENDENT;
			await this.saveSettings();
			
			new Notice('已切换到独立任务模式');
			console.log('Switched to independent task mode');
		} catch (error) {
			console.error('Failed to switch to independent mode:', error);
			throw new Error(`切换到独立任务模式失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 切换到项目模式
	 */
	async switchToProjectMode(): Promise<void> {
		if (this.settings.currentMode === TaskMode.PROJECT_BASED) {
			new Notice('已经处于项目模式');
			return;
		}

		try {
			this.settings.currentMode = TaskMode.PROJECT_BASED;
			await this.saveSettings();
			
			new Notice('已切换到项目模式');
			console.log('Switched to project-based mode');
		} catch (error) {
			console.error('Failed to switch to project mode:', error);
			throw new Error(`切换到项目模式失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 检查是否为独立任务模式
	 */
	isIndependentMode(): boolean {
		return this.settings.currentMode === TaskMode.INDEPENDENT;
	}

	/**
	 * 获取独立项目ID
	 */
	getIndependentProjectId(): string {
		return this.settings.independentProjectId;
	}

	/**
	 * 创建独立任务
	 */
	async createIndependentTask(options: {
		title: string;
		description?: string;
		priority?: Priority;
		assignee?: string;
		estimatedHours?: number;
		startDate?: Date;
		dueDate?: Date;
		tags?: string[];
		linkedNotes?: string[];
	}): Promise<Task> {
		if (!this.isIndependentMode()) {
			throw new Error('当前不在独立任务模式，无法创建独立任务');
		}

		try {
			const now = new Date();
			const startDate = options.startDate || now;
			const dueDate = options.dueDate || new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);

			const task: Task = {
				id: this.generateTaskId(),
				projectId: this.settings.independentProjectId,
				title: options.title,
				description: options.description,
				status: TaskStatus.TODO,
				priority: options.priority || this.settings.defaultPriority,
				assignee: options.assignee,
				parentTaskId: undefined, // 独立任务不支持层级关系
				childTaskIds: [],
				dependencies: [],
				estimatedHours: options.estimatedHours,
				startDate,
				dueDate,
				tags: options.tags || [],
				linkedNotes: options.linkedNotes || [],
				sprintId: undefined, // 独立任务不支持Sprint
				position: await this.getNextPosition(),
				createdAt: now,
				updatedAt: now
			};

			const createdTask = await this.taskRepository.create(task);
			new Notice(`独立任务 "${createdTask.title}" 已创建`);
			
			return createdTask;
		} catch (error) {
			console.error('Failed to create independent task:', error);
			throw new Error(`创建独立任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 获取所有独立任务
	 */
	async getIndependentTasks(): Promise<Task[]> {
		try {
			return await this.taskRepository.findByProject(this.settings.independentProjectId);
		} catch (error) {
			console.error('Failed to get independent tasks:', error);
			return [];
		}
	}

	/**
	 * 更新独立任务
	 */
	async updateIndependentTask(taskId: string, updates: {
		title?: string;
		description?: string;
		status?: TaskStatus;
		priority?: Priority;
		assignee?: string;
		estimatedHours?: number;
		actualHours?: number;
		startDate?: Date;
		dueDate?: Date;
		completedDate?: Date;
		tags?: string[];
		linkedNotes?: string[];
		position?: number;
	}): Promise<Task> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		if (task.projectId !== this.settings.independentProjectId) {
			throw new Error('只能更新独立任务');
		}

		try {
			const updatedTask = await this.taskRepository.update(taskId, {
				...updates,
				updatedAt: new Date()
			});

			if (!updatedTask) {
				throw new Error(`更新任务失败: ${taskId}`);
			}

			new Notice(`独立任务 "${updatedTask.title}" 已更新`);
			return updatedTask;
		} catch (error) {
			console.error('Failed to update independent task:', error);
			throw new Error(`更新独立任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 删除独立任务
	 */
	async deleteIndependentTask(taskId: string): Promise<void> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		if (task.projectId !== this.settings.independentProjectId) {
			throw new Error('只能删除独立任务');
		}

		try {
			await this.taskRepository.delete(taskId);
			new Notice(`独立任务 "${task.title}" 已删除`);
		} catch (error) {
			console.error('Failed to delete independent task:', error);
			throw new Error(`删除独立任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 获取独立任务统计信息
	 */
	async getIndependentTaskStats(): Promise<TaskModeStats> {
		try {
			const tasks = await this.getIndependentTasks();
			
			const stats: TaskModeStats = {
				totalTasks: tasks.length,
				completedTasks: 0,
				inProgressTasks: 0,
				overdueTasks: 0,
				tasksByPriority: {
					[Priority.LOW]: 0,
					[Priority.MEDIUM]: 0,
					[Priority.HIGH]: 0,
					[Priority.CRITICAL]: 0
				},
				tasksByStatus: {
					[TaskStatus.TODO]: 0,
					[TaskStatus.IN_PROGRESS]: 0,
					[TaskStatus.BLOCKED]: 0,
					[TaskStatus.REVIEW]: 0,
					[TaskStatus.COMPLETED]: 0,
					[TaskStatus.CANCELLED]: 0
				}
			};

			for (const task of tasks) {
				// 按状态统计
				stats.tasksByStatus[task.status]++;
				
				// 按优先级统计
				stats.tasksByPriority[task.priority]++;
				
				// 特殊状态统计
				if (task.status === TaskStatus.COMPLETED) {
					stats.completedTasks++;
				} else if (task.status === TaskStatus.IN_PROGRESS) {
					stats.inProgressTasks++;
				}
				
				// 逾期任务统计
				if (TaskUtils.isOverdue(task)) {
					stats.overdueTasks++;
				}
			}

			return stats;
		} catch (error) {
			console.error('Failed to get independent task stats:', error);
			// 返回空统计信息
			return {
				totalTasks: 0,
				completedTasks: 0,
				inProgressTasks: 0,
				overdueTasks: 0,
				tasksByPriority: {
					[Priority.LOW]: 0,
					[Priority.MEDIUM]: 0,
					[Priority.HIGH]: 0,
					[Priority.CRITICAL]: 0
				},
				tasksByStatus: {
					[TaskStatus.TODO]: 0,
					[TaskStatus.IN_PROGRESS]: 0,
					[TaskStatus.BLOCKED]: 0,
					[TaskStatus.REVIEW]: 0,
					[TaskStatus.COMPLETED]: 0,
					[TaskStatus.CANCELLED]: 0
				}
			};
		}
	}

	/**
	 * 更新设置
	 */
	async updateSettings(updates: Partial<IndependentTaskModeSettings>): Promise<void> {
		try {
			this.settings = { ...this.settings, ...updates };
			await this.saveSettings();
			console.log('Independent task mode settings updated');
		} catch (error) {
			console.error('Failed to update settings:', error);
			throw new Error(`更新设置失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 获取当前设置
	 */
	getSettings(): IndependentTaskModeSettings {
		return { ...this.settings };
	}

	/**
	 * 批量更新任务状态
	 */
	async batchUpdateTaskStatus(taskIds: string[], status: TaskStatus): Promise<{
		success: number;
		failed: number;
		errors: string[];
	}> {
		if (!this.settings.enableBatchOperations) {
			throw new Error('批量操作已禁用');
		}

		const result = {
			success: 0,
			failed: 0,
			errors: [] as string[]
		};

		for (const taskId of taskIds) {
			try {
				await this.updateIndependentTask(taskId, { status });
				result.success++;
			} catch (error) {
				result.failed++;
				result.errors.push(`任务 ${taskId}: ${error instanceof Error ? error.message : String(error)}`);
			}
		}

		new Notice(`批量更新完成: 成功 ${result.success} 个，失败 ${result.failed} 个`);
		return result;
	}

	/**
	 * 批量删除任务
	 */
	async batchDeleteTasks(taskIds: string[]): Promise<{
		success: number;
		failed: number;
		errors: string[];
	}> {
		if (!this.settings.enableBatchOperations) {
			throw new Error('批量操作已禁用');
		}

		const result = {
			success: 0,
			failed: 0,
			errors: [] as string[]
		};

		for (const taskId of taskIds) {
			try {
				await this.deleteIndependentTask(taskId);
				result.success++;
			} catch (error) {
				result.failed++;
				result.errors.push(`任务 ${taskId}: ${error instanceof Error ? error.message : String(error)}`);
			}
		}

		new Notice(`批量删除完成: 成功 ${result.success} 个，失败 ${result.failed} 个`);
		return result;
	}

	/**
	 * 清理资源
	 */
	cleanup(): void {
		// 清理任何需要清理的资源
		console.log('IndependentTaskModeController cleaned up');
	}

	/**
	 * 加载设置
	 */
	private async loadSettings(): Promise<void> {
		try {
			const savedSettings = await this.dataManager.load(this.SETTINGS_KEY);
			if (savedSettings) {
				this.settings = { ...this.settings, ...savedSettings };
			}
		} catch (error) {
			console.warn('Failed to load independent task mode settings, using defaults:', error);
		}
	}

	/**
	 * 保存设置
	 */
	private async saveSettings(): Promise<void> {
		try {
			await this.dataManager.save(this.SETTINGS_KEY, this.settings);
		} catch (error) {
			console.error('Failed to save independent task mode settings:', error);
			throw error;
		}
	}

	/**
	 * 确保独立项目存在
	 */
	private async ensureIndependentProjectExists(): Promise<void> {
		try {
			// 检查独立项目是否存在
			const existingTasks = await this.taskRepository.findByProject(this.settings.independentProjectId);
			
			// 如果没有任务，说明独立项目可能不存在，但这是正常的
			// 独立任务模式不需要真实的项目记录，只需要一个虚拟的项目ID
			console.log(`Independent project initialized with ID: ${this.settings.independentProjectId}`);
		} catch (error) {
			console.error('Failed to ensure independent project exists:', error);
			throw error;
		}
	}

	/**
	 * 生成独立项目ID
	 */
	private generateIndependentProjectId(): string {
		return 'independent_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
	}

	/**
	 * 生成任务ID
	 */
	private generateTaskId(): string {
		return 'task_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
	}

	/**
	 * 获取下一个位置
	 */
	private async getNextPosition(): Promise<number> {
		try {
			const tasks = await this.getIndependentTasks();
			const maxPosition = Math.max(0, ...tasks.map(t => t.position || 0));
			return maxPosition + 1;
		} catch (error) {
			console.error('Failed to get next position:', error);
			return 1;
		}
	}
}