// Project management core functionality

import { App, Notice } from 'obsidian';
import { Project, ProjectStatus, Task, TaskStatus, Priority } from '../models';
import { ProjectRepository } from './ProjectRepository';
import { TaskRepository } from './TaskRepository';
import { PTMFileHandler } from './PTMFileHandler';

export interface ProjectStatistics {
	totalTasks: number;
	completedTasks: number;
	inProgressTasks: number;
	blockedTasks: number;
	todoTasks: number;
	completionPercentage: number;
	averageTaskDuration?: number;
	estimatedCompletionDate?: Date;
	isOverdue: boolean;
	daysRemaining?: number;
}

export interface ProjectCreateOptions {
	name: string;
	description?: string;
	startDate?: Date;
	endDate?: Date;
	priority?: Priority;
	tags?: string[];
	createPTMFile?: boolean;
	ptmFilePath?: string;
}

export interface ProjectUpdateOptions {
	name?: string;
	description?: string;
	startDate?: Date;
	endDate?: Date;
	status?: ProjectStatus;
	priority?: Priority;
	tags?: string[];
}

export interface ProjectDeleteOptions {
	deleteAssociatedTasks?: boolean;
	moveTasksToProject?: string;
	createBackup?: boolean;
}

export class ProjectManager {
	private app: App;
	private projectRepository: ProjectRepository;
	private taskRepository: TaskRepository;
	private ptmFileHandler: PTMFileHandler;

	constructor(
		app: App,
		projectRepository: ProjectRepository,
		taskRepository: TaskRepository,
		ptmFileHandler: PTMFileHandler
	) {
		this.app = app;
		this.projectRepository = projectRepository;
		this.taskRepository = taskRepository;
		this.ptmFileHandler = ptmFileHandler;
	}

	/**
	 * Create a new project
	 */
	async createProject(options: ProjectCreateOptions): Promise<Project> {
		const now = new Date();
		const project: Project = {
			id: this.generateProjectId(),
			name: options.name,
			description: options.description,
			startDate: options.startDate || now,
			endDate: options.endDate,
			status: ProjectStatus.PLANNING,
			priority: options.priority || Priority.MEDIUM,
			tags: options.tags || [],
			settings: {
				workflowId: 'default',
				sprintDuration: 14,
				autoProgressTracking: true,
				ganttViewEnabled: true
			},
			createdAt: now,
			updatedAt: now
		};

		// Save project to repository
		await this.projectRepository.create(project);

		// Create PTM file if requested
		if (options.createPTMFile) {
			const ptmPath = options.ptmFilePath || `${project.name}.ptm`;
			try {
				await this.ptmFileHandler.createPTMFile(ptmPath, project);
			} catch (error) {
				console.warn('Failed to create PTM file:', error);
			}
		}

		new Notice(`项目 "${project.name}" 已创建`);
		return project;
	}

	/**
	 * Get project by ID
	 */
	async getProject(projectId: string): Promise<Project | null> {
		return await this.projectRepository.findById(projectId);
	}

	/**
	 * Get all projects
	 */
	async getAllProjects(): Promise<Project[]> {
		return await this.projectRepository.findAll();
	}

	/**
	 * Update project
	 */
	async updateProject(projectId: string, updates: ProjectUpdateOptions): Promise<Project> {
		const existingProject = await this.projectRepository.findById(projectId);
		if (!existingProject) {
			throw new Error(`项目不存在: ${projectId}`);
		}

		const updatedProject: Project = {
			...existingProject,
			...updates,
			updatedAt: new Date()
		};

		// Handle status transitions
		if (updates.status && updates.status !== existingProject.status) {
			await this.handleStatusTransition(existingProject, updatedProject);
		}

		await this.projectRepository.update(projectId, updatedProject);
		new Notice(`项目 "${updatedProject.name}" 已更新`);
		
		return updatedProject;
	}

	/**
	 * Delete project with options for handling associated tasks
	 */
	async deleteProject(projectId: string, options: ProjectDeleteOptions = {}): Promise<void> {
		const project = await this.projectRepository.findById(projectId);
		if (!project) {
			throw new Error(`项目不存在: ${projectId}`);
		}

		// Get associated tasks
		const associatedTasks = await this.taskRepository.findByProject(projectId);

		// Handle associated tasks based on options
		if (associatedTasks.length > 0) {
			if (options.deleteAssociatedTasks) {
				// Delete all associated tasks
				for (const task of associatedTasks) {
					await this.taskRepository.delete(task.id);
				}
				new Notice(`已删除 ${associatedTasks.length} 个关联任务`);
			} else if (options.moveTasksToProject) {
				// Move tasks to another project
				const targetProject = await this.projectRepository.findById(options.moveTasksToProject);
				if (!targetProject) {
					throw new Error(`目标项目不存在: ${options.moveTasksToProject}`);
				}

				for (const task of associatedTasks) {
					const updatedTask = { ...task, projectId: options.moveTasksToProject };
					await this.taskRepository.update(task.id, updatedTask);
				}
				new Notice(`已将 ${associatedTasks.length} 个任务移动到项目 "${targetProject.name}"`);
			} else {
				// Ask user what to do with tasks
				throw new Error(`项目包含 ${associatedTasks.length} 个任务，请指定如何处理这些任务`);
			}
		}

		// Create backup if requested
		if (options.createBackup) {
			try {
				// This would create a backup of project data
				console.log(`Creating backup for project: ${project.name}`);
			} catch (error) {
				console.warn('Failed to create project backup:', error);
			}
		}

		// Delete the project
		await this.projectRepository.delete(projectId);
		new Notice(`项目 "${project.name}" 已删除`);
	}

	/**
	 * Get project statistics
	 */
	async getProjectStatistics(projectId: string): Promise<ProjectStatistics> {
		const project = await this.projectRepository.findById(projectId);
		if (!project) {
			throw new Error(`项目不存在: ${projectId}`);
		}

		const tasks = await this.taskRepository.findByProject(projectId);
		
		const totalTasks = tasks.length;
		const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
		const inProgressTasks = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
		const blockedTasks = tasks.filter(t => t.status === TaskStatus.BLOCKED).length;
		const todoTasks = tasks.filter(t => t.status === TaskStatus.TODO).length;
		
		const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
		
		// Calculate average task duration for completed tasks
		const completedTasksWithDuration = tasks.filter(t => 
			t.status === TaskStatus.COMPLETED && 
			t.completedDate && 
			t.createdAt
		);
		
		let averageTaskDuration: number | undefined;
		if (completedTasksWithDuration.length > 0) {
			const totalDuration = completedTasksWithDuration.reduce((sum, task) => {
				const duration = task.completedDate!.getTime() - task.createdAt.getTime();
				return sum + duration;
			}, 0);
			averageTaskDuration = totalDuration / completedTasksWithDuration.length;
		}

		// Calculate estimated completion date
		let estimatedCompletionDate: Date | undefined;
		if (averageTaskDuration && inProgressTasks + todoTasks > 0) {
			const remainingTasks = inProgressTasks + todoTasks;
			const estimatedRemainingTime = remainingTasks * averageTaskDuration;
			estimatedCompletionDate = new Date(Date.now() + estimatedRemainingTime);
		}

		// Check if project is overdue
		const now = new Date();
		const isOverdue = project.endDate ? now > project.endDate && project.status !== ProjectStatus.COMPLETED : false;
		
		// Calculate days remaining
		let daysRemaining: number | undefined;
		if (project.endDate) {
			const timeDiff = project.endDate.getTime() - now.getTime();
			daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
		}

		return {
			totalTasks,
			completedTasks,
			inProgressTasks,
			blockedTasks,
			todoTasks,
			completionPercentage,
			averageTaskDuration,
			estimatedCompletionDate,
			isOverdue,
			daysRemaining
		};
	}

	/**
	 * Get projects by status
	 */
	async getProjectsByStatus(status: ProjectStatus): Promise<Project[]> {
		const allProjects = await this.projectRepository.findAll();
		return allProjects.filter(p => p.status === status);
	}

	/**
	 * Get overdue projects
	 */
	async getOverdueProjects(): Promise<Project[]> {
		const now = new Date();
		const allProjects = await this.projectRepository.findAll();
		
		return allProjects.filter(project => 
			project.endDate && 
			now > project.endDate && 
			project.status !== ProjectStatus.COMPLETED &&
			project.status !== ProjectStatus.CANCELLED
		);
	}

	/**
	 * Archive completed projects
	 */
	async archiveCompletedProjects(): Promise<number> {
		const completedProjects = await this.getProjectsByStatus(ProjectStatus.COMPLETED);
		let archivedCount = 0;

		for (const project of completedProjects) {
			// In a real implementation, this might move projects to an archive
			// For now, we'll just add an archived tag
			const updatedProject = {
				...project,
				tags: [...project.tags, 'archived'],
				updatedAt: new Date()
			};
			
			await this.projectRepository.update(project.id, updatedProject);
			archivedCount++;
		}

		if (archivedCount > 0) {
			new Notice(`已归档 ${archivedCount} 个已完成项目`);
		}

		return archivedCount;
	}

	/**
	 * Handle project status transitions
	 */
	private async handleStatusTransition(oldProject: Project, newProject: Project): Promise<void> {
		const oldStatus = oldProject.status;
		const newStatus = newProject.status;

		console.log(`Project ${oldProject.name} status changing from ${oldStatus} to ${newStatus}`);

		// Handle specific transitions
		switch (newStatus) {
			case ProjectStatus.ACTIVE:
				if (oldStatus === ProjectStatus.PLANNING) {
					// Project is starting - could trigger notifications
					console.log(`Project ${oldProject.name} is now active`);
				}
				break;

			case ProjectStatus.COMPLETED:
				// Mark completion date, calculate final statistics
				console.log(`Project ${oldProject.name} has been completed`);
				break;

			case ProjectStatus.CANCELLED:
				// Handle cancellation logic
				console.log(`Project ${oldProject.name} has been cancelled`);
				break;

			case ProjectStatus.ON_HOLD:
				// Handle pause logic
				console.log(`Project ${oldProject.name} has been put on hold`);
				break;
		}
	}

	/**
	 * Generate unique project ID
	 */
	private generateProjectId(): string {
		return 'proj_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
	}
}