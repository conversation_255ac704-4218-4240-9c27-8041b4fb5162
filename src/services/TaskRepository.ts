// Task repository implementation

import { BaseRepository } from './Repository';
import { DataManager } from './DataManager';
import { Task, TaskStatus, Priority, TaskValidator } from '../models';

export class TaskRepository extends BaseRepository<Task> {
	constructor(dataManager: DataManager) {
		super(dataManager, 'tasks');
	}

	protected beforeCreate(task: Task): Task {
		// Validate task before creation
		const validation = TaskValidator.validate(task);
		if (!validation.isValid) {
			throw new Error(`Invalid task data: ${validation.errors.join(', ')}`);
		}

		// Set timestamps
		const now = new Date();
		return {
			...task,
			createdAt: now,
			updatedAt: now
		};
	}

	protected beforeUpdate(task: Task): Task {
		// Validate task before update
		const validation = TaskValidator.validate(task);
		if (!validation.isValid) {
			throw new Error(`Invalid task data: ${validation.errors.join(', ')}`);
		}

		// Set completed date if status changed to completed
		if (task.status === TaskStatus.COMPLETED && !task.completedDate) {
			task.completedDate = new Date();
		}

		// Clear completed date if status changed from completed
		if (task.status !== TaskStatus.COMPLETED && task.completedDate) {
			task.completedDate = undefined;
		}

		// Update timestamp
		return {
			...task,
			updatedAt: new Date()
		};
	}

	protected afterLoad(task: any): Task {
		// 确保日期字段被正确转换为 Date 对象
		return {
			...task,
			startDate: task.startDate ? this.ensureDate(task.startDate) : undefined,
			dueDate: task.dueDate ? this.ensureDate(task.dueDate) : undefined,
			completedDate: task.completedDate ? this.ensureDate(task.completedDate) : undefined,
			createdAt: this.ensureDate(task.createdAt),
			updatedAt: this.ensureDate(task.updatedAt)
		};
	}

	private ensureDate(value: any): Date {
		if (value instanceof Date) {
			return value;
		}
		if (typeof value === 'string') {
			const date = new Date(value);
			if (isNaN(date.getTime())) {
				throw new Error(`Invalid date string: ${value}`);
			}
			return date;
		}
		throw new Error(`Invalid date value: ${value}`);
	}

	// Task-specific query methods
	async findByProject(projectId: string): Promise<Task[]> {
		return this.findBy(task => task.projectId === projectId);
	}

	async findByStatus(status: TaskStatus): Promise<Task[]> {
		return this.findBy(task => task.status === status);
	}

	async findByPriority(priority: Priority): Promise<Task[]> {
		return this.findBy(task => task.priority === priority);
	}

	async findByAssignee(assignee: string): Promise<Task[]> {
		return this.findBy(task => task.assignee === assignee);
	}

	async findBySprint(sprintId: string): Promise<Task[]> {
		return this.findBy(task => task.sprintId === sprintId);
	}

	async findRootTasks(projectId?: string): Promise<Task[]> {
		return this.findBy(task => 
			!task.parentTaskId && 
			(!projectId || task.projectId === projectId)
		);
	}

	async findChildTasks(parentTaskId: string): Promise<Task[]> {
		return this.findBy(task => task.parentTaskId === parentTaskId);
	}

	async findOverdue(): Promise<Task[]> {
		const now = new Date();
		return this.findBy(task => {
			return Boolean(
				task.dueDate && 
				task.dueDate < now && 
				task.status !== TaskStatus.COMPLETED &&
				task.status !== TaskStatus.CANCELLED
			);
		});
	}

	async findDueToday(): Promise<Task[]> {
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);

		return this.findBy(task => {
			return Boolean(
				task.dueDate && 
				task.dueDate >= today && 
				task.dueDate < tomorrow &&
				task.status !== TaskStatus.COMPLETED &&
				task.status !== TaskStatus.CANCELLED
			);
		});
	}

	async findByTag(tag: string): Promise<Task[]> {
		return this.findBy(task => task.tags.includes(tag));
	}

	async findByLinkedNote(notePath: string): Promise<Task[]> {
		return this.findBy(task => task.linkedNotes.includes(notePath));
	}

	async findByIds(taskIds: string[]): Promise<Task[]> {
		return this.findBy(task => taskIds.includes(task.id));
	}

	async getTaskHierarchy(projectId: string): Promise<Task[]> {
		const tasks = await this.findByProject(projectId);
		
		// Build hierarchy by organizing parent-child relationships
		const taskMap = new Map<string, Task>();
		const rootTasks: Task[] = [];

		// First pass: create task map
		for (const task of tasks) {
			taskMap.set(task.id, { ...task });
		}

		// Second pass: organize hierarchy
		for (const task of tasks) {
			if (!task.parentTaskId) {
				rootTasks.push(taskMap.get(task.id)!);
			}
		}

		return rootTasks;
	}

	async getTaskStats(projectId?: string): Promise<{
		total: number;
		byStatus: Record<TaskStatus, number>;
		byPriority: Record<Priority, number>;
		overdue: number;
		dueToday: number;
		completed: number;
		averageCompletionTime?: number;
	}> {
		const tasks = projectId ? 
			await this.findByProject(projectId) : 
			await this.findAll();

		const now = new Date();
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);

		const stats = {
			total: tasks.length,
			byStatus: {
				[TaskStatus.TODO]: 0,
				[TaskStatus.IN_PROGRESS]: 0,
				[TaskStatus.BLOCKED]: 0,
				[TaskStatus.REVIEW]: 0,
				[TaskStatus.COMPLETED]: 0,
				[TaskStatus.CANCELLED]: 0
			},
			byPriority: {
				[Priority.LOW]: 0,
				[Priority.MEDIUM]: 0,
				[Priority.HIGH]: 0,
				[Priority.CRITICAL]: 0
			},
			overdue: 0,
			dueToday: 0,
			completed: 0,
			averageCompletionTime: undefined as number | undefined
		};

		let totalCompletionTime = 0;
		let completedTasksWithTime = 0;

		for (const task of tasks) {
			// Count by status
			stats.byStatus[task.status]++;

			// Count by priority
			stats.byPriority[task.priority]++;

			// Count overdue tasks
			if (task.dueDate && task.dueDate < now && 
				task.status !== TaskStatus.COMPLETED &&
				task.status !== TaskStatus.CANCELLED) {
				stats.overdue++;
			}

			// Count due today
			if (task.dueDate && task.dueDate >= today && task.dueDate < tomorrow &&
				task.status !== TaskStatus.COMPLETED &&
				task.status !== TaskStatus.CANCELLED) {
				stats.dueToday++;
			}

			// Count completed tasks and calculate average completion time
			if (task.status === TaskStatus.COMPLETED) {
				stats.completed++;
				
				if (task.completedDate && task.createdAt) {
					const completionTime = task.completedDate.getTime() - task.createdAt.getTime();
					totalCompletionTime += completionTime;
					completedTasksWithTime++;
				}
			}
		}

		// Calculate average completion time in days
		if (completedTasksWithTime > 0) {
			const avgMilliseconds = totalCompletionTime / completedTasksWithTime;
			stats.averageCompletionTime = Math.round(avgMilliseconds / (1000 * 60 * 60 * 24));
		}

		return stats;
	}

	async searchTasks(query: string, projectId?: string): Promise<Task[]> {
		const searchTerm = query.toLowerCase();
		const tasks = projectId ? 
			await this.findByProject(projectId) : 
			await this.findAll();

		return tasks.filter(task => 
			task.title.toLowerCase().includes(searchTerm) ||
			(task.description && task.description.toLowerCase().includes(searchTerm)) ||
			task.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
			(task.assignee && task.assignee.toLowerCase().includes(searchTerm))
		);
	}

	async updateTaskPosition(taskId: string, newPosition: number): Promise<Task | null> {
		const task = await this.findById(taskId);
		if (!task) return null;

		return this.update(taskId, { position: newPosition });
	}

	async addChildTask(parentTaskId: string, childTaskId: string): Promise<boolean> {
		const parentTask = await this.findById(parentTaskId);
		if (!parentTask) return false;

		if (parentTask.childTaskIds.includes(childTaskId)) {
			return true; // Already a child
		}

		const updatedParent = await this.update(parentTaskId, {
			childTaskIds: [...parentTask.childTaskIds, childTaskId]
		});

		// Update child task's parent reference
		await this.update(childTaskId, { parentTaskId });

		return updatedParent !== null;
	}

	async removeChildTask(parentTaskId: string, childTaskId: string): Promise<boolean> {
		const parentTask = await this.findById(parentTaskId);
		if (!parentTask) return false;

		const updatedParent = await this.update(parentTaskId, {
			childTaskIds: parentTask.childTaskIds.filter(id => id !== childTaskId)
		});

		// Remove parent reference from child task
		await this.update(childTaskId, { parentTaskId: undefined });

		return updatedParent !== null;
	}
}