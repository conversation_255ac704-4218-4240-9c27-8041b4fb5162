// 看板管理服务

import { App, Notice } from 'obsidian';
import { 
	Kanban<PERSON>onfig, 
	KanbanColumn, 
	KanbanSwimLane, 
	KanbanTaskCard, 
	KanbanStats,
	DragDropData,
	KanbanUtils,
	SwimLaneType
} from '../models/Kanban';
import { Task, TaskStatus, TaskUtils } from '../models/Task';
import { Priority } from '../types/core';
import { TaskRepository } from './TaskRepository';
import { ProjectRepository } from './ProjectRepository';
import { DataManager } from './DataManager';

export interface KanbanCreateOptions {
	name: string;
	projectId?: string;
	enableSwimLanes?: boolean;
	swimLaneType?: SwimLaneType;
}

export interface KanbanUpdateOptions {
	name?: string;
	columns?: KanbanColumn[];
	swimLanes?: KanbanSwimLane[];
	enableSwimLanes?: boolean;
	showTaskCount?: boolean;
	showWipLimits?: boolean;
	autoRefresh?: boolean;
	refreshInterval?: number;
}

export class KanbanManager {
	private app: App;
	private taskRepository: TaskRepository;
	private projectRepository: ProjectRepository;
	private dataManager: DataManager;
	private kanbanConfigs: Map<string, KanbanConfig> = new Map();

	constructor(
		app: App,
		taskRepository: TaskRepository,
		projectRepository: ProjectRepository,
		dataManager: DataManager
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.projectRepository = projectRepository;
		this.dataManager = dataManager;
	}

	/**
	 * 初始化看板管理器
	 */
	async initialize(): Promise<void> {
		try {
			await this.loadKanbanConfigs();
			console.log('KanbanManager: 初始化完成');
		} catch (error) {
			console.error('KanbanManager: 初始化失败', error);
			throw error;
		}
	}

	/**
	 * 创建新的看板配置
	 */
	async createKanban(options: KanbanCreateOptions): Promise<KanbanConfig> {
		const config = KanbanUtils.createDefaultConfig(options.name, options.projectId);
		
		// 设置泳道
		if (options.enableSwimLanes && options.swimLaneType) {
			config.enableSwimLanes = true;
			
			switch (options.swimLaneType) {
				case SwimLaneType.PRIORITY:
					config.swimLanes = KanbanUtils.createPrioritySwimLanes();
					break;
				case SwimLaneType.ASSIGNEE:
					config.swimLanes = await this.createAssigneeSwimLanes(options.projectId);
					break;
				default:
					config.swimLanes = [];
			}
		}

		// 保存配置
		this.kanbanConfigs.set(config.id, config);
		await this.saveKanbanConfigs();

		new Notice(`看板 "${config.name}" 已创建`);
		return config;
	}

	/**
	 * 更新看板配置
	 */
	async updateKanban(kanbanId: string, updates: KanbanUpdateOptions): Promise<KanbanConfig> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const updatedConfig: KanbanConfig = {
			...config,
			...updates,
			updatedAt: new Date()
		};

		this.kanbanConfigs.set(kanbanId, updatedConfig);
		await this.saveKanbanConfigs();

		new Notice(`看板 "${updatedConfig.name}" 已更新`);
		return updatedConfig;
	}

	/**
	 * 删除看板配置
	 */
	async deleteKanban(kanbanId: string): Promise<void> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		this.kanbanConfigs.delete(kanbanId);
		await this.saveKanbanConfigs();

		new Notice(`看板 "${config.name}" 已删除`);
	}

	/**
	 * 获取看板配置
	 */
	getKanban(kanbanId: string): KanbanConfig | undefined {
		return this.kanbanConfigs.get(kanbanId);
	}

	/**
	 * 获取所有看板配置
	 */
	getAllKanbans(): KanbanConfig[] {
		return Array.from(this.kanbanConfigs.values())
			.sort((a, b) => a.name.localeCompare(b.name));
	}

	/**
	 * 获取项目的看板配置
	 */
	getProjectKanbans(projectId: string): KanbanConfig[] {
		return Array.from(this.kanbanConfigs.values())
			.filter(config => config.projectId === projectId)
			.sort((a, b) => a.name.localeCompare(b.name));
	}

	/**
	 * 获取看板任务数据
	 */
	async getKanbanTasks(kanbanId: string): Promise<KanbanTaskCard[]> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		// 获取任务数据
		const tasks = config.projectId ? 
			await this.taskRepository.findByProject(config.projectId) :
			await this.taskRepository.findAll();

		// 转换为看板任务卡片
		const kanbanTasks: KanbanTaskCard[] = [];
		
		for (const task of tasks) {
			const kanbanTask = await this.convertToKanbanTaskCard(task);
			kanbanTasks.push(kanbanTask);
		}

		// 按位置排序
		return kanbanTasks.sort((a, b) => {
			const aTask = tasks.find(t => t.id === a.taskId);
			const bTask = tasks.find(t => t.id === b.taskId);
			return (aTask?.position || 0) - (bTask?.position || 0);
		});
	}

	/**
	 * 获取看板统计信息
	 */
	async getKanbanStats(kanbanId: string): Promise<KanbanStats> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const tasks = await this.getKanbanTasks(kanbanId);
		return KanbanUtils.calculateStats(config, tasks);
	}

	/**
	 * 处理任务拖拽
	 */
	async handleTaskDrag(kanbanId: string, dragData: DragDropData): Promise<void> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const task = await this.taskRepository.findById(dragData.taskId);
		if (!task) {
			throw new Error(`任务不存在: ${dragData.taskId}`);
		}

		// 获取目标列的状态
		const targetColumn = config.columns.find(col => col.id === dragData.targetColumnId);
		if (!targetColumn) {
			throw new Error(`目标列不存在: ${dragData.targetColumnId}`);
		}

		// 检查WIP限制
		if (targetColumn.wipLimit && targetColumn.wipLimit > 0) {
			const stats = await this.getKanbanStats(kanbanId);
			const currentCount = stats.tasksByColumn[dragData.targetColumnId] || 0;
			
			// 如果是从其他列移动过来，需要检查是否会超过限制
			if (dragData.sourceColumnId !== dragData.targetColumnId && 
				currentCount >= targetColumn.wipLimit) {
				throw new Error(`目标列 "${targetColumn.title}" 已达到WIP限制 (${targetColumn.wipLimit})`);
			}
		}

		// 更新任务状态
		if (task.status !== targetColumn.status) {
			await this.taskRepository.update(dragData.taskId, {
				status: targetColumn.status,
				position: dragData.targetIndex
			});
		} else {
			// 只更新位置
			await this.taskRepository.update(dragData.taskId, {
				position: dragData.targetIndex
			});
		}

		// 更新其他任务的位置
		await this.updateTaskPositions(kanbanId, dragData);

		new Notice(`任务已移动到 "${targetColumn.title}"`);
	}

	/**
	 * 添加新列
	 */
	async addColumn(kanbanId: string, column: Omit<KanbanColumn, 'id' | 'position'>): Promise<KanbanConfig> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const newColumn: KanbanColumn = {
			...column,
			id: this.generateColumnId(),
			position: config.columns.length
		};

		const updatedConfig = {
			...config,
			columns: [...config.columns, newColumn],
			updatedAt: new Date()
		};

		this.kanbanConfigs.set(kanbanId, updatedConfig);
		await this.saveKanbanConfigs();

		new Notice(`列 "${newColumn.title}" 已添加`);
		return updatedConfig;
	}

	/**
	 * 更新列配置
	 */
	async updateColumn(kanbanId: string, columnId: string, updates: Partial<KanbanColumn>): Promise<KanbanConfig> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const columnIndex = config.columns.findIndex(col => col.id === columnId);
		if (columnIndex === -1) {
			throw new Error(`列不存在: ${columnId}`);
		}

		const updatedColumns = [...config.columns];
		updatedColumns[columnIndex] = {
			...updatedColumns[columnIndex],
			...updates
		};

		const updatedConfig = {
			...config,
			columns: updatedColumns,
			updatedAt: new Date()
		};

		this.kanbanConfigs.set(kanbanId, updatedConfig);
		await this.saveKanbanConfigs();

		new Notice(`列配置已更新`);
		return updatedConfig;
	}

	/**
	 * 删除列
	 */
	async deleteColumn(kanbanId: string, columnId: string): Promise<KanbanConfig> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const column = config.columns.find(col => col.id === columnId);
		if (!column) {
			throw new Error(`列不存在: ${columnId}`);
		}

		// 检查是否有任务在这个列中
		const tasks = await this.getKanbanTasks(kanbanId);
		const tasksInColumn = tasks.filter(task => task.status === column.status);
		
		if (tasksInColumn.length > 0) {
			throw new Error(`无法删除列 "${column.title}"，其中还有 ${tasksInColumn.length} 个任务`);
		}

		const updatedColumns = config.columns.filter(col => col.id !== columnId);
		
		// 重新排序位置
		updatedColumns.forEach((col, index) => {
			col.position = index;
		});

		const updatedConfig = {
			...config,
			columns: updatedColumns,
			updatedAt: new Date()
		};

		this.kanbanConfigs.set(kanbanId, updatedConfig);
		await this.saveKanbanConfigs();

		new Notice(`列 "${column.title}" 已删除`);
		return updatedConfig;
	}

	/**
	 * 重新排序列
	 */
	async reorderColumns(kanbanId: string, columnIds: string[]): Promise<KanbanConfig> {
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) {
			throw new Error(`看板配置不存在: ${kanbanId}`);
		}

		const reorderedColumns: KanbanColumn[] = [];
		
		for (let i = 0; i < columnIds.length; i++) {
			const column = config.columns.find(col => col.id === columnIds[i]);
			if (column) {
				reorderedColumns.push({
					...column,
					position: i
				});
			}
		}

		const updatedConfig = {
			...config,
			columns: reorderedColumns,
			updatedAt: new Date()
		};

		this.kanbanConfigs.set(kanbanId, updatedConfig);
		await this.saveKanbanConfigs();

		new Notice(`列顺序已更新`);
		return updatedConfig;
	}

	/**
	 * 转换任务为看板任务卡片
	 */
	private async convertToKanbanTaskCard(task: Task): Promise<KanbanTaskCard> {
		return {
			taskId: task.id,
			title: task.title,
			description: task.description,
			status: task.status,
			priority: task.priority,
			assignee: task.assignee,
			tags: task.tags,
			dueDate: task.dueDate,
			estimatedHours: task.estimatedHours,
			actualHours: task.actualHours,
			isOverdue: TaskUtils.isOverdue(task),
			hasChildren: task.childTaskIds.length > 0,
			childrenCount: task.childTaskIds.length,
			dependenciesCount: task.dependencies.length,
			linkedNotesCount: task.linkedNotes.length
		};
	}

	/**
	 * 创建负责人泳道
	 */
	private async createAssigneeSwimLanes(projectId?: string): Promise<KanbanSwimLane[]> {
		const tasks = projectId ? 
			await this.taskRepository.findByProject(projectId) :
			await this.taskRepository.findAll();

		const assignees = new Set<string>();
		
		for (const task of tasks) {
			if (task.assignee) {
				assignees.add(task.assignee);
			}
		}

		const swimLanes: KanbanSwimLane[] = [];
		let position = 0;

		for (const assignee of assignees) {
			swimLanes.push({
				id: `assignee_${assignee}`,
				title: assignee,
				type: SwimLaneType.ASSIGNEE,
				value: assignee,
				position: position++
			});
		}

		// 添加未分配泳道
		swimLanes.push({
			id: 'unassigned',
			title: '未分配',
			type: SwimLaneType.ASSIGNEE,
			value: '',
			color: '#6b7280',
			position: position
		});

		return swimLanes;
	}

	/**
	 * 更新任务位置
	 */
	private async updateTaskPositions(kanbanId: string, dragData: DragDropData): Promise<void> {
		const tasks = await this.getKanbanTasks(kanbanId);
		const config = this.kanbanConfigs.get(kanbanId);
		if (!config) return;

		// 获取目标列的所有任务
		const targetColumn = config.columns.find(col => col.id === dragData.targetColumnId);
		if (!targetColumn) return;

		const tasksInTargetColumn = tasks
			.filter(task => task.status === targetColumn.status && task.taskId !== dragData.taskId)
			.sort((a, b) => {
				const aTask = tasks.find(t => t.taskId === a.taskId);
				const bTask = tasks.find(t => t.taskId === b.taskId);
				return (aTask?.taskId === dragData.taskId ? dragData.targetIndex : 0) - 
					   (bTask?.taskId === dragData.taskId ? dragData.targetIndex : 0);
			});

		// 更新位置
		for (let i = 0; i < tasksInTargetColumn.length; i++) {
			const position = i >= dragData.targetIndex ? i + 1 : i;
			await this.taskRepository.update(tasksInTargetColumn[i].taskId, { position });
		}
	}

	/**
	 * 加载看板配置
	 */
	private async loadKanbanConfigs(): Promise<void> {
		try {
			const data = await this.dataManager.loadData<KanbanConfig[]>('kanban-configs');
			if (data && Array.isArray(data)) {
				this.kanbanConfigs.clear();
				for (const config of data) {
					this.kanbanConfigs.set(config.id, config);
				}
			}
		} catch (error) {
			console.warn('KanbanManager: 无法加载看板配置，使用默认配置', error);
		}
	}

	/**
	 * 保存看板配置
	 */
	private async saveKanbanConfigs(): Promise<void> {
		const configs = Array.from(this.kanbanConfigs.values());
		await this.dataManager.saveData(configs, 'kanban-configs');
	}

	/**
	 * 生成列ID
	 */
	private generateColumnId(): string {
		return 'col_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 5);
	}
}