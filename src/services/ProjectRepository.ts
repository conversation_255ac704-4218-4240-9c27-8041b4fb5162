// Project repository implementation

import { BaseRepository } from './Repository';
import { DataManager } from './DataManager';
import { Project, ProjectStatus, ProjectValidator } from '../models';

export class ProjectRepository extends BaseRepository<Project> {
	constructor(dataManager: DataManager) {
		super(dataManager, 'projects');
	}

	protected beforeCreate(project: Project): Project {
		// Validate project before creation
		const validation = ProjectValidator.validate(project);
		if (!validation.isValid) {
			throw new Error(`Invalid project data: ${validation.errors.join(', ')}`);
		}

		// Set timestamps
		const now = new Date();
		return {
			...project,
			createdAt: now,
			updatedAt: now
		};
	}

	protected beforeUpdate(project: Project): Project {
		// Validate project before update
		const validation = ProjectValidator.validate(project);
		if (!validation.isValid) {
			throw new Error(`Invalid project data: ${validation.errors.join(', ')}`);
		}

		// Update timestamp
		return {
			...project,
			updatedAt: new Date()
		};
	}

	// Project-specific query methods
	async findByStatus(status: ProjectStatus): Promise<Project[]> {
		return this.findBy(project => project.status === status);
	}

	async findActive(): Promise<Project[]> {
		return this.findBy(project => project.status === ProjectStatus.ACTIVE);
	}

	async findOverdue(): Promise<Project[]> {
		const now = new Date();
		return this.findBy(project => {
			return Boolean(
				project.endDate && 
				project.endDate < now && 
				project.status !== ProjectStatus.COMPLETED &&
				project.status !== ProjectStatus.CANCELLED
			);
		});
	}

	async findByTag(tag: string): Promise<Project[]> {
		return this.findBy(project => project.tags.includes(tag));
	}

	async findByDateRange(startDate: Date, endDate: Date): Promise<Project[]> {
		return this.findBy(project => {
			const projectStart = new Date(project.startDate);
			const projectEnd = project.endDate ? new Date(project.endDate) : new Date();
			
			return (projectStart >= startDate && projectStart <= endDate) ||
				   (projectEnd >= startDate && projectEnd <= endDate) ||
				   (projectStart <= startDate && projectEnd >= endDate);
		});
	}

	async getProjectStats(): Promise<{
		total: number;
		byStatus: Record<ProjectStatus, number>;
		overdue: number;
		completedThisMonth: number;
	}> {
		const projects = await this.findAll();
		const now = new Date();
		const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

		const stats = {
			total: projects.length,
			byStatus: {
				[ProjectStatus.PLANNING]: 0,
				[ProjectStatus.ACTIVE]: 0,
				[ProjectStatus.ON_HOLD]: 0,
				[ProjectStatus.COMPLETED]: 0,
				[ProjectStatus.CANCELLED]: 0
			},
			overdue: 0,
			completedThisMonth: 0
		};

		for (const project of projects) {
			// Count by status
			stats.byStatus[project.status]++;

			// Count overdue projects
			if (project.endDate && project.endDate < now && 
				project.status !== ProjectStatus.COMPLETED &&
				project.status !== ProjectStatus.CANCELLED) {
				stats.overdue++;
			}

			// Count completed this month
			if (project.status === ProjectStatus.COMPLETED &&
				project.updatedAt >= startOfMonth) {
				stats.completedThisMonth++;
			}
		}

		return stats;
	}

	async searchProjects(query: string): Promise<Project[]> {
		const searchTerm = query.toLowerCase();
		return this.findBy(project => 
			project.name.toLowerCase().includes(searchTerm) ||
			(project.description && project.description.toLowerCase().includes(searchTerm)) ||
			project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
		);
	}
}