import { Sprint, SprintStatus, BurndownPoint, SprintUtils } from '../models/Sprint';
import { Task, TaskStatus } from '../models/Task';
import { DataManager } from './DataManager';

/**
 * Sprint 管理服务
 * 负责 Sprint 的创建、更新、删除和查询操作
 */
export class SprintManager {
	private dataManager: DataManager;
	private sprints: Map<string, Sprint> = new Map();

	constructor(dataManager: DataManager) {
		this.dataManager = dataManager;
	}

	/**
	 * 初始化 Sprint 管理器
	 */
	async initialize(): Promise<void> {
		try {
			const sprintData = await this.dataManager.loadData('sprints');
			if (sprintData && Array.isArray(sprintData)) {
				sprintData.forEach((sprint: Sprint) => {
					// 确保日期字段是 Date 对象
					sprint.startDate = new Date(sprint.startDate);
					sprint.endDate = new Date(sprint.endDate);
					sprint.createdAt = new Date(sprint.createdAt);
					sprint.updatedAt = new Date(sprint.updatedAt);
					
					// 转换燃尽图数据中的日期
					sprint.burndownData = sprint.burndownData.map(point => ({
						...point,
						date: new Date(point.date)
					}));
					
					this.sprints.set(sprint.id, sprint);
				});
			}
		} catch (error) {
			console.error('初始化 Sprint 管理器失败:', error);
		}
	}

	/**
	 * 创建新的 Sprint
	 */
	async createSprint(name: string, projectId: string, startDate: Date, endDate: Date, goal?: string): Promise<Sprint> {
		try {
			const sprint = SprintUtils.createSprint(name, projectId, startDate, endDate);
			if (goal) {
				sprint.goal = goal;
			}

			this.sprints.set(sprint.id, sprint);
			await this.saveData();
			
			return sprint;
		} catch (error) {
			console.error('创建 Sprint 失败:', error);
			throw error;
		}
	}

	/**
	 * 更新 Sprint
	 */
	async updateSprint(sprintId: string, updates: Partial<Sprint>): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			const updatedSprint = SprintUtils.updateSprint(sprint, updates);
			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('更新 Sprint 失败:', error);
			throw error;
		}
	}

	/**
	 * 删除 Sprint
	 */
	async deleteSprint(sprintId: string): Promise<void> {
		try {
			if (!this.sprints.has(sprintId)) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			this.sprints.delete(sprintId);
			await this.saveData();
		} catch (error) {
			console.error('删除 Sprint 失败:', error);
			throw error;
		}
	}

	/**
	 * 获取 Sprint
	 */
	getSprint(sprintId: string): Sprint | undefined {
		return this.sprints.get(sprintId);
	}

	/**
	 * 获取项目的所有 Sprint
	 */
	getProjectSprints(projectId: string): Sprint[] {
		return Array.from(this.sprints.values())
			.filter(sprint => sprint.projectId === projectId)
			.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
	}

	/**
	 * 获取活跃的 Sprint
	 */
	getActiveSprints(projectId?: string): Sprint[] {
		const sprints = Array.from(this.sprints.values())
			.filter(sprint => sprint.status === SprintStatus.ACTIVE);
		
		if (projectId) {
			return sprints.filter(sprint => sprint.projectId === projectId);
		}
		
		return sprints;
	}

	/**
	 * 开始 Sprint
	 */
	async startSprint(sprintId: string): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			if (!SprintUtils.canStart(sprint)) {
				throw new Error('Sprint 无法开始：状态不正确或没有任务');
			}

			const updatedSprint = SprintUtils.updateSprint(sprint, {
				status: SprintStatus.ACTIVE,
				startDate: new Date() // 更新实际开始时间
			});

			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('开始 Sprint 失败:', error);
			throw error;
		}
	}

	/**
	 * 完成 Sprint
	 */
	async completeSprint(sprintId: string, tasks: Task[]): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			if (!SprintUtils.canComplete(sprint)) {
				throw new Error('Sprint 无法完成：状态不正确');
			}

			// 计算速度
			const velocity = SprintUtils.calculateVelocity(sprint, tasks);

			const updatedSprint = SprintUtils.updateSprint(sprint, {
				status: SprintStatus.COMPLETED,
				velocity
			});

			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('完成 Sprint 失败:', error);
			throw error;
		}
	}

	/**
	 * 向 Sprint 添加任务
	 */
	async addTaskToSprint(sprintId: string, taskId: string): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			const updatedSprint = SprintUtils.addTask(sprint, taskId);
			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('向 Sprint 添加任务失败:', error);
			throw error;
		}
	}

	/**
	 * 从 Sprint 移除任务
	 */
	async removeTaskFromSprint(sprintId: string, taskId: string): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			const updatedSprint = SprintUtils.removeTask(sprint, taskId);
			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('从 Sprint 移除任务失败:', error);
			throw error;
		}
	}

	/**
	 * 添加燃尽图数据点
	 */
	async addBurndownPoint(sprintId: string, point: BurndownPoint): Promise<Sprint> {
		try {
			const sprint = this.sprints.get(sprintId);
			if (!sprint) {
				throw new Error(`Sprint ${sprintId} 不存在`);
			}

			const updatedSprint = SprintUtils.addBurndownPoint(sprint, point);
			this.sprints.set(sprintId, updatedSprint);
			await this.saveData();
			
			return updatedSprint;
		} catch (error) {
			console.error('添加燃尽图数据点失败:', error);
			throw error;
		}
	}

	/**
	 * 生成燃尽图数据
	 */
	generateBurndownData(sprint: Sprint, tasks: Task[]): BurndownPoint[] {
		const sprintTasks = tasks.filter(task => sprint.taskIds.includes(task.id));
		const startDate = new Date(sprint.startDate);
		const endDate = new Date(sprint.endDate);
		const totalDays = SprintUtils.getDuration(sprint);
		
		const burndownData: BurndownPoint[] = [];
		
		// 计算每天的剩余工作量
		for (let i = 0; i <= totalDays; i++) {
			const currentDate = new Date(startDate);
			currentDate.setDate(startDate.getDate() + i);
			
			// 计算到当前日期为止完成的任务
			const completedTasks = sprintTasks.filter(task => {
				return task.completedDate && new Date(task.completedDate) <= currentDate;
			});
			
			// 计算剩余工作量（以小时为单位）
			const remainingTasks = sprintTasks.filter(task => {
				return !task.completedDate || new Date(task.completedDate) > currentDate;
			});
			
			const remainingHours = remainingTasks.reduce((sum, task) => {
				return sum + (task.estimatedHours || 1);
			}, 0);
			
			burndownData.push({
				date: currentDate,
				remainingHours,
				completedTasks: completedTasks.length
			});
		}
		
		return burndownData;
	}

	/**
	 * 获取 Sprint 统计信息
	 */
	getSprintStats(sprint: Sprint, tasks: Task[]) {
		const sprintTasks = tasks.filter(task => sprint.taskIds.includes(task.id));
		const completedTasks = sprintTasks.filter(task => task.status === TaskStatus.COMPLETED);
		const inProgressTasks = sprintTasks.filter(task => task.status === TaskStatus.IN_PROGRESS);
		const todoTasks = sprintTasks.filter(task => task.status === TaskStatus.TODO);
		
		const totalEstimatedHours = sprintTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
		const completedHours = completedTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
		
		return {
			totalTasks: sprintTasks.length,
			completedTasks: completedTasks.length,
			inProgressTasks: inProgressTasks.length,
			todoTasks: todoTasks.length,
			completionRate: sprintTasks.length > 0 ? (completedTasks.length / sprintTasks.length) * 100 : 0,
			totalEstimatedHours,
			completedHours,
			remainingHours: totalEstimatedHours - completedHours,
			daysRemaining: SprintUtils.getDaysRemaining(sprint),
			duration: SprintUtils.getDuration(sprint),
			isActive: SprintUtils.isActive(sprint),
			isOverdue: SprintUtils.isOverdue(sprint)
		};
	}

	/**
	 * 保存数据到存储
	 */
	private async saveData(): Promise<void> {
		try {
			const sprintArray = Array.from(this.sprints.values());
			await this.dataManager.saveData(sprintArray, 'sprints');
		} catch (error) {
			console.error('保存 Sprint 数据失败:', error);
			throw error;
		}
	}
}