// Task management core functionality with hierarchy and dependency support

import { App, Notice } from 'obsidian';
import { Task, TaskStatus, Priority, TaskDependency, DependencyType, TaskUtils } from '../models';
import { TaskRepository } from './TaskRepository';
import { ProjectRepository } from './ProjectRepository';
import { NoteLinkManager } from './NoteLinkManager';

export interface TaskCreateOptions {
	title: string;
	projectId: string;
	description?: string;
	priority?: Priority;
	assignee?: string;
	parentTaskId?: string;
	estimatedHours?: number;
	startDate?: Date;
	dueDate?: Date;
	tags?: string[];
	linkedNotes?: string[];
	sprintId?: string;
}

export interface TaskUpdateOptions {
	title?: string;
	description?: string;
	status?: TaskStatus;
	priority?: Priority;
	assignee?: string;
	estimatedHours?: number;
	actualHours?: number;
	startDate?: Date;
	dueDate?: Date;
	completedDate?: Date;
	tags?: string[];
	linkedNotes?: string[];
	sprintId?: string;
	position?: number;
}

export interface TaskHierarchy {
	task: Task;
	children: TaskHierarchy[];
	depth: number;
	hasChildren: boolean;
}

export interface DependencyValidationResult {
	isValid: boolean;
	errors: string[];
	circularDependencies: string[];
}

export class TaskManager {
	private app: App;
	private taskRepository: TaskRepository;
	private projectRepository: ProjectRepository;
	private noteLinkManager?: NoteLinkManager;

	constructor(
		app: App,
		taskRepository: TaskRepository,
		projectRepository: ProjectRepository
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.projectRepository = projectRepository;
	}

	/**
	 * 设置笔记链接管理器
	 */
	setNoteLinkManager(noteLinkManager: NoteLinkManager): void {
		this.noteLinkManager = noteLinkManager;
	}

	/**
	 * Create a new task
	 */
	async createTask(options: TaskCreateOptions): Promise<Task> {
		// Validate project exists
		const project = await this.projectRepository.findById(options.projectId);
		if (!project) {
			throw new Error(`项目不存在: ${options.projectId}`);
		}

		// Validate parent task if specified
		if (options.parentTaskId) {
			const parentTask = await this.taskRepository.findById(options.parentTaskId);
			if (!parentTask) {
				throw new Error(`父任务不存在: ${options.parentTaskId}`);
			}
			if (parentTask.projectId !== options.projectId) {
				throw new Error('父任务必须属于同一个项目');
			}
		}

		const now = new Date();
		
		// 确保日期字段有有效值
		const startDate = options.startDate || now;
		const dueDate = options.dueDate || new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000); // 默认7天后
		
		const task: Task = {
			id: this.generateTaskId(),
			projectId: options.projectId,
			title: options.title,
			description: options.description,
			status: TaskStatus.TODO,
			priority: options.priority || Priority.MEDIUM,
			assignee: options.assignee,
			parentTaskId: options.parentTaskId,
			childTaskIds: [],
			dependencies: [],
			estimatedHours: options.estimatedHours,
			startDate,
			dueDate,
			tags: options.tags || [],
			linkedNotes: options.linkedNotes || [],
			sprintId: options.sprintId,
			position: await this.getNextPosition(options.projectId),
			createdAt: now,
			updatedAt: now
		};

		// Save the task
		const createdTask = await this.taskRepository.create(task);

		// Update parent task's children list if this is a child task
		if (options.parentTaskId) {
			await this.addChildToParent(options.parentTaskId, createdTask.id);
		}

		new Notice(`任务 "${createdTask.title}" 已创建`);
		return createdTask;
	}

	/**
	 * Update a task
	 */
	async updateTask(taskId: string, updates: TaskUpdateOptions): Promise<Task> {
		const existingTask = await this.taskRepository.findById(taskId);
		if (!existingTask) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		const oldStatus = existingTask.status;
		const newStatus = updates.status;

		// Handle status transitions
		if (newStatus && newStatus !== oldStatus) {
			await this.handleStatusTransition(existingTask, newStatus);
		}

		const updatedTask = await this.taskRepository.update(taskId, {
			...updates,
			updatedAt: new Date()
		});

		if (!updatedTask) {
			throw new Error(`更新任务失败: ${taskId}`);
		}

		// Handle automatic status updates for parent/child tasks
		if (newStatus && newStatus !== oldStatus) {
			await this.propagateStatusChanges(updatedTask, oldStatus, newStatus);
			
			// 同步任务状态到笔记中并维护一致性
			if (this.noteLinkManager) {
				await this.noteLinkManager.syncTaskStatusToNote(taskId, newStatus);
				
				// 如果任务有多个笔记引用，维护一致性
				const linkedNotes = this.noteLinkManager.getNotesForTask(taskId);
				if (linkedNotes.length > 1) {
					await this.noteLinkManager.maintainConsistency(taskId);
				}
			}
		}

		new Notice(`任务 "${updatedTask.title}" 已更新`);
		return updatedTask;
	}

	/**
	 * Delete a task and handle hierarchy
	 */
	async deleteTask(taskId: string, options: { deleteChildren?: boolean } = {}): Promise<void> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		// Handle child tasks
		if (task.childTaskIds.length > 0) {
			if (options.deleteChildren) {
				// Recursively delete all child tasks
				for (const childId of task.childTaskIds) {
					await this.deleteTask(childId, { deleteChildren: true });
				}
			} else {
				// Move child tasks to parent level
				for (const childId of task.childTaskIds) {
					await this.taskRepository.update(childId, {
						parentTaskId: task.parentTaskId
					});

					// Update grandparent's children list if exists
					if (task.parentTaskId) {
						await this.addChildToParent(task.parentTaskId, childId);
					}
				}
			}
		}

		// Remove from parent's children list
		if (task.parentTaskId) {
			await this.removeChildFromParent(task.parentTaskId, taskId);
		}

		// Remove dependencies pointing to this task
		await this.removeDependenciesPointingToTask(taskId);

		// 处理笔记链接清理
		if (this.noteLinkManager) {
			await this.noteLinkManager.handleTaskDeletion(taskId);
		}

		// Delete the task
		await this.taskRepository.delete(taskId);
		new Notice(`任务 "${task.title}" 已删除`);
	}

	/**
	 * Get task hierarchy for a project
	 */
	async getTaskHierarchy(projectId: string): Promise<TaskHierarchy[]> {
		const allTasks = await this.taskRepository.findByProject(projectId);
		const taskMap = new Map<string, Task>();
		
		// Create task map
		for (const task of allTasks) {
			taskMap.set(task.id, task);
		}

		// Build hierarchy starting from root tasks
		const rootTasks = allTasks.filter(task => !task.parentTaskId);
		return rootTasks.map(task => this.buildTaskHierarchy(task, taskMap, 0));
	}

	/**
	 * Add dependency between tasks
	 */
	async addDependency(taskId: string, dependsOnTaskId: string, type: DependencyType = DependencyType.FINISH_TO_START): Promise<void> {
		const task = await this.taskRepository.findById(taskId);
		const dependsOnTask = await this.taskRepository.findById(dependsOnTaskId);

		if (!task || !dependsOnTask) {
			throw new Error('任务不存在');
		}

		if (task.projectId !== dependsOnTask.projectId) {
			throw new Error('依赖任务必须属于同一个项目');
		}

		const dependency: TaskDependency = {
			taskId: dependsOnTaskId,
			type
		};

		// Check for circular dependencies
		const validation = await this.validateDependency(taskId, dependency);
		if (!validation.isValid) {
			throw new Error(`依赖关系无效: ${validation.errors.join(', ')}`);
		}

		// Add dependency
		const updatedTask = TaskUtils.addDependency(task, dependency);
		await this.taskRepository.update(taskId, updatedTask);

		new Notice(`已添加任务依赖关系`);
	}

	/**
	 * Remove dependency between tasks
	 */
	async removeDependency(taskId: string, dependsOnTaskId: string): Promise<void> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		const updatedTask = TaskUtils.removeDependency(task, dependsOnTaskId);
		await this.taskRepository.update(taskId, updatedTask);

		new Notice(`已移除任务依赖关系`);
	}

	/**
	 * Get tasks that are blocked by dependencies
	 */
	async getBlockedTasks(projectId?: string): Promise<Task[]> {
		const tasks = projectId ? 
			await this.taskRepository.findByProject(projectId) : 
			await this.taskRepository.findAll();

		const blockedTasks: Task[] = [];

		for (const task of tasks) {
			if (task.dependencies.length > 0) {
				const isBlocked = await this.isTaskBlockedByDependencies(task);
				if (isBlocked) {
					blockedTasks.push(task);
				}
			}
		}

		return blockedTasks;
	}

	/**
	 * Move task to different parent
	 */
	async moveTask(taskId: string, newParentId?: string): Promise<void> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) {
			throw new Error(`任务不存在: ${taskId}`);
		}

		// Validate new parent if specified
		if (newParentId) {
			const newParent = await this.taskRepository.findById(newParentId);
			if (!newParent) {
				throw new Error(`新父任务不存在: ${newParentId}`);
			}
			if (newParent.projectId !== task.projectId) {
				throw new Error('新父任务必须属于同一个项目');
			}
			if (await this.wouldCreateCircularHierarchy(taskId, newParentId)) {
				throw new Error('移动操作会创建循环层级关系');
			}
		}

		// Remove from old parent
		if (task.parentTaskId) {
			await this.removeChildFromParent(task.parentTaskId, taskId);
		}

		// Add to new parent
		if (newParentId) {
			await this.addChildToParent(newParentId, taskId);
		}

		// Update task's parent reference
		await this.taskRepository.update(taskId, { parentTaskId: newParentId });

		new Notice(`任务已移动`);
	}

	/**
	 * Get task statistics with hierarchy information
	 */
	async getTaskStatistics(projectId: string): Promise<{
		total: number;
		byStatus: Record<TaskStatus, number>;
		byDepth: Record<number, number>;
		withDependencies: number;
		blocked: number;
		overdue: number;
	}> {
		const tasks = await this.taskRepository.findByProject(projectId);
		const hierarchy = await this.getTaskHierarchy(projectId);
		
		const stats = {
			total: tasks.length,
			byStatus: {
				[TaskStatus.TODO]: 0,
				[TaskStatus.IN_PROGRESS]: 0,
				[TaskStatus.BLOCKED]: 0,
				[TaskStatus.REVIEW]: 0,
				[TaskStatus.COMPLETED]: 0,
				[TaskStatus.CANCELLED]: 0
			},
			byDepth: {} as Record<number, number>,
			withDependencies: 0,
			blocked: 0,
			overdue: 0
		};

		// Count by status and other metrics
		for (const task of tasks) {
			stats.byStatus[task.status]++;
			
			if (task.dependencies.length > 0) {
				stats.withDependencies++;
			}
			
			if (TaskUtils.isOverdue(task)) {
				stats.overdue++;
			}
		}

		// Count by depth
		const countDepth = (hierarchies: TaskHierarchy[]) => {
			for (const h of hierarchies) {
				stats.byDepth[h.depth] = (stats.byDepth[h.depth] || 0) + 1;
				countDepth(h.children);
			}
		};
		countDepth(hierarchy);

		// Count blocked tasks
		const blockedTasks = await this.getBlockedTasks(projectId);
		stats.blocked = blockedTasks.length;

		return stats;
	}

	/**
	 * Handle status transitions and business logic
	 */
	private async handleStatusTransition(task: Task, newStatus: TaskStatus): Promise<void> {
		const oldStatus = task.status;

		// Validate transition
		if (!TaskUtils.canTransitionTo(oldStatus, newStatus)) {
			throw new Error(`无效的状态转换: ${oldStatus} -> ${newStatus}`);
		}

		// Handle specific transitions
		switch (newStatus) {
			case TaskStatus.IN_PROGRESS:
				// Check if dependencies are satisfied
				if (await this.isTaskBlockedByDependencies(task)) {
					throw new Error('任务被依赖关系阻塞，无法开始');
				}
				break;

			case TaskStatus.COMPLETED:
				// Set completion date
				break;

			case TaskStatus.BLOCKED:
				// Could add logic to identify blocking reasons
				break;
		}
	}

	/**
	 * Propagate status changes to related tasks
	 */
	private async propagateStatusChanges(task: Task, oldStatus: TaskStatus, newStatus: TaskStatus): Promise<void> {
		// Handle parent task status updates
		if (task.parentTaskId) {
			await this.updateParentTaskStatus(task.parentTaskId);
		}

		// Handle child task status updates
		if (newStatus === TaskStatus.CANCELLED && task.childTaskIds.length > 0) {
			// Optionally cancel child tasks when parent is cancelled
			for (const childId of task.childTaskIds) {
				const childTask = await this.taskRepository.findById(childId);
				if (childTask && childTask.status !== TaskStatus.COMPLETED) {
					await this.updateTask(childId, { status: TaskStatus.CANCELLED });
				}
			}
		}
	}

	/**
	 * Update parent task status based on children
	 */
	private async updateParentTaskStatus(parentTaskId: string): Promise<void> {
		const parentTask = await this.taskRepository.findById(parentTaskId);
		if (!parentTask) return;

		const childTasks = await Promise.all(
			parentTask.childTaskIds.map(id => this.taskRepository.findById(id))
		);
		const validChildren = childTasks.filter(task => task !== null) as Task[];

		if (validChildren.length === 0) return;

		// Check if all children are completed
		const allCompleted = validChildren.every(child => child.status === TaskStatus.COMPLETED);
		const anyInProgress = validChildren.some(child => child.status === TaskStatus.IN_PROGRESS);
		const anyBlocked = validChildren.some(child => child.status === TaskStatus.BLOCKED);

		let newStatus: TaskStatus | undefined;

		if (allCompleted && parentTask.status !== TaskStatus.COMPLETED) {
			newStatus = TaskStatus.COMPLETED;
		} else if (anyBlocked && parentTask.status !== TaskStatus.BLOCKED) {
			newStatus = TaskStatus.BLOCKED;
		} else if (anyInProgress && parentTask.status === TaskStatus.TODO) {
			newStatus = TaskStatus.IN_PROGRESS;
		}

		if (newStatus) {
			await this.taskRepository.update(parentTaskId, { status: newStatus });
		}
	}

	/**
	 * Build task hierarchy recursively
	 */
	private buildTaskHierarchy(task: Task, taskMap: Map<string, Task>, depth: number): TaskHierarchy {
		const children = task.childTaskIds
			.map(childId => taskMap.get(childId))
			.filter(child => child !== undefined)
			.map(child => this.buildTaskHierarchy(child!, taskMap, depth + 1));

		return {
			task,
			children,
			depth,
			hasChildren: children.length > 0
		};
	}

	/**
	 * Validate dependency to prevent circular references
	 */
	private async validateDependency(taskId: string, dependency: TaskDependency): Promise<DependencyValidationResult> {
		const errors: string[] = [];
		const circularDependencies: string[] = [];

		// Check for self-dependency
		if (taskId === dependency.taskId) {
			errors.push('任务不能依赖自己');
		}

		// Check for circular dependencies
		const visited = new Set<string>();
		const recursionStack = new Set<string>();

		const hasCircularDependency = async (currentTaskId: string): Promise<boolean> => {
			if (recursionStack.has(currentTaskId)) {
				circularDependencies.push(currentTaskId);
				return true;
			}

			if (visited.has(currentTaskId)) {
				return false;
			}

			visited.add(currentTaskId);
			recursionStack.add(currentTaskId);

			const currentTask = await this.taskRepository.findById(currentTaskId);
			if (currentTask) {
				for (const dep of currentTask.dependencies) {
					if (await hasCircularDependency(dep.taskId)) {
						return true;
					}
				}

				// Check the new dependency
				if (currentTaskId === taskId && await hasCircularDependency(dependency.taskId)) {
					return true;
				}
			}

			recursionStack.delete(currentTaskId);
			return false;
		};

		const hasCircular = await hasCircularDependency(taskId);
		if (hasCircular) {
			errors.push('检测到循环依赖关系');
		}

		return {
			isValid: errors.length === 0,
			errors,
			circularDependencies
		};
	}

	/**
	 * Check if task is blocked by dependencies
	 */
	private async isTaskBlockedByDependencies(task: Task): Promise<boolean> {
		for (const dependency of task.dependencies) {
			const dependentTask = await this.taskRepository.findById(dependency.taskId);
			if (!dependentTask) continue;

			// Check dependency based on type
			switch (dependency.type) {
				case DependencyType.FINISH_TO_START:
					if (dependentTask.status !== TaskStatus.COMPLETED) {
						return true;
					}
					break;
				case DependencyType.START_TO_START:
					if (dependentTask.status === TaskStatus.TODO) {
						return true;
					}
					break;
				// Add other dependency type checks as needed
			}
		}

		return false;
	}

	/**
	 * Check if moving a task would create circular hierarchy
	 */
	private async wouldCreateCircularHierarchy(taskId: string, newParentId: string): Promise<boolean> {
		const task = await this.taskRepository.findById(taskId);
		if (!task) return false;

		// Check if newParentId is a descendant of taskId
		const isDescendant = async (ancestorId: string, descendantId: string): Promise<boolean> => {
			const descendant = await this.taskRepository.findById(descendantId);
			if (!descendant || !descendant.parentTaskId) return false;

			if (descendant.parentTaskId === ancestorId) return true;
			return await isDescendant(ancestorId, descendant.parentTaskId);
		};

		return await isDescendant(taskId, newParentId);
	}

	/**
	 * Add child to parent's children list
	 */
	private async addChildToParent(parentId: string, childId: string): Promise<void> {
		const parent = await this.taskRepository.findById(parentId);
		if (parent && !parent.childTaskIds.includes(childId)) {
			await this.taskRepository.update(parentId, {
				childTaskIds: [...parent.childTaskIds, childId]
			});
		}
	}

	/**
	 * Remove child from parent's children list
	 */
	private async removeChildFromParent(parentId: string, childId: string): Promise<void> {
		const parent = await this.taskRepository.findById(parentId);
		if (parent) {
			await this.taskRepository.update(parentId, {
				childTaskIds: parent.childTaskIds.filter(id => id !== childId)
			});
		}
	}

	/**
	 * Remove all dependencies pointing to a task
	 */
	private async removeDependenciesPointingToTask(taskId: string): Promise<void> {
		const allTasks = await this.taskRepository.findAll();
		
		for (const task of allTasks) {
			const hasDependency = task.dependencies.some(dep => dep.taskId === taskId);
			if (hasDependency) {
				const updatedTask = TaskUtils.removeDependency(task, taskId);
				await this.taskRepository.update(task.id, updatedTask);
			}
		}
	}

	/**
	 * Get next position for task ordering
	 */
	private async getNextPosition(projectId: string): Promise<number> {
		const tasks = await this.taskRepository.findByProject(projectId);
		const maxPosition = Math.max(0, ...tasks.map(t => t.position || 0));
		return maxPosition + 1;
	}

	/**
	 * Generate unique task ID
	 */
	private generateTaskId(): string {
		return 'task_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
	}

	// 公共方法用于外部访问任务数据

	/**
	 * Get task by ID
	 */
	async getTaskById(taskId: string): Promise<Task | null> {
		return await this.taskRepository.findById(taskId);
	}

	/**
	 * Get all tasks for a project
	 */
	async getTasksByProject(projectId: string): Promise<Task[]> {
		return await this.taskRepository.findByProject(projectId);
	}

	/**
	 * Get all tasks
	 */
	async getAllTasks(): Promise<Task[]> {
		return await this.taskRepository.findAll();
	}
}