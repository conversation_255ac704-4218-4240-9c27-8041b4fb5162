// 笔记链接管理器 - 处理任务与笔记的双向链接关系

import { App, TFile, CachedMetadata, Notice, MetadataCache } from 'obsidian';
import { Task } from '../models';
import { TaskRepository } from './TaskRepository';
import { 
	NoteTaskReference, 
	NoteTaskReferenceMap, 
	TypeConverter,
	isNoteTaskReference,
	TaskStatus
} from '../types/core';

export interface TaskParseResult {
	title: string;
	status: TaskStatus;
	priority?: string;
	dueDate?: Date;
	tags: string[];
	description?: string;
	lineNumber: number;
	originalText: string;
	confidence: number; // 0-1, 识别置信度
}

export class NoteLinkManager {
	private app: App;
	private taskRepository: TaskRepository;
	private noteTaskReferences: NoteTaskReferenceMap = new Map();
	private taskNoteReferences: Map<string, string[]> = new Map();
	private isInitialized = false;

	// 增强的任务识别正则表达式
	private readonly ENHANCED_TASK_PATTERNS = [
		// 标准 markdown 任务格式
		/^(\s*)-\s*\[(.)\]\s*(.+)$/,
		// 带缩进的任务
		/^(\s{2,})-\s*\[(.)\]\s*(.+)$/,
		// 数字列表任务
		/^(\s*)\d+\.\s*\[(.)\]\s*(.+)$/,
		// 星号任务
		/^(\s*)\*\s*\[(.)\]\s*(.+)$/,
		// 加号任务
		/^(\s*)\+\s*\[(.)\]\s*(.+)$/
	];

	// 任务状态字符映射
	private readonly STATUS_CHAR_MAP: Record<string, TaskStatus> = {
		' ': TaskStatus.TODO,
		'x': TaskStatus.COMPLETED,
		'X': TaskStatus.COMPLETED,
		'/': TaskStatus.IN_PROGRESS,
		'-': TaskStatus.CANCELLED,
		'>': TaskStatus.IN_PROGRESS,
		'<': TaskStatus.BLOCKED,
		'!': TaskStatus.TODO,
		'?': TaskStatus.REVIEW,
		'~': TaskStatus.CANCELLED
	};

	// 优先级识别模式
	private readonly PRIORITY_PATTERNS = [
		{ regex: /⏫|🔥|!!!/g, priority: 'CRITICAL' },
		{ regex: /🔼|⭐|!!/g, priority: 'HIGH' },
		{ regex: /🔽|!/g, priority: 'LOW' }
	];

	// 日期识别模式
	private readonly DATE_PATTERNS = [
		{ regex: /📅\s*(\d{4}-\d{2}-\d{2})/g, type: 'due' },
		{ regex: /🗓️\s*(\d{4}-\d{2}-\d{2})/g, type: 'scheduled' },
		{ regex: /⏰\s*(\d{4}-\d{2}-\d{2})/g, type: 'start' },
		{ regex: /✅\s*(\d{4}-\d{2}-\d{2})/g, type: 'completed' },
		// 自然语言日期
		{ regex: /due:\s*(\d{4}-\d{2}-\d{2})/gi, type: 'due' },
		{ regex: /deadline:\s*(\d{4}-\d{2}-\d{2})/gi, type: 'due' },
		{ regex: /start:\s*(\d{4}-\d{2}-\d{2})/gi, type: 'start' }
	];

	constructor(app: App, taskRepository: TaskRepository) {
		this.app = app;
		this.taskRepository = taskRepository;
	}

	/**
	 * 类型转换工具：将字符串数组转换为NoteTaskReference数组
	 */
	private convertStringsToNoteTaskReferences(
		taskIds: string[], 
		noteId: string, 
		notePath: string
	): NoteTaskReference[] {
		return TypeConverter.stringsToNoteTaskReferences(taskIds, noteId, notePath);
	}

	/**
	 * 类型转换工具：将NoteTaskReference数组转换为字符串数组
	 */
	private convertNoteTaskReferencesToStrings(references: NoteTaskReference[]): string[] {
		return TypeConverter.noteTaskReferencesToStrings(references);
	}

	/**
	 * 运行时类型验证：验证NoteTaskReference对象
	 */
	private validateNoteTaskReference(obj: any): obj is NoteTaskReference {
		return isNoteTaskReference(obj);
	}

	/**
	 * 安全地获取笔记任务引用
	 */
	private safeGetNoteTaskReferences(notePath: string): NoteTaskReference[] {
		const references = this.noteTaskReferences.get(notePath);
		if (!references) return [];
		
		// 验证所有引用的类型安全性
		return references.filter(ref => this.validateNoteTaskReference(ref));
	}

	/**
	 * 安全地设置笔记任务引用
	 */
	private safeSetNoteTaskReferences(notePath: string, references: NoteTaskReference[]): void {
		// 验证所有引用
		const validReferences = references.filter(ref => this.validateNoteTaskReference(ref));
		
		if (validReferences.length !== references.length) {
			console.warn(`过滤了 ${references.length - validReferences.length} 个无效的笔记任务引用`);
		}
		
		this.noteTaskReferences.set(notePath, validReferences);
	}

	/**
	 * 初始化笔记链接管理器
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) return;

		try {
			// 构建现有任务的笔记引用索引
			await this.buildTaskNoteIndex();
			
			// 注册文件变更监听器
			this.registerFileWatchers();
			
			this.isInitialized = true;
			console.log('NoteLinkManager initialized successfully');
		} catch (error) {
			console.error('Failed to initialize NoteLinkManager:', error);
			throw error;
		}
	}

	/**
	 * 增强的任务解析 - 从笔记内容中智能识别任务
	 */
	parseTasksFromNote(content: string, filePath: string): TaskParseResult[] {
		const tasks: TaskParseResult[] = [];
		const lines = content.split('\n');

		lines.forEach((line, index) => {
			const taskResult = this.parseTaskLine(line, index + 1, filePath);
			if (taskResult && taskResult.confidence > 0.5) {
				tasks.push(taskResult);
			}
		});

		return tasks;
	}

	/**
	 * 解析单行任务，返回解析结果和置信度
	 */
	private parseTaskLine(line: string, lineNumber: number, filePath: string): TaskParseResult | null {
		let bestMatch: TaskParseResult | null = null;
		let highestConfidence = 0;

		// 尝试所有任务模式
		for (const pattern of this.ENHANCED_TASK_PATTERNS) {
			const match = line.match(pattern);
			if (match) {
				const [fullMatch, indent, statusChar, content] = match;
				const parseResult = this.createTaskParseResult(
					content, 
					statusChar, 
					lineNumber, 
					line, 
					filePath
				);

				if (parseResult && parseResult.confidence > highestConfidence) {
					highestConfidence = parseResult.confidence;
					bestMatch = parseResult;
				}
			}
		}

		return bestMatch;
	}

	/**
	 * 创建任务解析结果
	 */
	private createTaskParseResult(
		content: string, 
		statusChar: string, 
		lineNumber: number, 
		originalText: string,
		filePath: string
	): TaskParseResult | null {
		try {
			// 清理和提取任务标题
			const cleanedTitle = this.extractTaskTitle(content);
			if (!cleanedTitle || cleanedTitle.length < 2) {
				return null; // 标题太短，可能不是有效任务
			}

			// 解析状态
			const status = this.STATUS_CHAR_MAP[statusChar] || TaskStatus.TODO;

			// 提取优先级
			const priority = this.extractPriority(content);

			// 提取日期
			const dueDate = this.extractDueDate(content);

			// 提取标签
			const tags = this.extractTags(content);

			// 提取描述（去除元数据后的剩余内容）
			const description = this.extractDescription(content);

			// 计算置信度
			const confidence = this.calculateConfidence(content, statusChar, tags);

			return {
				title: cleanedTitle,
				status,
				priority,
				dueDate,
				tags,
				description,
				lineNumber,
				originalText,
				confidence
			};
		} catch (error) {
			console.warn('Error parsing task line:', error);
			return null;
		}
	}

	/**
	 * 提取任务标题（清理元数据）
	 */
	private extractTaskTitle(content: string): string {
		let title = content;

		// 移除优先级标记
		for (const pattern of this.PRIORITY_PATTERNS) {
			title = title.replace(pattern.regex, '');
		}

		// 移除日期标记
		for (const pattern of this.DATE_PATTERNS) {
			title = title.replace(pattern.regex, '');
		}

		// 移除标签（但保留一些重要标签用于上下文）
		title = title.replace(/#[\w-]+/g, '');

		// 清理多余空格
		title = title.replace(/\s+/g, ' ').trim();

		// 限制长度
		if (title.length > 200) {
			title = title.substring(0, 200).trim() + '...';
		}

		return title;
	}

	/**
	 * 提取优先级
	 */
	private extractPriority(content: string): string | undefined {
		for (const pattern of this.PRIORITY_PATTERNS) {
			if (pattern.regex.test(content)) {
				return pattern.priority;
			}
		}
		return undefined;
	}

	/**
	 * 提取到期日期
	 */
	private extractDueDate(content: string): Date | undefined {
		for (const pattern of this.DATE_PATTERNS) {
			if (pattern.type === 'due') {
				const match = content.match(pattern.regex);
				if (match) {
					const dateStr = match[1];
					const date = new Date(dateStr);
					if (!isNaN(date.getTime())) {
						return date;
					}
				}
			}
		}
		return undefined;
	}

	/**
	 * 提取标签
	 */
	private extractTags(content: string): string[] {
		const tagMatches = content.match(/#[\w-]+/g);
		return tagMatches ? tagMatches.map(tag => tag.substring(1)) : [];
	}

	/**
	 * 提取描述
	 */
	private extractDescription(content: string): string | undefined {
		// 如果内容很长，可能包含描述信息
		const cleaned = this.extractTaskTitle(content);
		if (content.length > cleaned.length + 50) {
			// 提取剩余的描述性内容
			let description = content;
			
			// 移除已提取的标题部分
			description = description.replace(cleaned, '');
			
			// 清理格式标记
			description = description.replace(/[⏫🔼🔽⏬📅🗓️⏰✅]/g, '');
			description = description.replace(/\s+/g, ' ').trim();
			
			return description.length > 10 ? description : undefined;
		}
		return undefined;
	}

	/**
	 * 计算任务识别置信度
	 */
	private calculateConfidence(content: string, statusChar: string, tags: string[]): number {
		let confidence = 0.5; // 基础置信度

		// 状态字符有效性
		if (this.STATUS_CHAR_MAP[statusChar]) {
			confidence += 0.2;
		}

		// 包含任务相关标签
		const taskTags = ['task', 'todo', 'action', 'work'];
		if (tags.some(tag => taskTags.includes(tag.toLowerCase()))) {
			confidence += 0.2;
		}

		// 包含日期信息
		if (this.DATE_PATTERNS.some(pattern => pattern.regex.test(content))) {
			confidence += 0.1;
		}

		// 包含优先级信息
		if (this.PRIORITY_PATTERNS.some(pattern => pattern.regex.test(content))) {
			confidence += 0.1;
		}

		// 内容长度合理性
		const cleanTitle = this.extractTaskTitle(content);
		if (cleanTitle.length >= 5 && cleanTitle.length <= 100) {
			confidence += 0.1;
		}

		// 避免识别标题或其他非任务内容
		if (content.startsWith('#') || content.includes('##')) {
			confidence -= 0.3;
		}

		return Math.min(1.0, Math.max(0.0, confidence));
	}

	/**
	 * 构建任务-笔记索引
	 */
	private async buildTaskNoteIndex(): Promise<void> {
		try {
			const allTasks = await this.taskRepository.findAll();
			
			// 清空现有索引
			this.taskNoteReferences.clear();
			this.noteTaskReferences.clear();

			// 重建索引
			for (const task of allTasks) {
				if (task.linkedNotes && task.linkedNotes.length > 0) {
					this.taskNoteReferences.set(task.id, task.linkedNotes);
					
					for (const notePath of task.linkedNotes) {
						// 获取现有引用
						const existingReferences = this.safeGetNoteTaskReferences(notePath);
						
						// 创建新的引用
						const newReference: NoteTaskReference = {
							taskId: task.id,
							noteId: this.generateNoteId(notePath),
							notePath: notePath,
							lastSyncAt: new Date()
						};
						
						// 检查是否已存在
						const exists = existingReferences.some(ref => ref.taskId === task.id);
						if (!exists) {
							existingReferences.push(newReference);
							this.safeSetNoteTaskReferences(notePath, existingReferences);
						}
					}
				}
			}

			console.log(`Built task-note index: ${this.taskNoteReferences.size} tasks, ${this.noteTaskReferences.size} notes`);
		} catch (error) {
			console.error('Error building task-note index:', error);
		}
	}

	/**
	 * 生成笔记ID（基于路径）
	 */
	private generateNoteId(notePath: string): string {
		// 使用路径的哈希值作为noteId
		return 'note_' + notePath.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
	}

	/**
	 * 注册文件监听器
	 */
	private registerFileWatchers(): void {
		// 监听文件修改
		this.app.vault.on('modify', this.onFileModified.bind(this));
		
		// 监听文件删除
		this.app.vault.on('delete', this.onFileDeleted.bind(this));
		
		// 监听文件重命名
		this.app.vault.on('rename', this.onFileRenamed.bind(this));
	}

	/**
	 * 处理文件修改事件
	 */
	private async onFileModified(file: TFile): Promise<void> {
		if (file.extension !== 'md') return;

		try {
			// 解析文件中的任务
			const content = await this.app.vault.read(file);
			const parsedTasks = this.parseTasksFromNote(content, file.path);
			
			// 检查是否有高置信度的新任务
			const highConfidenceTasks = parsedTasks.filter(task => task.confidence > 0.8);
			
			if (highConfidenceTasks.length > 0) {
				console.log(`Found ${highConfidenceTasks.length} high-confidence tasks in ${file.path}`);
				// 这里可以触发任务自动创建的逻辑
				// 但为了避免意外创建，暂时只记录日志
			}
		} catch (error) {
			console.error('Error processing file modification:', error);
		}
	}

	/**
	 * 处理文件删除事件
	 */
	private async onFileDeleted(file: TFile): Promise<void> {
		if (file.extension !== 'md') return;

		try {
			const taskReferences = this.safeGetNoteTaskReferences(file.path);
			if (taskReferences.length > 0) {
				const taskIds = this.convertNoteTaskReferencesToStrings(taskReferences);
				console.log(`Note ${file.path} deleted, found ${taskIds.length} linked tasks`);
				
				// 通知用户有关联的任务
				new Notice(`笔记 ${file.name} 已删除，包含 ${taskIds.length} 个关联任务。请检查任务管理器。`);
				
				// 清理索引
				this.noteTaskReferences.delete(file.path);
				
				// 更新任务的笔记链接
				for (const taskId of taskIds) {
					await this.removeNoteFromTask(taskId, file.path);
				}
			}
		} catch (error) {
			console.error('Error handling file deletion:', error);
		}
	}

	/**
	 * 处理文件重命名事件
	 */
	private async onFileRenamed(file: TFile, oldPath: string): Promise<void> {
		if (file.extension !== 'md') return;

		try {
			const taskReferences = this.safeGetNoteTaskReferences(oldPath);
			if (taskReferences.length > 0) {
				const taskIds = this.convertNoteTaskReferencesToStrings(taskReferences);
				console.log(`Note renamed from ${oldPath} to ${file.path}, updating ${taskIds.length} tasks`);
				
				// 更新引用中的路径
				const updatedReferences = taskReferences.map(ref => ({
					...ref,
					notePath: file.path,
					noteId: this.generateNoteId(file.path),
					lastSyncAt: new Date()
				}));
				
				// 更新索引
				this.noteTaskReferences.delete(oldPath);
				this.safeSetNoteTaskReferences(file.path, updatedReferences);
				
				// 更新任务的笔记链接
				for (const taskId of taskIds) {
					await this.updateNotePathInTask(taskId, oldPath, file.path);
				}
			}
		} catch (error) {
			console.error('Error handling file rename:', error);
		}
	}

	/**
	 * 从任务中移除笔记链接
	 */
	private async removeNoteFromTask(taskId: string, notePath: string): Promise<void> {
		try {
			const task = await this.taskRepository.findById(taskId);
			if (task && task.linkedNotes) {
				const updatedNotes = task.linkedNotes.filter(path => path !== notePath);
				await this.taskRepository.update(taskId, { linkedNotes: updatedNotes });
				
				// 更新本地索引
				this.taskNoteReferences.set(taskId, updatedNotes);
			}
		} catch (error) {
			console.error('Error removing note from task:', error);
		}
	}

	/**
	 * 更新任务中的笔记路径
	 */
	private async updateNotePathInTask(taskId: string, oldPath: string, newPath: string): Promise<void> {
		try {
			const task = await this.taskRepository.findById(taskId);
			if (task && task.linkedNotes) {
				const updatedNotes = task.linkedNotes.map(path => 
					path === oldPath ? newPath : path
				);
				await this.taskRepository.update(taskId, { linkedNotes: updatedNotes });
				
				// 更新本地索引
				this.taskNoteReferences.set(taskId, updatedNotes);
			}
		} catch (error) {
			console.error('Error updating note path in task:', error);
		}
	}

	/**
	 * 获取笔记中的任务引用
	 */
	getTasksInNote(notePath: string): string[] {
		const references = this.safeGetNoteTaskReferences(notePath);
		return this.convertNoteTaskReferencesToStrings(references);
	}

	/**
	 * 获取笔记中的任务引用详情
	 */
	getTaskReferencesInNote(notePath: string): NoteTaskReference[] {
		return this.safeGetNoteTaskReferences(notePath);
	}

	/**
	 * 获取任务关联的笔记
	 */
	getNotesForTask(taskId: string): string[] {
		return this.taskNoteReferences.get(taskId) || [];
	}

	/**
	 * 添加任务与笔记的链接关系
	 */
	async addTaskNoteLink(taskId: string, notePath: string): Promise<void> {
		try {
			// 更新任务的笔记链接
			const task = await this.taskRepository.findById(taskId);
			if (task) {
				const linkedNotes = task.linkedNotes || [];
				if (!linkedNotes.includes(notePath)) {
					linkedNotes.push(notePath);
					await this.taskRepository.update(taskId, { linkedNotes });
					
					// 更新本地索引
					this.taskNoteReferences.set(taskId, linkedNotes);
					
					// 更新反向索引
					const existingReferences = this.safeGetNoteTaskReferences(notePath);
					const exists = existingReferences.some(ref => ref.taskId === taskId);
					
					if (!exists) {
						const newReference: NoteTaskReference = {
							taskId,
							noteId: this.generateNoteId(notePath),
							notePath,
							lastSyncAt: new Date()
						};
						existingReferences.push(newReference);
						this.safeSetNoteTaskReferences(notePath, existingReferences);
					}
					
					console.log(`Added link: Task ${taskId} <-> Note ${notePath}`);
				}
			}
		} catch (error) {
			console.error('Error adding task-note link:', error);
		}
	}

	/**
	 * 移除任务与笔记的链接关系
	 */
	async removeTaskNoteLink(taskId: string, notePath: string): Promise<void> {
		try {
			// 更新任务的笔记链接
			const task = await this.taskRepository.findById(taskId);
			if (task && task.linkedNotes) {
				const linkedNotes = task.linkedNotes.filter(path => path !== notePath);
				await this.taskRepository.update(taskId, { linkedNotes });
				
				// 更新本地索引
				this.taskNoteReferences.set(taskId, linkedNotes);
				
				// 更新反向索引
				const existingReferences = this.safeGetNoteTaskReferences(notePath);
				const updatedReferences = existingReferences.filter(ref => ref.taskId !== taskId);
				
				if (updatedReferences.length > 0) {
					this.safeSetNoteTaskReferences(notePath, updatedReferences);
				} else {
					this.noteTaskReferences.delete(notePath);
				}
				
				console.log(`Removed link: Task ${taskId} <-> Note ${notePath}`);
			}
		} catch (error) {
			console.error('Error removing task-note link:', error);
		}
	}

	/**
	 * 获取笔记中任务引用的详细信息
	 */
	async getNoteTaskDetails(notePath: string): Promise<Array<{
		taskId: string;
		task: Task | null;
		lineNumber?: number;
		isValid: boolean;
	}>> {
		const taskIds = this.getTasksInNote(notePath);
		const details = [];

		for (const taskId of taskIds) {
			try {
				const task = await this.taskRepository.findById(taskId);
				details.push({
					taskId,
					task,
					isValid: task !== null
				});
			} catch (error) {
				details.push({
					taskId,
					task: null,
					isValid: false
				});
			}
		}

		return details;
	}

	/**
	 * 同步任务状态到笔记中
	 */
	async syncTaskStatusToNote(taskId: string, newStatus: TaskStatus): Promise<void> {
		try {
			const notePaths = this.getNotesForTask(taskId);
			const task = await this.taskRepository.findById(taskId);
			
			if (!task) return;

			for (const notePath of notePaths) {
				await this.updateTaskStatusInNote(notePath, task, newStatus);
			}
		} catch (error) {
			console.error('Error syncing task status to notes:', error);
		}
	}

	/**
	 * 在笔记中更新任务状态
	 */
	private async updateTaskStatusInNote(notePath: string, task: Task, newStatus: TaskStatus): Promise<void> {
		try {
			const file = this.app.vault.getAbstractFileByPath(notePath);
			if (!(file instanceof TFile)) return;

			const content = await this.app.vault.read(file);
			const lines = content.split('\n');
			let modified = false;

			// 查找包含该任务的行
			for (let i = 0; i < lines.length; i++) {
				const line = lines[i];
				
				// 检查这一行是否包含我们的任务
				if (this.lineContainsTask(line, task)) {
					const newStatusChar = this.getStatusChar(newStatus);
					const updatedLine = line.replace(/\[(.)\]/, `[${newStatusChar}]`);
					
					if (updatedLine !== line) {
						lines[i] = updatedLine;
						modified = true;
						console.log(`Updated task status in ${notePath}:${i + 1}`);
					}
				}
			}

			if (modified) {
				const newContent = lines.join('\n');
				await this.app.vault.modify(file, newContent);
			}
		} catch (error) {
			console.error('Error updating task status in note:', error);
		}
	}

	/**
	 * 检查行是否包含指定任务
	 */
	private lineContainsTask(line: string, task: Task): boolean {
		// 简单的匹配逻辑 - 检查任务标题是否在行中
		const taskMatch = line.match(/^(\s*)-\s*\[(.)\]\s*(.+)$/);
		if (taskMatch) {
			const content = taskMatch[3];
			const cleanTitle = this.extractTaskTitle(content);
			return cleanTitle === task.title;
		}
		return false;
	}

	/**
	 * 获取任务状态对应的字符
	 */
	private getStatusChar(status: TaskStatus): string {
		const statusMap: Record<TaskStatus, string> = {
			[TaskStatus.TODO]: ' ',
			[TaskStatus.IN_PROGRESS]: '/',
			[TaskStatus.BLOCKED]: '<',
			[TaskStatus.REVIEW]: '?',
			[TaskStatus.COMPLETED]: 'x',
			[TaskStatus.CANCELLED]: '-'
		};
		return statusMap[status] || ' ';
	}

	/**
	 * 处理任务删除时的笔记清理
	 */
	async handleTaskDeletion(taskId: string): Promise<void> {
		try {
			const notePaths = this.getNotesForTask(taskId);
			
			if (notePaths.length > 0) {
				console.log(`Task ${taskId} deleted, cleaning up references in ${notePaths.length} notes`);
				
				// 清理索引
				this.taskNoteReferences.delete(taskId);
				
				// 从笔记的反向索引中移除任务ID
				for (const notePath of notePaths) {
					const existingReferences = this.safeGetNoteTaskReferences(notePath);
					const updatedReferences = existingReferences.filter(ref => ref.taskId !== taskId);
					
					if (updatedReferences.length > 0) {
						this.safeSetNoteTaskReferences(notePath, updatedReferences);
					} else {
						this.noteTaskReferences.delete(notePath);
					}
				}
				
				// 可选：在笔记中标记任务为已删除（而不是直接删除行）
				// 这样用户可以看到任务曾经存在过
				for (const notePath of notePaths) {
					await this.markTaskAsDeletedInNote(notePath, taskId);
				}
			}
		} catch (error) {
			console.error('Error handling task deletion:', error);
		}
	}

	/**
	 * 在笔记中标记任务为已删除
	 */
	private async markTaskAsDeletedInNote(notePath: string, taskId: string): Promise<void> {
		try {
			const file = this.app.vault.getAbstractFileByPath(notePath);
			if (!(file instanceof TFile)) return;

			// 这里可以实现在笔记中添加删除标记的逻辑
			// 例如添加注释或修改任务状态
			console.log(`Marked task ${taskId} as deleted in note ${notePath}`);
		} catch (error) {
			console.error('Error marking task as deleted in note:', error);
		}
	}

	/**
	 * 验证和修复链接一致性
	 */
	async validateAndRepairLinks(): Promise<{
		validLinks: number;
		repairedLinks: number;
		brokenLinks: number;
	}> {
		let validLinks = 0;
		let repairedLinks = 0;
		let brokenLinks = 0;

		try {
			// 检查所有任务的笔记链接
			const allTasks = await this.taskRepository.findAll();
			
			for (const task of allTasks) {
				if (task.linkedNotes && task.linkedNotes.length > 0) {
					for (const notePath of task.linkedNotes) {
						const file = this.app.vault.getAbstractFileByPath(notePath);
						
						if (file instanceof TFile) {
							// 文件存在，检查内容是否包含任务
							const content = await this.app.vault.read(file);
							const containsTask = this.noteContainsTask(content, task);
							
							if (containsTask) {
								validLinks++;
							} else {
								// 尝试修复链接
								const repaired = await this.repairTaskInNote(file, task);
								if (repaired) {
									repairedLinks++;
								} else {
									brokenLinks++;
								}
							}
						} else {
							// 文件不存在，移除链接
							await this.removeTaskNoteLink(task.id, notePath);
							brokenLinks++;
						}
					}
				}
			}

			console.log(`Link validation completed: ${validLinks} valid, ${repairedLinks} repaired, ${brokenLinks} broken`);
		} catch (error) {
			console.error('Error validating links:', error);
		}

		return { validLinks, repairedLinks, brokenLinks };
	}

	/**
	 * 检查笔记是否包含指定任务
	 */
	private noteContainsTask(content: string, task: Task): boolean {
		const parsedTasks = this.parseTasksFromNote(content, '');
		return parsedTasks.some(parsed => 
			parsed.title === task.title && parsed.confidence > 0.7
		);
	}

	/**
	 * 尝试修复笔记中的任务
	 */
	private async repairTaskInNote(file: TFile, task: Task): Promise<boolean> {
		try {
			// 这里可以实现智能修复逻辑
			// 例如查找相似的任务行并更新
			console.log(`Attempting to repair task ${task.id} in ${file.path}`);
			return false; // 暂时返回false，表示未修复
		} catch (error) {
			console.error('Error repairing task in note:', error);
			return false;
		}
	}

	/**
	 * 多笔记任务引用一致性维护
	 */
	async maintainConsistency(taskId: string): Promise<{
		conflicts: Array<{
			notePath: string;
			conflictType: 'status_mismatch' | 'content_mismatch' | 'missing_reference';
			details: string;
		}>;
		resolved: number;
		failed: number;
	}> {
		const conflicts = [];
		let resolved = 0;
		let failed = 0;

		try {
			const task = await this.taskRepository.findById(taskId);
			if (!task) {
				return { conflicts, resolved, failed };
			}

			const notePaths = this.getNotesForTask(taskId);
			const taskReferences = new Map<string, {
				status: TaskStatus;
				content: string;
				lineNumber: number;
			}>();

			// 收集所有笔记中的任务引用信息
			for (const notePath of notePaths) {
				const reference = await this.extractTaskReferenceFromNote(notePath, task);
				if (reference) {
					taskReferences.set(notePath, reference);
				} else {
					conflicts.push({
						notePath,
						conflictType: 'missing_reference',
						details: `任务 "${task.title}" 在笔记中未找到对应引用`
					});
				}
			}

			// 检测状态冲突
			const statusConflicts = this.detectStatusConflicts(task, taskReferences);
			conflicts.push(...statusConflicts);

			// 检测内容冲突
			const contentConflicts = this.detectContentConflicts(task, taskReferences);
			conflicts.push(...contentConflicts);

			// 尝试解决冲突
			for (const conflict of conflicts) {
				try {
					const success = await this.resolveConflict(task, conflict, taskReferences);
					if (success) {
						resolved++;
					} else {
						failed++;
					}
				} catch (error) {
					console.error(`Failed to resolve conflict in ${conflict.notePath}:`, error);
					failed++;
				}
			}

			console.log(`Consistency check for task ${taskId}: ${conflicts.length} conflicts found, ${resolved} resolved, ${failed} failed`);
		} catch (error) {
			console.error('Error maintaining consistency:', error);
		}

		return { conflicts, resolved, failed };
	}

	/**
	 * 从笔记中提取任务引用信息
	 */
	private async extractTaskReferenceFromNote(notePath: string, task: Task): Promise<{
		status: TaskStatus;
		content: string;
		lineNumber: number;
	} | null> {
		try {
			const file = this.app.vault.getAbstractFileByPath(notePath);
			if (!(file instanceof TFile)) return null;

			const content = await this.app.vault.read(file);
			const lines = content.split('\n');

			for (let i = 0; i < lines.length; i++) {
				const line = lines[i];
				if (this.lineContainsTask(line, task)) {
					const statusMatch = line.match(/\[(.)\]/);
					if (statusMatch) {
						const statusChar = statusMatch[1];
						const status = this.STATUS_CHAR_MAP[statusChar] || TaskStatus.TODO;
						return {
							status,
							content: line.trim(),
							lineNumber: i + 1
						};
					}
				}
			}
		} catch (error) {
			console.error(`Error extracting task reference from ${notePath}:`, error);
		}

		return null;
	}

	/**
	 * 检测状态冲突
	 */
	private detectStatusConflicts(task: Task, taskReferences: Map<string, any>): Array<{
		notePath: string;
		conflictType: 'status_mismatch';
		details: string;
	}> {
		const conflicts = [];
		const expectedStatus = task.status;

		for (const [notePath, reference] of taskReferences) {
			if (reference.status !== expectedStatus) {
				conflicts.push({
					notePath,
					conflictType: 'status_mismatch' as const,
					details: `任务状态不一致: 期望 ${expectedStatus}, 实际 ${reference.status} (行 ${reference.lineNumber})`
				});
			}
		}

		return conflicts;
	}

	/**
	 * 检测内容冲突
	 */
	private detectContentConflicts(task: Task, taskReferences: Map<string, any>): Array<{
		notePath: string;
		conflictType: 'content_mismatch';
		details: string;
	}> {
		const conflicts = [];
		const expectedTitle = task.title;

		for (const [notePath, reference] of taskReferences) {
			// 提取引用中的任务标题
			const referenceTitle = this.extractTaskTitle(reference.content);
			
			// 检查标题是否匹配（允许一定的差异）
			if (!this.titlesMatch(expectedTitle, referenceTitle)) {
				conflicts.push({
					notePath,
					conflictType: 'content_mismatch' as const,
					details: `任务标题不一致: 期望 "${expectedTitle}", 实际 "${referenceTitle}" (行 ${reference.lineNumber})`
				});
			}
		}

		return conflicts;
	}

	/**
	 * 检查两个标题是否匹配
	 */
	private titlesMatch(title1: string, title2: string): boolean {
		// 简单的相似度检查
		const normalized1 = title1.toLowerCase().trim();
		const normalized2 = title2.toLowerCase().trim();
		
		// 完全匹配
		if (normalized1 === normalized2) return true;
		
		// 检查是否一个是另一个的子串
		if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
			return true;
		}
		
		// 计算编辑距离（简化版）
		const distance = this.calculateEditDistance(normalized1, normalized2);
		const maxLength = Math.max(normalized1.length, normalized2.length);
		const similarity = 1 - (distance / maxLength);
		
		return similarity > 0.8; // 80%相似度阈值
	}

	/**
	 * 计算编辑距离（简化版）
	 */
	private calculateEditDistance(str1: string, str2: string): number {
		const matrix = [];
		const len1 = str1.length;
		const len2 = str2.length;

		for (let i = 0; i <= len1; i++) {
			matrix[i] = [i];
		}

		for (let j = 0; j <= len2; j++) {
			matrix[0][j] = j;
		}

		for (let i = 1; i <= len1; i++) {
			for (let j = 1; j <= len2; j++) {
				if (str1.charAt(i - 1) === str2.charAt(j - 1)) {
					matrix[i][j] = matrix[i - 1][j - 1];
				} else {
					matrix[i][j] = Math.min(
						matrix[i - 1][j - 1] + 1,
						matrix[i][j - 1] + 1,
						matrix[i - 1][j] + 1
					);
				}
			}
		}

		return matrix[len1][len2];
	}

	/**
	 * 解决冲突
	 */
	private async resolveConflict(
		task: Task, 
		conflict: { notePath: string; conflictType: string; details: string },
		taskReferences: Map<string, any>
	): Promise<boolean> {
		try {
			switch (conflict.conflictType) {
				case 'status_mismatch':
					return await this.resolveStatusConflict(task, conflict.notePath);
				
				case 'content_mismatch':
					return await this.resolveContentConflict(task, conflict.notePath, taskReferences);
				
				case 'missing_reference':
					return await this.resolveMissingReference(task, conflict.notePath);
				
				default:
					console.warn(`Unknown conflict type: ${conflict.conflictType}`);
					return false;
			}
		} catch (error) {
			console.error(`Error resolving conflict in ${conflict.notePath}:`, error);
			return false;
		}
	}

	/**
	 * 解决状态冲突
	 */
	private async resolveStatusConflict(task: Task, notePath: string): Promise<boolean> {
		try {
			await this.updateTaskStatusInNote(notePath, task, task.status);
			console.log(`Resolved status conflict in ${notePath}`);
			return true;
		} catch (error) {
			console.error(`Failed to resolve status conflict in ${notePath}:`, error);
			return false;
		}
	}

	/**
	 * 解决内容冲突
	 */
	private async resolveContentConflict(
		task: Task, 
		notePath: string, 
		taskReferences: Map<string, any>
	): Promise<boolean> {
		try {
			const file = this.app.vault.getAbstractFileByPath(notePath);
			if (!(file instanceof TFile)) return false;

			const content = await this.app.vault.read(file);
			const lines = content.split('\n');
			const reference = taskReferences.get(notePath);
			
			if (!reference) return false;

			// 找到需要更新的行
			const lineIndex = reference.lineNumber - 1;
			if (lineIndex >= 0 && lineIndex < lines.length) {
				const oldLine = lines[lineIndex];
				
				// 保持原有格式，只更新任务标题
				const statusMatch = oldLine.match(/^(\s*[-*+]|\d+\.)\s*\[(.)\]\s*/);
				if (statusMatch) {
					const prefix = statusMatch[0];
					const statusChar = statusMatch[2];
					
					// 重建任务行，保持其他元数据
					const metadata = this.extractMetadataFromLine(oldLine);
					const newLine = `${prefix}${task.title}${metadata}`;
					
					lines[lineIndex] = newLine;
					
					const newContent = lines.join('\n');
					await this.app.vault.modify(file, newContent);
					
					console.log(`Resolved content conflict in ${notePath}:${reference.lineNumber}`);
					return true;
				}
			}
		} catch (error) {
			console.error(`Failed to resolve content conflict in ${notePath}:`, error);
		}
		
		return false;
	}

	/**
	 * 从行中提取元数据（标签、日期等）
	 */
	private extractMetadataFromLine(line: string): string {
		// 提取标签
		const tags = line.match(/#[\w-]+/g) || [];
		
		// 提取日期
		const dates = line.match(/📅\s*\d{4}-\d{2}-\d{2}|🗓️\s*\d{4}-\d{2}-\d{2}|⏰\s*\d{4}-\d{2}-\d{2}/g) || [];
		
		// 提取优先级
		const priorities = line.match(/[⏫🔼🔽⏬]/g) || [];
		
		// 组合元数据
		const metadata = [...priorities, ...dates, ...tags];
		return metadata.length > 0 ? ` ${metadata.join(' ')}` : '';
	}

	/**
	 * 解决缺失引用
	 */
	private async resolveMissingReference(task: Task, notePath: string): Promise<boolean> {
		try {
			// 这里可以实现智能添加任务引用的逻辑
			// 例如在笔记的特定位置添加任务行
			console.log(`Missing reference resolution not implemented for ${notePath}`);
			return false;
		} catch (error) {
			console.error(`Failed to resolve missing reference in ${notePath}:`, error);
			return false;
		}
	}

	/**
	 * 批量维护所有任务的一致性
	 */
	async maintainAllTasksConsistency(): Promise<{
		totalTasks: number;
		processedTasks: number;
		totalConflicts: number;
		resolvedConflicts: number;
		failedConflicts: number;
	}> {
		const result = {
			totalTasks: 0,
			processedTasks: 0,
			totalConflicts: 0,
			resolvedConflicts: 0,
			failedConflicts: 0
		};

		try {
			const allTasks = await this.taskRepository.findAll();
			result.totalTasks = allTasks.length;

			for (const task of allTasks) {
				if (task.linkedNotes && task.linkedNotes.length > 1) {
					// 只处理有多个笔记引用的任务
					const consistency = await this.maintainConsistency(task.id);
					
					result.processedTasks++;
					result.totalConflicts += consistency.conflicts.length;
					result.resolvedConflicts += consistency.resolved;
					result.failedConflicts += consistency.failed;
				}
			}

			console.log(`Consistency maintenance completed: ${result.processedTasks}/${result.totalTasks} tasks processed`);
		} catch (error) {
			console.error('Error maintaining all tasks consistency:', error);
		}

		return result;
	}

	/**
	 * 清理资源
	 */
	cleanup(): void {
		// 移除事件监听器
		this.app.vault.off('modify', this.onFileModified.bind(this));
		this.app.vault.off('delete', this.onFileDeleted.bind(this));
		this.app.vault.off('rename', this.onFileRenamed.bind(this));
		
		// 清空索引
		this.noteTaskReferences.clear();
		this.taskNoteReferences.clear();
		
		this.isInitialized = false;
	}
}