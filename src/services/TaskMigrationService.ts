// 任务迁移服务 - 处理独立任务到项目的迁移

import { App, Notice } from 'obsidian';
import { Task, TaskStatus, Priority, Project } from '../models';
import { TaskRepository } from './TaskRepository';
import { ProjectRepository } from './ProjectRepository';
import { IndependentTaskModeController } from './IndependentTaskModeController';
import { TaskManager } from './TaskManager';

export interface TaskMigrationOptions {
	taskIds: string[];
	targetProjectId: string;
	preserveStatus?: boolean;
	preservePriority?: boolean;
	preserveAssignee?: boolean;
	preserveTags?: boolean;
	preserveDates?: boolean;
	addMigrationTag?: boolean;
	migrationTagName?: string;
}

export interface TaskMigrationResult {
	success: number;
	failed: number;
	migratedTasks: Task[];
	errors: Array<{
		taskId: string;
		taskTitle: string;
		error: string;
	}>;
	warnings: Array<{
		taskId: string;
		taskTitle: string;
		warning: string;
	}>;
}

export interface MigrationValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
	taskCount: number;
	targetProject: Project | null;
}

export interface MigrationPreview {
	tasks: Array<{
		task: Task;
		changes: {
			projectId: { from: string; to: string };
			status?: { from: TaskStatus; to: TaskStatus };
			priority?: { from: Priority; to: Priority };
			assignee?: { from: string | undefined; to: string | undefined };
			tags?: { from: string[]; to: string[] };
		};
		conflicts: string[];
		warnings: string[];
	}>;
	summary: {
		totalTasks: number;
		tasksWithConflicts: number;
		tasksWithWarnings: number;
		estimatedDuration: number; // 预估迁移时间（秒）
	};
}

/**
 * 任务迁移服务
 * 负责处理独立任务到项目的迁移，包括数据验证、冲突检测和批量迁移
 */
export class TaskMigrationService {
	private app: App;
	private taskRepository: TaskRepository;
	private projectRepository: ProjectRepository;
	private independentController: IndependentTaskModeController;
	private taskManager: TaskManager;

	constructor(
		app: App,
		taskRepository: TaskRepository,
		projectRepository: ProjectRepository,
		independentController: IndependentTaskModeController,
		taskManager: TaskManager
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.projectRepository = projectRepository;
		this.independentController = independentController;
		this.taskManager = taskManager;
	}

	/**
	 * 验证迁移操作
	 */
	async validateMigration(options: TaskMigrationOptions): Promise<MigrationValidationResult> {
		const result: MigrationValidationResult = {
			isValid: true,
			errors: [],
			warnings: [],
			taskCount: options.taskIds.length,
			targetProject: null
		};

		try {
			// 验证目标项目
			const targetProject = await this.projectRepository.findById(options.targetProjectId);
			if (!targetProject) {
				result.errors.push(`目标项目不存在: ${options.targetProjectId}`);
				result.isValid = false;
			} else {
				result.targetProject = targetProject;
			}

			// 验证任务
			if (options.taskIds.length === 0) {
				result.errors.push('没有选择要迁移的任务');
				result.isValid = false;
			}

			// 检查任务是否存在且为独立任务
			const independentProjectId = this.independentController.getIndependentProjectId();
			for (const taskId of options.taskIds) {
				const task = await this.taskRepository.findById(taskId);
				if (!task) {
					result.errors.push(`任务不存在: ${taskId}`);
					result.isValid = false;
				} else if (task.projectId !== independentProjectId) {
					result.errors.push(`任务 "${task.title}" 不是独立任务，无法迁移`);
					result.isValid = false;
				}
			}

			// 检查是否在独立任务模式
			if (!this.independentController.isIndependentMode()) {
				result.warnings.push('当前不在独立任务模式，建议切换到独立任务模式进行迁移');
			}

			// 检查目标项目状态
			if (result.targetProject && result.targetProject.status === 'completed') {
				result.warnings.push('目标项目已完成，迁移的任务可能需要重新激活');
			}

		} catch (error) {
			result.errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
			result.isValid = false;
		}

		return result;
	}

	/**
	 * 生成迁移预览
	 */
	async generateMigrationPreview(options: TaskMigrationOptions): Promise<MigrationPreview> {
		const preview: MigrationPreview = {
			tasks: [],
			summary: {
				totalTasks: options.taskIds.length,
				tasksWithConflicts: 0,
				tasksWithWarnings: 0,
				estimatedDuration: 0
			}
		};

		try {
			const targetProject = await this.projectRepository.findById(options.targetProjectId);
			if (!targetProject) {
				throw new Error(`目标项目不存在: ${options.targetProjectId}`);
			}

			for (const taskId of options.taskIds) {
				const task = await this.taskRepository.findById(taskId);
				if (!task) {
					continue;
				}

				const taskPreview = {
					task,
					changes: {
						projectId: {
							from: task.projectId,
							to: options.targetProjectId
						}
					} as any,
					conflicts: [] as string[],
					warnings: [] as string[]
				};

				// 检查状态变更
				if (!options.preserveStatus && task.status !== TaskStatus.TODO) {
					taskPreview.changes.status = {
						from: task.status,
						to: TaskStatus.TODO
					};
					taskPreview.warnings.push('任务状态将重置为待办');
				}

				// 检查优先级变更
				if (!options.preservePriority && task.priority !== Priority.MEDIUM) {
					taskPreview.changes.priority = {
						from: task.priority,
						to: Priority.MEDIUM
					};
					taskPreview.warnings.push('任务优先级将重置为中等');
				}

				// 检查负责人变更
				if (!options.preserveAssignee && task.assignee) {
					taskPreview.changes.assignee = {
						from: task.assignee,
						to: undefined
					};
					taskPreview.warnings.push('任务负责人将被清除');
				}

				// 检查标签变更
				if (options.addMigrationTag) {
					const migrationTag = options.migrationTagName || 'migrated';
					const newTags = options.preserveTags ? 
						[...task.tags, migrationTag] : 
						[migrationTag];
					
					taskPreview.changes.tags = {
						from: task.tags,
						to: newTags
					};
				} else if (!options.preserveTags && task.tags.length > 0) {
					taskPreview.changes.tags = {
						from: task.tags,
						to: []
					};
					taskPreview.warnings.push('任务标签将被清除');
				}

				// 检查日期冲突
				if (!options.preserveDates) {
					if (task.startDate || task.dueDate) {
						taskPreview.warnings.push('任务日期将被清除');
					}
				}

				// 检查任务标题冲突
				const existingTasks = await this.taskRepository.findByProject(options.targetProjectId);
				const titleConflict = existingTasks.find(t => t.title === task.title);
				if (titleConflict) {
					taskPreview.conflicts.push(`目标项目中已存在同名任务: "${task.title}"`);
				}

				// 检查依赖关系
				if (task.dependencies.length > 0) {
					taskPreview.conflicts.push('任务包含依赖关系，迁移后依赖关系将丢失');
				}

				// 检查子任务
				if (task.childTaskIds.length > 0) {
					taskPreview.conflicts.push('任务包含子任务，独立任务不支持层级关系');
				}

				// 检查父任务
				if (task.parentTaskId) {
					taskPreview.conflicts.push('任务是子任务，独立任务不支持层级关系');
				}

				if (taskPreview.conflicts.length > 0) {
					preview.summary.tasksWithConflicts++;
				}

				if (taskPreview.warnings.length > 0) {
					preview.summary.tasksWithWarnings++;
				}

				preview.tasks.push(taskPreview);
			}

			// 估算迁移时间（每个任务约0.5秒）
			preview.summary.estimatedDuration = Math.ceil(options.taskIds.length * 0.5);

		} catch (error) {
			console.error('Failed to generate migration preview:', error);
			throw new Error(`生成迁移预览失败: ${error instanceof Error ? error.message : String(error)}`);
		}

		return preview;
	}

	/**
	 * 执行任务迁移
	 */
	async migrateTasks(options: TaskMigrationOptions): Promise<TaskMigrationResult> {
		const result: TaskMigrationResult = {
			success: 0,
			failed: 0,
			migratedTasks: [],
			errors: [],
			warnings: []
		};

		try {
			// 首先验证迁移
			const validation = await this.validateMigration(options);
			if (!validation.isValid) {
				throw new Error(`迁移验证失败: ${validation.errors.join(', ')}`);
			}

			const independentProjectId = this.independentController.getIndependentProjectId();

			// 逐个迁移任务
			for (const taskId of options.taskIds) {
				try {
					const task = await this.taskRepository.findById(taskId);
					if (!task) {
						result.errors.push({
							taskId,
							taskTitle: '未知任务',
							error: '任务不存在'
						});
						result.failed++;
						continue;
					}

					// 确认是独立任务
					if (task.projectId !== independentProjectId) {
						result.errors.push({
							taskId,
							taskTitle: task.title,
							error: '不是独立任务'
						});
						result.failed++;
						continue;
					}

					// 准备迁移的任务数据
					const migratedTaskData: Partial<Task> = {
						projectId: options.targetProjectId
					};

					// 处理状态
					if (!options.preserveStatus) {
						migratedTaskData.status = TaskStatus.TODO;
					}

					// 处理优先级
					if (!options.preservePriority) {
						migratedTaskData.priority = Priority.MEDIUM;
					}

					// 处理负责人
					if (!options.preserveAssignee) {
						migratedTaskData.assignee = undefined;
					}

					// 处理标签
					let newTags = options.preserveTags ? [...task.tags] : [];
					if (options.addMigrationTag) {
						const migrationTag = options.migrationTagName || 'migrated';
						if (!newTags.includes(migrationTag)) {
							newTags.push(migrationTag);
						}
					}
					migratedTaskData.tags = newTags;

					// 处理日期
					if (!options.preserveDates) {
						migratedTaskData.startDate = undefined;
						migratedTaskData.dueDate = undefined;
						migratedTaskData.completedDate = undefined;
					}

					// 清除独立任务不支持的字段
					migratedTaskData.parentTaskId = undefined;
					migratedTaskData.childTaskIds = [];
					migratedTaskData.dependencies = [];
					migratedTaskData.sprintId = undefined;

					// 重新计算位置
					const existingTasks = await this.taskRepository.findByProject(options.targetProjectId);
					const maxPosition = Math.max(0, ...existingTasks.map(t => t.position || 0));
					migratedTaskData.position = maxPosition + 1;

					// 更新任务
					const migratedTask = await this.taskRepository.update(taskId, {
						...migratedTaskData,
						updatedAt: new Date()
					});

					if (migratedTask) {
						result.migratedTasks.push(migratedTask);
						result.success++;

						// 添加警告信息
						if (task.dependencies.length > 0) {
							result.warnings.push({
								taskId,
								taskTitle: task.title,
								warning: '任务的依赖关系已丢失'
							});
						}

						if (task.childTaskIds.length > 0) {
							result.warnings.push({
								taskId,
								taskTitle: task.title,
								warning: '任务的子任务关系已丢失'
							});
						}
					} else {
						result.errors.push({
							taskId,
							taskTitle: task.title,
							error: '更新任务失败'
						});
						result.failed++;
					}

				} catch (error) {
					const task = await this.taskRepository.findById(taskId);
					result.errors.push({
						taskId,
						taskTitle: task?.title || '未知任务',
						error: error instanceof Error ? error.message : String(error)
					});
					result.failed++;
				}
			}

			// 显示迁移结果通知
			if (result.success > 0) {
				new Notice(`成功迁移 ${result.success} 个任务到项目`);
			}

			if (result.failed > 0) {
				new Notice(`${result.failed} 个任务迁移失败`, 5000);
			}

		} catch (error) {
			console.error('Failed to migrate tasks:', error);
			throw new Error(`任务迁移失败: ${error instanceof Error ? error.message : String(error)}`);
		}

		return result;
	}

	/**
	 * 回滚任务迁移
	 */
	async rollbackMigration(migratedTasks: Task[]): Promise<{
		success: number;
		failed: number;
		errors: string[];
	}> {
		const result = {
			success: 0,
			failed: 0,
			errors: [] as string[]
		};

		const independentProjectId = this.independentController.getIndependentProjectId();

		for (const task of migratedTasks) {
			try {
				// 将任务迁移回独立任务项目
				const rolledBackTask = await this.taskRepository.update(task.id, {
					projectId: independentProjectId,
					updatedAt: new Date()
				});

				if (rolledBackTask) {
					result.success++;
				} else {
					result.failed++;
					result.errors.push(`回滚任务失败: ${task.title}`);
				}
			} catch (error) {
				result.failed++;
				result.errors.push(`回滚任务 "${task.title}" 失败: ${error instanceof Error ? error.message : String(error)}`);
			}
		}

		if (result.success > 0) {
			new Notice(`成功回滚 ${result.success} 个任务`);
		}

		if (result.failed > 0) {
			new Notice(`${result.failed} 个任务回滚失败`, 5000);
		}

		return result;
	}

	/**
	 * 获取可用的目标项目列表
	 */
	async getAvailableTargetProjects(): Promise<Project[]> {
		try {
			const allProjects = await this.projectRepository.findAll();
			// 过滤掉已完成或已取消的项目
			return allProjects.filter(project => 
				project.status !== 'completed' && 
				project.status !== 'cancelled'
			);
		} catch (error) {
			console.error('Failed to get available target projects:', error);
			return [];
		}
	}

	/**
	 * 检查迁移数据一致性
	 */
	async checkMigrationConsistency(taskIds: string[]): Promise<{
		consistent: boolean;
		issues: Array<{
			taskId: string;
			taskTitle: string;
			issue: string;
			severity: 'warning' | 'error';
		}>;
	}> {
		const result = {
			consistent: true,
			issues: [] as Array<{
				taskId: string;
				taskTitle: string;
				issue: string;
				severity: 'warning' | 'error';
			}>
		};

		try {
			const independentProjectId = this.independentController.getIndependentProjectId();

			for (const taskId of taskIds) {
				const task = await this.taskRepository.findById(taskId);
				if (!task) {
					result.issues.push({
						taskId,
						taskTitle: '未知任务',
						issue: '任务不存在',
						severity: 'error'
					});
					result.consistent = false;
					continue;
				}

				// 检查是否为独立任务
				if (task.projectId !== independentProjectId) {
					result.issues.push({
						taskId,
						taskTitle: task.title,
						issue: '不是独立任务',
						severity: 'error'
					});
					result.consistent = false;
				}

				// 检查数据完整性
				if (!task.title || task.title.trim() === '') {
					result.issues.push({
						taskId,
						taskTitle: task.title || '无标题',
						issue: '任务标题为空',
						severity: 'error'
					});
					result.consistent = false;
				}

				// 检查日期逻辑
				if (task.startDate && task.dueDate) {
					const startDate = new Date(task.startDate);
					const dueDate = new Date(task.dueDate);
					if (startDate > dueDate) {
						result.issues.push({
							taskId,
							taskTitle: task.title,
							issue: '开始日期晚于截止日期',
							severity: 'warning'
						});
					}
				}

				// 检查状态逻辑
				if (task.status === TaskStatus.COMPLETED && !task.completedDate) {
					result.issues.push({
						taskId,
						taskTitle: task.title,
						issue: '任务已完成但没有完成日期',
						severity: 'warning'
					});
				}

				// 检查工时逻辑
				if (task.actualHours && task.estimatedHours && task.actualHours > task.estimatedHours * 2) {
					result.issues.push({
						taskId,
						taskTitle: task.title,
						issue: '实际工时远超预估工时',
						severity: 'warning'
					});
				}
			}

		} catch (error) {
			result.issues.push({
				taskId: 'system',
				taskTitle: '系统检查',
				issue: `一致性检查失败: ${error instanceof Error ? error.message : String(error)}`,
				severity: 'error'
			});
			result.consistent = false;
		}

		return result;
	}
}