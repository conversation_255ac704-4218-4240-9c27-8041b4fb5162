// Repository pattern implementation for data access

import { DataManager } from './DataManager';

export interface Repository<T> {
	findAll(): Promise<T[]>;
	findById(id: string): Promise<T | null>;
	create(entity: T): Promise<T>;
	update(id: string, entity: Partial<T>): Promise<T | null>;
	delete(id: string): Promise<boolean>;
	exists(id: string): Promise<boolean>;
}

export abstract class BaseRepository<T extends { id: string }> implements Repository<T> {
	protected dataManager: DataManager;
	protected collectionKey: string;

	constructor(dataManager: DataManager, collectionKey: string) {
		this.dataManager = dataManager;
		this.collectionKey = collectionKey;
	}

	async findAll(): Promise<T[]> {
		const data = await this.dataManager.loadData<T[]>(this.collectionKey);
		const items = data || [];
		return items.map(item => this.afterLoad(item));
	}

	async findById(id: string): Promise<T | null> {
		const items = await this.findAll();
		return items.find(item => item.id === id) || null;
	}

	async create(entity: T): Promise<T> {
		const items = await this.findAll();
		
		// Check if entity with same ID already exists
		if (items.some(item => item.id === entity.id)) {
			throw new Error(`Entity with ID ${entity.id} already exists`);
		}

		const newEntity = this.beforeCreate(entity);
		items.push(newEntity);
		
		await this.dataManager.saveData(items, this.collectionKey);
		return newEntity;
	}

	async update(id: string, updates: Partial<T>): Promise<T | null> {
		const items = await this.findAll();
		const index = items.findIndex(item => item.id === id);
		
		if (index === -1) {
			return null;
		}

		const updatedEntity = this.beforeUpdate({ ...items[index], ...updates });
		items[index] = updatedEntity;
		
		await this.dataManager.saveData(items, this.collectionKey);
		return updatedEntity;
	}

	async delete(id: string): Promise<boolean> {
		const items = await this.findAll();
		const initialLength = items.length;
		const filteredItems = items.filter(item => item.id !== id);
		
		if (filteredItems.length === initialLength) {
			return false; // Item not found
		}

		await this.dataManager.saveData(filteredItems, this.collectionKey);
		return true;
	}

	async exists(id: string): Promise<boolean> {
		const entity = await this.findById(id);
		return entity !== null;
	}

	async findBy(predicate: (item: T) => boolean): Promise<T[]> {
		const items = await this.findAll();
		return items.filter(predicate);
	}

	async findOneBy(predicate: (item: T) => boolean): Promise<T | null> {
		const items = await this.findAll();
		return items.find(predicate) || null;
	}

	async count(): Promise<number> {
		const items = await this.findAll();
		return items.length;
	}

	async clear(): Promise<void> {
		await this.dataManager.saveData([], this.collectionKey);
	}

	// Hook methods for subclasses to override
	protected beforeCreate(entity: T): T {
		return entity;
	}

	protected beforeUpdate(entity: T): T {
		return entity;
	}

	protected afterLoad(entity: any): T {
		return entity as T;
	}
}