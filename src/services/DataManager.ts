// Data management service for persistent storage and caching

import { App } from 'obsidian';
import { 
	TypeSafeDataManager, 
	CacheManager, 
	CacheEntry, 
	CacheStats,
	DataConsistencyError,
	ValidationError
} from '../types/core';

export interface DataManagerOptions {
	enableCaching: boolean;
	maxCacheSize: number;
	dataFileName: string;
}

export class DataManager implements TypeSafeDataManager, CacheManager {
	private app: App;
	private options: DataManagerOptions;
	private cache: Map<string, CacheEntry> = new Map();
	private dataFile: string;
	private cacheStats: CacheStats;
	private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟默认TTL

	constructor(app: App, options: DataManagerOptions) {
		this.app = app;
		this.options = options;
		this.dataFile = `.obsidian/plugins/obsidian-project-task-manager/${options.dataFileName}`;
		this.cacheStats = {
			totalEntries: 0,
			totalSize: 0,
			hitRate: 0,
			missRate: 0,
			evictionCount: 0,
			oldestEntry: Date.now(),
			newestEntry: Date.now()
		};
	}

	async initialize(): Promise<void> {
		// Ensure plugin data directory exists
		const pluginDir = '.obsidian/plugins/obsidian-project-task-manager';
		if (!await this.app.vault.adapter.exists(pluginDir)) {
			await this.app.vault.adapter.mkdir(pluginDir);
		}

		// Initialize data file if it doesn't exist
		if (!await this.app.vault.adapter.exists(this.dataFile)) {
			await this.saveData({});
		}
	}

	/**
	 * TypeSafeDataManager接口实现：加载数据
	 */
	async load<T>(key: string): Promise<T | null> {
		try {
			// 验证key
			if (!key || typeof key !== 'string') {
				throw new ValidationError('Invalid key provided', 'key', key);
			}

			// 检查缓存
			if (this.options.enableCaching) {
				const cachedResult = await this.get<T>(key);
				if (cachedResult !== null) {
					this.updateCacheStats('hit');
					return cachedResult;
				}
				this.updateCacheStats('miss');
			}

			// 从文件读取
			const fileData = await this.readFileData();
			const result = fileData[key] || null;

			// 缓存结果
			if (this.options.enableCaching && result !== null) {
				await this.set(key, result);
			}

			return result;
		} catch (error) {
			console.error(`Error loading data for key "${key}":`, error);
			throw new DataConsistencyError(`Failed to load data for key "${key}"`, { key, error });
		}
	}

	/**
	 * 兼容性方法：保持向后兼容
	 */
	async loadData<T>(key?: string): Promise<T> {
		try {
			if (key) {
				const result = await this.load<T>(key);
				return result || ({} as T);
			}

			// 加载所有数据
			const fileData = await this.readFileData();
			return fileData as T;
		} catch (error) {
			console.error('Error in loadData:', error);
			return (key ? null : {}) as T;
		}
	}

	/**
	 * TypeSafeDataManager接口实现：保存数据
	 */
	async save<T>(key: string, data: T): Promise<void> {
		try {
			// 验证参数
			if (!key || typeof key !== 'string') {
				throw new ValidationError('Invalid key provided', 'key', key);
			}

			if (data === undefined) {
				throw new ValidationError('Data cannot be undefined', 'data', data);
			}

			// 读取现有数据
			const fileData = await this.readFileData();
			
			// 更新数据
			fileData[key] = data;

			// 写入文件
			await this.writeFileData(fileData);

			// 更新缓存
			if (this.options.enableCaching) {
				await this.set(key, data);
			}

			console.log(`Data saved successfully for key: ${key}`);
		} catch (error) {
			console.error(`Error saving data for key "${key}":`, error);
			throw new DataConsistencyError(`Failed to save data for key "${key}"`, { key, data, error });
		}
	}

	/**
	 * 兼容性方法：保持向后兼容
	 */
	async saveData<T>(data: T, key?: string): Promise<void> {
		try {
			if (key) {
				await this.save(key, data);
			} else {
				// 保存整个数据结构
				await this.writeFileData(data);
				
				// 清空缓存
				if (this.options.enableCaching) {
					await this.clear();
				}
			}
		} catch (error) {
			console.error('Error in saveData:', error);
			throw error;
		}
	}

	/**
	 * TypeSafeDataManager接口实现：删除数据
	 */
	async delete(key: string): Promise<boolean> {
		try {
			// 验证key
			if (!key || typeof key !== 'string') {
				throw new ValidationError('Invalid key provided', 'key', key);
			}

			// 检查数据是否存在
			const exists = await this.exists(key);
			if (!exists) {
				return false;
			}

			// 读取现有数据
			const fileData = await this.readFileData();
			
			// 删除数据
			delete fileData[key];

			// 写入文件
			await this.writeFileData(fileData);

			// 从缓存中删除
			if (this.options.enableCaching) {
				this.cache.delete(key);
				this.updateCacheStats('eviction');
			}

			console.log(`Data deleted successfully for key: ${key}`);
			return true;
		} catch (error) {
			console.error(`Error deleting data for key "${key}":`, error);
			throw new DataConsistencyError(`Failed to delete data for key "${key}"`, { key, error });
		}
	}

	/**
	 * 兼容性方法：保持向后兼容
	 */
	async deleteData(key: string): Promise<void> {
		await this.delete(key);
	}

	/**
	 * TypeSafeDataManager接口实现：检查数据是否存在
	 */
	async exists(key: string): Promise<boolean> {
		try {
			// 验证key
			if (!key || typeof key !== 'string') {
				return false;
			}

			// 先检查缓存
			if (this.options.enableCaching && this.cache.has(key)) {
				const entry = this.cache.get(key);
				if (entry && !this.isCacheEntryExpired(entry)) {
					return true;
				}
			}

			// 检查文件数据
			const fileData = await this.readFileData();
			return Boolean(fileData && typeof fileData === 'object' && key in fileData);
		} catch (error) {
			console.error(`Error checking existence for key "${key}":`, error);
			return false;
		}
	}

	/**
	 * TypeSafeDataManager接口实现：清空所有数据
	 */
	async clear(): Promise<void> {
		try {
			// 清空文件数据
			await this.writeFileData({});

			// 清空缓存
			this.cache.clear();
			this.resetCacheStats();

			console.log('All data cleared successfully');
		} catch (error) {
			console.error('Error clearing data:', error);
			throw new DataConsistencyError('Failed to clear all data', { error });
		}
	}



	async backup(): Promise<string> {
		try {
			const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
			const backupFileName = `.obsidian/plugins/obsidian-project-task-manager/backup-${timestamp}.json`;
			
			const data = await this.app.vault.adapter.read(this.dataFile);
			await this.app.vault.adapter.write(backupFileName, data);
			
			return backupFileName;
		} catch (error) {
			console.error('Error creating backup:', error);
			throw error;
		}
	}

	async restore(backupFileName: string): Promise<void> {
		try {
			const backupData = await this.app.vault.adapter.read(backupFileName);
			await this.app.vault.adapter.write(this.dataFile, backupData);
			
			// Clear cache after restore
			if (this.options.enableCaching) {
				this.cache.clear();
			}
		} catch (error) {
			console.error('Error restoring backup:', error);
			throw error;
		}
	}

	/**
	 * CacheManager接口实现：获取缓存数据
	 */
	async get<T>(key: string): Promise<T | null> {
		if (!this.options.enableCaching) {
			return null;
		}

		const entry = this.cache.get(key);
		if (!entry) {
			return null;
		}

		// 检查是否过期
		if (this.isCacheEntryExpired(entry)) {
			this.cache.delete(key);
			this.updateCacheStats('eviction');
			return null;
		}

		// 更新访问统计
		entry.accessCount++;
		entry.lastAccessed = Date.now();

		return entry.data as T;
	}

	/**
	 * CacheManager接口实现：设置缓存数据
	 */
	async set<T>(key: string, data: T, ttl?: number): Promise<void> {
		if (!this.options.enableCaching) {
			return;
		}

		// 检查缓存大小限制
		if (this.cache.size >= this.options.maxCacheSize) {
			await this.evictLRU();
		}

		const now = Date.now();
		const entry: CacheEntry<T> = {
			data,
			timestamp: now,
			ttl: ttl || this.CACHE_TTL,
			size: this.calculateSize(data),
			accessCount: 1,
			lastAccessed: now
		};

		this.cache.set(key, entry);
		this.updateCacheStats('set', entry);
	}

	/**
	 * CacheManager接口实现：获取缓存大小
	 */
	size(): number {
		return this.cache.size;
	}

	/**
	 * CacheManager接口实现：获取所有缓存键
	 */
	getCacheKeys(): string[] {
		return Array.from(this.cache.keys());
	}

	/**
	 * TypeSafeDataManager接口实现：获取所有键
	 */
	async keys(): Promise<string[]> {
		try {
			const fileData = await this.readFileData();
			return Object.keys(fileData);
		} catch (error) {
			console.error('Error getting keys:', error);
			throw new DataConsistencyError('Failed to get keys', { error });
		}
	}

	/**
	 * CacheManager接口实现：获取缓存统计
	 */
	stats(): CacheStats {
		return { ...this.cacheStats };
	}

	/**
	 * 清空缓存（兼容性方法）
	 */
	clearCache(): void {
		this.cache.clear();
		this.resetCacheStats();
	}

	/**
	 * 获取缓存大小（兼容性方法）
	 */
	getCacheSize(): number {
		return this.cache.size;
	}

	// ============================================================================
	// 私有辅助方法
	// ============================================================================

	/**
	 * 读取文件数据
	 */
	private async readFileData(): Promise<any> {
		try {
			if (!await this.app.vault.adapter.exists(this.dataFile)) {
				return {};
			}

			const content = await this.app.vault.adapter.read(this.dataFile);
			return JSON.parse(content);
		} catch (error) {
			console.error('Error reading file data:', error);
			return {};
		}
	}

	/**
	 * 写入文件数据
	 */
	private async writeFileData(data: any): Promise<void> {
		try {
			// 确保目录存在
			await this.ensureDirectoryExists();

			// 写入数据
			const content = JSON.stringify(data, null, 2);
			await this.app.vault.adapter.write(this.dataFile, content);
		} catch (error) {
			console.error('Error writing file data:', error);
			throw error;
		}
	}

	/**
	 * 确保目录存在
	 */
	private async ensureDirectoryExists(): Promise<void> {
		const pluginDir = '.obsidian/plugins/obsidian-project-task-manager';
		if (!await this.app.vault.adapter.exists(pluginDir)) {
			await this.app.vault.adapter.mkdir(pluginDir);
		}
	}

	/**
	 * 检查缓存条目是否过期
	 */
	private isCacheEntryExpired(entry: CacheEntry): boolean {
		return Date.now() - entry.timestamp > entry.ttl;
	}

	/**
	 * LRU缓存淘汰
	 */
	private async evictLRU(): Promise<void> {
		if (this.cache.size === 0) return;

		// 找到最少使用的条目
		let lruKey: string | null = null;
		let lruTime = Date.now();

		for (const [key, entry] of this.cache.entries()) {
			if (entry.lastAccessed < lruTime) {
				lruTime = entry.lastAccessed;
				lruKey = key;
			}
		}

		if (lruKey) {
			this.cache.delete(lruKey);
			this.updateCacheStats('eviction');
		}
	}

	/**
	 * 计算数据大小（简化版）
	 */
	private calculateSize(data: any): number {
		try {
			return JSON.stringify(data).length;
		} catch {
			return 0;
		}
	}

	/**
	 * 更新缓存统计
	 */
	private updateCacheStats(operation: 'hit' | 'miss' | 'set' | 'eviction', entry?: CacheEntry): void {
		const now = Date.now();

		switch (operation) {
			case 'hit':
				// 计算命中率
				const totalRequests = this.cacheStats.hitRate + this.cacheStats.missRate + 1;
				this.cacheStats.hitRate = (this.cacheStats.hitRate + 1) / totalRequests;
				this.cacheStats.missRate = this.cacheStats.missRate / totalRequests;
				break;

			case 'miss':
				const totalRequestsMiss = this.cacheStats.hitRate + this.cacheStats.missRate + 1;
				this.cacheStats.hitRate = this.cacheStats.hitRate / totalRequestsMiss;
				this.cacheStats.missRate = (this.cacheStats.missRate + 1) / totalRequestsMiss;
				break;

			case 'set':
				if (entry) {
					this.cacheStats.totalEntries = this.cache.size;
					this.cacheStats.totalSize += entry.size;
					this.cacheStats.newestEntry = now;
					
					if (this.cacheStats.oldestEntry > now) {
						this.cacheStats.oldestEntry = now;
					}
				}
				break;

			case 'eviction':
				this.cacheStats.evictionCount++;
				this.cacheStats.totalEntries = this.cache.size;
				// 重新计算总大小
				this.cacheStats.totalSize = Array.from(this.cache.values())
					.reduce((total, entry) => total + entry.size, 0);
				break;
		}
	}

	/**
	 * 重置缓存统计
	 */
	private resetCacheStats(): void {
		this.cacheStats = {
			totalEntries: 0,
			totalSize: 0,
			hitRate: 0,
			missRate: 0,
			evictionCount: 0,
			oldestEntry: Date.now(),
			newestEntry: Date.now()
		};
	}

	// ============================================================================
	// 数据一致性和验证方法
	// ============================================================================

	/**
	 * 验证数据完整性
	 */
	async validateDataIntegrity(): Promise<{
		isValid: boolean;
		errors: string[];
		warnings: string[];
	}> {
		const result = {
			isValid: true,
			errors: [] as string[],
			warnings: [] as string[]
		};

		try {
			// 检查文件是否存在
			if (!await this.app.vault.adapter.exists(this.dataFile)) {
				result.warnings.push('Data file does not exist');
				return result;
			}

			// 检查文件是否可读
			const content = await this.app.vault.adapter.read(this.dataFile);
			
			// 检查JSON格式
			try {
				const data = JSON.parse(content);
				
				// 检查数据结构
				if (typeof data !== 'object' || data === null) {
					result.errors.push('Data file contains invalid structure');
					result.isValid = false;
				}
			} catch (parseError) {
				result.errors.push('Data file contains invalid JSON');
				result.isValid = false;
			}

			// 检查缓存一致性
			if (this.options.enableCaching) {
				const cacheInconsistencies = await this.checkCacheConsistency();
				if (cacheInconsistencies.length > 0) {
					result.warnings.push(...cacheInconsistencies);
				}
			}

		} catch (error) {
			result.errors.push(`Error validating data integrity: ${error.message}`);
			result.isValid = false;
		}

		return result;
	}

	/**
	 * 检查缓存一致性
	 */
	private async checkCacheConsistency(): Promise<string[]> {
		const inconsistencies: string[] = [];

		try {
			const fileData = await this.readFileData();
			
			for (const [key, entry] of this.cache.entries()) {
				// 检查缓存中的数据是否与文件中的数据一致
				if (!(key in fileData)) {
					inconsistencies.push(`Cache contains key "${key}" not found in file`);
					continue;
				}

				// 简单的数据比较（深度比较可能很昂贵）
				const fileValue = JSON.stringify(fileData[key]);
				const cacheValue = JSON.stringify(entry.data);
				
				if (fileValue !== cacheValue) {
					inconsistencies.push(`Cache data for key "${key}" differs from file data`);
				}
			}
		} catch (error) {
			inconsistencies.push(`Error checking cache consistency: ${error.message}`);
		}

		return inconsistencies;
	}

	/**
	 * 修复数据不一致问题
	 */
	async repairDataInconsistencies(): Promise<{
		repaired: number;
		failed: number;
		details: string[];
	}> {
		const result = {
			repaired: 0,
			failed: 0,
			details: [] as string[]
		};

		try {
			// 验证数据完整性
			const validation = await this.validateDataIntegrity();
			
			if (validation.isValid) {
				result.details.push('No inconsistencies found');
				return result;
			}

			// 尝试修复JSON格式错误
			if (validation.errors.some(e => e.includes('invalid JSON'))) {
				try {
					await this.repairCorruptedFile();
					result.repaired++;
					result.details.push('Repaired corrupted JSON file');
				} catch (error) {
					result.failed++;
					result.details.push(`Failed to repair JSON file: ${error.message}`);
				}
			}

			// 清理缓存不一致
			if (this.options.enableCaching) {
				const cacheRepairs = await this.repairCacheInconsistencies();
				result.repaired += cacheRepairs.repaired;
				result.failed += cacheRepairs.failed;
				result.details.push(...cacheRepairs.details);
			}

		} catch (error) {
			result.failed++;
			result.details.push(`Error during repair: ${error.message}`);
		}

		return result;
	}

	/**
	 * 修复损坏的文件
	 */
	private async repairCorruptedFile(): Promise<void> {
		try {
			// 尝试从备份恢复
			const backupFiles = await this.findBackupFiles();
			
			if (backupFiles.length > 0) {
				// 使用最新的备份
				const latestBackup = backupFiles[0];
				await this.restore(latestBackup);
				console.log(`Restored from backup: ${latestBackup}`);
			} else {
				// 创建空的数据文件
				await this.writeFileData({});
				console.log('Created new empty data file');
			}
		} catch (error) {
			throw new Error(`Failed to repair corrupted file: ${error.message}`);
		}
	}

	/**
	 * 修复缓存不一致
	 */
	private async repairCacheInconsistencies(): Promise<{
		repaired: number;
		failed: number;
		details: string[];
	}> {
		const result = {
			repaired: 0,
			failed: 0,
			details: [] as string[]
		};

		try {
			const fileData = await this.readFileData();
			const keysToRemove: string[] = [];

			for (const [key, entry] of this.cache.entries()) {
				if (!(key in fileData)) {
					// 缓存中有文件中没有的键，删除缓存条目
					keysToRemove.push(key);
				} else {
					// 更新缓存数据以匹配文件数据
					entry.data = fileData[key];
					entry.timestamp = Date.now();
					result.repaired++;
				}
			}

			// 删除无效的缓存条目
			keysToRemove.forEach(key => {
				this.cache.delete(key);
				result.repaired++;
			});

			if (result.repaired > 0) {
				result.details.push(`Repaired ${result.repaired} cache inconsistencies`);
			}

		} catch (error) {
			result.failed++;
			result.details.push(`Failed to repair cache: ${error.message}`);
		}

		return result;
	}

	/**
	 * 查找备份文件
	 */
	private async findBackupFiles(): Promise<string[]> {
		try {
			const pluginDir = '.obsidian/plugins/obsidian-project-task-manager';
			const files = await this.app.vault.adapter.list(pluginDir);
			
			const backupFiles = files.files
				.filter(file => file.includes('backup-') && file.endsWith('.json'))
				.sort((a, b) => b.localeCompare(a)); // 按时间戳降序排列

			return backupFiles;
		} catch (error) {
			console.error('Error finding backup files:', error);
			return [];
		}
	}
}
