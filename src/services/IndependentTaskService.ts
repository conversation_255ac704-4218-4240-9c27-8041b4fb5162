// 独立任务服务 - 提供独立任务的业务逻辑和数据操作

import { App, Notice } from 'obsidian';
import { Task, TaskStatus, Priority, TaskUtils } from '../models';
import { TaskRepository } from './TaskRepository';
import { IndependentTaskModeController } from './IndependentTaskModeController';

export interface TaskSearchOptions {
	searchText?: string;
	status?: TaskStatus[];
	priority?: Priority[];
	assignee?: string[];
	tags?: string[];
	dateRange?: {
		start?: Date;
		end?: Date;
		field: 'dueDate' | 'startDate' | 'createdAt' | 'updatedAt';
	};
	sortBy?: 'title' | 'priority' | 'status' | 'dueDate' | 'createdAt' | 'updatedAt';
	sortOrder?: 'asc' | 'desc';
	limit?: number;
	offset?: number;
}

export interface TaskSearchResult {
	tasks: Task[];
	total: number;
	hasMore: boolean;
}

export interface TaskBulkOperation {
	taskIds: string[];
	operation: 'updateStatus' | 'updatePriority' | 'updateAssignee' | 'addTags' | 'removeTags' | 'delete';
	data?: any;
}

export interface TaskBulkResult {
	success: number;
	failed: number;
	errors: Array<{
		taskId: string;
		error: string;
	}>;
}

/**
 * 独立任务服务
 * 提供独立任务的高级搜索、筛选和批量操作功能
 */
export class IndependentTaskService {
	private app: App;
	private taskRepository: TaskRepository;
	private controller: IndependentTaskModeController;

	constructor(
		app: App,
		taskRepository: TaskRepository,
		controller: IndependentTaskModeController
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.controller = controller;
	}

	/**
	 * 高级搜索任务
	 */
	async searchTasks(options: TaskSearchOptions = {}): Promise<TaskSearchResult> {
		try {
			// 获取所有独立任务
			let tasks = await this.controller.getIndependentTasks();

			// 应用筛选条件
			tasks = this.applyFilters(tasks, options);

			// 应用排序
			tasks = this.applySorting(tasks, options);

			// 计算分页
			const total = tasks.length;
			const offset = options.offset || 0;
			const limit = options.limit || total;
			const paginatedTasks = tasks.slice(offset, offset + limit);
			const hasMore = offset + limit < total;

			return {
				tasks: paginatedTasks,
				total,
				hasMore
			};
		} catch (error) {
			console.error('Failed to search tasks:', error);
			throw new Error(`搜索任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 获取任务的相关建议
	 */
	async getTaskSuggestions(query: string, limit: number = 10): Promise<{
		tasks: Task[];
		tags: string[];
		assignees: string[];
	}> {
		try {
			const allTasks = await this.controller.getIndependentTasks();
			const queryLower = query.toLowerCase();

			// 任务建议 - 基于标题和描述匹配
			const taskSuggestions = allTasks
				.filter(task => 
					task.title.toLowerCase().includes(queryLower) ||
					(task.description && task.description.toLowerCase().includes(queryLower))
				)
				.slice(0, limit);

			// 标签建议
			const allTags = new Set<string>();
			allTasks.forEach(task => task.tags.forEach(tag => allTags.add(tag)));
			const tagSuggestions = Array.from(allTags)
				.filter(tag => tag.toLowerCase().includes(queryLower))
				.slice(0, limit);

			// 负责人建议
			const allAssignees = new Set<string>();
			allTasks.forEach(task => {
				if (task.assignee) allAssignees.add(task.assignee);
			});
			const assigneeSuggestions = Array.from(allAssignees)
				.filter(assignee => assignee.toLowerCase().includes(queryLower))
				.slice(0, limit);

			return {
				tasks: taskSuggestions,
				tags: tagSuggestions,
				assignees: assigneeSuggestions
			};
		} catch (error) {
			console.error('Failed to get task suggestions:', error);
			return { tasks: [], tags: [], assignees: [] };
		}
	}

	/**
	 * 批量操作任务
	 */
	async bulkOperateTasks(operation: TaskBulkOperation): Promise<TaskBulkResult> {
		const result: TaskBulkResult = {
			success: 0,
			failed: 0,
			errors: []
		};

		for (const taskId of operation.taskIds) {
			try {
				switch (operation.operation) {
					case 'updateStatus':
						await this.controller.updateIndependentTask(taskId, { 
							status: operation.data as TaskStatus 
						});
						break;

					case 'updatePriority':
						await this.controller.updateIndependentTask(taskId, { 
							priority: operation.data as Priority 
						});
						break;

					case 'updateAssignee':
						await this.controller.updateIndependentTask(taskId, { 
							assignee: operation.data as string 
						});
						break;

					case 'addTags':
						const task = await this.taskRepository.findById(taskId);
						if (task) {
							const newTags = Array.from(new Set([...task.tags, ...(operation.data as string[])]));
							await this.controller.updateIndependentTask(taskId, { tags: newTags });
						}
						break;

					case 'removeTags':
						const taskForRemove = await this.taskRepository.findById(taskId);
						if (taskForRemove) {
							const tagsToRemove = operation.data as string[];
							const filteredTags = taskForRemove.tags.filter(tag => !tagsToRemove.includes(tag));
							await this.controller.updateIndependentTask(taskId, { tags: filteredTags });
						}
						break;

					case 'delete':
						await this.controller.deleteIndependentTask(taskId);
						break;

					default:
						throw new Error(`不支持的操作: ${operation.operation}`);
				}

				result.success++;
			} catch (error) {
				result.failed++;
				result.errors.push({
					taskId,
					error: error instanceof Error ? error.message : String(error)
				});
			}
		}

		const operationNames = {
			updateStatus: '更新状态',
			updatePriority: '更新优先级',
			updateAssignee: '更新负责人',
			addTags: '添加标签',
			removeTags: '移除标签',
			delete: '删除'
		};

		new Notice(`批量${operationNames[operation.operation]}完成: 成功 ${result.success} 个，失败 ${result.failed} 个`);
		return result;
	}

	/**
	 * 导出任务数据
	 */
	async exportTasks(format: 'json' | 'csv' | 'markdown', options: TaskSearchOptions = {}): Promise<string> {
		try {
			const searchResult = await this.searchTasks({ ...options, limit: undefined });
			const tasks = searchResult.tasks;

			switch (format) {
				case 'json':
					return JSON.stringify(tasks, null, 2);

				case 'csv':
					return this.exportToCSV(tasks);

				case 'markdown':
					return this.exportToMarkdown(tasks);

				default:
					throw new Error(`不支持的导出格式: ${format}`);
			}
		} catch (error) {
			console.error('Failed to export tasks:', error);
			throw new Error(`导出任务失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 获取任务统计分析
	 */
	async getTaskAnalytics(): Promise<{
		statusDistribution: Record<TaskStatus, number>;
		priorityDistribution: Record<Priority, number>;
		assigneeDistribution: Record<string, number>;
		tagDistribution: Record<string, number>;
		completionRate: number;
		averageCompletionTime: number; // 天数
		overdueCount: number;
		upcomingDeadlines: Task[]; // 未来7天内到期的任务
	}> {
		try {
			const tasks = await this.controller.getIndependentTasks();
			
			const analytics = {
				statusDistribution: {
					[TaskStatus.TODO]: 0,
					[TaskStatus.IN_PROGRESS]: 0,
					[TaskStatus.BLOCKED]: 0,
					[TaskStatus.REVIEW]: 0,
					[TaskStatus.COMPLETED]: 0,
					[TaskStatus.CANCELLED]: 0
				},
				priorityDistribution: {
					[Priority.LOW]: 0,
					[Priority.MEDIUM]: 0,
					[Priority.HIGH]: 0,
					[Priority.CRITICAL]: 0
				},
				assigneeDistribution: {} as Record<string, number>,
				tagDistribution: {} as Record<string, number>,
				completionRate: 0,
				averageCompletionTime: 0,
				overdueCount: 0,
				upcomingDeadlines: [] as Task[]
			};

			let completedTasks = 0;
			let totalCompletionTime = 0;
			const now = new Date();
			const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

			for (const task of tasks) {
				// 状态分布
				analytics.statusDistribution[task.status]++;

				// 优先级分布
				analytics.priorityDistribution[task.priority]++;

				// 负责人分布
				if (task.assignee) {
					analytics.assigneeDistribution[task.assignee] = 
						(analytics.assigneeDistribution[task.assignee] || 0) + 1;
				}

				// 标签分布
				for (const tag of task.tags) {
					analytics.tagDistribution[tag] = (analytics.tagDistribution[tag] || 0) + 1;
				}

				// 完成率和平均完成时间
				if (task.status === TaskStatus.COMPLETED) {
					completedTasks++;
					if (task.completedDate && task.createdAt) {
						const completionTime = new Date(task.completedDate).getTime() - new Date(task.createdAt).getTime();
						totalCompletionTime += completionTime / (1000 * 60 * 60 * 24); // 转换为天数
					}
				}

				// 逾期任务
				if (TaskUtils.isOverdue(task)) {
					analytics.overdueCount++;
				}

				// 即将到期的任务
				if (task.dueDate && task.status !== TaskStatus.COMPLETED) {
					const dueDate = new Date(task.dueDate);
					if (dueDate >= now && dueDate <= nextWeek) {
						analytics.upcomingDeadlines.push(task);
					}
				}
			}

			analytics.completionRate = tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0;
			analytics.averageCompletionTime = completedTasks > 0 ? totalCompletionTime / completedTasks : 0;

			// 按到期日期排序即将到期的任务
			analytics.upcomingDeadlines.sort((a, b) => {
				if (!a.dueDate || !b.dueDate) return 0;
				return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
			});

			return analytics;
		} catch (error) {
			console.error('Failed to get task analytics:', error);
			throw new Error(`获取任务分析失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 应用筛选条件
	 */
	private applyFilters(tasks: Task[], options: TaskSearchOptions): Task[] {
		return tasks.filter(task => {
			// 文本搜索
			if (options.searchText) {
				const searchLower = options.searchText.toLowerCase();
				const titleMatch = task.title.toLowerCase().includes(searchLower);
				const descMatch = task.description?.toLowerCase().includes(searchLower) || false;
				const tagMatch = task.tags.some(tag => tag.toLowerCase().includes(searchLower));
				if (!titleMatch && !descMatch && !tagMatch) {
					return false;
				}
			}

			// 状态筛选
			if (options.status && options.status.length > 0) {
				if (!options.status.includes(task.status)) {
					return false;
				}
			}

			// 优先级筛选
			if (options.priority && options.priority.length > 0) {
				if (!options.priority.includes(task.priority)) {
					return false;
				}
			}

			// 负责人筛选
			if (options.assignee && options.assignee.length > 0) {
				if (!task.assignee || !options.assignee.includes(task.assignee)) {
					return false;
				}
			}

			// 标签筛选
			if (options.tags && options.tags.length > 0) {
				const hasMatchingTag = options.tags.some(tag => task.tags.includes(tag));
				if (!hasMatchingTag) {
					return false;
				}
			}

			// 日期范围筛选
			if (options.dateRange) {
				const { start, end, field } = options.dateRange;
				const taskDate = task[field];
				
				if (taskDate) {
					const date = new Date(taskDate);
					if (start && date < start) return false;
					if (end && date > end) return false;
				} else if (start || end) {
					// 如果设置了日期范围但任务没有对应日期字段，则排除
					return false;
				}
			}

			return true;
		});
	}

	/**
	 * 应用排序
	 */
	private applySorting(tasks: Task[], options: TaskSearchOptions): Task[] {
		if (!options.sortBy) {
			return tasks;
		}

		const { sortBy, sortOrder = 'asc' } = options;

		return tasks.sort((a, b) => {
			let aValue: any;
			let bValue: any;

			switch (sortBy) {
				case 'title':
					aValue = a.title.toLowerCase();
					bValue = b.title.toLowerCase();
					break;
				case 'priority':
					// 优先级排序：CRITICAL > HIGH > MEDIUM > LOW
					const priorityOrder = { [Priority.CRITICAL]: 4, [Priority.HIGH]: 3, [Priority.MEDIUM]: 2, [Priority.LOW]: 1 };
					aValue = priorityOrder[a.priority];
					bValue = priorityOrder[b.priority];
					break;
				case 'status':
					// 状态排序：TODO > IN_PROGRESS > REVIEW > BLOCKED > COMPLETED > CANCELLED
					const statusOrder = {
						[TaskStatus.TODO]: 1,
						[TaskStatus.IN_PROGRESS]: 2,
						[TaskStatus.REVIEW]: 3,
						[TaskStatus.BLOCKED]: 4,
						[TaskStatus.COMPLETED]: 5,
						[TaskStatus.CANCELLED]: 6
					};
					aValue = statusOrder[a.status];
					bValue = statusOrder[b.status];
					break;
				case 'dueDate':
				case 'createdAt':
				case 'updatedAt':
					aValue = a[sortBy] ? new Date(a[sortBy]!).getTime() : 0;
					bValue = b[sortBy] ? new Date(b[sortBy]!).getTime() : 0;
					break;
				default:
					return 0;
			}

			if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
			if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
			return 0;
		});
	}

	/**
	 * 导出为CSV格式
	 */
	private exportToCSV(tasks: Task[]): string {
		const headers = [
			'ID', '标题', '描述', '状态', '优先级', '负责人', 
			'预估工时', '实际工时', '开始日期', '截止日期', '完成日期',
			'标签', '创建时间', '更新时间'
		];

		const rows = tasks.map(task => [
			task.id,
			task.title,
			task.description || '',
			task.status,
			task.priority,
			task.assignee || '',
			task.estimatedHours || '',
			task.actualHours || '',
			task.startDate ? new Date(task.startDate).toLocaleDateString() : '',
			task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '',
			task.completedDate ? new Date(task.completedDate).toLocaleDateString() : '',
			task.tags.join(';'),
			new Date(task.createdAt).toLocaleString(),
			new Date(task.updatedAt).toLocaleString()
		]);

		return [headers, ...rows]
			.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
			.join('\n');
	}

	/**
	 * 导出为Markdown格式
	 */
	private exportToMarkdown(tasks: Task[]): string {
		let markdown = '# 独立任务列表\n\n';
		
		for (const task of tasks) {
			markdown += `## ${task.title}\n\n`;
			
			if (task.description) {
				markdown += `${task.description}\n\n`;
			}

			markdown += `- **状态**: ${task.status}\n`;
			markdown += `- **优先级**: ${task.priority}\n`;
			
			if (task.assignee) {
				markdown += `- **负责人**: ${task.assignee}\n`;
			}
			
			if (task.estimatedHours) {
				markdown += `- **预估工时**: ${task.estimatedHours}小时\n`;
			}
			
			if (task.dueDate) {
				markdown += `- **截止日期**: ${new Date(task.dueDate).toLocaleDateString()}\n`;
			}
			
			if (task.tags.length > 0) {
				markdown += `- **标签**: ${task.tags.map(tag => `#${tag}`).join(' ')}\n`;
			}

			markdown += `- **创建时间**: ${new Date(task.createdAt).toLocaleString()}\n`;
			markdown += `- **更新时间**: ${new Date(task.updatedAt).toLocaleString()}\n\n`;
			markdown += '---\n\n';
		}

		return markdown;
	}
}