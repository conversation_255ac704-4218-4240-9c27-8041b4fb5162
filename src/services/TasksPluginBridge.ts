// Bridge for integrating with the Tasks plugin

import { App, TFile, CachedMetadata, Notice } from 'obsidian';
import { Task, TaskStatus, Priority, TASKS_EMOJI_MAP, STATUS_TO_EMOJI, Project, ProjectStatus } from '../models';
import { TaskRepository } from './TaskRepository';
import { TaskManager } from './TaskManager';
import { ProjectRepository } from './ProjectRepository';
import { NoteLinkManager, TaskParseResult } from './NoteLinkManager';

export interface TasksPluginTask {
	description: string;
	status: string;
	priority: string;
	scheduledDate?: Date;
	dueDate?: Date;
	doneDate?: Date;
	tags: string[];
	filePath: string;
	lineNumber: number;
	originalText: string;
}

export interface TasksPluginBridgeOptions {
	enableSync: boolean;
	syncInterval: number; // milliseconds
	autoDetectTasks: boolean;
	preserveTasksPluginFormat: boolean;
	defaultProjectId?: string;
}

export class TasksPluginBridge {
	private app: App;
	private taskRepository: TaskRepository;
	private taskManager: TaskManager;
	private projectRepository: ProjectRepository;
	private noteLinkManager: NoteLinkManager;
	private options: TasksPluginBridgeOptions;
	private tasksPlugin: any = null;
	private syncTimer?: NodeJS.Timeout;
	private lastSyncTime = 0;
	private isInitialized = false;

	// Tasks plugin emoji patterns
	private readonly TASK_REGEX = /^(\s*)-\s*\[(.)\]\s*(.+)$/gm;
	private readonly TASK_TAG_REGEX = /#task\b/gi;
	private readonly PRIORITY_REGEX = /[⏫🔼🔽⏬]/g;
	private readonly DATE_REGEX = /📅\s*(\d{4}-\d{2}-\d{2})|🗓️\s*(\d{4}-\d{2}-\d{2})|⏰\s*(\d{4}-\d{2}-\d{2})/g;

	constructor(
		app: App,
		taskRepository: TaskRepository,
		taskManager: TaskManager,
		projectRepository: ProjectRepository,
		options: Partial<TasksPluginBridgeOptions> = {}
	) {
		this.app = app;
		this.taskRepository = taskRepository;
		this.taskManager = taskManager;
		this.projectRepository = projectRepository;
		this.noteLinkManager = new NoteLinkManager(app, taskRepository);
		this.options = {
			enableSync: true,
			syncInterval: 5000, // 5 seconds
			autoDetectTasks: true,
			preserveTasksPluginFormat: true,
			...options
		};
	}

	/**
	 * Initialize the bridge and detect Tasks plugin
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) return;

		try {
			// Initialize note link manager first
			await this.noteLinkManager.initialize();
			
			// Try to detect Tasks plugin
			this.tasksPlugin = this.detectTasksPlugin();
			
			if (this.tasksPlugin) {
				console.log('Tasks plugin detected, enabling integration');
				await this.setupIntegration();
			} else {
				console.log('Tasks plugin not found, running in standalone mode');
			}

			// Disable automatic sync to prevent mass import - sync only on manual request
			// if (this.options.enableSync) {
			//     this.startSync();
			// }

			this.isInitialized = true;
		} catch (error) {
			console.error('Failed to initialize TasksPluginBridge:', error);
			throw error;
		}
	}

	/**
	 * Parse markdown content for tasks (legacy method)
	 */
	parseTasksFromMarkdown(content: string, filePath: string): TasksPluginTask[] {
		const tasks: TasksPluginTask[] = [];
		const lines = content.split('\n');

		lines.forEach((line, index) => {
			const taskMatch = line.match(/^(\s*)-\s*\[(.)\]\s*(.+)$/);
			if (taskMatch) {
				const [, indent, statusChar, description] = taskMatch;
				const task = this.parseTaskLine(description, statusChar, filePath, index + 1, line);
				if (task) {
					tasks.push(task);
				}
			}
		});

		return tasks;
	}

	/**
	 * 增强的任务解析方法 - 使用智能识别
	 */
	parseTasksFromMarkdownEnhanced(content: string, filePath: string): TasksPluginTask[] {
		// 使用NoteLinkManager的增强解析功能
		const parseResults = this.noteLinkManager.parseTasksFromNote(content, filePath);
		
		// 转换为TasksPluginTask格式
		return parseResults
			.filter(result => result.confidence > 0.7) // 只保留高置信度的任务
			.map(result => this.convertParseResultToTasksPluginTask(result, filePath));
	}

	/**
	 * 将解析结果转换为TasksPluginTask格式
	 */
	private convertParseResultToTasksPluginTask(parseResult: TaskParseResult, filePath: string): TasksPluginTask {
		return {
			description: parseResult.title,
			status: this.getStatusCharFromTaskStatus(parseResult.status),
			priority: parseResult.priority || 'MEDIUM',
			scheduledDate: undefined, // 可以从parseResult中提取
			dueDate: parseResult.dueDate,
			doneDate: parseResult.status === TaskStatus.COMPLETED ? new Date() : undefined,
			tags: parseResult.tags,
			filePath,
			lineNumber: parseResult.lineNumber,
			originalText: parseResult.originalText
		};
	}

	/**
	 * 从TaskStatus获取状态字符
	 */
	private getStatusCharFromTaskStatus(status: TaskStatus): string {
		const statusMap: Record<TaskStatus, string> = {
			[TaskStatus.TODO]: ' ',
			[TaskStatus.IN_PROGRESS]: '/',
			[TaskStatus.BLOCKED]: '<',
			[TaskStatus.REVIEW]: '?',
			[TaskStatus.COMPLETED]: 'x',
			[TaskStatus.CANCELLED]: '-'
		};
		return statusMap[status] || ' ';
	}

	/**
	 * Convert Tasks plugin task to our Task model
	 */
	convertToInternalTask(tasksPluginTask: TasksPluginTask, projectId?: string): Partial<Task> {
		const status = this.mapEmojiToStatus(tasksPluginTask.status);
		const priority = this.mapPriorityFromDescription(tasksPluginTask.description);

		return {
			title: this.cleanTaskDescription(tasksPluginTask.description),
			projectId: projectId || this.options.defaultProjectId || 'default',
			status,
			priority,
			dueDate: tasksPluginTask.dueDate,
			completedDate: tasksPluginTask.doneDate,
			tags: tasksPluginTask.tags,
			linkedNotes: [tasksPluginTask.filePath],
			position: tasksPluginTask.lineNumber
		};
	}

	/**
	 * 增强的任务转换方法 - 自动建立笔记链接
	 */
	convertToInternalTaskEnhanced(tasksPluginTask: TasksPluginTask, projectId?: string): Partial<Task> {
		const basicTask = this.convertToInternalTask(tasksPluginTask, projectId);
		
		// 自动建立笔记链接
		const linkedNotes = this.establishNoteLinks(tasksPluginTask);
		
		return {
			...basicTask,
			linkedNotes,
			// 添加更多元数据
			description: this.extractTaskDescription(tasksPluginTask.originalText),
			createdAt: new Date(),
			updatedAt: new Date()
		};
	}

	/**
	 * 建立任务与笔记的链接关系
	 */
	private establishNoteLinks(tasksPluginTask: TasksPluginTask): string[] {
		const linkedNotes = [tasksPluginTask.filePath];
		
		// 检查任务内容中是否包含其他笔记的链接
		const noteLinks = this.extractNoteLinksFromContent(tasksPluginTask.originalText);
		linkedNotes.push(...noteLinks);
		
		// 去重
		return [...new Set(linkedNotes)];
	}

	/**
	 * 从任务内容中提取笔记链接
	 */
	private extractNoteLinksFromContent(content: string): string[] {
		const links: string[] = [];
		
		// 匹配 [[笔记名]] 格式的链接
		const wikiLinkRegex = /\[\[([^\]]+)\]\]/g;
		let match;
		while ((match = wikiLinkRegex.exec(content)) !== null) {
			const noteName = match[1];
			// 尝试找到对应的文件
			const file = this.app.metadataCache.getFirstLinkpathDest(noteName, '');
			if (file) {
				links.push(file.path);
			}
		}
		
		// 匹配 [文本](链接) 格式的链接
		const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
		while ((match = markdownLinkRegex.exec(content)) !== null) {
			const linkPath = match[2];
			// 如果是内部链接（不是http开头）
			if (!linkPath.startsWith('http') && linkPath.endsWith('.md')) {
				links.push(linkPath);
			}
		}
		
		return links;
	}

	/**
	 * 提取任务描述（保留更多上下文信息）
	 */
	private extractTaskDescription(originalText: string): string | undefined {
		// 移除任务标记符号
		let description = originalText.replace(/^(\s*)-\s*\[(.)\]\s*/, '');
		
		// 如果描述很长，可能包含有用信息
		if (description.length > 50) {
			return description.trim();
		}
		
		return undefined;
	}

	/**
	 * Convert our Task model to Tasks plugin format
	 */
	convertToTasksPluginFormat(task: Task): string {
		const emoji = STATUS_TO_EMOJI[task.status] || '📝';
		const priorityEmoji = this.getPriorityEmoji(task.priority);
		const dueDateText = task.dueDate ? ` 📅 ${task.dueDate.toISOString().split('T')[0]}` : '';
		const tagsText = task.tags.length > 0 ? ` ${task.tags.map(tag => `#${tag}`).join(' ')}` : '';
		
		return `- [${this.getStatusChar(task.status)}] ${priorityEmoji}${task.title}${dueDateText}${tagsText} #task`;
	}

	/**
	 * Sync tasks from markdown files to our system
	 */
	async syncFromMarkdown(): Promise<void> {
		if (!this.options.enableSync) return;

		try {
			const markdownFiles = this.app.vault.getMarkdownFiles();
			const syncedTasks = new Set<string>();

			for (const file of markdownFiles) {
				const content = await this.app.vault.read(file);
				// 使用增强的解析方法
				const tasksInFile = this.parseTasksFromMarkdownEnhanced(content, file.path);

				for (const tasksPluginTask of tasksInFile) {
					// Check if this task has #task tag or is in a project context
					if (this.shouldSyncTask(tasksPluginTask)) {
						await this.syncTaskToInternalEnhanced(tasksPluginTask);
						syncedTasks.add(`${file.path}:${tasksPluginTask.lineNumber}`);
					}
				}
			}

			this.lastSyncTime = Date.now();
			console.log(`Synced ${syncedTasks.size} tasks from markdown files`);
		} catch (error) {
			console.error('Error syncing from markdown:', error);
		}
	}

	/**
	 * 增强的任务同步方法
	 */
	private async syncTaskToInternalEnhanced(tasksPluginTask: TasksPluginTask): Promise<void> {
		try {
			// 检查任务是否已存在
			const existingTasks = await this.taskRepository.findByLinkedNote(tasksPluginTask.filePath);
			const existingTask = existingTasks.find(t => 
				t.linkedNotes.includes(tasksPluginTask.filePath) && 
				t.position === tasksPluginTask.lineNumber
			);

			const taskData = this.convertToInternalTaskEnhanced(tasksPluginTask);

			if (existingTask) {
				// 更新现有任务
				await this.taskRepository.update(existingTask.id, taskData);
				console.log(`Updated existing task: ${existingTask.id}`);
			} else {
				// 创建新任务 - 确保默认项目存在
				if (taskData.projectId) {
					await this.ensureDefaultProjectExists(taskData.projectId);
					const newTask = await this.taskManager.createTask({
						title: taskData.title!,
						projectId: taskData.projectId,
						description: taskData.description,
						priority: taskData.priority,
						dueDate: taskData.dueDate,
						tags: taskData.tags,
						linkedNotes: taskData.linkedNotes
					});
					console.log(`Created new task: ${newTask.id} from ${tasksPluginTask.filePath}:${tasksPluginTask.lineNumber}`);
				}
			}
		} catch (error) {
			console.error('Error syncing task to internal system (enhanced):', error);
		}
	}

	/**
	 * Sync tasks from our system to markdown files
	 */
	async syncToMarkdown(): Promise<void> {
		if (!this.options.enableSync || !this.options.preserveTasksPluginFormat) return;

		try {
			const allTasks = await this.taskRepository.findAll();
			
			for (const task of allTasks) {
				if (task.linkedNotes.length > 0) {
					for (const notePath of task.linkedNotes) {
						await this.updateTaskInFile(task, notePath);
					}
				}
			}
		} catch (error) {
			console.error('Error syncing to markdown:', error);
		}
	}

	/**
	 * Handle file modification events
	 */
	async onFileModified(file: TFile): Promise<void> {
		// Disabled automatic sync on file modification to prevent mass task creation
		// Users can manually sync tasks through plugin settings if needed
		return;
		
		// Original code (disabled):
		/*
		if (!this.options.autoDetectTasks || file.extension !== 'md') return;

		try {
			const content = await this.app.vault.read(file);
			const tasks = this.parseTasksFromMarkdown(content, file.path);
			
			for (const task of tasks) {
				if (this.shouldSyncTask(task)) {
					await this.syncTaskToInternal(task);
				}
			}
		} catch (error) {
			console.error('Error handling file modification:', error);
		}
		*/
	}

	/**
	 * Get Tasks plugin compatibility status
	 */
	getCompatibilityStatus(): {
		tasksPluginDetected: boolean;
		syncEnabled: boolean;
		lastSyncTime: number;
		supportedFeatures: string[];
	} {
		return {
			tasksPluginDetected: this.tasksPlugin !== null,
			syncEnabled: this.options.enableSync,
			lastSyncTime: this.lastSyncTime,
			supportedFeatures: [
				'Enhanced task recognition',
				'Intelligent note linking',
				'Emoji status mapping',
				'Priority detection',
				'Due date parsing',
				'Tag extraction',
				'#task global tag support',
				'Bidirectional sync',
				'Note-task relationship management'
			]
		};
	}

	/**
	 * 获取笔记链接管理器实例
	 */
	getNoteLinkManager(): NoteLinkManager {
		return this.noteLinkManager;
	}

	/**
	 * 手动触发智能任务识别
	 */
	async recognizeTasksInFile(filePath: string): Promise<TaskParseResult[]> {
		try {
			const file = this.app.vault.getAbstractFileByPath(filePath);
			if (!(file instanceof TFile) || file.extension !== 'md') {
				return [];
			}

			const content = await this.app.vault.read(file);
			return this.noteLinkManager.parseTasksFromNote(content, filePath);
		} catch (error) {
			console.error('Error recognizing tasks in file:', error);
			return [];
		}
	}

	/**
	 * 批量创建识别到的任务
	 */
	async createTasksFromRecognition(parseResults: TaskParseResult[], projectId?: string): Promise<Task[]> {
		const createdTasks: Task[] = [];

		for (const result of parseResults) {
			if (result.confidence > 0.8) { // 只创建高置信度的任务
				try {
					const tasksPluginTask: TasksPluginTask = this.convertParseResultToTasksPluginTask(result, '');
					const taskData = this.convertToInternalTaskEnhanced(tasksPluginTask, projectId);
					
					if (taskData.projectId) {
						await this.ensureDefaultProjectExists(taskData.projectId);
						const newTask = await this.taskManager.createTask({
							title: taskData.title!,
							projectId: taskData.projectId,
							description: taskData.description,
							priority: taskData.priority,
							dueDate: taskData.dueDate,
							tags: taskData.tags,
							linkedNotes: taskData.linkedNotes
						});
						createdTasks.push(newTask);
					}
				} catch (error) {
					console.error('Error creating task from recognition:', error);
				}
			}
		}

		return createdTasks;
	}

	/**
	 * Cleanup resources
	 */
	cleanup(): void {
		if (this.syncTimer) {
			clearInterval(this.syncTimer);
			this.syncTimer = undefined;
		}
		
		// 清理NoteLinkManager
		this.noteLinkManager.cleanup();
		
		this.isInitialized = false;
	}

	/**
	 * Clear all automatically synced tasks (emergency cleanup)
	 */
	async clearAutoSyncedTasks(): Promise<number> {
		try {
			const allTasks = await this.taskRepository.findAll();
			let deletedCount = 0;

			for (const task of allTasks) {
				// Delete tasks that were auto-synced (have linkedNotes and belong to default project)
				if (task.projectId === 'default' && task.linkedNotes && task.linkedNotes.length > 0) {
					await this.taskRepository.delete(task.id);
					deletedCount++;
				}
			}

			console.log(`Cleared ${deletedCount} auto-synced tasks`);
			return deletedCount;
		} catch (error) {
			console.error('Error clearing auto-synced tasks:', error);
			return 0;
		}
	}

	/**
	 * Detect if Tasks plugin is installed and enabled
	 */
	private detectTasksPlugin(): any {
		// @ts-ignore - Access Obsidian's plugin system
		const plugins = this.app.plugins;
		
		if (plugins && plugins.enabledPlugins && plugins.enabledPlugins.has('obsidian-tasks-plugin')) {
			// @ts-ignore
			return plugins.plugins['obsidian-tasks-plugin'];
		}
		
		return null;
	}

	/**
	 * Setup integration with Tasks plugin
	 */
	private async setupIntegration(): Promise<void> {
		// Register file modification listener
		// Note: Simplified event handling for now
		console.log('TasksPluginBridge integration setup completed');
		
		// Skip initial sync to avoid mass import - will sync on demand
		// await this.syncFromMarkdown();
	}

	/**
	 * Start periodic sync
	 */
	private startSync(): void {
		if (this.syncTimer) return;

		this.syncTimer = setInterval(async () => {
			await this.syncFromMarkdown();
		}, this.options.syncInterval);
	}

	/**
	 * Parse individual task line
	 */
	private parseTaskLine(description: string, statusChar: string, filePath: string, lineNumber: number, originalText: string): TasksPluginTask | null {
		try {
			const tags = this.extractTags(description);
			const priority = this.mapPriorityFromDescription(description);
			const dates = this.extractDates(description);

			return {
				description: this.cleanTaskDescription(description),
				status: statusChar,
				priority,
				scheduledDate: dates.scheduled,
				dueDate: dates.due,
				doneDate: dates.done,
				tags,
				filePath,
				lineNumber,
				originalText
			};
		} catch (error) {
			console.warn('Error parsing task line:', error);
			return null;
		}
	}

	/**
	 * Map emoji/character to our TaskStatus
	 */
	private mapEmojiToStatus(statusChar: string): TaskStatus {
		// Direct character mapping
		const charMap: Record<string, TaskStatus> = {
			' ': TaskStatus.TODO,
			'x': TaskStatus.COMPLETED,
			'X': TaskStatus.COMPLETED,
			'/': TaskStatus.IN_PROGRESS,
			'-': TaskStatus.CANCELLED,
			'>': TaskStatus.IN_PROGRESS,
			'<': TaskStatus.BLOCKED,
			'!': TaskStatus.TODO, // High priority
			'?': TaskStatus.REVIEW,
		};

		if (charMap[statusChar]) {
			return charMap[statusChar];
		}

		// Try emoji mapping
		return TASKS_EMOJI_MAP[statusChar] || TaskStatus.TODO;
	}

	/**
	 * Get status character for our TaskStatus
	 */
	private getStatusChar(status: TaskStatus): string {
		const statusMap: Record<TaskStatus, string> = {
			[TaskStatus.TODO]: ' ',
			[TaskStatus.IN_PROGRESS]: '/',
			[TaskStatus.BLOCKED]: '<',
			[TaskStatus.REVIEW]: '?',
			[TaskStatus.COMPLETED]: 'x',
			[TaskStatus.CANCELLED]: '-'
		};

		return statusMap[status] || ' ';
	}

	/**
	 * Extract tags from task description
	 */
	private extractTags(description: string): string[] {
		const tagMatches = description.match(/#[\w-]+/g);
		return tagMatches ? tagMatches.map(tag => tag.substring(1)) : [];
	}

	/**
	 * Extract dates from task description
	 */
	private extractDates(description: string): { scheduled?: Date; due?: Date; done?: Date } {
		const dates: { scheduled?: Date; due?: Date; done?: Date } = {};
		
		// Look for various date patterns
		const datePatterns = [
			{ regex: /📅\s*(\d{4}-\d{2}-\d{2})/, type: 'due' },
			{ regex: /🗓️\s*(\d{4}-\d{2}-\d{2})/, type: 'scheduled' },
			{ regex: /✅\s*(\d{4}-\d{2}-\d{2})/, type: 'done' },
		];

		for (const pattern of datePatterns) {
			const match = description.match(pattern.regex);
			if (match) {
				const dateStr = match[1];
				const date = new Date(dateStr);
				if (!isNaN(date.getTime())) {
					dates[pattern.type as keyof typeof dates] = date;
				}
			}
		}

		return dates;
	}

	/**
	 * Map priority from task description
	 */
	private mapPriorityFromDescription(description: string): Priority {
		if (description.includes('⏫') || description.includes('🔥')) {
			return Priority.CRITICAL;
		}
		if (description.includes('🔼') || description.includes('⭐')) {
			return Priority.HIGH;
		}
		if (description.includes('🔽')) {
			return Priority.LOW;
		}
		return Priority.MEDIUM;
	}

	/**
	 * Get priority emoji for our Priority
	 */
	private getPriorityEmoji(priority: Priority): string {
		const priorityMap: Record<Priority, string> = {
			[Priority.CRITICAL]: '⏫ ',
			[Priority.HIGH]: '🔼 ',
			[Priority.MEDIUM]: '',
			[Priority.LOW]: '🔽 '
		};

		return priorityMap[priority] || '';
	}

	/**
	 * Clean task description by removing metadata
	 */
	private cleanTaskDescription(description: string): string {
		let cleaned = description
			.replace(this.PRIORITY_REGEX, '')
			.replace(this.DATE_REGEX, '')
			.replace(/#[\w-]+/g, '')
			.replace(/\s+/g, ' ')
			.trim();
		
		// Ensure title is not too long (max 280 characters to leave some margin)
		if (cleaned.length > 280) {
			cleaned = cleaned.substring(0, 280).trim() + '...';
		}
		
		return cleaned;
	}

	/**
	 * Check if task should be synced to our system
	 */
	private shouldSyncTask(task: TasksPluginTask): boolean {
		// Only sync tasks that explicitly have #task tag AND are not too long
		const hasTaskTag = task.tags.includes('task') || this.TASK_TAG_REGEX.test(task.originalText);
		const hasReasonableLength = task.description.length <= 300;
		const isNotTooLong = task.originalText.length <= 500; // Also check original text length
		
		// Be more conservative - only sync if all conditions are met
		return hasTaskTag && hasReasonableLength && isNotTooLong;
	}

	/**
	 * Sync Tasks plugin task to our internal system
	 */
	private async syncTaskToInternal(tasksPluginTask: TasksPluginTask): Promise<void> {
		try {
			// Check if task already exists
			const existingTasks = await this.taskRepository.findByLinkedNote(tasksPluginTask.filePath);
			const existingTask = existingTasks.find(t => 
				t.linkedNotes.includes(tasksPluginTask.filePath) && 
				t.position === tasksPluginTask.lineNumber
			);

			const taskData = this.convertToInternalTask(tasksPluginTask);

			if (existingTask) {
				// Update existing task
				await this.taskRepository.update(existingTask.id, taskData);
			} else {
				// Create new task - ensure default project exists first
				if (taskData.projectId) {
					await this.ensureDefaultProjectExists(taskData.projectId);
					await this.taskManager.createTask({
						title: taskData.title!,
						projectId: taskData.projectId,
						description: tasksPluginTask.description,
						priority: taskData.priority,
						dueDate: taskData.dueDate,
						tags: taskData.tags,
						linkedNotes: taskData.linkedNotes
					});
				}
			}
		} catch (error) {
			console.error('Error syncing task to internal system:', error);
		}
	}

	/**
	 * Ensure default project exists for task synchronization
	 */
	private async ensureDefaultProjectExists(projectId: string): Promise<void> {
		try {
			const existingProject = await this.projectRepository.findById(projectId);
			if (!existingProject) {
				// Create default project
				const defaultProject: Project = {
					id: projectId,
					name: projectId === 'default' ? '默认项目' : projectId,
					description: '自动创建的默认项目，用于同步Tasks插件中的任务',
					startDate: new Date(),
					status: ProjectStatus.ACTIVE,
					priority: Priority.MEDIUM,
					tags: ['auto-created'],
					settings: {
						workflowId: 'default',
						sprintDuration: 14,
						autoProgressTracking: true,
						ganttViewEnabled: false
					},
					createdAt: new Date(),
					updatedAt: new Date()
				};

				await this.projectRepository.create(defaultProject);
				console.log(`Created default project: ${projectId}`);
			}
		} catch (error) {
			console.error('Error ensuring default project exists:', error);
			throw error;
		}
	}

	/**
	 * Update task in markdown file
	 */
	private async updateTaskInFile(task: Task, filePath: string): Promise<void> {
		try {
			const file = this.app.vault.getAbstractFileByPath(filePath);
			if (!(file instanceof TFile)) return;

			const content = await this.app.vault.read(file);
			const lines = content.split('\n');
			
			// Find the task line (this is simplified - in reality would need better matching)
			const taskLineIndex = lines.findIndex(line => 
				line.includes(task.title) && line.match(/^(\s*)-\s*\[(.)\]/)
			);

			if (taskLineIndex !== -1) {
				const newTaskLine = this.convertToTasksPluginFormat(task);
				lines[taskLineIndex] = newTaskLine;
				
				const newContent = lines.join('\n');
				await this.app.vault.modify(file, newContent);
			}
		} catch (error) {
			console.error('Error updating task in file:', error);
		}
	}
}