// PTM (Project Task Manager) main service coordinator

import { App } from 'obsidian';
import { DataManager, DataManagerOptions } from './DataManager';
import { ProjectRepository } from './ProjectRepository';
import { TaskRepository } from './TaskRepository';
import { ProjectManager } from './ProjectManager';
import { TaskManager } from './TaskManager';
import { PTMFileHandler } from './PTMFileHandler';
import { TasksPluginBridge, TasksPluginBridgeOptions } from './TasksPluginBridge';
import { NoteLinkManager } from './NoteLinkManager';
import { KanbanManager } from './KanbanManager';
import { SprintManager } from './SprintManager';
import { GanttManager } from './GanttManager';
import { IndependentTaskModeController } from './IndependentTaskModeController';
import { IndependentTaskService } from './IndependentTaskService';
import { Project, Priority } from '../models';

export interface PTMManagerOptions {
	dataManagerOptions?: Partial<DataManagerOptions>;
	tasksPluginBridgeOptions?: Partial<TasksPluginBridgeOptions>;
}

/**
 * Main service coordinator for the Project Task Manager plugin
 * Manages all core services and provides a unified interface
 */
export class PTMManager {
	private app: App;
	private dataManager: DataManager;
	private projectRepository: ProjectRepository;
	private taskRepository: TaskRepository;
	private projectManager: ProjectManager;
	private taskManager: TaskManager;
	private ptmFileHandler: PTMFileHandler;
	private tasksPluginBridge: TasksPluginBridge;
	private noteLinkManager: NoteLinkManager;
	private kanbanManager: KanbanManager;
	private sprintManager: SprintManager;
	private ganttManager: GanttManager;
	private independentTaskModeController: IndependentTaskModeController;
	private independentTaskService: IndependentTaskService;
	private initialized = false;

	constructor(app: App, options: PTMManagerOptions = {}) {
		this.app = app;

		// Initialize DataManager with default options
		const dataManagerOptions: DataManagerOptions = {
			enableCaching: true,
			maxCacheSize: 1000,
			dataFileName: 'data.json',
			...options.dataManagerOptions
		};

		this.dataManager = new DataManager(app, dataManagerOptions);
		this.projectRepository = new ProjectRepository(this.dataManager);
		this.taskRepository = new TaskRepository(this.dataManager);
		this.ptmFileHandler = new PTMFileHandler(app);
		
		this.projectManager = new ProjectManager(
			app,
			this.projectRepository,
			this.taskRepository,
			this.ptmFileHandler
		);
		
		this.taskManager = new TaskManager(
			app,
			this.taskRepository,
			this.projectRepository
		);
		
		// 创建NoteLinkManager并设置到TaskManager中
		this.noteLinkManager = new NoteLinkManager(app, this.taskRepository);
		this.taskManager.setNoteLinkManager(this.noteLinkManager);
		
		this.tasksPluginBridge = new TasksPluginBridge(
			app,
			this.taskRepository,
			this.taskManager,
			this.projectRepository,
			options.tasksPluginBridgeOptions
		);

		this.kanbanManager = new KanbanManager(
			app,
			this.taskRepository,
			this.projectRepository,
			this.dataManager
		);

		this.sprintManager = new SprintManager(this.dataManager);
		
		this.ganttManager = new GanttManager(
			this.taskManager,
			this.projectManager
		);

		// 创建独立任务模式控制器和服务
		this.independentTaskModeController = new IndependentTaskModeController(
			app,
			this.taskRepository,
			this.dataManager
		);

		this.independentTaskService = new IndependentTaskService(
			app,
			this.taskRepository,
			this.independentTaskModeController
		);
	}

	/**
	 * Initialize all services
	 */
	async initialize(): Promise<void> {
		if (this.initialized) {
			return;
		}

		try {
			await this.dataManager.initialize();
			await this.noteLinkManager.initialize();
			await this.tasksPluginBridge.initialize();
			await this.kanbanManager.initialize();
			await this.sprintManager.initialize();
			await this.independentTaskModeController.initialize();
			this.initialized = true;
			console.log('PTMManager initialized successfully');
		} catch (error) {
			console.error('Failed to initialize PTMManager:', error);
			throw error;
		}
	}

	/**
	 * Get the project manager instance
	 */
	getProjectManager(): ProjectManager {
		this.ensureInitialized();
		return this.projectManager;
	}

	/**
	 * Get the project repository instance
	 */
	getProjectRepository(): ProjectRepository {
		this.ensureInitialized();
		return this.projectRepository;
	}

	/**
	 * Get the task repository instance
	 */
	getTaskRepository(): TaskRepository {
		this.ensureInitialized();
		return this.taskRepository;
	}

	/**
	 * Get the task manager instance
	 */
	getTaskManager(): TaskManager {
		this.ensureInitialized();
		return this.taskManager;
	}

	/**
	 * Get the note link manager instance
	 */
	getNoteLinkManager(): NoteLinkManager {
		this.ensureInitialized();
		return this.noteLinkManager;
	}

	/**
	 * 维护所有任务的笔记引用一致性
	 */
	async maintainTaskNoteConsistency(): Promise<{
		totalTasks: number;
		processedTasks: number;
		totalConflicts: number;
		resolvedConflicts: number;
		failedConflicts: number;
	}> {
		this.ensureInitialized();
		return await this.noteLinkManager.maintainAllTasksConsistency();
	}

	/**
	 * 维护特定任务的笔记引用一致性
	 */
	async maintainTaskConsistency(taskId: string): Promise<{
		conflicts: Array<{
			notePath: string;
			conflictType: 'status_mismatch' | 'content_mismatch' | 'missing_reference';
			details: string;
		}>;
		resolved: number;
		failed: number;
	}> {
		this.ensureInitialized();
		return await this.noteLinkManager.maintainConsistency(taskId);
	}

	/**
	 * Get the PTM file handler instance
	 */
	getPTMFileHandler(): PTMFileHandler {
		this.ensureInitialized();
		return this.ptmFileHandler;
	}

	/**
	 * Get the data manager instance
	 */
	getDataManager(): DataManager {
		this.ensureInitialized();
		return this.dataManager;
	}

	/**
	 * Get the tasks plugin bridge instance
	 */
	getTasksPluginBridge(): TasksPluginBridge {
		this.ensureInitialized();
		return this.tasksPluginBridge;
	}

	/**
	 * Get the kanban manager instance
	 */
	getKanbanManager(): KanbanManager {
		this.ensureInitialized();
		return this.kanbanManager;
	}

	/**
	 * Get the sprint manager instance
	 */
	getSprintManager(): SprintManager {
		this.ensureInitialized();
		return this.sprintManager;
	}

	/**
	 * Get the gantt manager instance
	 */
	getGanttManager(): GanttManager {
		this.ensureInitialized();
		return this.ganttManager;
	}

	/**
	 * Get the independent task mode controller instance
	 */
	getIndependentTaskModeController(): IndependentTaskModeController {
		this.ensureInitialized();
		return this.independentTaskModeController;
	}

	/**
	 * Get the independent task service instance
	 */
	getIndependentTaskService(): IndependentTaskService {
		this.ensureInitialized();
		return this.independentTaskService;
	}

	/**
	 * Get the PTM file handler instance (alias for compatibility)
	 */
	get ptmHandler(): PTMFileHandler {
		return this.getPTMFileHandler();
	}

	/**
	 * Create a backup of all data
	 */
	async createBackup(): Promise<string> {
		this.ensureInitialized();
		return await this.dataManager.backup();
	}

	/**
	 * Restore data from backup
	 */
	async restoreFromBackup(backupFileName: string): Promise<void> {
		this.ensureInitialized();
		await this.dataManager.restore(backupFileName);
	}

	/**
	 * Clear all cached data
	 */
	clearCache(): void {
		if (this.initialized) {
			this.dataManager.clearCache();
		}
	}

	/**
	 * Get system health status
	 */
	async getHealthStatus(): Promise<{
		initialized: boolean;
		cacheSize: number;
		projectCount: number;
		taskCount: number;
		dataFileExists: boolean;
	}> {
		const status = {
			initialized: this.initialized,
			cacheSize: this.initialized ? this.dataManager.getCacheSize() : 0,
			projectCount: 0,
			taskCount: 0,
			dataFileExists: false
		};

		if (this.initialized) {
			try {
				const projects = await this.projectRepository.findAll();
				const tasks = await this.taskRepository.findAll();
				status.projectCount = projects.length;
				status.taskCount = tasks.length;
				status.dataFileExists = await this.dataManager.exists('projects') || await this.dataManager.exists('tasks');
			} catch (error) {
				console.warn('Error getting health status:', error);
			}
		}

		return status;
	}

	/**
	 * Create a new PTM project with file
	 */
	async createPTMProject(projectName: string, options?: {
		description?: string;
		filePath?: string;
		priority?: Priority;
		tags?: string[];
	}): Promise<{ project: Project; ptmFile: string }> {
		this.ensureInitialized();

		try {
			// 创建项目
			const project = await this.projectManager.createProject({
				name: projectName,
				description: options?.description,
				priority: options?.priority || Priority.MEDIUM,
				tags: options?.tags || [],
				createPTMFile: false // 我们手动创建PTM文件
			});

			// 生成PTM文件路径
			const ptmFilePath = options?.filePath || `${projectName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.ptm`;

			// 获取项目的所有任务
			const projectTasks = await this.taskRepository.findByProject(project.id);

			// 创建PTM文件
			const ptmFile = await this.ptmFileHandler.createPTMFile(ptmFilePath, project, {
				tasks: projectTasks,
				sprints: [], // 暂时为空，后续可以添加Sprint支持
				workflows: [], // 暂时为空，后续可以添加工作流支持
			});

			console.log(`PTM项目已创建: ${project.name}, 文件: ${ptmFile.path}`);
			
			return {
				project,
				ptmFile: ptmFile.path
			};
		} catch (error) {
			console.error('Error creating PTM project:', error);
			throw new Error(`创建PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Load PTM project from file
	 */
	async loadPTMProject(filePath: string): Promise<Project> {
		this.ensureInitialized();

		try {
			// 读取PTM文件
			const ptmContent = await this.ptmFileHandler.readPTMFile(filePath);

			// 检查项目是否已存在
			const existingProject = await this.projectRepository.findById(ptmContent.project.id);
			
			let project: Project;
			if (existingProject) {
				// 更新现有项目
				project = await this.projectManager.updateProject(ptmContent.project.id, {
					name: ptmContent.project.name,
					description: ptmContent.project.description,
					startDate: ptmContent.project.startDate,
					endDate: ptmContent.project.endDate,
					status: ptmContent.project.status,
					priority: ptmContent.project.priority,
					tags: ptmContent.project.tags
				});
			} else {
				// 创建新项目
				await this.projectRepository.create(ptmContent.project);
				project = ptmContent.project;
			}

			// 同步任务数据
			for (const task of ptmContent.tasks) {
				const existingTask = await this.taskRepository.findById(task.id);
				if (existingTask) {
					await this.taskRepository.update(task.id, task);
				} else {
					await this.taskRepository.create(task);
				}
			}

			// TODO: 同步Sprint和工作流数据（当这些功能实现后）

			console.log(`PTM项目已加载: ${project.name} from ${filePath}`);
			return project;
		} catch (error) {
			console.error('Error loading PTM project:', error);
			throw new Error(`加载PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Get all PTM projects (projects that have associated PTM files)
	 */
	getAllPTMProjects(): Array<{ project: Project; ptmFile: string }> {
		this.ensureInitialized();

		try {
			// 这是一个同步方法，返回当前已知的PTM项目
			// 实际实现中，我们可能需要扫描所有PTM文件并匹配项目
			// 为了简化，这里返回一个基于内存数据的结果
			
			// TODO: 实现更完整的PTM项目发现机制
			// 目前返回空数组，因为我们需要异步操作来扫描文件
			console.warn('getAllPTMProjects: 当前实现返回空数组，需要异步扫描PTM文件');
			return [];
		} catch (error) {
			console.error('Error getting PTM projects:', error);
			return [];
		}
	}

	/**
	 * Get all PTM projects asynchronously
	 */
	async getAllPTMProjectsAsync(): Promise<Array<{ project: Project; ptmFile: string }>> {
		this.ensureInitialized();

		try {
			const ptmProjects: Array<{ project: Project; ptmFile: string }> = [];
			
			// 获取所有PTM文件
			const ptmFiles = await this.ptmFileHandler.getAllPTMFiles();
			
			// 读取每个PTM文件并匹配项目
			for (const file of ptmFiles) {
				try {
					const ptmContent = await this.ptmFileHandler.readPTMFile(file.path);
					const project = await this.projectRepository.findById(ptmContent.project.id);
					
					if (project) {
						ptmProjects.push({
							project,
							ptmFile: file.path
						});
					}
				} catch (error) {
					console.warn(`无法读取PTM文件 ${file.path}:`, error);
					// 继续处理其他文件
				}
			}

			return ptmProjects;
		} catch (error) {
			console.error('Error getting PTM projects async:', error);
			throw new Error(`获取PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Backup PTM project
	 */
	async backupPTMProject(projectId: string): Promise<string> {
		this.ensureInitialized();

		try {
			// 获取项目信息
			const project = await this.projectRepository.findById(projectId);
			if (!project) {
				throw new Error(`项目不存在: ${projectId}`);
			}

			// 查找项目对应的PTM文件
			const ptmFiles = await this.ptmFileHandler.getAllPTMFiles();
			let targetPTMFile: string | null = null;

			for (const file of ptmFiles) {
				try {
					const ptmContent = await this.ptmFileHandler.readPTMFile(file.path);
					if (ptmContent.project.id === projectId) {
						targetPTMFile = file.path;
						break;
					}
				} catch (error) {
					// 忽略无法读取的文件
					continue;
				}
			}

			if (!targetPTMFile) {
				// 如果没有找到PTM文件，创建一个临时的PTM文件进行备份
				const tempPTMPath = `${project.name}_temp.ptm`;
				const projectTasks = await this.taskRepository.findByProject(projectId);
				
				const ptmFile = await this.ptmFileHandler.createPTMFile(tempPTMPath, project, {
					tasks: projectTasks,
					sprints: [],
					workflows: []
				});
				
				targetPTMFile = ptmFile.path;
			}

			// 创建备份
			const backupPath = await this.ptmFileHandler.backupPTMFile(targetPTMFile);
			
			console.log(`PTM项目备份已创建: ${project.name} -> ${backupPath}`);
			return backupPath;
		} catch (error) {
			console.error('Error backing up PTM project:', error);
			throw new Error(`备份PTM项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Cleanup resources
	 */
	async cleanup(): Promise<void> {
		if (this.initialized) {
			this.ptmFileHandler.cleanup();
			this.tasksPluginBridge.cleanup();
			this.noteLinkManager.cleanup();
			this.dataManager.clearCache();
			this.initialized = false;
			console.log('PTMManager cleaned up');
		}
	}

	/**
	 * Ensure the manager is initialized
	 */
	private ensureInitialized(): void {
		if (!this.initialized) {
			throw new Error('PTMManager not initialized. Call initialize() first.');
		}
	}
}