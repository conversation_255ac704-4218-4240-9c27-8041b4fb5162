// 项目报告生成器服务

import { App, Notice } from 'obsidian';
import { 
	ProjectProgressReport, 
	TimeTrackingReport, 
	TeamPerformanceReport,
	ReportGenerationConfig,
	ReportGenerationResult,
	ReportType,
	ReportFormat,
	ReportTemplate,
	ReportSection,
	DailyProgressPoint,
	WeeklyVelocityPoint,
	TaskTimeAnalysis,
	TeamMemberTimeAnalysis,
	MemberPerformance,
	ProjectRisk,
	TaskBlocker,
	DEFAULT_REPORT_TEMPLATES
} from '../models/Report';
import { Project, ProjectStatus } from '../models/Project';
import { Task, TaskStatus } from '../models/Task';
import { ProjectManager } from './ProjectManager';
import { TaskRepository } from './TaskRepository';
import { SprintManager } from './SprintManager';

export class ProjectReportGenerator {
	private app: App;
	private projectManager: ProjectManager;
	private taskRepository: TaskRepository;
	private sprintManager: SprintManager;
	private reportTemplates: Map<string, ReportTemplate> = new Map();

	constructor(
		app: App,
		projectManager: ProjectManager,
		taskRepository: TaskRepository,
		sprintManager: SprintManager
	) {
		this.app = app;
		this.projectManager = projectManager;
		this.taskRepository = taskRepository;
		this.sprintManager = sprintManager;
		
		// 初始化默认模板
		this.initializeDefaultTemplates();
	}

	/**
	 * 生成项目进度报告
	 */
	async generateProjectProgressReport(config: ReportGenerationConfig): Promise<ProjectProgressReport> {
		const project = await this.projectManager.getProject(config.projectId);
		if (!project) {
			throw new Error(`项目不存在: ${config.projectId}`);
		}

		const tasks = await this.taskRepository.findByProject(config.projectId);
		const filteredTasks = this.filterTasksByTimeRange(tasks, config.timeRange);

		// 基础统计
		const totalTasks = filteredTasks.length;
		const completedTasks = filteredTasks.filter(t => t.status === TaskStatus.COMPLETED).length;
		const inProgressTasks = filteredTasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
		const blockedTasks = filteredTasks.filter(t => t.status === TaskStatus.BLOCKED).length;
		const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

		// 时间分析
		const timeAnalysis = this.calculateTimeAnalysis(project, filteredTasks);
		
		// 任务分析
		const taskAnalysis = this.calculateTaskAnalysis(filteredTasks);
		
		// 趋势数据
		const dailyProgress = await this.calculateDailyProgress(config.projectId, config.timeRange);
		const weeklyVelocity = await this.calculateWeeklyVelocity(config.projectId, config.timeRange);
		
		// 风险和问题
		const risks = this.identifyProjectRisks(project, filteredTasks);
		const blockers = this.identifyTaskBlockers(filteredTasks);

		const report: ProjectProgressReport = {
			projectId: config.projectId,
			projectName: project.name,
			reportDate: new Date(),
			timeRange: config.timeRange,
			
			// 基础统计
			totalTasks,
			completedTasks,
			inProgressTasks,
			blockedTasks,
			completionPercentage,
			
			// 时间分析
			...timeAnalysis,
			
			// 任务分析
			...taskAnalysis,
			
			// 趋势数据
			dailyProgress,
			weeklyVelocity,
			
			// 里程碑（暂时为空，后续可扩展）
			milestones: [],
			
			// 风险和问题
			risks,
			blockers
		};

		return report;
	}

	/**
	 * 生成时间跟踪报告
	 */
	async generateTimeTrackingReport(config: ReportGenerationConfig): Promise<TimeTrackingReport> {
		const project = await this.projectManager.getProject(config.projectId);
		if (!project) {
			throw new Error(`项目不存在: ${config.projectId}`);
		}

		const tasks = await this.taskRepository.findByProject(config.projectId);
		const filteredTasks = this.filterTasksByTimeRange(tasks, config.timeRange);

		// 总体时间统计
		const totalEstimatedHours = filteredTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
		const totalActualHours = filteredTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);
		const timeVariance = totalActualHours - totalEstimatedHours;
		const timeVariancePercentage = totalEstimatedHours > 0 ? (timeVariance / totalEstimatedHours) * 100 : 0;

		// 按任务分析
		const taskTimeAnalysis = this.calculateTaskTimeAnalysis(filteredTasks);
		
		// 按人员分析
		const teamTimeAnalysis = this.calculateTeamTimeAnalysis(filteredTasks);
		
		// 按时间段分析
		const dailyTimeTracking = await this.calculateDailyTimeTracking(config.projectId, config.timeRange);
		const weeklyTimeTracking = await this.calculateWeeklyTimeTracking(config.projectId, config.timeRange);
		
		// 效率分析
		const productivityMetrics = this.calculateProductivityMetrics(filteredTasks);

		const report: TimeTrackingReport = {
			projectId: config.projectId,
			projectName: project.name,
			reportDate: new Date(),
			timeRange: config.timeRange,
			
			totalEstimatedHours,
			totalActualHours,
			timeVariance,
			timeVariancePercentage,
			
			taskTimeAnalysis,
			teamTimeAnalysis,
			dailyTimeTracking,
			weeklyTimeTracking,
			productivityMetrics
		};

		return report;
	}

	/**
	 * 生成团队绩效报告
	 */
	async generateTeamPerformanceReport(config: ReportGenerationConfig): Promise<TeamPerformanceReport> {
		const project = await this.projectManager.getProject(config.projectId);
		if (!project) {
			throw new Error(`项目不存在: ${config.projectId}`);
		}

		const tasks = await this.taskRepository.findByProject(config.projectId);
		const filteredTasks = this.filterTasksByTimeRange(tasks, config.timeRange);

		// 团队概览
		const assignees = new Set(filteredTasks.filter(t => t.assignee).map(t => t.assignee!));
		const teamSize = assignees.size;
		const activeMembers = new Set(
			filteredTasks.filter(t => t.assignee && t.status !== TaskStatus.TODO).map(t => t.assignee!)
		).size;
		const totalTasksAssigned = filteredTasks.filter(t => t.assignee).length;
		const totalTasksCompleted = filteredTasks.filter(t => t.status === TaskStatus.COMPLETED).length;
		const teamVelocity = this.calculateTeamVelocity(filteredTasks, config.timeRange);

		// 个人绩效
		const memberPerformance = this.calculateMemberPerformance(filteredTasks);
		
		// 协作分析（简化实现）
		const collaborationMetrics = {
			crossFunctionalTasks: 0,
			sharedTasks: 0,
			communicationFrequency: 0,
			knowledgeSharing: 0,
			teamCohesion: 0
		};
		
		// 质量指标（简化实现）
		const qualityMetrics = {
			defectRate: 0,
			reworkRate: 0,
			reviewCoverage: 0,
			testCoverage: 0,
			customerSatisfaction: 0
		};
		
		// 团队健康度（简化实现）
		const teamHealthMetrics = {
			burnoutRisk: 0,
			workLifeBalance: 0,
			jobSatisfaction: 0,
			skillDevelopment: 0,
			retentionRisk: 0
		};

		const report: TeamPerformanceReport = {
			projectId: config.projectId,
			projectName: project.name,
			reportDate: new Date(),
			timeRange: config.timeRange,
			
			teamSize,
			activeMembers,
			totalTasksAssigned,
			totalTasksCompleted,
			teamVelocity,
			
			memberPerformance,
			collaborationMetrics,
			qualityMetrics,
			teamHealthMetrics
		};

		return report;
	}

	/**
	 * 生成并导出报告
	 */
	async generateAndExportReport(config: ReportGenerationConfig): Promise<ReportGenerationResult> {
		try {
			let reportData: any;
			
			// 根据报告类型生成相应报告
			switch (config.reportType) {
				case ReportType.PROJECT_PROGRESS:
					reportData = await this.generateProjectProgressReport(config);
					break;
				case ReportType.TIME_TRACKING:
					reportData = await this.generateTimeTrackingReport(config);
					break;
				case ReportType.TEAM_PERFORMANCE:
					reportData = await this.generateTeamPerformanceReport(config);
					break;
				default:
					throw new Error(`不支持的报告类型: ${config.reportType}`);
			}

			// 导出报告
			const filePath = await this.exportReport(reportData, config);

			const result: ReportGenerationResult = {
				success: true,
				reportId: this.generateReportId(),
				filePath,
				data: reportData,
				generatedAt: new Date()
			};

			new Notice(`报告生成成功: ${filePath || '内存中'}`);
			return result;

		} catch (error) {
			console.error('报告生成失败:', error);
			
			const result: ReportGenerationResult = {
				success: false,
				reportId: this.generateReportId(),
				data: null,
				generatedAt: new Date(),
				error: error instanceof Error ? error.message : '未知错误'
			};

			new Notice(`报告生成失败: ${result.error}`);
			return result;
		}
	}

	/**
	 * 导出报告到文件
	 */
	private async exportReport(reportData: any, config: ReportGenerationConfig): Promise<string | undefined> {
		if (!config.outputPath) {
			return undefined; // 不导出文件，仅返回数据
		}

		const fileName = this.generateReportFileName(config);
		const fullPath = `${config.outputPath}/${fileName}`;

		try {
			let content: string;
			
			switch (config.format) {
				case ReportFormat.JSON:
					content = JSON.stringify(reportData, null, 2);
					break;
				case ReportFormat.MARKDOWN:
					content = this.convertToMarkdown(reportData, config.reportType);
					break;
				case ReportFormat.CSV:
					content = this.convertToCSV(reportData, config.reportType);
					break;
				case ReportFormat.HTML:
					content = this.convertToHTML(reportData, config.reportType);
					break;
				default:
					throw new Error(`不支持的导出格式: ${config.format}`);
			}

			await this.app.vault.create(fullPath, content);
			return fullPath;

		} catch (error) {
			console.error('报告导出失败:', error);
			throw new Error(`报告导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	}

	// 私有辅助方法

	private filterTasksByTimeRange(tasks: Task[], timeRange: { startDate: Date; endDate: Date }): Task[] {
		return tasks.filter(task => {
			const taskDate = task.createdAt;
			return taskDate >= timeRange.startDate && taskDate <= timeRange.endDate;
		});
	}

	private calculateTimeAnalysis(project: Project, tasks: Task[]) {
		const now = new Date();
		const plannedDuration = project.endDate ? 
			Math.ceil((project.endDate.getTime() - project.startDate.getTime()) / (1000 * 60 * 60 * 24)) : 
			undefined;
		
		const actualDuration = Math.ceil((now.getTime() - project.startDate.getTime()) / (1000 * 60 * 60 * 24));
		
		const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED);
		const totalTasks = tasks.length;
		const completionRate = totalTasks > 0 ? completedTasks.length / totalTasks : 0;
		
		const remainingDuration = plannedDuration ? 
			Math.max(0, plannedDuration - actualDuration) : 
			undefined;
		
		const isOnSchedule = project.endDate ? now <= project.endDate : true;
		const scheduleVariance = plannedDuration ? actualDuration - plannedDuration : 0;

		return {
			plannedDuration,
			actualDuration,
			remainingDuration,
			isOnSchedule,
			scheduleVariance
		};
	}

	private calculateTaskAnalysis(tasks: Task[]) {
		const tasksByPriority: Record<string, number> = {};
		const tasksByStatus: Record<string, number> = {};
		const tasksByAssignee: Record<string, number> = {};
		
		let overdueTasksCount = 0;
		let totalDuration = 0;
		let completedTasksWithDuration = 0;

		tasks.forEach(task => {
			// 按优先级统计
			tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;
			
			// 按状态统计
			tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1;
			
			// 按负责人统计
			if (task.assignee) {
				tasksByAssignee[task.assignee] = (tasksByAssignee[task.assignee] || 0) + 1;
			}
			
			// 逾期任务统计
			if (task.dueDate && new Date() > task.dueDate && task.status !== TaskStatus.COMPLETED) {
				overdueTasksCount++;
			}
			
			// 平均任务时长计算
			if (task.status === TaskStatus.COMPLETED && task.completedDate && task.actualHours) {
				totalDuration += task.actualHours;
				completedTasksWithDuration++;
			}
		});

		const averageTaskDuration = completedTasksWithDuration > 0 ? 
			totalDuration / completedTasksWithDuration : 0;

		return {
			tasksByPriority,
			tasksByStatus,
			tasksByAssignee,
			overdueTasksCount,
			averageTaskDuration
		};
	}

	private async calculateDailyProgress(projectId: string, timeRange: { startDate: Date; endDate: Date }): Promise<DailyProgressPoint[]> {
		// 简化实现：生成每日进度点
		const points: DailyProgressPoint[] = [];
		const tasks = await this.taskRepository.findByProject(projectId);
		
		const currentDate = new Date(timeRange.startDate);
		while (currentDate <= timeRange.endDate) {
			const tasksCompletedByDate = tasks.filter(task => 
				task.completedDate && task.completedDate <= currentDate
			).length;
			
			points.push({
				date: new Date(currentDate),
				completedTasks: tasksCompletedByDate,
				totalTasks: tasks.length,
				completionPercentage: tasks.length > 0 ? (tasksCompletedByDate / tasks.length) * 100 : 0
			});
			
			currentDate.setDate(currentDate.getDate() + 1);
		}
		
		return points;
	}

	private async calculateWeeklyVelocity(projectId: string, timeRange: { startDate: Date; endDate: Date }): Promise<WeeklyVelocityPoint[]> {
		// 简化实现：生成每周速度点
		const points: WeeklyVelocityPoint[] = [];
		const tasks = await this.taskRepository.findByProject(projectId);
		
		const currentWeekStart = new Date(timeRange.startDate);
		currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay()); // 调整到周一
		
		while (currentWeekStart < timeRange.endDate) {
			const weekEnd = new Date(currentWeekStart);
			weekEnd.setDate(weekEnd.getDate() + 6);
			
			const weekTasks = tasks.filter(task => 
				task.completedDate && 
				task.completedDate >= currentWeekStart && 
				task.completedDate <= weekEnd
			);
			
			const hoursWorked = weekTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0);
			const velocity = weekTasks.length; // 简化：以完成任务数作为速度
			
			points.push({
				weekStart: new Date(currentWeekStart),
				weekEnd: new Date(weekEnd),
				tasksCompleted: weekTasks.length,
				hoursWorked,
				velocity
			});
			
			currentWeekStart.setDate(currentWeekStart.getDate() + 7);
		}
		
		return points;
	}

	private identifyProjectRisks(project: Project, tasks: Task[]): ProjectRisk[] {
		const risks: ProjectRisk[] = [];
		
		// 进度风险
		const overdueTasks = tasks.filter(task => 
			task.dueDate && new Date() > task.dueDate && task.status !== TaskStatus.COMPLETED
		);
		
		if (overdueTasks.length > 0) {
			risks.push({
				id: 'overdue_tasks',
				description: `有 ${overdueTasks.length} 个任务已逾期`,
				severity: overdueTasks.length > 5 ? 'high' : 'medium',
				probability: 1,
				impact: '可能影响项目整体进度',
				identifiedDate: new Date()
			});
		}
		
		// 阻塞风险
		const blockedTasks = tasks.filter(task => task.status === TaskStatus.BLOCKED);
		if (blockedTasks.length > 0) {
			risks.push({
				id: 'blocked_tasks',
				description: `有 ${blockedTasks.length} 个任务被阻塞`,
				severity: 'medium',
				probability: 0.8,
				impact: '可能导致后续任务延期',
				identifiedDate: new Date()
			});
		}
		
		return risks;
	}

	private identifyTaskBlockers(tasks: Task[]): TaskBlocker[] {
		return tasks
			.filter(task => task.status === TaskStatus.BLOCKED)
			.map(task => ({
				taskId: task.id,
				taskTitle: task.title,
				blockerDescription: '任务状态为阻塞',
				blockedSince: task.updatedAt,
				assignee: task.assignee,
				priority: task.priority
			}));
	}

	private calculateTaskTimeAnalysis(tasks: Task[]): TaskTimeAnalysis[] {
		return tasks
			.filter(task => task.estimatedHours || task.actualHours)
			.map(task => {
				const estimatedHours = task.estimatedHours || 0;
				const actualHours = task.actualHours || 0;
				const timeVariance = actualHours - estimatedHours;
				const efficiency = estimatedHours > 0 ? estimatedHours / actualHours : 1;
				
				return {
					taskId: task.id,
					taskTitle: task.title,
					estimatedHours,
					actualHours,
					timeVariance,
					efficiency,
					status: task.status,
					assignee: task.assignee
				};
			});
	}

	private calculateTeamTimeAnalysis(tasks: Task[]): TeamMemberTimeAnalysis[] {
		const memberMap = new Map<string, {
			estimatedHours: number;
			actualHours: number;
			tasksCompleted: number;
			totalTasks: number;
		}>();

		tasks.forEach(task => {
			if (!task.assignee) return;
			
			const current = memberMap.get(task.assignee) || {
				estimatedHours: 0,
				actualHours: 0,
				tasksCompleted: 0,
				totalTasks: 0
			};
			
			current.estimatedHours += task.estimatedHours || 0;
			current.actualHours += task.actualHours || 0;
			current.totalTasks += 1;
			
			if (task.status === TaskStatus.COMPLETED) {
				current.tasksCompleted += 1;
			}
			
			memberMap.set(task.assignee, current);
		});

		return Array.from(memberMap.entries()).map(([assignee, data]) => ({
			assignee,
			totalEstimatedHours: data.estimatedHours,
			totalActualHours: data.actualHours,
			tasksCompleted: data.tasksCompleted,
			averageTaskTime: data.tasksCompleted > 0 ? data.actualHours / data.tasksCompleted : 0,
			efficiency: data.estimatedHours > 0 ? data.estimatedHours / data.actualHours : 1,
			workload: data.actualHours // 简化实现
		}));
	}

	private async calculateDailyTimeTracking(projectId: string, timeRange: { startDate: Date; endDate: Date }) {
		// 简化实现
		return [];
	}

	private async calculateWeeklyTimeTracking(projectId: string, timeRange: { startDate: Date; endDate: Date }) {
		// 简化实现
		return [];
	}

	private calculateProductivityMetrics(tasks: Task[]) {
		// 简化实现
		return {
			overallEfficiency: 0.85,
			timeAccuracy: 0.75,
			focusTime: 0.6,
			multitaskingIndex: 0.3,
			peakProductivityHours: ['09:00-11:00', '14:00-16:00']
		};
	}

	private calculateTeamVelocity(tasks: Task[], timeRange: { startDate: Date; endDate: Date }): number {
		const completedTasks = tasks.filter(task => 
			task.status === TaskStatus.COMPLETED &&
			task.completedDate &&
			task.completedDate >= timeRange.startDate &&
			task.completedDate <= timeRange.endDate
		);
		
		const days = Math.ceil((timeRange.endDate.getTime() - timeRange.startDate.getTime()) / (1000 * 60 * 60 * 24));
		return days > 0 ? completedTasks.length / days : 0;
	}

	private calculateMemberPerformance(tasks: Task[]): MemberPerformance[] {
		const memberMap = new Map<string, {
			assigned: number;
			completed: number;
			totalTime: number;
			onTime: number;
		}>();

		tasks.forEach(task => {
			if (!task.assignee) return;
			
			const current = memberMap.get(task.assignee) || {
				assigned: 0,
				completed: 0,
				totalTime: 0,
				onTime: 0
			};
			
			current.assigned += 1;
			current.totalTime += task.actualHours || 0;
			
			if (task.status === TaskStatus.COMPLETED) {
				current.completed += 1;
				
				// 检查是否按时完成
				if (task.dueDate && task.completedDate && task.completedDate <= task.dueDate) {
					current.onTime += 1;
				}
			}
			
			memberMap.set(task.assignee, current);
		});

		return Array.from(memberMap.entries()).map(([assignee, data]) => ({
			assignee,
			tasksAssigned: data.assigned,
			tasksCompleted: data.completed,
			completionRate: data.assigned > 0 ? data.completed / data.assigned : 0,
			averageTaskTime: data.completed > 0 ? data.totalTime / data.completed : 0,
			qualityScore: 0.8, // 简化实现
			collaborationScore: 0.7, // 简化实现
			onTimeDelivery: data.completed > 0 ? data.onTime / data.completed : 0,
			workload: data.totalTime > 40 ? 'overloaded' : data.totalTime < 20 ? 'underloaded' : 'optimal'
		}));
	}

	private convertToMarkdown(reportData: any, reportType: ReportType): string {
		// 简化的Markdown转换实现
		let markdown = `# ${this.getReportTitle(reportType)}\n\n`;
		markdown += `生成时间: ${new Date().toLocaleString()}\n\n`;
		
		if (reportType === ReportType.PROJECT_PROGRESS) {
			const report = reportData as ProjectProgressReport;
			markdown += `## 项目概览\n\n`;
			markdown += `- 项目名称: ${report.projectName}\n`;
			markdown += `- 总任务数: ${report.totalTasks}\n`;
			markdown += `- 已完成: ${report.completedTasks}\n`;
			markdown += `- 进行中: ${report.inProgressTasks}\n`;
			markdown += `- 已阻塞: ${report.blockedTasks}\n`;
			markdown += `- 完成率: ${report.completionPercentage.toFixed(1)}%\n\n`;
		}
		
		return markdown;
	}

	private convertToCSV(reportData: any, reportType: ReportType): string {
		// 简化的CSV转换实现
		if (reportType === ReportType.PROJECT_PROGRESS) {
			const report = reportData as ProjectProgressReport;
			let csv = '指标,数值\n';
			csv += `项目名称,${report.projectName}\n`;
			csv += `总任务数,${report.totalTasks}\n`;
			csv += `已完成,${report.completedTasks}\n`;
			csv += `进行中,${report.inProgressTasks}\n`;
			csv += `已阻塞,${report.blockedTasks}\n`;
			csv += `完成率,${report.completionPercentage.toFixed(1)}%\n`;
			return csv;
		}
		
		return '';
	}

	private convertToHTML(reportData: any, reportType: ReportType): string {
		// 简化的HTML转换实现
		let html = `<!DOCTYPE html>
<html>
<head>
    <title>${this.getReportTitle(reportType)}</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>${this.getReportTitle(reportType)}</h1>
    <p>生成时间: ${new Date().toLocaleString()}</p>
`;
		
		if (reportType === ReportType.PROJECT_PROGRESS) {
			const report = reportData as ProjectProgressReport;
			html += `
    <h2>项目概览</h2>
    <table>
        <tr><th>指标</th><th>数值</th></tr>
        <tr><td>项目名称</td><td>${report.projectName}</td></tr>
        <tr><td>总任务数</td><td>${report.totalTasks}</td></tr>
        <tr><td>已完成</td><td>${report.completedTasks}</td></tr>
        <tr><td>进行中</td><td>${report.inProgressTasks}</td></tr>
        <tr><td>已阻塞</td><td>${report.blockedTasks}</td></tr>
        <tr><td>完成率</td><td>${report.completionPercentage.toFixed(1)}%</td></tr>
    </table>
`;
		}
		
		html += `
</body>
</html>`;
		
		return html;
	}

	private getReportTitle(reportType: ReportType): string {
		switch (reportType) {
			case ReportType.PROJECT_PROGRESS:
				return '项目进度报告';
			case ReportType.TIME_TRACKING:
				return '时间跟踪报告';
			case ReportType.TEAM_PERFORMANCE:
				return '团队绩效报告';
			default:
				return '项目报告';
		}
	}

	private generateReportFileName(config: ReportGenerationConfig): string {
		const date = new Date().toISOString().split('T')[0];
		const extension = config.format === ReportFormat.JSON ? 'json' : 
						 config.format === ReportFormat.CSV ? 'csv' :
						 config.format === ReportFormat.HTML ? 'html' : 'md';
		
		return `${config.reportType}_${config.projectId}_${date}.${extension}`;
	}

	private generateReportId(): string {
		return 'report_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
	}

	/**
	 * 初始化默认报告模板
	 */
	private initializeDefaultTemplates(): void {
		DEFAULT_REPORT_TEMPLATES.forEach(template => {
			this.reportTemplates.set(template.id, template);
		});
	}

	/**
	 * 获取所有可用的报告模板
	 */
	getAvailableTemplates(): ReportTemplate[] {
		return Array.from(this.reportTemplates.values());
	}

	/**
	 * 根据类型获取报告模板
	 */
	getTemplatesByType(type: ReportType): ReportTemplate[] {
		return Array.from(this.reportTemplates.values()).filter(template => template.type === type);
	}

	/**
	 * 获取指定的报告模板
	 */
	getTemplate(templateId: string): ReportTemplate | undefined {
		return this.reportTemplates.get(templateId);
	}

	/**
	 * 创建自定义报告模板
	 */
	createCustomTemplate(template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>): ReportTemplate {
		const newTemplate: ReportTemplate = {
			...template,
			id: this.generateTemplateId(),
			createdAt: new Date(),
			updatedAt: new Date()
		};

		this.reportTemplates.set(newTemplate.id, newTemplate);
		return newTemplate;
	}

	/**
	 * 更新报告模板
	 */
	updateTemplate(templateId: string, updates: Partial<ReportTemplate>): ReportTemplate | null {
		const template = this.reportTemplates.get(templateId);
		if (!template) {
			return null;
		}

		const updatedTemplate: ReportTemplate = {
			...template,
			...updates,
			id: templateId, // 确保ID不被修改
			updatedAt: new Date()
		};

		this.reportTemplates.set(templateId, updatedTemplate);
		return updatedTemplate;
	}

	/**
	 * 删除报告模板
	 */
	deleteTemplate(templateId: string): boolean {
		return this.reportTemplates.delete(templateId);
	}

	/**
	 * 使用模板生成报告
	 */
	async generateReportWithTemplate(config: ReportGenerationConfig): Promise<ReportGenerationResult> {
		try {
			// 如果指定了模板，使用模板配置
			if (config.template) {
				const template = this.getTemplate(config.template);
				if (!template) {
					throw new Error(`报告模板不存在: ${config.template}`);
				}

				// 使用模板配置覆盖部分配置
				const templateConfig: ReportGenerationConfig = {
					...config,
					reportType: template.type,
					filters: template.filters.length > 0 ? template.filters : config.filters
				};

				return await this.generateCustomReport(templateConfig, template);
			}

			// 否则使用标准报告生成
			return await this.generateAndExportReport(config);

		} catch (error) {
			console.error('使用模板生成报告失败:', error);
			
			const result: ReportGenerationResult = {
				success: false,
				reportId: this.generateReportId(),
				data: null,
				generatedAt: new Date(),
				error: error instanceof Error ? error.message : '未知错误'
			};

			new Notice(`报告生成失败: ${result.error}`);
			return result;
		}
	}

	/**
	 * 生成自定义报告
	 */
	private async generateCustomReport(config: ReportGenerationConfig, template: ReportTemplate): Promise<ReportGenerationResult> {
		// 首先生成基础报告数据
		let baseReportData: any;
		
		switch (template.type) {
			case ReportType.PROJECT_PROGRESS:
				baseReportData = await this.generateProjectProgressReport(config);
				break;
			case ReportType.TIME_TRACKING:
				baseReportData = await this.generateTimeTrackingReport(config);
				break;
			case ReportType.TEAM_PERFORMANCE:
				baseReportData = await this.generateTeamPerformanceReport(config);
				break;
			default:
				throw new Error(`不支持的报告类型: ${template.type}`);
		}

		// 根据模板配置处理数据
		const customReportData = this.processReportWithTemplate(baseReportData, template);

		// 导出报告
		const filePath = await this.exportCustomReport(customReportData, config, template);

		const result: ReportGenerationResult = {
			success: true,
			reportId: this.generateReportId(),
			filePath,
			data: customReportData,
			generatedAt: new Date()
		};

		new Notice(`自定义报告生成成功: ${filePath || '内存中'}`);
		return result;
	}

	/**
	 * 根据模板处理报告数据
	 */
	private processReportWithTemplate(reportData: any, template: ReportTemplate): any {
		const processedData = {
			...reportData,
			templateId: template.id,
			templateName: template.name,
			sections: []
		};

		// 根据模板的sections配置处理数据
		template.sections.forEach(section => {
			const sectionData = this.processSectionData(reportData, section);
			if (sectionData) {
				processedData.sections.push({
					...section,
					data: sectionData
				});
			}
		});

		return processedData;
	}

	/**
	 * 处理单个section的数据
	 */
	private processSectionData(reportData: any, section: ReportSection): any {
		switch (section.type) {
			case 'metrics':
				return this.extractMetrics(reportData, section.config.metrics || []);
			case 'chart':
				return this.prepareChartData(reportData, section.config);
			case 'table':
				return this.prepareTableData(reportData, section.config);
			case 'text':
				return this.generateTextContent(reportData, section.config);
			default:
				return null;
		}
	}

	/**
	 * 提取指标数据
	 */
	private extractMetrics(reportData: any, metricNames: string[]): any {
		const metrics: Record<string, any> = {};
		
		metricNames.forEach(metricName => {
			if (reportData.hasOwnProperty(metricName)) {
				metrics[metricName] = reportData[metricName];
			}
		});

		return metrics;
	}

	/**
	 * 准备图表数据
	 */
	private prepareChartData(reportData: any, config: any): any {
		const { chartType, dataSource } = config;
		
		if (!reportData[dataSource]) {
			return null;
		}

		return {
			type: chartType,
			data: reportData[dataSource],
			config: config
		};
	}

	/**
	 * 准备表格数据
	 */
	private prepareTableData(reportData: any, config: any): any {
		const { columns, dataSource } = config;
		
		// 如果没有指定数据源，尝试从报告数据中找到合适的数组数据
		let data = dataSource ? reportData[dataSource] : null;
		
		if (!data) {
			// 尝试找到第一个数组类型的数据
			for (const key in reportData) {
				if (Array.isArray(reportData[key]) && reportData[key].length > 0) {
					data = reportData[key];
					break;
				}
			}
		}

		if (!data || !Array.isArray(data)) {
			return null;
		}

		return {
			columns: columns || Object.keys(data[0] || {}),
			rows: data
		};
	}

	/**
	 * 生成文本内容
	 */
	private generateTextContent(reportData: any, config: any): any {
		const { template: textTemplate } = config;
		
		if (!textTemplate) {
			return '无文本模板配置';
		}

		// 简单的模板替换
		let content = textTemplate;
		Object.keys(reportData).forEach(key => {
			const placeholder = `{{${key}}}`;
			if (content.includes(placeholder)) {
				content = content.replace(new RegExp(placeholder, 'g'), String(reportData[key]));
			}
		});

		return content;
	}

	/**
	 * 导出自定义报告
	 */
	private async exportCustomReport(reportData: any, config: ReportGenerationConfig, template: ReportTemplate): Promise<string | undefined> {
		if (!config.outputPath) {
			return undefined;
		}

		const fileName = this.generateCustomReportFileName(config, template);
		const fullPath = `${config.outputPath}/${fileName}`;

		try {
			let content: string;
			
			switch (config.format) {
				case ReportFormat.JSON:
					content = JSON.stringify(reportData, null, 2);
					break;
				case ReportFormat.MARKDOWN:
					content = this.convertCustomReportToMarkdown(reportData, template);
					break;
				case ReportFormat.CSV:
					content = this.convertCustomReportToCSV(reportData, template);
					break;
				case ReportFormat.HTML:
					content = this.convertCustomReportToHTML(reportData, template);
					break;
				default:
					throw new Error(`不支持的导出格式: ${config.format}`);
			}

			await this.app.vault.create(fullPath, content);
			return fullPath;

		} catch (error) {
			console.error('自定义报告导出失败:', error);
			throw new Error(`自定义报告导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	}

	/**
	 * 生成自定义报告文件名
	 */
	private generateCustomReportFileName(config: ReportGenerationConfig, template: ReportTemplate): string {
		const date = new Date().toISOString().split('T')[0];
		const extension = config.format === ReportFormat.JSON ? 'json' : 
						 config.format === ReportFormat.CSV ? 'csv' :
						 config.format === ReportFormat.HTML ? 'html' : 'md';
		
		return `${template.id}_${config.projectId}_${date}.${extension}`;
	}

	/**
	 * 转换自定义报告为Markdown格式
	 */
	private convertCustomReportToMarkdown(reportData: any, template: ReportTemplate): string {
		let markdown = `# ${template.name}\n\n`;
		
		if (template.description) {
			markdown += `${template.description}\n\n`;
		}
		
		markdown += `生成时间: ${new Date().toLocaleString()}\n`;
		markdown += `项目: ${reportData.projectName}\n\n`;

		// 处理各个section
		reportData.sections?.forEach((section: any) => {
			markdown += `## ${section.title}\n\n`;
			
			switch (section.type) {
				case 'metrics':
					Object.entries(section.data).forEach(([key, value]) => {
						markdown += `- ${key}: ${value}\n`;
					});
					break;
				case 'table':
					if (section.data.rows && section.data.rows.length > 0) {
						// 生成表格头
						markdown += `| ${section.data.columns.join(' | ')} |\n`;
						markdown += `| ${section.data.columns.map(() => '---').join(' | ')} |\n`;
						
						// 生成表格行
						section.data.rows.forEach((row: any) => {
							const values = section.data.columns.map((col: string) => row[col] || '');
							markdown += `| ${values.join(' | ')} |\n`;
						});
					}
					break;
				case 'text':
					markdown += `${section.data}\n`;
					break;
			}
			
			markdown += '\n';
		});

		return markdown;
	}

	/**
	 * 转换自定义报告为CSV格式
	 */
	private convertCustomReportToCSV(reportData: any, template: ReportTemplate): string {
		let csv = `报告名称,${template.name}\n`;
		csv += `生成时间,${new Date().toLocaleString()}\n`;
		csv += `项目,${reportData.projectName}\n\n`;

		// 处理metrics类型的section
		reportData.sections?.forEach((section: any) => {
			if (section.type === 'metrics') {
				csv += `${section.title}\n`;
				Object.entries(section.data).forEach(([key, value]) => {
					csv += `${key},${value}\n`;
				});
				csv += '\n';
			}
		});

		return csv;
	}

	/**
	 * 转换自定义报告为HTML格式
	 */
	private convertCustomReportToHTML(reportData: any, template: ReportTemplate): string {
		let html = `<!DOCTYPE html>
<html>
<head>
    <title>${template.name}</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .metric-card { border: 1px solid #ddd; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>${template.name}</h1>`;

		if (template.description) {
			html += `<p>${template.description}</p>`;
		}

		html += `<p>生成时间: ${new Date().toLocaleString()}</p>`;
		html += `<p>项目: ${reportData.projectName}</p>`;

		// 处理各个section
		reportData.sections?.forEach((section: any) => {
			html += `<h2>${section.title}</h2>`;
			
			switch (section.type) {
				case 'metrics':
					html += '<div class="metrics">';
					Object.entries(section.data).forEach(([key, value]) => {
						html += `<div class="metric-card"><strong>${key}</strong><br>${value}</div>`;
					});
					html += '</div>';
					break;
				case 'table':
					if (section.data.rows && section.data.rows.length > 0) {
						html += '<table>';
						html += '<tr>' + section.data.columns.map((col: string) => `<th>${col}</th>`).join('') + '</tr>';
						section.data.rows.forEach((row: any) => {
							const values = section.data.columns.map((col: string) => row[col] || '');
							html += '<tr>' + values.map(val => `<td>${val}</td>`).join('') + '</tr>';
						});
						html += '</table>';
					}
					break;
				case 'text':
					html += `<p>${section.data}</p>`;
					break;
			}
		});

		html += '</body></html>';
		return html;
	}

	/**
	 * 生成模板ID
	 */
	private generateTemplateId(): string {
		return 'template_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
	}
}