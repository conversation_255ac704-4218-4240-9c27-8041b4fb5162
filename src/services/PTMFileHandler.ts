// PTM (Project Task Manager) file format handler

import { App, TFile, TFolder, Notice } from 'obsidian';
import { Project, Task, Sprint, Workflow } from '../models';

export interface PTMFileContent {
	version: string;
	metadata: PTMMetadata;
	project: Project;
	tasks: Task[];
	sprints: Sprint[];
	workflows: Workflow[];
	settings: PTMProjectSettings;
	createdAt: string;
	updatedAt: string;
}

export interface PTMMetadata {
	name: string;
	description?: string;
	author?: string;
	tags: string[];
	lastModifiedBy?: string;
}

export interface PTMProjectSettings {
	defaultWorkflow: string;
	enableTimeTracking: boolean;
	enableDependencies: boolean;
	autoArchiveCompleted: boolean;
	sprintDuration: number;
	workingDays: number[];
	customFields: Record<string, any>;
}

export class PTMFileHandler {
	private app: App;
	private fileWatchers: Map<string, () => void> = new Map();

	constructor(app: App) {
		this.app = app;
	}

	/**
	 * Create a new PTM file
	 */
	async createPTMFile(filePath: string, project: Project, initialData?: Partial<PTMFileContent>): Promise<TFile> {
		const ptmContent: PTMFileContent = {
			version: '1.0.0',
			metadata: {
				name: project.name,
				description: project.description,
				tags: project.tags,
				...initialData?.metadata
			},
			project,
			tasks: initialData?.tasks || [],
			sprints: initialData?.sprints || [],
			workflows: initialData?.workflows || [],
			settings: {
				defaultWorkflow: 'default',
				enableTimeTracking: true,
				enableDependencies: true,
				autoArchiveCompleted: false,
				sprintDuration: 14,
				workingDays: [1, 2, 3, 4, 5], // Monday to Friday
				customFields: {},
				...initialData?.settings
			},
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString()
		};

		const jsonContent = JSON.stringify(ptmContent, null, 2);
		
		// Add file header comment
		const fileContent = this.addFileHeader(jsonContent, ptmContent.metadata);

		try {
			const file = await this.app.vault.create(filePath, fileContent);
			new Notice(`PTM项目文件已创建: ${file.name}`);
			return file;
		} catch (error) {
			console.error('Error creating PTM file:', error);
			throw new Error(`创建PTM文件失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Read and parse PTM file
	 */
	async readPTMFile(filePath: string): Promise<PTMFileContent> {
		try {
			const file = this.app.vault.getAbstractFileByPath(filePath);
			if (!file || !(file instanceof TFile)) {
				throw new Error(`PTM文件不存在: ${filePath}`);
			}

			const content = await this.app.vault.read(file);
			const jsonContent = this.removeFileHeader(content);
			const ptmData = JSON.parse(jsonContent) as PTMFileContent;

			// Validate PTM file structure
			this.validatePTMContent(ptmData);

			return ptmData;
		} catch (error) {
			console.error('Error reading PTM file:', error);
			throw new Error(`读取PTM文件失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Update PTM file content
	 */
	async updatePTMFile(filePath: string, updates: Partial<PTMFileContent>): Promise<void> {
		try {
			const currentData = await this.readPTMFile(filePath);
			const updatedData: PTMFileContent = {
				...currentData,
				...updates,
				updatedAt: new Date().toISOString()
			};

			const jsonContent = JSON.stringify(updatedData, null, 2);
			const fileContent = this.addFileHeader(jsonContent, updatedData.metadata);

			const file = this.app.vault.getAbstractFileByPath(filePath);
			if (file instanceof TFile) {
				await this.app.vault.modify(file, fileContent);
				new Notice(`PTM项目文件已更新: ${file.name}`);
			}
		} catch (error) {
			console.error('Error updating PTM file:', error);
			throw new Error(`更新PTM文件失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Watch PTM file for changes
	 */
	watchPTMFile(filePath: string, callback: (content: PTMFileContent) => void): void {
		const watchCallback = async () => {
			try {
				const content = await this.readPTMFile(filePath);
				callback(content);
			} catch (error) {
				console.error('Error in PTM file watcher:', error);
			}
		};

		// Store the watcher for cleanup
		this.fileWatchers.set(filePath, watchCallback);

		// Register file modification event
		this.app.vault.on('modify', (file) => {
			if (file.path === filePath && file.path.endsWith('.ptm')) {
				watchCallback();
			}
		});
	}

	/**
	 * Stop watching PTM file
	 */
	unwatchPTMFile(filePath: string): void {
		this.fileWatchers.delete(filePath);
	}

	/**
	 * Create backup of PTM file
	 */
	async backupPTMFile(filePath: string): Promise<string> {
		try {
			const file = this.app.vault.getAbstractFileByPath(filePath);
			if (!file || !(file instanceof TFile)) {
				throw new Error(`PTM文件不存在: ${filePath}`);
			}

			const content = await this.app.vault.read(file);
			const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
			const backupPath = filePath.replace('.ptm', `_backup_${timestamp}.ptm`);

			await this.app.vault.create(backupPath, content);
			new Notice(`PTM文件备份已创建: ${backupPath}`);
			return backupPath;
		} catch (error) {
			console.error('Error backing up PTM file:', error);
			throw new Error(`备份PTM文件失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Restore PTM file from backup
	 */
	async restorePTMFile(originalPath: string, backupPath: string): Promise<void> {
		try {
			const backupFile = this.app.vault.getAbstractFileByPath(backupPath);
			if (!backupFile || !(backupFile instanceof TFile)) {
				throw new Error(`备份文件不存在: ${backupPath}`);
			}

			const backupContent = await this.app.vault.read(backupFile);
			
			// Check if original file exists
			const originalFile = this.app.vault.getAbstractFileByPath(originalPath);
			if (originalFile instanceof TFile) {
				await this.app.vault.modify(originalFile, backupContent);
			} else {
				await this.app.vault.create(originalPath, backupContent);
			}

			new Notice(`PTM文件已从备份恢复: ${originalPath}`);
		} catch (error) {
			console.error('Error restoring PTM file:', error);
			throw new Error(`恢复PTM文件失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * Get all PTM files in vault
	 */
	async getAllPTMFiles(): Promise<TFile[]> {
		const ptmFiles: TFile[] = [];
		
		const processFolder = (folder: TFolder) => {
			for (const child of folder.children) {
				if (child instanceof TFile && child.extension === 'ptm') {
					ptmFiles.push(child);
				} else if (child instanceof TFolder) {
					processFolder(child);
				}
			}
		};

		processFolder(this.app.vault.getRoot());
		return ptmFiles;
	}

	/**
	 * Validate PTM file content structure
	 */
	private validatePTMContent(content: PTMFileContent): void {
		const requiredFields = ['version', 'metadata', 'project', 'tasks', 'sprints', 'workflows', 'settings'];
		
		for (const field of requiredFields) {
			if (!(field in content)) {
				throw new Error(`PTM文件格式错误: 缺少必需字段 '${field}'`);
			}
		}

		// Validate version compatibility
		if (!content.version || !this.isVersionCompatible(content.version)) {
			throw new Error(`PTM文件版本不兼容: ${content.version}`);
		}

		// Validate project structure
		if (!content.project.id || !content.project.name) {
			throw new Error('PTM文件格式错误: 项目信息不完整');
		}
	}

	/**
	 * Check if PTM file version is compatible
	 */
	private isVersionCompatible(version: string): boolean {
		const supportedVersions = ['1.0.0'];
		return supportedVersions.includes(version);
	}

	/**
	 * Add file header with metadata
	 */
	private addFileHeader(jsonContent: string, metadata: PTMMetadata): string {
		const header = [
			'/*',
			' * PTM (Project Task Manager) File',
			` * Project: ${metadata.name}`,
			metadata.description ? ` * Description: ${metadata.description}` : '',
			metadata.author ? ` * Author: ${metadata.author}` : '',
			` * Created: ${new Date().toISOString()}`,
			' * ',
			' * This file contains project configuration and data.',
			' * Do not edit manually unless you know what you are doing.',
			' */',
			''
		].filter(line => line !== '').join('\n');

		return header + jsonContent;
	}

	/**
	 * Remove file header to get pure JSON
	 */
	private removeFileHeader(content: string): string {
		const lines = content.split('\n');
		let jsonStartIndex = 0;

		// Find the end of the header comment
		for (let i = 0; i < lines.length; i++) {
			if (lines[i].trim() === '*/') {
				jsonStartIndex = i + 1;
				break;
			}
		}

		// Skip empty lines after header
		while (jsonStartIndex < lines.length && lines[jsonStartIndex].trim() === '') {
			jsonStartIndex++;
		}

		return lines.slice(jsonStartIndex).join('\n');
	}

	/**
	 * Cleanup watchers
	 */
	cleanup(): void {
		this.fileWatchers.clear();
	}
}