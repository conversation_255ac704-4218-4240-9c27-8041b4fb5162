// 甘特图管理服务

import { Task, TaskStatus } from '../models/Task';
import { 
	GanttTask, 
	GanttUtils, 
	CriticalPathAnalysis, 
	ResourceConflict,
	GanttViewConfig,
	GanttViewMode 
} from '../models/Gantt';
import { TaskManager } from './TaskManager';
import { ProjectManager } from './ProjectManager';

export class GanttManager {
	private taskManager: TaskManager;
	private projectManager: ProjectManager;
	private viewConfig: Partial<GanttViewConfig>;

	constructor(taskManager: TaskManager, projectManager: ProjectManager) {
		this.taskManager = taskManager;
		this.projectManager = projectManager;
		this.viewConfig = GanttUtils.getDefaultConfig();
	}

	/**
	 * 获取项目的甘特图数据
	 */
	async getProjectGanttData(projectId: string): Promise<GanttTask[]> {
		try {
			const tasks = await this.taskManager.getTasksByProject(projectId);
			return GanttUtils.tasksToGanttTasks(tasks);
		} catch (error) {
			console.error('获取项目甘特图数据失败:', error);
			throw new Error('无法加载甘特图数据');
		}
	}

	/**
	 * 更新任务时间
	 */
	async updateTaskTiming(
		taskId: string, 
		startDate: Date, 
		endDate: Date
	): Promise<void> {
		try {
			const task = await this.taskManager.getTaskById(taskId);
			if (!task) {
				throw new Error('任务不存在');
			}

			// 更新任务时间
			await this.taskManager.updateTask(taskId, {
				startDate,
				dueDate: endDate
			});

			// 检查并更新依赖任务
			await this.updateDependentTasks(taskId, endDate);
		} catch (error) {
			console.error('更新任务时间失败:', error);
			throw error;
		}
	}

	/**
	 * 更新任务进度
	 */
	async updateTaskProgress(taskId: string, progress: number): Promise<void> {
		try {
			const task = await this.taskManager.getTaskById(taskId);
			if (!task) {
				throw new Error('任务不存在');
			}

			// 根据进度更新任务状态
			let newStatus = task.status;
			if (progress === 0) {
				newStatus = TaskStatus.TODO;
			} else if (progress === 100) {
				newStatus = TaskStatus.COMPLETED;
			} else if (progress > 0 && task.status === TaskStatus.TODO) {
				newStatus = TaskStatus.IN_PROGRESS;
			}

			await this.taskManager.updateTask(taskId, {
				status: newStatus
			});
		} catch (error) {
			console.error('更新任务进度失败:', error);
			throw error;
		}
	}

	/**
	 * 添加任务依赖关系
	 */
	async addTaskDependency(
		taskId: string, 
		dependencyTaskId: string, 
		dependencyType: string = 'fs'
	): Promise<void> {
		try {
			const task = await this.taskManager.getTaskById(taskId);
			if (!task) {
				throw new Error('任务不存在');
			}

			// 检查循环依赖
			if (await this.hasCyclicDependency(taskId, dependencyTaskId)) {
				throw new Error('不能创建循环依赖');
			}

			// 添加依赖关系
			await this.taskManager.addDependency(taskId, dependencyTaskId, dependencyType as any);

			// 自动调整任务时间
			await this.adjustTaskTimingForDependency(taskId, dependencyTaskId);
		} catch (error) {
			console.error('添加任务依赖失败:', error);
			throw error;
		}
	}

	/**
	 * 移除任务依赖关系
	 */
	async removeTaskDependency(taskId: string, dependencyTaskId: string): Promise<void> {
		try {
			await this.taskManager.removeDependency(taskId, dependencyTaskId);
		} catch (error) {
			console.error('移除任务依赖失败:', error);
			throw error;
		}
	}

	/**
	 * 计算关键路径
	 */
	async calculateCriticalPath(projectId: string): Promise<CriticalPathAnalysis> {
		try {
			const tasks = await this.taskManager.getTasksByProject(projectId);
			return GanttUtils.calculateCriticalPath(tasks);
		} catch (error) {
			console.error('计算关键路径失败:', error);
			throw error;
		}
	}

	/**
	 * 检测资源冲突
	 */
	async detectResourceConflicts(projectId: string): Promise<ResourceConflict[]> {
		try {
			const tasks = await this.taskManager.getTasksByProject(projectId);
			return GanttUtils.detectResourceConflicts(tasks);
		} catch (error) {
			console.error('检测资源冲突失败:', error);
			throw error;
		}
	}

	/**
	 * 获取甘特图视图配置
	 */
	getViewConfig(): Partial<GanttViewConfig> {
		return { ...this.viewConfig };
	}

	/**
	 * 更新甘特图视图配置
	 */
	updateViewConfig(config: Partial<GanttViewConfig>): void {
		this.viewConfig = { ...this.viewConfig, ...config };
	}

	/**
	 * 切换视图模式
	 */
	setViewMode(mode: GanttViewMode): void {
		this.viewConfig.viewMode = mode;
	}

	/**
	 * 导出甘特图数据
	 */
	async exportGanttData(projectId: string, format: 'json' | 'csv' = 'json'): Promise<string> {
		try {
			const ganttTasks = await this.getProjectGanttData(projectId);
			
			if (format === 'json') {
				return JSON.stringify(ganttTasks, null, 2);
			} else {
				// CSV格式导出
				const headers = ['ID', '名称', '开始时间', '结束时间', '进度', '类型', '依赖'];
				const rows = ganttTasks.map(task => [
					task.id,
					task.name,
					task.start.toISOString().split('T')[0],
					task.end.toISOString().split('T')[0],
					`${task.progress}%`,
					task.type,
					(task.dependencies || []).join(';')
				]);
				
				return [headers, ...rows].map(row => row.join(',')).join('\n');
			}
		} catch (error) {
			console.error('导出甘特图数据失败:', error);
			throw error;
		}
	}

	// 私有方法

	/**
	 * 检查循环依赖
	 */
	private async hasCyclicDependency(taskId: string, dependencyTaskId: string): Promise<boolean> {
		const visited = new Set<string>();
		const recursionStack = new Set<string>();

		const hasCycle = async (currentTaskId: string): Promise<boolean> => {
			if (recursionStack.has(currentTaskId)) {
				return true; // 发现循环
			}
			if (visited.has(currentTaskId)) {
				return false;
			}

			visited.add(currentTaskId);
			recursionStack.add(currentTaskId);

			const task = await this.taskManager.getTaskById(currentTaskId);
			if (task) {
				for (const dep of task.dependencies) {
					if (await hasCycle(dep.taskId)) {
						return true;
					}
				}
			}

			recursionStack.delete(currentTaskId);
			return false;
		};

		// 检查从dependencyTaskId开始是否能回到taskId
		return await hasCycle(dependencyTaskId);
	}

	/**
	 * 更新依赖任务的时间
	 */
	private async updateDependentTasks(taskId: string, endDate: Date): Promise<void> {
		try {
			const allTasks = await this.taskManager.getAllTasks();
			const dependentTasks = allTasks.filter((task: any) => 
				task.dependencies.some((dep: any) => dep.taskId === taskId)
			);

			for (const dependentTask of dependentTasks) {
				const dependency = dependentTask.dependencies.find((dep: any) => dep.taskId === taskId);
				if (dependency && dependency.type === 'fs') { // Finish-to-Start
					// 依赖任务应该在当前任务结束后开始
					const newStartDate = new Date(endDate.getTime() + 24 * 60 * 60 * 1000); // 下一天开始
					
					if (!dependentTask.startDate || dependentTask.startDate < newStartDate) {
						const duration = dependentTask.dueDate && dependentTask.startDate ? 
							dependentTask.dueDate.getTime() - dependentTask.startDate.getTime() : 
							24 * 60 * 60 * 1000; // 默认1天

						await this.taskManager.updateTask(dependentTask.id, {
							startDate: newStartDate,
							dueDate: new Date(newStartDate.getTime() + duration)
						});
					}
				}
			}
		} catch (error) {
			console.error('更新依赖任务失败:', error);
		}
	}

	/**
	 * 根据依赖关系调整任务时间
	 */
	private async adjustTaskTimingForDependency(taskId: string, dependencyTaskId: string): Promise<void> {
		try {
			const task = await this.taskManager.getTaskById(taskId);
			const dependencyTask = await this.taskManager.getTaskById(dependencyTaskId);

			if (!task || !dependencyTask) {
				return;
			}

			// 如果依赖任务有结束时间，调整当前任务的开始时间
			if (dependencyTask.dueDate) {
				const newStartDate = new Date(dependencyTask.dueDate.getTime() + 24 * 60 * 60 * 1000);
				
				if (!task.startDate || task.startDate < newStartDate) {
					const duration = task.dueDate && task.startDate ? 
						task.dueDate.getTime() - task.startDate.getTime() : 
						24 * 60 * 60 * 1000;

					await this.taskManager.updateTask(taskId, {
						startDate: newStartDate,
						dueDate: new Date(newStartDate.getTime() + duration)
					});
				}
			}
		} catch (error) {
			console.error('调整任务时间失败:', error);
		}
	}
}