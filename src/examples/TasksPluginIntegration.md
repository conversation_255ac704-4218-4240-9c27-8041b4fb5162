# Tasks Plugin Integration Examples

This document shows how the Project Task Manager integrates with the popular Tasks plugin for Obsidian.

## Supported Task Formats

### Basic Task Status

```markdown
- [ ] Todo task
- [x] Completed task
- [/] In progress task
- [-] Cancelled task
- [>] Forwarded/In progress task
- [<] Blocked task
- [?] Under review task
```

### Priority Indicators

```markdown
- [ ] ⏫ Critical priority task
- [ ] 🔼 High priority task
- [ ] 🔽 Low priority task
- [ ] 🔥 Urgent task
- [ ] ⭐ Important task
```

### Date Formats

```markdown
- [ ] Task with due date 📅 2024-12-31
- [ ] Task with scheduled date 🗓️2024-12-31
- [x] Completed task ✅ 2024-12-31
```

### Tags and Global Task Marker

```markdown
- [ ] Task with project tags #work #important
- [ ] Task synced to PTM #task
- [ ] Project task #task #project-alpha
```

## Integration Features

### 1. Automatic Detection

The bridge automatically detects tasks that should be managed by PTM:

- Tasks with the `#task` global tag
- Tasks in files with project context
- Tasks that match configured patterns

### 2. Bidirectional Sync

Changes in either system are synchronized:

**Markdown → PTM:**
- Status changes in markdown files update PTM tasks
- New tasks with `#task` are added to PTM
- Priority and date changes are synchronized

**PTM → Markdown:**
- PTM task updates modify the original markdown
- Status changes update the checkbox character
- Maintains Tasks plugin formatting

### 3. Status Mapping

| Tasks Plugin | PTM Status | Description |
|--------------|------------|-------------|
| `[ ]` | TODO | Not started |
| `[x]` | COMPLETED | Finished |
| `[/]` | IN_PROGRESS | Being worked on |
| `[-]` | CANCELLED | Cancelled |
| `[>]` | IN_PROGRESS | Forwarded/Started |
| `[<]` | BLOCKED | Blocked |
| `[?]` | REVIEW | Under review |

### 4. Priority Mapping

| Tasks Plugin | PTM Priority | Description |
|--------------|--------------|-------------|
| ⏫ 🔥 | CRITICAL | Highest priority |
| 🔼 ⭐ | HIGH | High priority |
| (none) | MEDIUM | Default priority |
| 🔽 | LOW | Low priority |

## Configuration Options

```typescript
const bridgeOptions = {
  enableSync: true,           // Enable bidirectional sync
  syncInterval: 5000,         // Sync every 5 seconds
  autoDetectTasks: true,      // Auto-detect new tasks
  preserveTasksPluginFormat: true, // Keep original formatting
  defaultProjectId: 'inbox'  // Default project for orphaned tasks
};
```

## Example Workflows

### 1. Daily Note Integration

```markdown
# Daily Note - 2024-01-15

## Tasks for Today

- [ ] Review project proposal #task #work
- [ ] ⏫ Fix critical bug #task #development 📅 2024-01-15
- [/] Write documentation #task #writing
- [x] Team standup meeting ✅ 2024-01-15

## Regular Notes (not synced)

- [ ] Buy groceries
- [ ] Call dentist
```

### 2. Project-Specific Tasks

```markdown
# Project Alpha Planning

## Sprint Tasks

- [ ] 🔼 Design user interface #task #project-alpha #design
- [ ] Implement authentication #task #project-alpha #backend
- [ ] Write unit tests #task #project-alpha #testing 📅 2024-01-20
- [/] Set up CI/CD pipeline #task #project-alpha #devops

## Meeting Notes

- Regular meeting notes without #task tag remain local
- Only tasks marked with #task are synced to PTM
```

### 3. Hierarchical Tasks

```markdown
# Feature Development

- [ ] 🔼 Implement user management feature #task #feature-dev
  - [ ] Design database schema #task #feature-dev
  - [ ] Create API endpoints #task #feature-dev
  - [ ] Build frontend components #task #feature-dev
  - [ ] Write integration tests #task #feature-dev
```

## Conflict Resolution

When conflicts occur between Tasks plugin and PTM:

1. **Last Modified Wins**: The most recently modified version takes precedence
2. **Manual Resolution**: Users can manually resolve conflicts through PTM interface
3. **Backup Creation**: Automatic backups before major sync operations

## Best Practices

### 1. Use Global Tags Consistently

```markdown
✅ Good: - [ ] Important task #task #project
❌ Avoid: - [ ] Important task #project (missing #task)
```

### 2. Maintain Task Context

```markdown
✅ Good: - [ ] Fix login bug in user module #task #bugfix
❌ Avoid: - [ ] Fix bug #task (too vague)
```

### 3. Use Appropriate Priorities

```markdown
✅ Good: - [ ] ⏫ Security vulnerability fix #task
✅ Good: - [ ] 🔽 Update documentation #task
❌ Avoid: - [ ] ⏫ Update README #task (wrong priority)
```

### 4. Include Due Dates When Relevant

```markdown
✅ Good: - [ ] Submit report #task 📅 2024-01-31
✅ Good: - [ ] Ongoing maintenance #task (no due date needed)
```

## Troubleshooting

### Common Issues

1. **Tasks Not Syncing**
   - Check for `#task` global tag
   - Verify Tasks plugin is enabled
   - Check sync settings in PTM

2. **Formatting Issues**
   - Ensure proper markdown task syntax
   - Check for special characters in task titles
   - Verify date formats are correct

3. **Performance Issues**
   - Reduce sync frequency for large vaults
   - Use selective sync with specific folders
   - Clear cache if sync becomes slow

### Debug Information

Access debug information through PTM settings:

```typescript
const status = ptmManager.getTasksPluginBridge().getCompatibilityStatus();
console.log('Tasks Plugin Detected:', status.tasksPluginDetected);
console.log('Sync Enabled:', status.syncEnabled);
console.log('Last Sync:', new Date(status.lastSyncTime));
```