// Obsidian API mocks for testing

export class Notice {
  constructor(message: string) {
    // Mock implementation
  }
}

export class App {
  vault = {
    adapter: {
      exists: jest.fn(),
      read: jest.fn(),
      write: jest.fn(),
      mkdir: jest.fn()
    }
  };
}

export class Plugin {
  app: App;
  
  constructor(app: App) {
    this.app = app;
  }
  
  onload() {}
  onunload() {}
}

export class TFile {
  path: string;
  name: string;
  
  constructor(path: string) {
    this.path = path;
    this.name = path.split('/').pop() || '';
  }
}

export class TFolder {
  path: string;
  name: string;
  
  constructor(path: string) {
    this.path = path;
    this.name = path.split('/').pop() || '';
  }
}