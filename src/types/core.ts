/**
 * 核心类型定义文件
 * 统一管理所有核心接口和类型定义，解决类型不一致问题
 */

// ============================================================================
// 基础枚举定义
// ============================================================================

/**
 * 任务状态枚举 - 统一定义
 */
export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in-progress',
  BLOCKED = 'blocked',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

/**
 * 任务优先级枚举 - 统一定义（修复URGENT不存在的问题）
 */
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'  // 替代URGENT，保持一致性
}

/**
 * 项目状态枚举
 */
export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on-hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

/**
 * Sprint状态枚举
 */
export enum SprintStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// ============================================================================
// 核心接口定义
// ============================================================================

/**
 * 基础实体接口
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 任务接口 - 统一定义
 */
export interface Task extends BaseEntity {
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  projectId: string;
  parentTaskId?: string;
  assigneeId?: string;
  dueDate?: Date;
  startDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  linkedNotes?: string[];
  dependencies?: string[];
  completedAt?: Date;
  sprintId?: string;
}

/**
 * 项目接口 - 统一定义
 */
export interface Project extends BaseEntity {
  name: string;
  description?: string;
  status: ProjectStatus;
  ownerId?: string;
  startDate?: Date;
  endDate?: Date;
  tags?: string[];
  settings?: ProjectSettings;
}

/**
 * 项目设置接口
 */
export interface ProjectSettings {
  defaultTaskStatus: TaskStatus;
  allowedStatuses: TaskStatus[];
  workflowId?: string;
  enableTimeTracking: boolean;
  enableDependencies: boolean;
  autoArchiveCompleted: boolean;
}

/**
 * Sprint接口 - 统一定义
 */
export interface Sprint extends BaseEntity {
  name: string;
  description?: string;
  projectId: string;
  status: SprintStatus;
  startDate: Date;
  endDate: Date;
  goal?: string;
  capacity?: number;
  velocity?: number;
}

// ============================================================================
// 笔记任务引用系统
// ============================================================================

/**
 * 笔记任务引用接口 - 解决类型混用问题
 */
export interface NoteTaskReference {
  taskId: string;
  noteId: string;
  notePath: string;
  lineNumber?: number;
  context?: string;
  lastSyncAt: Date;
}

/**
 * 笔记任务引用映射类型
 */
export type NoteTaskReferenceMap = Map<string, NoteTaskReference[]>;

// ============================================================================
// 数据管理器接口
// ============================================================================

/**
 * 类型安全的数据管理器接口 - 解决load/save方法缺失问题
 */
export interface TypeSafeDataManager {
  /**
   * 加载数据
   */
  load<T>(key: string): Promise<T | null>;
  
  /**
   * 保存数据
   */
  save<T>(key: string, data: T): Promise<void>;
  
  /**
   * 删除数据
   */
  delete(key: string): Promise<boolean>;
  
  /**
   * 检查数据是否存在
   */
  exists(key: string): Promise<boolean>;
  
  /**
   * 清空所有数据
   */
  clear(): Promise<void>;
  
  /**
   * 获取所有键
   */
  keys(): Promise<string[]>;
}

// ============================================================================
// 服务响应接口
// ============================================================================

/**
 * 服务错误接口
 */
export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
  timestamp: number;
}

/**
 * 服务响应接口 - 统一响应格式
 */
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  metadata?: {
    timestamp: number;
    operation: string;
    duration: number;
    version?: string;
  };
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// Repository接口定义
// ============================================================================

/**
 * 基础Repository接口
 */
export interface BaseRepository<T extends BaseEntity> {
  findAll(): Promise<T[]>;
  findById(id: string): Promise<T | null>;
  create(entity: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, updates: Partial<T>): Promise<T>;
  delete(id: string): Promise<boolean>;
}

/**
 * 任务Repository接口
 */
export interface TaskRepository extends BaseRepository<Task> {
  findByProjectId(projectId: string): Promise<Task[]>;
  findByStatus(status: TaskStatus): Promise<Task[]>;
  findByPriority(priority: Priority): Promise<Task[]>;
  findByAssignee(assigneeId: string): Promise<Task[]>;
  findOverdue(): Promise<Task[]>;
  findBySprintId(sprintId: string): Promise<Task[]>;
}

/**
 * 项目Repository接口
 */
export interface ProjectRepository extends BaseRepository<Project> {
  findByStatus(status: ProjectStatus): Promise<Project[]>;
  findByOwner(ownerId: string): Promise<Project[]>;
  findByTag(tag: string): Promise<Project[]>;
}

/**
 * SprintRepository接口
 */
export interface SprintRepository extends BaseRepository<Sprint> {
  findByProjectId(projectId: string): Promise<Sprint[]>;
  findByStatus(status: SprintStatus): Promise<Sprint[]>;
  findActive(): Promise<Sprint[]>;
  findByDateRange(startDate: Date, endDate: Date): Promise<Sprint[]>;
}

// ============================================================================
// UI组件接口定义
// ============================================================================

/**
 * 标准按钮属性接口 - 解决属性不匹配问题
 */
export interface StandardButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';  // 统一使用完整单词
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  style?: React.CSSProperties;
}

/**
 * 卡片组件属性接口
 */
export interface StandardCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
}

/**
 * 布局组件属性接口
 */
export interface FlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'column';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
  gap?: 'none' | 'sm' | 'md' | 'lg';
  wrap?: boolean;
  className?: string;
}

// ============================================================================
// 统计和分析接口
// ============================================================================

/**
 * 任务统计接口
 */
export interface TaskStats {
  total: number;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<Priority, number>;
  overdue: number;
  completedThisWeek: number;
  completedThisMonth: number;
}

/**
 * 项目统计接口
 */
export interface ProjectStats {
  total: number;
  byStatus: Record<ProjectStatus, number>;
  averageCompletionTime: number;
  totalTasks: number;
  completedTasks: number;
  completionRate: number;
}

/**
 * 独立任务模式统计接口
 */
export interface TaskModeStats {
  totalTasks: number;
  tasksByStatus: Record<TaskStatus, number>;
  tasksByPriority: Record<Priority, number>;
  overdueTasks: number;
  completedToday: number;
  completedThisWeek: number;
  averageCompletionTime: number;
}

// ============================================================================
// 缓存接口定义
// ============================================================================

/**
 * 缓存条目接口
 */
export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * 缓存管理器接口
 */
export interface CacheManager {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, data: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(): Promise<void>;
  size(): number;
  getCacheKeys(): string[];
  stats(): CacheStats;
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  oldestEntry: number;
  newestEntry: number;
}

// ============================================================================
// 类型守卫和工具函数
// ============================================================================

/**
 * 检查是否为有效的任务状态
 */
export function isValidTaskStatus(status: any): status is TaskStatus {
  return Object.values(TaskStatus).includes(status);
}

/**
 * 检查是否为有效的优先级
 */
export function isValidPriority(priority: any): priority is Priority {
  return Object.values(Priority).includes(priority);
}

/**
 * 检查是否为有效的项目状态
 */
export function isValidProjectStatus(status: any): status is ProjectStatus {
  return Object.values(ProjectStatus).includes(status);
}

/**
 * 检查是否为基础实体
 */
export function isBaseEntity(obj: any): obj is BaseEntity {
  return obj && 
         typeof obj.id === 'string' && 
         obj.createdAt instanceof Date && 
         obj.updatedAt instanceof Date;
}

/**
 * 检查是否为任务对象
 */
export function isTask(obj: any): obj is Task {
  return isBaseEntity(obj) &&
         typeof (obj as any).title === 'string' &&
         isValidTaskStatus((obj as any).status) &&
         isValidPriority((obj as any).priority) &&
         typeof (obj as any).projectId === 'string';
}

/**
 * 检查是否为项目对象
 */
export function isProject(obj: any): obj is Project {
  return isBaseEntity(obj) &&
         typeof (obj as any).name === 'string' &&
         isValidProjectStatus((obj as any).status);
}

/**
 * 检查是否为笔记任务引用
 */
export function isNoteTaskReference(obj: any): obj is NoteTaskReference {
  return obj !== null &&
         obj !== undefined &&
         typeof obj === 'object' &&
         typeof obj.taskId === 'string' &&
         typeof obj.noteId === 'string' &&
         typeof obj.notePath === 'string' &&
         obj.lastSyncAt instanceof Date;
}

// ============================================================================
// 类型转换工具
// ============================================================================

/**
 * 类型转换器类
 */
export class TypeConverter {
  /**
   * 将字符串转换为任务状态
   */
  static stringToTaskStatus(status: string): TaskStatus | null {
    return isValidTaskStatus(status) ? status : null;
  }
  
  /**
   * 将字符串转换为优先级
   */
  static stringToPriority(priority: string): Priority | null {
    return isValidPriority(priority) ? priority : null;
  }
  
  /**
   * 将URGENT转换为CRITICAL（向后兼容）
   */
  static migratePriority(priority: string): Priority {
    if (priority === 'urgent' || priority === 'URGENT') {
      return Priority.CRITICAL;
    }
    return isValidPriority(priority) ? priority : Priority.MEDIUM;
  }
  
  /**
   * 将字符串数组转换为笔记任务引用数组
   */
  static stringsToNoteTaskReferences(
    taskIds: string[], 
    noteId: string, 
    notePath: string
  ): NoteTaskReference[] {
    return taskIds.map(taskId => ({
      taskId,
      noteId,
      notePath,
      lastSyncAt: new Date()
    }));
  }
  
  /**
   * 将笔记任务引用数组转换为字符串数组
   */
  static noteTaskReferencesToStrings(references: NoteTaskReference[]): string[] {
    return references.map(ref => ref.taskId);
  }
  
  /**
   * 安全的JSON解析
   */
  static safeJsonParse<T>(json: string, fallback: T): T {
    try {
      return JSON.parse(json) as T;
    } catch {
      return fallback;
    }
  }
  
  /**
   * 安全的日期转换
   */
  static safeDate(value: any): Date | null {
    if (value instanceof Date) return value;
    if (typeof value === 'string' || typeof value === 'number') {
      const date = new Date(value);
      return isNaN(date.getTime()) ? null : date;
    }
    return null;
  }
}

// ============================================================================
// 错误类定义
// ============================================================================

/**
 * 类型安全错误
 */
export class TypeSafetyError extends Error {
  constructor(message: string, public readonly expectedType: string, public readonly actualValue: any) {
    super(`Type safety error: ${message}. Expected ${expectedType}, got ${typeof actualValue}`);
    this.name = 'TypeSafetyError';
  }
}

/**
 * 验证错误
 */
export class ValidationError extends Error {
  constructor(message: string, public readonly field: string, public readonly value: any) {
    super(`Validation error: ${message} for field '${field}' with value '${value}'`);
    this.name = 'ValidationError';
  }
}

/**
 * 数据不一致错误
 */
export class DataConsistencyError extends Error {
  constructor(message: string, public readonly details: any) {
    super(`Data consistency error: ${message}`);
    this.name = 'DataConsistencyError';
    this.details = details;
  }
}

// ============================================================================
// 导出所有类型和工具
// ============================================================================

// 默认导出常用工具
export default {
  TypeConverter,
  isValidTaskStatus,
  isValidPriority,
  isValidProjectStatus,
  isTask,
  isProject,
  isNoteTaskReference,
  TypeSafetyError,
  ValidationError,
  DataConsistencyError
};