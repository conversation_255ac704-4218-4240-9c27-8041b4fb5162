// Main React application component

import React, { useEffect, useState } from 'react';
import { App as ObsidianApp } from 'obsidian';
import { PTMManager } from '../services/PTMManager';
import { ThemeManager } from './utils/theme';
import { styleManager } from './utils/styleManager';
import { Container, Stack } from './components/layout/Layout';
import { Button } from './components/common/Button';
import { Card, CardHeader, CardContent } from './components/common/Card';
import { ComponentShowcase } from './components/examples/ComponentShowcase';
import { StyleSystemTest } from './components/examples/StyleSystemTest';
import { ProjectDashboard } from './components/dashboard/ProjectDashboard';
import { KanbanTaskCardTest } from './components/kanban/KanbanTaskCardTest';
import { HomepageDashboard } from './components/homepage/HomepageDashboard';

export interface AppProps {
	app: ObsidianApp;
	ptmManager: PTMManager;
}

export const App: React.FC<AppProps> = ({ app, ptmManager }) => {
	const [themeManager, setThemeManager] = useState<ThemeManager | null>(null);
	const [isInitialized, setIsInitialized] = useState(false);
	const [currentView, setCurrentView] = useState<'homepage' | 'overview' | 'dashboard' | 'showcase' | 'kanban-test' | 'style-test'>('homepage');

	useEffect(() => {
		const initializeApp = async () => {
			try {
				// Initialize style manager first
				await styleManager.initialize();
				
				// Initialize theme manager
				const theme = new ThemeManager(app);
				theme.applyTheme();
				setThemeManager(theme);

				// Initialize PTM manager if not already done
				if (!isInitialized) {
					await ptmManager.initialize();
					setIsInitialized(true);
				}
			} catch (error) {
				console.error('Failed to initialize PTM App:', error);
			}
		};

		initializeApp();

		return () => {
			// Cleanup on unmount
			if (themeManager) {
				// Theme manager cleanup would go here if needed
			}
		};
	}, [app, ptmManager, isInitialized, themeManager]);

	if (!isInitialized) {
		return (
			<Container padding>
				<div className="ptm-loading">
					<div className="ptm-spinner">
						<svg viewBox="0 0 24 24">
							<circle
								className="ptm-spinner__circle"
								cx="12"
								cy="12"
								r="10"
								fill="none"
								strokeWidth="2"
							/>
						</svg>
					</div>
					<p>Initializing Project Task Manager...</p>
				</div>
			</Container>
		);
	}

	// 处理导航
	const handleNavigate = (view: string, params?: any) => {
		console.log('导航到:', view, params);
		// 这里可以根据需要实现具体的导航逻辑
	};

	return (
		<div className="ptm-app">
			{currentView === 'homepage' ? (
				<HomepageDashboard 
					app={app}
					ptmManager={ptmManager}
					onNavigate={handleNavigate}
				/>
			) : currentView === 'overview' ? (
				<Container padding>
					<Stack spacing="lg">
						<Card variant="elevated">
							<CardHeader>
								<h1>Project Task Manager</h1>
								<p>Welcome to the Project Task Manager plugin!</p>
							</CardHeader>
							<CardContent>
								<Stack spacing="md">
									<div>
										<p><strong>Theme:</strong> {themeManager?.isDark() ? 'Dark' : 'Light'}</p>
										<p><strong>Status:</strong> {isInitialized ? 'Initialized' : 'Loading...'}</p>
									</div>
									
									<div>
										<h3>Implementation Progress</h3>
										<ul>
											<li>✅ React + TypeScript environment setup</li>
											<li>✅ Base UI components (Button, Card, Layout)</li>
											<li>✅ Obsidian theme integration</li>
											<li>✅ Responsive design support</li>
											<li>⏳ Project Dashboard (Task 10)</li>
											<li>⏳ Task List View (Task 11)</li>
											<li>⏳ Kanban Board (Task 12)</li>
										</ul>
									</div>
									
									<div>
										<h3>Available Actions</h3>
										<Stack spacing="sm">
											<Button 
												variant="primary"
												onClick={() => setCurrentView('homepage')}
											>
												Open Homepage Dashboard
											</Button>
											<Button 
												variant="secondary"
												onClick={() => setCurrentView('dashboard')}
											>
												Open Project Dashboard
											</Button>
											<Button 
												variant="secondary"
												onClick={() => setCurrentView('showcase')}
											>
												View Component Showcase
											</Button>
											<Button 
												variant="secondary"
												onClick={() => setCurrentView('kanban-test')}
											>
												Test Kanban Task Cards
											</Button>
											<Button 
												variant="secondary"
												onClick={() => setCurrentView('style-test')}
											>
												Test Style System
											</Button>
											<Button 
												variant="ghost"
												onClick={() => {
													console.log('PTM Manager Health:', ptmManager.getHealthStatus());
												}}
											>
												Check System Health
											</Button>
										</Stack>
									</div>
								</Stack>
							</CardContent>
						</Card>
					</Stack>
				</Container>
			) : currentView === 'dashboard' ? (
				<div>
					<Container padding>
						<Stack spacing="md">
							<Button 
								variant="ghost"
								onClick={() => setCurrentView('overview')}
							>
								← Back to Overview
							</Button>
						</Stack>
					</Container>
					<ProjectDashboard 
						ptmManager={ptmManager}
						app={app}
						onProjectSelect={(projectId) => {
							console.log('Selected project:', projectId);
						}}
						onTaskSelect={(taskId) => {
							console.log('Selected task:', taskId);
						}}
					/>
				</div>
			) : currentView === 'showcase' ? (
				<div>
					<Container padding>
						<Stack spacing="md">
							<Button 
								variant="ghost"
								onClick={() => setCurrentView('overview')}
							>
								← Back to Overview
							</Button>
						</Stack>
					</Container>
					<ComponentShowcase />
				</div>
			) : currentView === 'kanban-test' ? (
				<div>
					<Container padding>
						<Stack spacing="md">
							<Button 
								variant="ghost"
								onClick={() => setCurrentView('overview')}
							>
								← Back to Overview
							</Button>
						</Stack>
					</Container>
					<KanbanTaskCardTest />
				</div>
			) : currentView === 'style-test' ? (
				<div>
					<Container padding>
						<Stack spacing="md">
							<Button 
								variant="ghost"
								onClick={() => setCurrentView('overview')}
							>
								← Back to Overview
							</Button>
						</Stack>
					</Container>
					<StyleSystemTest />
				</div>
			) : null}
		</div>
	);
};