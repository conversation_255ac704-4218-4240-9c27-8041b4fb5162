/**
 * 组件属性验证工具
 * 提供运行时的属性类型检查和验证
 */

import {
  StandardSize,
  StandardVariant,
  StandardColor,
  StandardSpacing,
  isStandardSize,
  isStandardVariant,
  isStandardColor,
  isStandardSpacing,
} from '../types/components';

// ============================================================================
// 验证错误类型
// ============================================================================

export interface ValidationError {
  property: string;
  value: any;
  expectedType: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// ============================================================================
// 基础验证函数
// ============================================================================

/** 创建验证错误 */
function createValidationError(
  property: string,
  value: any,
  expectedType: string,
  message: string
): ValidationError {
  return {
    property,
    value,
    expectedType,
    message,
  };
}

/** 验证必需属性 */
export function validateRequired(
  value: any,
  property: string
): ValidationError | null {
  if (value === undefined || value === null || value === '') {
    return createValidationError(
      property,
      value,
      'any',
      `属性 ${property} 是必需的`
    );
  }
  return null;
}

/** 验证字符串类型 */
export function validateString(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && typeof value !== 'string') {
    return createValidationError(
      property,
      value,
      'string',
      `属性 ${property} 必须是字符串类型`
    );
  }
  
  return null;
}

/** 验证数字类型 */
export function validateNumber(
  value: any,
  property: string,
  min?: number,
  max?: number,
  required = false
): ValidationError | null {
  if (required && (value === undefined || value === null)) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && typeof value !== 'number') {
    return createValidationError(
      property,
      value,
      'number',
      `属性 ${property} 必须是数字类型`
    );
  }
  
  if (typeof value === 'number') {
    if (min !== undefined && value < min) {
      return createValidationError(
        property,
        value,
        `number >= ${min}`,
        `属性 ${property} 不能小于 ${min}`
      );
    }
    
    if (max !== undefined && value > max) {
      return createValidationError(
        property,
        value,
        `number <= ${max}`,
        `属性 ${property} 不能大于 ${max}`
      );
    }
  }
  
  return null;
}

/** 验证布尔类型 */
export function validateBoolean(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && value === undefined) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && typeof value !== 'boolean') {
    return createValidationError(
      property,
      value,
      'boolean',
      `属性 ${property} 必须是布尔类型`
    );
  }
  
  return null;
}

/** 验证函数类型 */
export function validateFunction(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && typeof value !== 'function') {
    return createValidationError(
      property,
      value,
      'function',
      `属性 ${property} 必须是函数类型`
    );
  }
  
  return null;
}

/** 验证枚举值 */
export function validateEnum<T extends string>(
  value: any,
  property: string,
  validValues: T[],
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && !validValues.includes(value)) {
    return createValidationError(
      property,
      value,
      `one of: ${validValues.join(', ')}`,
      `属性 ${property} 必须是以下值之一: ${validValues.join(', ')}`
    );
  }
  
  return null;
}

// ============================================================================
// 标准化属性验证函数
// ============================================================================

/** 验证标准尺寸 */
export function validateStandardSize(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && !isStandardSize(value)) {
    return createValidationError(
      property,
      value,
      'StandardSize',
      `属性 ${property} 必须是标准尺寸: small, medium, large`
    );
  }
  
  return null;
}

/** 验证标准变体 */
export function validateStandardVariant(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && !isStandardVariant(value)) {
    return createValidationError(
      property,
      value,
      'StandardVariant',
      `属性 ${property} 必须是标准变体: primary, secondary, ghost, danger`
    );
  }
  
  return null;
}

/** 验证标准颜色 */
export function validateStandardColor(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && !isStandardColor(value)) {
    return createValidationError(
      property,
      value,
      'StandardColor',
      `属性 ${property} 必须是标准颜色: primary, secondary, success, warning, danger, info`
    );
  }
  
  return null;
}

/** 验证标准间距 */
export function validateStandardSpacing(
  value: any,
  property: string,
  required = false
): ValidationError | null {
  if (required && !value) {
    return validateRequired(value, property);
  }
  
  if (value !== undefined && !isStandardSpacing(value)) {
    return createValidationError(
      property,
      value,
      'StandardSpacing',
      `属性 ${property} 必须是标准间距: none, small, medium, large`
    );
  }
  
  return null;
}

// ============================================================================
// 组件属性验证器
// ============================================================================

/** Button组件属性验证器 */
export function validateButtonProps(props: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  
  // 验证必需属性
  const childrenError = validateRequired(props.children, 'children');
  if (childrenError) errors.push(childrenError);
  
  // 验证可选属性
  const variantError = validateStandardVariant(props.variant, 'variant');
  if (variantError) errors.push(variantError);
  
  const sizeError = validateStandardSize(props.size, 'size');
  if (sizeError) errors.push(sizeError);
  
  const disabledError = validateBoolean(props.disabled, 'disabled');
  if (disabledError) errors.push(disabledError);
  
  const loadingError = validateBoolean(props.loading, 'loading');
  if (loadingError) errors.push(loadingError);
  
  const onClickError = validateFunction(props.onClick, 'onClick');
  if (onClickError) errors.push(onClickError);
  
  // 检查废弃的属性
  if (props.size === 'sm' || props.size === 'xs') {
    warnings.push(createValidationError(
      'size',
      props.size,
      'StandardSize',
      `尺寸 "${props.size}" 已废弃，请使用 "small" 替代`
    ));
  }
  
  if (props.size === 'lg' || props.size === 'xl') {
    warnings.push(createValidationError(
      'size',
      props.size,
      'StandardSize',
      `尺寸 "${props.size}" 已废弃，请使用 "large" 替代`
    ));
  }
  
  if (props.variant === 'destructive') {
    warnings.push(createValidationError(
      'variant',
      props.variant,
      'StandardVariant',
      `变体 "destructive" 已废弃，请使用 "danger" 替代`
    ));
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/** Card组件属性验证器 */
export function validateCardProps(props: any): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  
  // 验证可选属性
  const variantError = validateEnum(
    props.variant,
    'variant',
    ['default', 'elevated', 'outlined']
  );
  if (variantError) errors.push(variantError);
  
  const paddingError = validateStandardSpacing(props.padding, 'padding');
  if (paddingError) errors.push(paddingError);
  
  const clickableError = validateBoolean(props.clickable, 'clickable');
  if (clickableError) errors.push(clickableError);
  
  const onClickError = validateFunction(props.onClick, 'onClick');
  if (onClickError) errors.push(onClickError);
  
  // 检查废弃的属性
  if (props.padding === 'sm') {
    warnings.push(createValidationError(
      'padding',
      props.padding,
      'StandardSpacing',
      `间距 "sm" 已废弃，请使用 "small" 替代`
    ));
  }
  
  if (props.padding === 'md') {
    warnings.push(createValidationError(
      'padding',
      props.padding,
      'StandardSpacing',
      `间距 "md" 已废弃，请使用 "medium" 替代`
    ));
  }
  
  if (props.padding === 'lg') {
    warnings.push(createValidationError(
      'padding',
      props.padding,
      'StandardSpacing',
      `间距 "lg" 已废弃，请使用 "large" 替代`
    ));
  }
  
  if (props.variant === 'none') {
    errors.push(createValidationError(
      'variant',
      props.variant,
      'CardVariant',
      `变体 "none" 不存在，请使用 "default", "elevated", 或 "outlined"`
    ));
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// ============================================================================
// 通用验证工具
// ============================================================================

/** 验证组件属性并输出警告 */
export function validateAndWarn(
  componentName: string,
  props: any,
  validator: (props: any) => ValidationResult
): void {
  if (process.env.NODE_ENV === 'development') {
    const result = validator(props);
    
    // 输出错误
    result.errors.forEach(error => {
      console.error(
        `[${componentName}] 属性验证错误:`,
        error.message,
        { property: error.property, value: error.value }
      );
    });
    
    // 输出警告
    result.warnings.forEach(warning => {
      console.warn(
        `[${componentName}] 属性验证警告:`,
        warning.message,
        { property: warning.property, value: warning.value }
      );
    });
  }
}

/** 创建带验证的组件高阶函数 */
export function withValidation<P extends object>(
  Component: React.ComponentType<P>,
  validator: (props: any) => ValidationResult,
  componentName?: string
): React.ComponentType<P> {
  const ValidatedComponent: React.FC<P> = (props) => {
    validateAndWarn(componentName || Component.name, props, validator);
    return React.createElement(Component, props);
  };
  
  ValidatedComponent.displayName = `withValidation(${componentName || Component.name})`;
  
  return ValidatedComponent;
}

// ============================================================================
// 导出所有验证函数
// ============================================================================

export {
  ValidationError,
  ValidationResult,
};