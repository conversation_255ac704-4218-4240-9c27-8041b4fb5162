/**
 * 组件迁移工具
 * 帮助自动修复使用了旧属性的组件
 */

import { normalizeSize, normalizeVariant, normalizeSpacing } from '../types/components';

// ============================================================================
// 属性迁移映射
// ============================================================================

/** 尺寸属性迁移映射 */
export const SIZE_MIGRATION_MAP = {
  'xs': 'small',
  'sm': 'small',
  'md': 'medium',
  'lg': 'large',
  'xl': 'large',
} as const;

/** 变体属性迁移映射 */
export const VARIANT_MIGRATION_MAP = {
  'destructive': 'danger',
  'outline': 'secondary',
  'link': 'ghost',
} as const;

/** 间距属性迁移映射 */
export const SPACING_MIGRATION_MAP = {
  'xs': 'small',
  'sm': 'small',
  'md': 'medium',
  'lg': 'large',
  'xl': 'large',
} as const;

// ============================================================================
// 迁移函数
// ============================================================================

/** 迁移Button组件属性 */
export function migrateButtonProps(props: any): any {
  const migratedProps = { ...props };

  // 迁移size属性
  if (migratedProps.size && SIZE_MIGRATION_MAP[migratedProps.size as keyof typeof SIZE_MIGRATION_MAP]) {
    const oldSize = migratedProps.size;
    migratedProps.size = SIZE_MIGRATION_MAP[oldSize as keyof typeof SIZE_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Button] size属性 "${oldSize}" 已废弃，已自动迁移为 "${migratedProps.size}"`);
    }
  }

  // 迁移variant属性
  if (migratedProps.variant && VARIANT_MIGRATION_MAP[migratedProps.variant as keyof typeof VARIANT_MIGRATION_MAP]) {
    const oldVariant = migratedProps.variant;
    migratedProps.variant = VARIANT_MIGRATION_MAP[oldVariant as keyof typeof VARIANT_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Button] variant属性 "${oldVariant}" 已废弃，已自动迁移为 "${migratedProps.variant}"`);
    }
  }

  return migratedProps;
}

/** 迁移Card组件属性 */
export function migrateCardProps(props: any): any {
  const migratedProps = { ...props };

  // 迁移padding属性
  if (migratedProps.padding && SPACING_MIGRATION_MAP[migratedProps.padding as keyof typeof SPACING_MIGRATION_MAP]) {
    const oldPadding = migratedProps.padding;
    migratedProps.padding = SPACING_MIGRATION_MAP[oldPadding as keyof typeof SPACING_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Card] padding属性 "${oldPadding}" 已废弃，已自动迁移为 "${migratedProps.padding}"`);
    }
  }

  // 处理不存在的variant
  if (migratedProps.variant === 'none') {
    migratedProps.variant = 'default';
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Card] variant属性 "none" 不存在，已自动迁移为 "default"`);
    }
  }

  return migratedProps;
}

/** 迁移Flex组件属性 */
export function migrateFlexProps(props: any): any {
  const migratedProps = { ...props };

  // 迁移gap属性
  if (migratedProps.gap && SPACING_MIGRATION_MAP[migratedProps.gap as keyof typeof SPACING_MIGRATION_MAP]) {
    const oldGap = migratedProps.gap;
    migratedProps.gap = SPACING_MIGRATION_MAP[oldGap as keyof typeof SPACING_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Flex] gap属性 "${oldGap}" 已废弃，已自动迁移为 "${migratedProps.gap}"`);
    }
  }

  // 处理不存在的gap值
  if (migratedProps.gap === 'xs') {
    migratedProps.gap = 'small';
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Flex] gap属性 "xs" 不存在，已自动迁移为 "small"`);
    }
  }

  return migratedProps;
}

/** 迁移Grid组件属性 */
export function migrateGridProps(props: any): any {
  const migratedProps = { ...props };

  // 迁移gap属性
  if (migratedProps.gap && SPACING_MIGRATION_MAP[migratedProps.gap as keyof typeof SPACING_MIGRATION_MAP]) {
    const oldGap = migratedProps.gap;
    migratedProps.gap = SPACING_MIGRATION_MAP[oldGap as keyof typeof SPACING_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Grid] gap属性 "${oldGap}" 已废弃，已自动迁移为 "${migratedProps.gap}"`);
    }
  }

  return migratedProps;
}

/** 迁移Stack组件属性 */
export function migrateStackProps(props: any): any {
  const migratedProps = { ...props };

  // 迁移spacing属性
  if (migratedProps.spacing && SPACING_MIGRATION_MAP[migratedProps.spacing as keyof typeof SPACING_MIGRATION_MAP]) {
    const oldSpacing = migratedProps.spacing;
    migratedProps.spacing = SPACING_MIGRATION_MAP[oldSpacing as keyof typeof SPACING_MIGRATION_MAP];
    
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[Stack] spacing属性 "${oldSpacing}" 已废弃，已自动迁移为 "${migratedProps.spacing}"`);
    }
  }

  return migratedProps;
}

// ============================================================================
// 通用迁移工具
// ============================================================================

/** 创建带迁移功能的组件高阶函数 */
export function withMigration<P extends object>(
  Component: React.ComponentType<P>,
  migrationFn: (props: any) => any,
  componentName?: string
): React.ComponentType<P> {
  const MigratedComponent: React.FC<P> = (props) => {
    const migratedProps = migrationFn(props);
    return React.createElement(Component, migratedProps);
  };
  
  MigratedComponent.displayName = `withMigration(${componentName || Component.name})`;
  
  return MigratedComponent;
}

/** 批量迁移组件属性 */
export function batchMigrateProps(
  components: Array<{
    name: string;
    props: any;
    migrationFn: (props: any) => any;
  }>
): Array<{
  name: string;
  originalProps: any;
  migratedProps: any;
  changes: Array<{
    property: string;
    oldValue: any;
    newValue: any;
  }>;
}> {
  return components.map(({ name, props, migrationFn }) => {
    const originalProps = { ...props };
    const migratedProps = migrationFn(props);
    
    // 检测变化
    const changes: Array<{
      property: string;
      oldValue: any;
      newValue: any;
    }> = [];
    
    Object.keys(originalProps).forEach(key => {
      if (originalProps[key] !== migratedProps[key]) {
        changes.push({
          property: key,
          oldValue: originalProps[key],
          newValue: migratedProps[key],
        });
      }
    });
    
    return {
      name,
      originalProps,
      migratedProps,
      changes,
    };
  });
}

// ============================================================================
// 代码生成工具
// ============================================================================

/** 生成迁移报告 */
export function generateMigrationReport(
  migrations: Array<{
    name: string;
    originalProps: any;
    migratedProps: any;
    changes: Array<{
      property: string;
      oldValue: any;
      newValue: any;
    }>;
  }>
): string {
  let report = '# 组件属性迁移报告\n\n';
  
  migrations.forEach(({ name, changes }) => {
    if (changes.length > 0) {
      report += `## ${name}\n\n`;
      
      changes.forEach(({ property, oldValue, newValue }) => {
        report += `- **${property}**: \`${oldValue}\` → \`${newValue}\`\n`;
      });
      
      report += '\n';
    }
  });
  
  if (migrations.every(m => m.changes.length === 0)) {
    report += '所有组件属性都已符合标准规范。\n';
  }
  
  return report;
}

/** 生成TypeScript代码修复建议 */
export function generateCodeFixSuggestions(
  migrations: Array<{
    name: string;
    originalProps: any;
    migratedProps: any;
    changes: Array<{
      property: string;
      oldValue: any;
      newValue: any;
    }>;
  }>
): Array<{
  component: string;
  fixes: Array<{
    description: string;
    search: string;
    replace: string;
  }>;
}> {
  return migrations
    .filter(m => m.changes.length > 0)
    .map(({ name, changes }) => ({
      component: name,
      fixes: changes.map(({ property, oldValue, newValue }) => ({
        description: `将 ${property} 属性从 "${oldValue}" 更新为 "${newValue}"`,
        search: `${property}="${oldValue}"`,
        replace: `${property}="${newValue}"`,
      })),
    }));
}

// ============================================================================
// 导出所有迁移工具
// ============================================================================

export {
  migrateButtonProps,
  migrateCardProps,
  migrateFlexProps,
  migrateGridProps,
  migrateStackProps,
};