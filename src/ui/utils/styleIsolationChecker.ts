/**
 * 样式隔离检查工具
 * 用于验证组件是否正确应用了样式隔离
 */

import { PTM_CLASS_PREFIX, CSS_VAR_PREFIX } from '../styles/tokens';

export interface StyleIsolationReport {
  /** 是否通过检查 */
  passed: boolean;
  /** 检查结果详情 */
  details: {
    hasProjectPrefix: boolean;
    hasContainerClass: boolean;
    hasProperCSSVars: boolean;
    hasGlobalStyleReset: boolean;
  };
  /** 建议修复的问题 */
  suggestions: string[];
}

/**
 * 检查元素的样式隔离情况
 * @param element 要检查的HTML元素
 * @returns 检查报告
 */
export const checkStyleIsolation = (element: HTMLElement): StyleIsolationReport => {
  const suggestions: string[] = [];
  
  // 检查是否有项目前缀类名
  const classList = Array.from(element.classList);
  const hasProjectPrefix = classList.some(className => className.startsWith(PTM_CLASS_PREFIX));
  
  if (!hasProjectPrefix) {
    suggestions.push(`元素缺少项目前缀类名，应添加以 "${PTM_CLASS_PREFIX}" 开头的类名`);
  }
  
  // 检查是否在容器内
  const hasContainerClass = element.closest('.ptm-app-container') !== null;
  
  if (!hasContainerClass) {
    suggestions.push('元素不在 .ptm-app-container 容器内，可能会受到Obsidian主题影响');
  }
  
  // 检查CSS变量使用情况
  const computedStyle = window.getComputedStyle(element);
  const cssText = computedStyle.cssText || '';
  const hasProperCSSVars = cssText.includes(CSS_VAR_PREFIX) || 
    element.style.cssText.includes(CSS_VAR_PREFIX);
  
  if (!hasProperCSSVars) {
    suggestions.push(`建议使用项目CSS变量（以 "${CSS_VAR_PREFIX}" 开头）来确保主题隔离`);
  }
  
  // 检查是否有全局样式重置
  const hasGlobalStyleReset = checkGlobalStyleReset(element);
  
  if (!hasGlobalStyleReset) {
    suggestions.push('建议为通用元素（h1-h6, p, button等）添加样式重置');
  }
  
  const passed = hasProjectPrefix && hasContainerClass;
  
  return {
    passed,
    details: {
      hasProjectPrefix,
      hasContainerClass,
      hasProperCSSVars,
      hasGlobalStyleReset,
    },
    suggestions,
  };
};

/**
 * 检查全局样式重置情况
 * @param element 要检查的元素
 * @returns 是否有适当的样式重置
 */
const checkGlobalStyleReset = (element: HTMLElement): boolean => {
  const tagName = element.tagName.toLowerCase();
  const computedStyle = window.getComputedStyle(element);
  
  // 检查标题元素
  if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
    const fontFamily = computedStyle.fontFamily;
    return fontFamily.includes('var(--ptm-font-family)') || 
           fontFamily.includes('-apple-system');
  }
  
  // 检查按钮元素
  if (tagName === 'button') {
    const border = computedStyle.border;
    const background = computedStyle.background;
    return border === 'none' || background === 'none';
  }
  
  // 检查输入元素
  if (['input', 'textarea', 'select'].includes(tagName)) {
    const fontFamily = computedStyle.fontFamily;
    return fontFamily.includes('var(--ptm-font-family)') || 
           fontFamily.includes('-apple-system');
  }
  
  return true;
};

/**
 * 批量检查多个元素的样式隔离
 * @param elements 要检查的元素数组
 * @returns 检查报告数组
 */
export const batchCheckStyleIsolation = (elements: HTMLElement[]): StyleIsolationReport[] => {
  return elements.map(element => checkStyleIsolation(element));
};

/**
 * 检查整个组件树的样式隔离
 * @param rootElement 根元素
 * @returns 汇总的检查报告
 */
export const checkComponentTreeIsolation = (rootElement: HTMLElement): {
  totalElements: number;
  passedElements: number;
  failedElements: number;
  overallPassed: boolean;
  commonIssues: string[];
} => {
  const allElements = rootElement.querySelectorAll('*');
  const reports = Array.from(allElements).map(el => checkStyleIsolation(el as HTMLElement));
  
  const totalElements = reports.length;
  const passedElements = reports.filter(report => report.passed).length;
  const failedElements = totalElements - passedElements;
  const overallPassed = failedElements === 0;
  
  // 统计常见问题
  const allSuggestions = reports.flatMap(report => report.suggestions);
  const suggestionCounts = allSuggestions.reduce((acc, suggestion) => {
    acc[suggestion] = (acc[suggestion] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const commonIssues = Object.entries(suggestionCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([suggestion, count]) => `${suggestion} (${count}个元素)`);
  
  return {
    totalElements,
    passedElements,
    failedElements,
    overallPassed,
    commonIssues,
  };
};

/**
 * 生成样式隔离修复建议
 * @param element 要修复的元素
 * @returns 修复建议
 */
export const generateFixSuggestions = (element: HTMLElement): {
  className: string;
  cssRules: string[];
  jsCode: string;
} => {
  const tagName = element.tagName.toLowerCase();
  const currentClasses = Array.from(element.classList);
  
  // 生成建议的类名
  const suggestedClassName = currentClasses.length > 0 
    ? `ptm-${currentClasses[0]}` 
    : `ptm-${tagName}`;
  
  // 生成CSS规则建议
  const cssRules = [
    `.${suggestedClassName} {`,
    '  /* 基础样式重置 */',
    '  font-family: var(--ptm-font-family) !important;',
    '  color: var(--ptm-text-primary) !important;',
    '  background: var(--ptm-bg-primary) !important;',
    '  border: none !important;',
    '  margin: 0 !important;',
    '  padding: 0 !important;',
    '  box-sizing: border-box;',
    '}',
  ];
  
  // 生成JS代码建议
  const jsCode = `
// 使用样式工具函数
import { cn, classNames } from '../utils/styles';

// 在组件中应用类名
const className = classNames('${tagName}', 'your-custom-class');

// 或者使用条件类名
const className = cn('${tagName}');
  `.trim();
  
  return {
    className: suggestedClassName,
    cssRules,
    jsCode,
  };
};

/**
 * 开发环境下的样式隔离警告
 * @param element 要检查的元素
 */
export const devStyleIsolationWarning = (element: HTMLElement): void => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  const report = checkStyleIsolation(element);
  
  if (!report.passed) {
    console.warn('样式隔离检查失败:', {
      element,
      report,
      suggestions: generateFixSuggestions(element),
    });
  }
};

/**
 * 自动修复样式隔离问题（仅开发环境）
 * @param element 要修复的元素
 */
export const autoFixStyleIsolation = (element: HTMLElement): void => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  const report = checkStyleIsolation(element);
  
  if (!report.details.hasProjectPrefix) {
    const tagName = element.tagName.toLowerCase();
    element.classList.add(`ptm-${tagName}`);
  }
  
  if (!report.details.hasContainerClass) {
    console.warn('元素不在PTM容器内，请确保组件被AppContainer包裹');
  }
};