/**
 * 样式管理器
 * 负责样式系统的初始化、CSS样式隔离和主题管理
 */

import { designTokens } from '../styles/tokens';

export class StyleManager {
  private static instance: StyleManager;
  private isInitialized = false;
  private styleElement: HTMLStyleElement | null = null;

  private constructor() {}

  public static getInstance(): StyleManager {
    if (!StyleManager.instance) {
      StyleManager.instance = new StyleManager();
    }
    return StyleManager.instance;
  }

  /**
   * 初始化样式系统
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 加载全局样式
      await this.loadGlobalStyles();
      
      // 加载组件样式
      await this.loadComponentStyles();
      
      // 应用设计令牌
      this.applyDesignTokens();
      
      // 设置样式隔离
      this.setupStyleIsolation();
      
      this.isInitialized = true;
      console.log('PTM样式系统初始化完成');
    } catch (error) {
      console.error('PTM样式系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载全局样式
   */
  private async loadGlobalStyles(): Promise<void> {
    const globalStyles = await this.loadStyleFile('/src/ui/styles/globals.css');
    this.injectStyles(globalStyles, 'ptm-global-styles');
  }

  /**
   * 加载组件样式
   */
  private async loadComponentStyles(): Promise<void> {
    const componentStyles = await this.loadStyleFile('/src/ui/styles/components.css');
    this.injectStyles(componentStyles, 'ptm-component-styles');
  }

  /**
   * 加载样式文件
   */
  private async loadStyleFile(path: string): Promise<string> {
    try {
      // 在实际环境中，这些样式会被构建工具处理
      // 这里我们直接返回样式内容
      return this.getInlineStyles();
    } catch (error) {
      console.error(`加载样式文件失败: ${path}`, error);
      return '';
    }
  }

  /**
   * 获取内联样式（用于开发环境）
   */
  private getInlineStyles(): string {
    return `
      /* PTM样式系统已通过CSS文件加载 */
      .ptm-app {
        /* 样式隔离容器已准备就绪 */
      }
    `;
  }

  /**
   * 注入样式到页面
   */
  private injectStyles(css: string, id: string): void {
    // 移除已存在的样式
    const existingStyle = document.getElementById(id);
    if (existingStyle) {
      existingStyle.remove();
    }

    // 创建新的样式元素
    const styleElement = document.createElement('style');
    styleElement.id = id;
    styleElement.textContent = css;
    
    // 插入到head中
    document.head.appendChild(styleElement);
  }

  /**
   * 应用设计令牌到CSS自定义属性
   */
  private applyDesignTokens(): void {
    const root = document.documentElement;
    
    // 应用颜色令牌
    root.style.setProperty('--ptm-primary', designTokens.colors.primary);
    root.style.setProperty('--ptm-primary-gradient', designTokens.colors.primaryGradient);
    root.style.setProperty('--ptm-secondary', designTokens.colors.secondary);
    root.style.setProperty('--ptm-accent', designTokens.colors.accent);
    root.style.setProperty('--ptm-warning', designTokens.colors.warning);
    root.style.setProperty('--ptm-danger', designTokens.colors.danger);
    root.style.setProperty('--ptm-info', designTokens.colors.info);

    // 应用灰度色阶
    Object.entries(designTokens.colors.gray).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-gray-${key}`, value);
    });

    // 应用背景色
    Object.entries(designTokens.colors.background).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-bg-${key}`, value);
    });

    // 应用文本颜色
    Object.entries(designTokens.colors.text).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-text-${key}`, value);
    });

    // 应用边框颜色
    Object.entries(designTokens.colors.border).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-border-${key}`, value);
    });

    // 应用字体系统
    root.style.setProperty('--ptm-font-family', designTokens.typography.fontFamily.base);
    root.style.setProperty('--ptm-font-mono', designTokens.typography.fontFamily.mono);

    // 应用字体大小
    Object.entries(designTokens.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-text-${key}`, value);
    });

    // 应用间距系统
    Object.entries(designTokens.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-spacing-${key}`, value);
    });

    // 应用圆角系统
    Object.entries(designTokens.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-radius-${key}`, value);
    });

    // 应用阴影系统
    Object.entries(designTokens.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-shadow-${key}`, value);
    });

    // 应用布局系统
    Object.entries(designTokens.layout).forEach(([key, value]) => {
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      root.style.setProperty(`--ptm-${cssKey}`, value);
    });

    // 应用动画系统
    Object.entries(designTokens.animation.duration).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-duration-${key}`, value);
    });

    Object.entries(designTokens.animation.easing).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-easing-${key}`, value);
    });

    // 应用Z-index层级
    Object.entries(designTokens.zIndex).forEach(([key, value]) => {
      root.style.setProperty(`--ptm-z-${key}`, value.toString());
    });
  }

  /**
   * 设置样式隔离
   */
  private setupStyleIsolation(): void {
    // 确保PTM应用容器具有正确的类名
    const ptmContainers = document.querySelectorAll('.ptm-app');
    ptmContainers.forEach(container => {
      // 添加样式隔离属性
      container.setAttribute('data-ptm-isolated', 'true');
    });

    // 监听DOM变化，确保新添加的PTM容器也被正确处理
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.classList.contains('ptm-app')) {
              element.setAttribute('data-ptm-isolated', 'true');
            }
            
            // 检查子元素
            const ptmElements = element.querySelectorAll('.ptm-app');
            ptmElements.forEach(ptmElement => {
              ptmElement.setAttribute('data-ptm-isolated', 'true');
            });
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 获取设计令牌值
   */
  public getToken(path: string): string {
    const keys = path.split('.');
    let value: any = designTokens;
    
    for (const key of keys) {
      value = value?.[key];
    }
    
    return value || '';
  }

  /**
   * 获取CSS变量值
   */
  public getCSSVariable(name: string): string {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(`--ptm-${name}`)
      .trim();
  }

  /**
   * 设置CSS变量值
   */
  public setCSSVariable(name: string, value: string): void {
    document.documentElement.style.setProperty(`--ptm-${name}`, value);
  }

  /**
   * 创建样式隔离的容器
   */
  public createIsolatedContainer(className?: string): HTMLDivElement {
    const container = document.createElement('div');
    container.className = `ptm-app ${className || ''}`.trim();
    container.setAttribute('data-ptm-isolated', 'true');
    return container;
  }

  /**
   * 检查是否为暗色主题
   */
  public isDarkTheme(): boolean {
    return document.body.classList.contains('theme-dark');
  }

  /**
   * 响应主题变化
   */
  public onThemeChange(callback: (isDark: boolean) => void): () => void {
    const observer = new MutationObserver(() => {
      callback(this.isDarkTheme());
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class']
    });

    // 返回清理函数
    return () => observer.disconnect();
  }

  /**
   * 清理样式系统
   */
  public cleanup(): void {
    // 移除注入的样式
    const globalStyles = document.getElementById('ptm-global-styles');
    const componentStyles = document.getElementById('ptm-component-styles');
    
    if (globalStyles) globalStyles.remove();
    if (componentStyles) componentStyles.remove();
    
    this.isInitialized = false;
  }

  /**
   * 获取响应式断点信息
   */
  public getBreakpointInfo(): {
    current: string;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isWide: boolean;
  } {
    const width = window.innerWidth;
    const breakpoints = designTokens.breakpoints;
    
    let current = 'mobile';
    if (width >= parseInt(breakpoints.wide)) current = 'wide';
    else if (width >= parseInt(breakpoints.desktop)) current = 'desktop';
    else if (width >= parseInt(breakpoints.tablet)) current = 'tablet';
    
    return {
      current,
      isMobile: current === 'mobile',
      isTablet: current === 'tablet',
      isDesktop: current === 'desktop',
      isWide: current === 'wide',
    };
  }

  /**
   * 监听窗口大小变化
   */
  public onBreakpointChange(callback: (info: ReturnType<typeof this.getBreakpointInfo>) => void): () => void {
    let currentInfo = this.getBreakpointInfo();
    
    const handleResize = () => {
      const newInfo = this.getBreakpointInfo();
      if (newInfo.current !== currentInfo.current) {
        currentInfo = newInfo;
        callback(newInfo);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    // 返回清理函数
    return () => window.removeEventListener('resize', handleResize);
  }
}

// 导出单例实例
export const styleManager = StyleManager.getInstance();