// Theme utilities for Obsidian integration

import { App } from 'obsidian';

export interface ThemeColors {
	// Background colors
	background: string;
	backgroundPrimary: string;
	backgroundSecondary: string;
	backgroundModifier: string;
	
	// Text colors
	text: string;
	textMuted: string;
	textFaint: string;
	textAccent: string;
	
	// Interactive colors
	interactive: string;
	interactiveHover: string;
	interactiveAccent: string;
	interactiveAccentHover: string;
	
	// Border colors
	border: string;
	borderModifier: string;
	
	// Status colors
	success: string;
	warning: string;
	error: string;
	info: string;
}

export class ThemeManager {
	private app: App;
	private isDarkMode: boolean = false;
	private colors: ThemeColors;

	constructor(app: App) {
		this.app = app;
		this.isDarkMode = this.detectDarkMode();
		this.colors = this.extractThemeColors();
		this.setupThemeListener();
	}

	/**
	 * Get current theme colors
	 */
	getColors(): ThemeColors {
		return this.colors;
	}

	/**
	 * Check if dark mode is active
	 */
	isDark(): boolean {
		return this.isDarkMode;
	}

	/**
	 * Get CSS custom property value
	 */
	getCSSVariable(property: string): string {
		return getComputedStyle(document.documentElement)
			.getPropertyValue(property)
			.trim();
	}

	/**
	 * Set CSS custom property
	 */
	setCSSVariable(property: string, value: string): void {
		document.documentElement.style.setProperty(property, value);
	}

	/**
	 * Apply theme to PTM components
	 */
	applyTheme(): void {
		const colors = this.colors;
		
		// Set PTM-specific CSS variables
		this.setCSSVariable('--ptm-bg-primary', colors.backgroundPrimary);
		this.setCSSVariable('--ptm-bg-secondary', colors.backgroundSecondary);
		this.setCSSVariable('--ptm-bg-modifier', colors.backgroundModifier);
		
		this.setCSSVariable('--ptm-text-normal', colors.text);
		this.setCSSVariable('--ptm-text-muted', colors.textMuted);
		this.setCSSVariable('--ptm-text-faint', colors.textFaint);
		this.setCSSVariable('--ptm-text-accent', colors.textAccent);
		
		this.setCSSVariable('--ptm-interactive-normal', colors.interactive);
		this.setCSSVariable('--ptm-interactive-hover', colors.interactiveHover);
		this.setCSSVariable('--ptm-interactive-accent', colors.interactiveAccent);
		this.setCSSVariable('--ptm-interactive-accent-hover', colors.interactiveAccentHover);
		
		this.setCSSVariable('--ptm-border-normal', colors.border);
		this.setCSSVariable('--ptm-border-modifier', colors.borderModifier);
		
		this.setCSSVariable('--ptm-success', colors.success);
		this.setCSSVariable('--ptm-warning', colors.warning);
		this.setCSSVariable('--ptm-error', colors.error);
		this.setCSSVariable('--ptm-info', colors.info);

		// Apply theme class to body
		document.body.classList.toggle('ptm-theme-dark', this.isDarkMode);
		document.body.classList.toggle('ptm-theme-light', !this.isDarkMode);
	}

	/**
	 * Get status color based on task status
	 */
	getStatusColor(status: string): string {
		const statusColors: Record<string, string> = {
			todo: this.colors.textMuted,
			'in_progress': this.colors.info,
			blocked: this.colors.warning,
			review: this.colors.textAccent,
			completed: this.colors.success,
			cancelled: this.colors.error
		};

		return statusColors[status] || this.colors.textMuted;
	}

	/**
	 * Get priority color
	 */
	getPriorityColor(priority: string): string {
		const priorityColors: Record<string, string> = {
			low: this.colors.textFaint,
			medium: this.colors.text,
			high: this.colors.warning,
			critical: this.colors.error
		};

		return priorityColors[priority] || this.colors.text;
	}

	/**
	 * Detect if dark mode is active
	 */
	private detectDarkMode(): boolean {
		// Check CSS class on body
		if (document.body.classList.contains('theme-dark')) return true;
		if (document.body.classList.contains('theme-light')) return false;
		
		// Check system preference
		return window.matchMedia('(prefers-color-scheme: dark)').matches;
	}

	/**
	 * Extract theme colors from Obsidian CSS variables
	 */
	private extractThemeColors(): ThemeColors {
		return {
			// Background colors
			background: this.getCSSVariable('--background-primary') || '#ffffff',
			backgroundPrimary: this.getCSSVariable('--background-primary') || '#ffffff',
			backgroundSecondary: this.getCSSVariable('--background-secondary') || '#f8f9fa',
			backgroundModifier: this.getCSSVariable('--background-modifier-border') || '#e1e4e8',
			
			// Text colors
			text: this.getCSSVariable('--text-normal') || '#2e3338',
			textMuted: this.getCSSVariable('--text-muted') || '#6c757d',
			textFaint: this.getCSSVariable('--text-faint') || '#999999',
			textAccent: this.getCSSVariable('--text-accent') || '#007acc',
			
			// Interactive colors
			interactive: this.getCSSVariable('--interactive-normal') || '#f8f9fa',
			interactiveHover: this.getCSSVariable('--interactive-hover') || '#e9ecef',
			interactiveAccent: this.getCSSVariable('--interactive-accent') || '#007acc',
			interactiveAccentHover: this.getCSSVariable('--interactive-accent-hover') || '#0056b3',
			
			// Border colors
			border: this.getCSSVariable('--background-modifier-border') || '#e1e4e8',
			borderModifier: this.getCSSVariable('--background-modifier-border-hover') || '#d1d5da',
			
			// Status colors (fallback to common values)
			success: this.getCSSVariable('--text-success') || '#28a745',
			warning: this.getCSSVariable('--text-warning') || '#ffc107',
			error: this.getCSSVariable('--text-error') || '#dc3545',
			info: this.getCSSVariable('--text-info') || '#17a2b8'
		};
	}

	/**
	 * Setup theme change listener
	 */
	private setupThemeListener(): void {
		// Listen for theme changes
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
					const newIsDarkMode = this.detectDarkMode();
					if (newIsDarkMode !== this.isDarkMode) {
						this.isDarkMode = newIsDarkMode;
						this.colors = this.extractThemeColors();
						this.applyTheme();
					}
				}
			});
		});

		observer.observe(document.body, {
			attributes: true,
			attributeFilter: ['class']
		});

		// Listen for system theme changes
		window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
			const newIsDarkMode = this.detectDarkMode();
			if (newIsDarkMode !== this.isDarkMode) {
				this.isDarkMode = newIsDarkMode;
				this.colors = this.extractThemeColors();
				this.applyTheme();
			}
		});
	}
}

/**
 * Utility function to create theme-aware styles
 */
export function createThemeStyles(app: App): string {
	const themeManager = new ThemeManager(app);
	const colors = themeManager.getColors();
	
	return `
		:root {
			--ptm-bg-primary: ${colors.backgroundPrimary};
			--ptm-bg-secondary: ${colors.backgroundSecondary};
			--ptm-bg-modifier: ${colors.backgroundModifier};
			
			--ptm-text-normal: ${colors.text};
			--ptm-text-muted: ${colors.textMuted};
			--ptm-text-faint: ${colors.textFaint};
			--ptm-text-accent: ${colors.textAccent};
			
			--ptm-interactive-normal: ${colors.interactive};
			--ptm-interactive-hover: ${colors.interactiveHover};
			--ptm-interactive-accent: ${colors.interactiveAccent};
			--ptm-interactive-accent-hover: ${colors.interactiveAccentHover};
			
			--ptm-border-normal: ${colors.border};
			--ptm-border-modifier: ${colors.borderModifier};
			
			--ptm-success: ${colors.success};
			--ptm-warning: ${colors.warning};
			--ptm-error: ${colors.error};
			--ptm-info: ${colors.info};
		}
	`;
}