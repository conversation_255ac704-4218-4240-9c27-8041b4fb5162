/**
 * 样式工具函数
 * 提供样式隔离和类名生成功能，确保与Obsidian主题完全隔离
 */

import { PTM_CLASS_PREFIX, CSS_VAR_PREFIX } from '../styles/tokens';

/**
 * 生成带项目前缀的类名
 * @param className 原始类名
 * @returns 带前缀的类名
 */
export const cn = (className: string): string => {
  return `${PTM_CLASS_PREFIX}${className}`;
};

/**
 * 生成多个带前缀的类名
 * @param classNames 类名数组
 * @returns 带前缀的类名字符串
 */
export const classNames = (...classNames: (string | undefined | null | false | boolean)[]): string => {
  return classNames
    .filter(Boolean)
    .filter(className => typeof className === 'string' && className.length > 0)
    .map(className => {
      const cls = className as string;
      return cls.startsWith(PTM_CLASS_PREFIX) ? cls : cn(cls);
    })
    .join(' ');
};

/**
 * 条件类名生成器
 * @param baseClass 基础类名
 * @param conditionalClasses 条件类名对象
 * @returns 最终的类名字符串
 */
export const conditionalClass = (
  baseClass: string,
  conditionalClasses: Record<string, boolean>
): string => {
  const classes = [cn(baseClass)];
  
  Object.entries(conditionalClasses).forEach(([className, condition]) => {
    if (condition) {
      classes.push(cn(className));
    }
  });
  
  return classes.join(' ');
};

/**
 * 安全的条件类名辅助函数
 * @param condition 条件
 * @param className 类名
 * @returns 条件为真时返回类名，否则返回undefined
 */
export const conditionalClassName = (condition: boolean, className: string): string | undefined => {
  return condition ? className : undefined;
};

/**
 * 生成CSS自定义属性名
 * @param property 属性名
 * @returns 带前缀的CSS变量名
 */
export const cssVar = (property: string): string => {
  return `${CSS_VAR_PREFIX}-${property}`;
};

/**
 * 获取CSS变量值
 * @param property 属性名
 * @returns CSS var() 函数调用
 */
export const getCSSVar = (property: string): string => {
  return `var(${cssVar(property)})`;
};

/**
 * 样式对象转换器，将样式对象转换为使用CSS变量的形式
 * @param styles 样式对象
 * @returns 转换后的样式对象
 */
export const transformStyles = (styles: Record<string, any>): Record<string, any> => {
  const transformed: Record<string, any> = {};
  
  Object.entries(styles).forEach(([key, value]) => {
    if (typeof value === 'string' && value.startsWith('--ptm-')) {
      transformed[key] = `var(${value})`;
    } else {
      transformed[key] = value;
    }
  });
  
  return transformed;
};

/**
 * 创建样式隔离的容器类名
 * @param componentName 组件名称
 * @returns 容器类名
 */
export const createContainer = (componentName: string): string => {
  return cn(`${componentName}-container`);
};

/**
 * 创建BEM风格的类名
 * @param block 块名称
 * @param element 元素名称（可选）
 * @param modifier 修饰符名称（可选）
 * @returns BEM风格的类名
 */
export const bem = (block: string, element?: string, modifier?: string): string => {
  let className = cn(block);
  
  if (element) {
    className += `__${element}`;
  }
  
  if (modifier) {
    className += `--${modifier}`;
  }
  
  return className;
};

/**
 * 响应式类名生成器
 * @param baseClass 基础类名
 * @param breakpoints 断点类名映射
 * @returns 响应式类名字符串
 */
export const responsive = (
  baseClass: string,
  breakpoints: Partial<{
    mobile: string;
    tablet: string;
    desktop: string;
    wide: string;
  }>
): string => {
  const classes = [cn(baseClass)];
  
  Object.entries(breakpoints).forEach(([breakpoint, className]) => {
    if (className) {
      classes.push(cn(`${breakpoint}:${className}`));
    }
  });
  
  return classes.join(' ');
};

/**
 * 主题相关的类名生成器
 * @param lightClass 亮色主题类名
 * @param darkClass 暗色主题类名
 * @returns 主题相关的类名字符串
 */
export const themeClass = (lightClass: string, darkClass: string): string => {
  return `${cn(`light:${lightClass}`)} ${cn(`dark:${darkClass}`)}`;
};

/**
 * 状态类名生成器
 * @param baseClass 基础类名
 * @param states 状态映射
 * @returns 状态类名字符串
 */
export const stateClass = (
  baseClass: string,
  states: Partial<{
    hover: string;
    focus: string;
    active: string;
    disabled: string;
  }>
): string => {
  const classes = [cn(baseClass)];
  
  Object.entries(states).forEach(([state, className]) => {
    if (className) {
      classes.push(cn(`${state}:${className}`));
    }
  });
  
  return classes.join(' ');
};

/**
 * 验证类名是否已经有项目前缀
 * @param className 类名
 * @returns 是否已有前缀
 */
export const hasPrefix = (className: string): boolean => {
  return className.startsWith(PTM_CLASS_PREFIX);
};

/**
 * 移除项目前缀
 * @param className 带前缀的类名
 * @returns 不带前缀的类名
 */
export const removePrefix = (className: string): string => {
  return hasPrefix(className) ? className.slice(PTM_CLASS_PREFIX.length) : className;
};

/**
 * 样式隔离检查器
 * @param element HTML元素
 * @returns 是否正确应用了样式隔离
 */
export const checkStyleIsolation = (element: HTMLElement): boolean => {
  const classList = Array.from(element.classList);
  const hasProjectClass = classList.some(className => hasPrefix(className));
  const hasContainerClass = element.closest('.ptm-app-container') !== null || element.closest('.ptm-app') !== null;
  
  return hasProjectClass && hasContainerClass;
};

/**
 * 为通用HTML元素生成样式隔离的类名
 * @param tagName HTML标签名
 * @param modifier 修饰符（可选）
 * @returns 隔离的类名
 */
export const elementClass = (tagName: string, modifier?: string): string => {
  const baseClass = cn(`element-${tagName}`);
  return modifier ? `${baseClass} ${cn(`element-${tagName}--${modifier}`)}` : baseClass;
};

/**
 * 生成全局样式重置的CSS规则
 * @param containerSelector 容器选择器
 * @returns CSS规则字符串
 */
export const generateGlobalReset = (containerSelector: string = '.ptm-app-container'): string => {
  const commonElements = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'span', 'button', 'input', 'textarea', 'select', 'a'];
  
  return commonElements.map(element => `
${containerSelector} ${element} {
  font-family: var(--ptm-font-family) !important;
  color: inherit !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  box-sizing: border-box !important;
}

${containerSelector} ${element}:not([class*="ptm-"]) {
  /* 为没有PTM类名的通用元素提供基础重置 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}
  `).join('\n');
};

/**
 * 检查并警告样式隔离问题（开发环境）
 * @param element HTML元素
 * @param componentName 组件名称
 */
export const devStyleWarning = (element: HTMLElement, componentName?: string): void => {
  if (process.env.NODE_ENV !== 'development') return;
  
  const classList = Array.from(element.classList);
  const hasProjectClass = classList.some(className => hasPrefix(className));
  const hasContainerClass = element.closest('.ptm-app-container') !== null || element.closest('.ptm-app') !== null;
  
  if (!hasProjectClass) {
    console.warn(`[样式隔离警告] ${componentName || '组件'} 缺少PTM前缀类名:`, element);
  }
  
  if (!hasContainerClass) {
    console.warn(`[样式隔离警告] ${componentName || '组件'} 不在PTM容器内:`, element);
  }
  
  // 检查是否使用了可能冲突的全局类名
  const globalClasses = classList.filter(className => 
    !hasPrefix(className) && 
    !className.startsWith('obsidian-') &&
    !className.startsWith('workspace-') &&
    !className.startsWith('view-')
  );
  
  if (globalClasses.length > 0) {
    console.warn(`[样式隔离警告] ${componentName || '组件'} 使用了可能冲突的全局类名:`, globalClasses);
  }
};

/**
 * 动态样式注入器
 * @param styles CSS样式字符串
 * @param id 样式标签ID
 */
export const injectStyles = (styles: string, id: string): void => {
  const existingStyle = document.getElementById(id);
  if (existingStyle) {
    existingStyle.remove();
  }
  
  const styleElement = document.createElement('style');
  styleElement.id = id;
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
};

/**
 * 清理样式
 * @param id 样式标签ID
 */
export const removeStyles = (id: string): void => {
  const styleElement = document.getElementById(id);
  if (styleElement) {
    styleElement.remove();
  }
};