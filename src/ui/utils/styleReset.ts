/**
 * 样式重置工具
 * 为通用HTML元素提供样式隔离和重置功能
 */

import { PTM_CLASS_PREFIX, CSS_VAR_PREFIX } from '../styles/tokens';

/**
 * 通用HTML元素列表
 */
const COMMON_ELEMENTS = [
  'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'p', 'div', 'span', 'section', 'article', 'aside', 'nav',
  'button', 'input', 'textarea', 'select', 'label',
  'a', 'ul', 'ol', 'li', 'table', 'tr', 'td', 'th'
] as const;

/**
 * 生成完整的样式重置CSS
 * @param containerSelector 容器选择器
 * @returns 完整的CSS重置规则
 */
export const generateCompleteStyleReset = (containerSelector: string = '.ptm-app-container'): string => {
  return `
/* PTM 样式隔离 - 通用元素重置 */
${containerSelector} {
  /* 容器基础样式 */
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: var(${CSS_VAR_PREFIX}-text-primary) !important;
  background: var(${CSS_VAR_PREFIX}-bg-primary) !important;
  line-height: var(${CSS_VAR_PREFIX}-leading-normal) !important;
  box-sizing: border-box !important;
}

${containerSelector} *,
${containerSelector} *::before,
${containerSelector} *::after {
  box-sizing: border-box !important;
}

/* 标题元素重置 */
${COMMON_ELEMENTS.filter(el => el.startsWith('h')).map(heading => `
${containerSelector} ${heading} {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: var(${CSS_VAR_PREFIX}-text-primary) !important;
  font-weight: var(${CSS_VAR_PREFIX}-font-semibold) !important;
  line-height: var(${CSS_VAR_PREFIX}-leading-tight) !important;
  margin: 0 !important;
  padding: 0 !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

${containerSelector} ${heading}:not([class*="${PTM_CLASS_PREFIX}"]) {
  /* 为没有PTM类名的标题提供默认样式 */
  margin-bottom: var(${CSS_VAR_PREFIX}-spacing-4) !important;
}
`).join('')}

/* 段落和文本元素重置 */
${containerSelector} p,
${containerSelector} span,
${containerSelector} div {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: inherit !important;
  line-height: var(${CSS_VAR_PREFIX}-leading-normal) !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

${containerSelector} p:not([class*="${PTM_CLASS_PREFIX}"]) {
  margin: 0 0 var(${CSS_VAR_PREFIX}-spacing-4) 0 !important;
  padding: 0 !important;
}

/* 按钮元素重置 */
${containerSelector} button {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  font-size: var(${CSS_VAR_PREFIX}-text-sm) !important;
  color: var(${CSS_VAR_PREFIX}-text-primary) !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  cursor: pointer !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  outline: none !important;
}

${containerSelector} button:focus-visible {
  outline: 2px solid var(${CSS_VAR_PREFIX}-primary) !important;
  outline-offset: 2px !important;
}

/* 输入元素重置 */
${containerSelector} input,
${containerSelector} textarea,
${containerSelector} select {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  font-size: var(${CSS_VAR_PREFIX}-text-sm) !important;
  color: var(${CSS_VAR_PREFIX}-text-primary) !important;
  background: var(${CSS_VAR_PREFIX}-bg-primary) !important;
  border: 1px solid var(${CSS_VAR_PREFIX}-border-normal) !important;
  border-radius: var(${CSS_VAR_PREFIX}-radius-base) !important;
  padding: var(${CSS_VAR_PREFIX}-spacing-2) var(${CSS_VAR_PREFIX}-spacing-3) !important;
  margin: 0 !important;
  outline: none !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

${containerSelector} input:focus,
${containerSelector} textarea:focus,
${containerSelector} select:focus {
  border-color: var(${CSS_VAR_PREFIX}-primary) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* 链接元素重置 */
${containerSelector} a {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: var(${CSS_VAR_PREFIX}-primary) !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

${containerSelector} a:hover {
  text-decoration: underline !important;
}

/* 列表元素重置 */
${containerSelector} ul,
${containerSelector} ol {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

${containerSelector} li {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 表格元素重置 */
${containerSelector} table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  width: 100% !important;
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
}

${containerSelector} th,
${containerSelector} td {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: inherit !important;
  text-align: left !important;
  padding: var(${CSS_VAR_PREFIX}-spacing-2) var(${CSS_VAR_PREFIX}-spacing-3) !important;
  border-bottom: 1px solid var(${CSS_VAR_PREFIX}-border-light) !important;
}

${containerSelector} th {
  font-weight: var(${CSS_VAR_PREFIX}-font-semibold) !important;
  background: var(${CSS_VAR_PREFIX}-bg-secondary) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  ${containerSelector} {
    font-size: var(${CSS_VAR_PREFIX}-text-sm) !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  ${containerSelector} button,
  ${containerSelector} input,
  ${containerSelector} textarea,
  ${containerSelector} select {
    border-width: 2px !important;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  ${containerSelector} * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  ${containerSelector} {
    background: var(${CSS_VAR_PREFIX}-gray-900) !important;
    color: var(${CSS_VAR_PREFIX}-gray-100) !important;
  }
}
`;
};

/**
 * 为特定元素生成样式重置
 * @param element 元素标签名
 * @param containerSelector 容器选择器
 * @returns 特定元素的CSS重置规则
 */
export const generateElementReset = (element: string, containerSelector: string = '.ptm-app-container'): string => {
  const baseReset = `
${containerSelector} ${element} {
  font-family: var(${CSS_VAR_PREFIX}-font-family) !important;
  color: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  box-sizing: border-box !important;
}
`;

  // 为特定元素类型添加额外的重置规则
  switch (element) {
    case 'button':
      return baseReset + `
${containerSelector} ${element} {
  cursor: pointer !important;
  outline: none !important;
}
${containerSelector} ${element}:focus-visible {
  outline: 2px solid var(${CSS_VAR_PREFIX}-primary) !important;
  outline-offset: 2px !important;
}
`;
    case 'input':
    case 'textarea':
    case 'select':
      return baseReset + `
${containerSelector} ${element} {
  border: 1px solid var(${CSS_VAR_PREFIX}-border-normal) !important;
  border-radius: var(${CSS_VAR_PREFIX}-radius-base) !important;
  padding: var(${CSS_VAR_PREFIX}-spacing-2) var(${CSS_VAR_PREFIX}-spacing-3) !important;
  background: var(${CSS_VAR_PREFIX}-bg-primary) !important;
}
${containerSelector} ${element}:focus {
  border-color: var(${CSS_VAR_PREFIX}-primary) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}
`;
    default:
      return baseReset;
  }
};

/**
 * 注入样式重置到页面
 * @param containerSelector 容器选择器
 * @param id 样式标签ID
 */
export const injectStyleReset = (containerSelector: string = '.ptm-app-container', id: string = 'ptm-style-reset'): void => {
  // 移除已存在的样式
  const existingStyle = document.getElementById(id);
  if (existingStyle) {
    existingStyle.remove();
  }

  // 创建新的样式标签
  const styleElement = document.createElement('style');
  styleElement.id = id;
  styleElement.textContent = generateCompleteStyleReset(containerSelector);
  
  // 插入到head的开头，确保优先级
  document.head.insertBefore(styleElement, document.head.firstChild);
};

/**
 * 移除样式重置
 * @param id 样式标签ID
 */
export const removeStyleReset = (id: string = 'ptm-style-reset'): void => {
  const styleElement = document.getElementById(id);
  if (styleElement) {
    styleElement.remove();
  }
};

/**
 * 检查元素是否需要样式重置
 * @param element HTML元素
 * @returns 是否需要重置
 */
export const needsStyleReset = (element: HTMLElement): boolean => {
  const tagName = element.tagName.toLowerCase();
  const classList = Array.from(element.classList);
  
  // 如果已经有PTM类名，可能不需要额外重置
  const hasPTMClass = classList.some(className => className.startsWith(PTM_CLASS_PREFIX));
  
  // 如果是通用元素且没有PTM类名，需要重置
  return COMMON_ELEMENTS.includes(tagName as any) && !hasPTMClass;
};