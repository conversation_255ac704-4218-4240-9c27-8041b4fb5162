// Utility for rendering React components in Obsidian

import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { App as ObsidianApp } from 'obsidian';

export class ReactRenderer {
	private roots: Map<HTMLElement, Root> = new Map();

	/**
	 * Render a React component into a DOM element
	 */
	render(
		component: React.ReactElement,
		container: HTMLElement,
		callback?: () => void
	): void {
		// Clean up existing root if it exists
		this.unmount(container);

		// Create new root and render
		const root = createRoot(container);
		root.render(component);
		
		// Store root for cleanup
		this.roots.set(container, root);

		if (callback) {
			callback();
		}
	}

	/**
	 * Unmount React component from DOM element
	 */
	unmount(container: HTMLElement): void {
		const root = this.roots.get(container);
		if (root) {
			root.unmount();
			this.roots.delete(container);
		}
	}

	/**
	 * Unmount all React components
	 */
	unmountAll(): void {
		for (const [container, root] of this.roots.entries()) {
			root.unmount();
		}
		this.roots.clear();
	}

	/**
	 * Check if container has a React component mounted
	 */
	isMounted(container: HTMLElement): boolean {
		return this.roots.has(container);
	}

	/**
	 * Get the number of mounted components
	 */
	getMountedCount(): number {
		return this.roots.size;
	}
}

// Global instance for the plugin
let globalRenderer: ReactRenderer | null = null;

/**
 * Get the global React renderer instance
 */
export function getReactRenderer(): ReactRenderer {
	if (!globalRenderer) {
		globalRenderer = new ReactRenderer();
	}
	return globalRenderer;
}

/**
 * Cleanup the global React renderer
 */
export function cleanupReactRenderer(): void {
	if (globalRenderer) {
		globalRenderer.unmountAll();
		globalRenderer = null;
	}
}

/**
 * Higher-order component for Obsidian integration
 */
export interface WithObsidianProps {
	app: ObsidianApp;
}

export function withObsidian<P extends WithObsidianProps>(
	Component: React.ComponentType<P>
) {
	return function WithObsidianComponent(props: P) {
		return <Component {...props} />;
	};
}

/**
 * Hook for accessing Obsidian app instance
 */
export function useObsidianApp(): ObsidianApp | null {
	// This would need to be provided through React context
	// For now, return null as a placeholder
	return null;
}

/**
 * Utility function to create a container element with proper styling
 */
export function createReactContainer(className?: string): HTMLElement {
	const container = document.createElement('div');
	container.className = `ptm-react-container ${className || ''}`;
	return container;
}

/**
 * Utility function to render React component in Obsidian view
 */
export function renderInObsidianView(
	component: React.ReactElement,
	containerEl: HTMLElement,
	className?: string
): () => void {
	const container = createReactContainer(className);
	containerEl.appendChild(container);
	
	const renderer = getReactRenderer();
	renderer.render(component, container);

	// Return cleanup function
	return () => {
		renderer.unmount(container);
		if (container.parentNode) {
			container.parentNode.removeChild(container);
		}
	};
}