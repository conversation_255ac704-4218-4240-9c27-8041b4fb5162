// 独立的任务列表视图，作为Obsidian视图实现

import { ItemView, WorkspaceLeaf } from 'obsidian';
import { Root, createRoot } from 'react-dom/client';
import React from 'react';
import { PTMManager } from '../../services/PTMManager';
import { TaskListViewComponent } from '../components/views/TaskListViewComponent';
import { SimpleTaskListViewComponent } from '../components/views/SimpleTaskListViewComponent';

export const TASK_LIST_VIEW_TYPE = 'project-task-manager-task-list';

export class TaskListView extends ItemView {
	private root: Root | null = null;
	private ptmManager: PTMManager;

	constructor(leaf: WorkspaceLeaf, ptmManager: PTMManager) {
		super(leaf);
		this.ptmManager = ptmManager;
	}

	getViewType(): string {
		return TASK_LIST_VIEW_TYPE;
	}

	getDisplayText(): string {
		return '任务列表';
	}

	getIcon(): string {
		return 'list-checks';
	}

	async onOpen(): Promise<void> {
		try {
			console.log('TaskListView: Opening view...');
			
			// 创建React根节点
			const container = this.containerEl.children[1];
			container.empty();
			
			// 创建React应用容器
			const reactContainer = container.createDiv();
			reactContainer.addClass('ptm-task-list-view-container');
			
			// 检查 PTMManager 是否已初始化
			if (!this.ptmManager) {
				throw new Error('PTMManager 未初始化');
			}
			
			console.log('TaskListView: Creating React root...');
			
			// 渲染React组件
			this.root = createRoot(reactContainer);
			
			// 先尝试渲染一个简单的测试组件
			console.log('TaskListView: Rendering simple test component first...');
			this.root.render(
				React.createElement('div', {
					style: {
						padding: '2rem',
						textAlign: 'center',
						color: 'var(--text-normal)'
					}
				}, [
					React.createElement('h2', { key: 'title' }, '任务列表测试'),
					React.createElement('p', { key: 'message' }, '正在加载任务列表组件...'),
					React.createElement('button', {
						key: 'load-btn',
						onClick: () => {
							console.log('Loading full component...');
							this.loadFullComponent();
						},
						style: {
							padding: '0.5rem 1rem',
							backgroundColor: 'var(--interactive-accent)',
							color: 'var(--text-on-accent)',
							border: 'none',
							borderRadius: '0.25rem',
							cursor: 'pointer'
						}
					}, '加载完整组件')
				])
			);
			
			console.log('TaskListView: React component rendered successfully');
		} catch (error) {
			console.error('TaskListView: Error opening view:', error);
			
			// 显示错误信息给用户
			const container = this.containerEl.children[1];
			container.empty();
			container.createDiv({
				text: `任务列表加载失败: ${error instanceof Error ? error.message : String(error)}`,
				cls: 'ptm-error-message'
			});
			
			// 添加重试按钮
			const retryButton = container.createEl('button', {
				text: '重试',
				cls: 'ptm-retry-button'
			});
			retryButton.onclick = () => {
				this.onOpen();
			};
		}
	}

	async onClose(): Promise<void> {
		// 清理React根节点
		if (this.root) {
			this.root.unmount();
			this.root = null;
		}
	}

	async onResize(): Promise<void> {
		// 处理视图大小变化
		// React组件会自动处理响应式布局
	}

	private loadFullComponent(): void {
		try {
			console.log('TaskListView: Loading full TaskListViewComponent...');
			
			if (!this.root) {
				console.error('TaskListView: React root not available');
				return;
			}

			// 先尝试渲染一个简化版本来诊断问题
			this.root.render(
				React.createElement('div', {
					style: {
						padding: '1rem',
						color: 'var(--text-normal)'
					}
				}, [
					React.createElement('h1', { key: 'title' }, '任务列表'),
					React.createElement('p', { key: 'status' }, '正在测试组件加载...'),
					React.createElement('button', {
						key: 'test-data-btn',
						onClick: () => {
							this.testDataLoading();
						},
						style: {
							padding: '0.5rem 1rem',
							backgroundColor: 'var(--interactive-accent)',
							color: 'var(--text-on-accent)',
							border: 'none',
							borderRadius: '0.25rem',
							cursor: 'pointer',
							marginRight: '0.5rem'
						}
					}, '测试数据加载'),
					React.createElement('button', {
						key: 'test-simple-btn',
						onClick: () => {
							this.testSimpleComponent();
						},
						style: {
							padding: '0.5rem 1rem',
							backgroundColor: 'var(--text-success)',
							color: 'white',
							border: 'none',
							borderRadius: '0.25rem',
							cursor: 'pointer',
							marginRight: '0.5rem'
						}
					}, '测试简化组件'),
					React.createElement('button', {
						key: 'test-full-btn',
						onClick: () => {
							this.testFullComponent();
						},
						style: {
							padding: '0.5rem 1rem',
							backgroundColor: 'var(--text-warning)',
							color: 'white',
							border: 'none',
							borderRadius: '0.25rem',
							cursor: 'pointer'
						}
					}, '测试完整组件')
				])
			);
			
			console.log('TaskListView: Full component rendered');
		} catch (error) {
			console.error('TaskListView: Error loading full component:', error);
			
			// 显示错误信息
			if (this.root) {
				this.root.render(
					React.createElement('div', {
						style: {
							padding: '2rem',
							textAlign: 'center',
							color: 'var(--text-error)'
						}
					}, [
						React.createElement('h2', { key: 'error-title' }, '组件加载失败'),
						React.createElement('p', { key: 'error-message' }, `错误: ${error instanceof Error ? error.message : String(error)}`),
						React.createElement('button', {
							key: 'retry-btn',
							onClick: () => {
								this.onOpen();
							},
							style: {
								padding: '0.5rem 1rem',
								backgroundColor: 'var(--interactive-accent)',
								color: 'var(--text-on-accent)',
								border: 'none',
								borderRadius: '0.25rem',
								cursor: 'pointer'
							}
						}, '重试')
					])
				);
			}
		}
	}

	private async testDataLoading(): Promise<void> {
		try {
			console.log('TaskListView: Testing data loading...');
			
			if (!this.ptmManager) {
				throw new Error('PTMManager not available');
			}

			const projectManager = this.ptmManager.getProjectManager();
			const taskRepository = this.ptmManager.getTaskRepository();

			console.log('TaskListView: Getting projects and tasks...');
			const [projects, tasks] = await Promise.all([
				projectManager.getAllProjects(),
				taskRepository.findAll()
			]);

			console.log('TaskListView: Data loaded successfully');
			console.log('Projects:', projects.length);
			console.log('Tasks:', tasks.length);

			// 显示数据加载结果
			if (this.root) {
				this.root.render(
					React.createElement('div', {
						style: {
							padding: '1rem',
							color: 'var(--text-normal)'
						}
					}, [
						React.createElement('h1', { key: 'title' }, '数据加载测试'),
						React.createElement('p', { key: 'projects' }, `项目数量: ${projects.length}`),
						React.createElement('p', { key: 'tasks' }, `任务数量: ${tasks.length}`),
						React.createElement('button', {
							key: 'back-btn',
							onClick: () => {
								this.loadFullComponent();
							},
							style: {
								padding: '0.5rem 1rem',
								backgroundColor: 'var(--interactive-accent)',
								color: 'var(--text-on-accent)',
								border: 'none',
								borderRadius: '0.25rem',
								cursor: 'pointer'
							}
						}, '返回测试菜单')
					])
				);
			}
		} catch (error) {
			console.error('TaskListView: Error testing data loading:', error);
			
			if (this.root) {
				this.root.render(
					React.createElement('div', {
						style: {
							padding: '1rem',
							color: 'var(--text-error)'
						}
					}, [
						React.createElement('h1', { key: 'title' }, '数据加载失败'),
						React.createElement('p', { key: 'error' }, `错误: ${error instanceof Error ? error.message : String(error)}`),
						React.createElement('button', {
							key: 'back-btn',
							onClick: () => {
								this.loadFullComponent();
							},
							style: {
								padding: '0.5rem 1rem',
								backgroundColor: 'var(--interactive-accent)',
								color: 'var(--text-on-accent)',
								border: 'none',
								borderRadius: '0.25rem',
								cursor: 'pointer'
							}
						}, '返回测试菜单')
					])
				);
			}
		}
	}

	private testFullComponent(): void {
		try {
			console.log('TaskListView: Testing full TaskListViewComponent...');
			
			if (!this.root) {
				console.error('TaskListView: React root not available');
				return;
			}

			// 尝试渲染完整的 TaskListViewComponent
			this.root.render(
				React.createElement(TaskListViewComponent, {
					ptmManager: this.ptmManager,
					onTaskSelect: (taskId: string) => {
						console.log('Selected task:', taskId);
					},
					onTaskCreate: () => {
						console.log('Create new task');
					},
					onTaskEdit: (taskId: string) => {
						console.log('Edit task:', taskId);
					},
					onTaskDelete: (taskId: string) => {
						console.log('Delete task:', taskId);
					}
				})
			);
			
			console.log('TaskListView: Full TaskListViewComponent rendered successfully');
		} catch (error) {
			console.error('TaskListView: Error testing full component:', error);
			
			// 显示错误信息
			if (this.root) {
				this.root.render(
					React.createElement('div', {
						style: {
							padding: '1rem',
							color: 'var(--text-error)'
						}
					}, [
						React.createElement('h1', { key: 'title' }, '完整组件测试失败'),
						React.createElement('p', { key: 'error' }, `错误: ${error instanceof Error ? error.message : String(error)}`),
						React.createElement('pre', { 
							key: 'stack',
							style: {
								fontSize: '0.8rem',
								backgroundColor: 'var(--background-secondary)',
								padding: '0.5rem',
								borderRadius: '0.25rem',
								overflow: 'auto',
								maxHeight: '200px'
							}
						}, error instanceof Error ? error.stack || error.message : String(error)),
						React.createElement('button', {
							key: 'back-btn',
							onClick: () => {
								this.loadFullComponent();
							},
							style: {
								padding: '0.5rem 1rem',
								backgroundColor: 'var(--interactive-accent)',
								color: 'var(--text-on-accent)',
								border: 'none',
								borderRadius: '0.25rem',
								cursor: 'pointer'
							}
						}, '返回测试菜单')
					])
				);
			}
		}
	}

	private testSimpleComponent(): void {
		try {
			console.log('TaskListView: Testing SimpleTaskListViewComponent...');
			
			if (!this.root) {
				console.error('TaskListView: React root not available');
				return;
			}

			// 尝试渲染简化的 SimpleTaskListViewComponent
			this.root.render(
				React.createElement(SimpleTaskListViewComponent, {
					ptmManager: this.ptmManager,
					onTaskSelect: (taskId: string) => {
						console.log('Selected task:', taskId);
					},
					onTaskCreate: () => {
						console.log('Create new task');
					},
					onTaskEdit: (taskId: string) => {
						console.log('Edit task:', taskId);
					},
					onTaskDelete: (taskId: string) => {
						console.log('Delete task:', taskId);
					}
				})
			);
			
			console.log('TaskListView: SimpleTaskListViewComponent rendered successfully');
		} catch (error) {
			console.error('TaskListView: Error testing simple component:', error);
			
			// 显示错误信息
			if (this.root) {
				this.root.render(
					React.createElement('div', {
						style: {
							padding: '1rem',
							color: 'var(--text-error)'
						}
					}, [
						React.createElement('h1', { key: 'title' }, '简化组件测试失败'),
						React.createElement('p', { key: 'error' }, `错误: ${error instanceof Error ? error.message : String(error)}`),
						React.createElement('pre', { 
							key: 'stack',
							style: {
								fontSize: '0.8rem',
								backgroundColor: 'var(--background-secondary)',
								padding: '0.5rem',
								borderRadius: '0.25rem',
								overflow: 'auto',
								maxHeight: '200px'
							}
						}, error instanceof Error ? error.stack || error.message : String(error)),
						React.createElement('button', {
							key: 'back-btn',
							onClick: () => {
								this.loadFullComponent();
							},
							style: {
								padding: '0.5rem 1rem',
								backgroundColor: 'var(--interactive-accent)',
								color: 'var(--text-on-accent)',
								border: 'none',
								borderRadius: '0.25rem',
								cursor: 'pointer'
							}
						}, '返回测试菜单')
					])
				);
			}
		}
	}
}