import { ItemView, WorkspaceLeaf } from 'obsidian';
import React from 'react';
import { ReactRenderer } from '../utils/ReactRenderer';
import { SprintViewComponent } from '../components/views/SprintViewComponent';

export const SPRINT_VIEW_TYPE = 'project-task-manager-sprint';

/**
 * Sprint 管理视图
 */
export class SprintView extends ItemView {
	private reactRenderer: ReactRenderer;
	private ptmManager: any;

	constructor(leaf: WorkspaceLeaf, ptmManager?: any) {
		super(leaf);
		this.reactRenderer = new ReactRenderer();
		this.ptmManager = ptmManager;
	}

	getViewType(): string {
		return SPRINT_VIEW_TYPE;
	}

	getDisplayText(): string {
		return 'Sprint 管理';
	}

	getIcon(): string {
		return 'zap';
	}

	async onOpen(): Promise<void> {
		const container = this.containerEl.children[1];
		container.empty();
		container.addClass('ptm-sprint-view');

		// 渲染 React 组件
		this.reactRenderer.render(
			React.createElement(SprintViewComponent, { ptmManager: this.ptmManager }),
			container as HTMLElement
		);
	}

	async onClose(): Promise<void> {
		const container = this.containerEl.children[1];
		this.reactRenderer.unmount(container as HTMLElement);
	}
}