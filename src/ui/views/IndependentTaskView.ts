// 独立任务视图 - Obsidian视图实现

import { ItemView, WorkspaceLeaf } from 'obsidian';
import { ReactRenderer } from '../utils/ReactRenderer';
import { IndependentTaskViewComponent } from '../components/views/IndependentTaskViewComponent';
import { IndependentTaskModeController } from '../../services/IndependentTaskModeController';

export const INDEPENDENT_TASK_VIEW_TYPE = 'independent-task-view';

/**
 * 独立任务视图
 * 提供无项目环境下的任务管理界面
 */
export class IndependentTaskView extends ItemView {
	private renderer: ReactRenderer;
	private controller: IndependentTaskModeController;

	constructor(leaf: WorkspaceLeaf, controller: IndependentTaskModeController) {
		super(leaf);
		this.controller = controller;
		this.renderer = new ReactRenderer();
	}

	getViewType(): string {
		return INDEPENDENT_TASK_VIEW_TYPE;
	}

	getDisplayText(): string {
		return '独立任务管理';
	}

	getIcon(): string {
		return 'check-square';
	}

	async onOpen(): Promise<void> {
		const container = this.containerEl.children[1];
		container.empty();
		
		// 添加视图样式
		container.addClass('independent-task-view-container');
		
		// 渲染React组件
		this.renderer.render(
			IndependentTaskViewComponent,
			{ controller: this.controller },
			container as HTMLElement
		);
	}

	async onClose(): Promise<void> {
		// 清理React组件
		this.renderer.unmount();
	}

	/**
	 * 刷新视图
	 */
	async refresh(): Promise<void> {
		await this.onClose();
		await this.onOpen();
	}

	/**
	 * 获取控制器实例
	 */
	getController(): IndependentTaskModeController {
		return this.controller;
	}
}