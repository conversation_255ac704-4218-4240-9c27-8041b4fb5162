// 看板视图，作为Obsidian视图实现

import { ItemView, WorkspaceLeaf } from 'obsidian';
import { Root, createRoot } from 'react-dom/client';
import React from 'react';
import { PTMManager } from '../../services/PTMManager';
import { KanbanBoard } from '../components/kanban/KanbanBoard';
import { Container, Stack } from '../components/layout/Layout';
import { Card, CardHeader, CardContent } from '../components/common/Card';
import { Button } from '../components/common/Button';

export const KANBAN_VIEW_TYPE = 'project-task-manager-kanban';

export class KanbanView extends ItemView {
	private root: Root | null = null;
	private ptmManager: PTMManager;
	private currentKanbanId: string | null = null;

	constructor(leaf: WorkspaceLeaf, ptmManager: PTMManager) {
		super(leaf);
		this.ptmManager = ptmManager;
	}

	getViewType(): string {
		return KANBAN_VIEW_TYPE;
	}

	getDisplayText(): string {
		return '看板视图';
	}

	getIcon(): string {
		return 'kanban-square';
	}

	async onOpen(): Promise<void> {
		try {
			console.log('KanbanView: Opening view...');
			
			// 创建React根节点
			const container = this.containerEl.children[1];
			container.empty();
			
			// 创建React应用容器
			const reactContainer = container.createDiv();
			reactContainer.addClass('ptm-kanban-view-container');
			
			// 检查 PTMManager 是否已初始化
			if (!this.ptmManager) {
				throw new Error('PTMManager 未初始化');
			}
			
			console.log('KanbanView: Creating React root...');
			
			// 渲染React组件
			this.root = createRoot(reactContainer);
			
			// 渲染看板选择界面
			await this.renderKanbanSelector();
			
			console.log('KanbanView: React component rendered successfully');
		} catch (error) {
			console.error('KanbanView: Error opening view:', error);
			
			// 显示错误信息给用户
			const container = this.containerEl.children[1];
			container.empty();
			container.createDiv({
				text: `看板视图加载失败: ${error instanceof Error ? error.message : String(error)}`,
				cls: 'ptm-error-message'
			});
			
			// 添加重试按钮
			const retryButton = container.createEl('button', {
				text: '重试',
				cls: 'ptm-retry-button'
			});
			retryButton.onclick = () => {
				this.onOpen();
			};
		}
	}

	async onClose(): Promise<void> {
		// 清理React根节点
		if (this.root) {
			this.root.unmount();
			this.root = null;
		}
	}

	async onResize(): Promise<void> {
		// 处理视图大小变化
		// React组件会自动处理响应式布局
	}

	/**
	 * 设置当前看板ID
	 */
	setKanbanId(kanbanId: string): void {
		this.currentKanbanId = kanbanId;
		this.renderKanban(kanbanId);
	}

	/**
	 * 渲染看板选择界面
	 */
	private async renderKanbanSelector(): Promise<void> {
		if (!this.root) return;

		try {
			// 获取看板管理器
			const kanbanManager = this.ptmManager.getKanbanManager();
			if (!kanbanManager) {
				throw new Error('看板管理器未初始化');
			}

			// 获取所有看板配置
			const kanbans = kanbanManager.getAllKanbans();

			this.root.render(
				React.createElement(Container, { padding: true }, [
					React.createElement(Stack, { spacing: 'lg', key: 'main-stack' }, [
						React.createElement(Card, { variant: 'elevated', key: 'header-card' }, [
							React.createElement(CardHeader, { key: 'header' }, [
								React.createElement('h2', { key: 'title' }, '选择看板'),
								React.createElement('p', { key: 'description' }, '选择要查看的看板，或创建新的看板配置')
							])
						]),

						// 现有看板列表
						kanbans.length > 0 ? React.createElement(Card, { variant: 'elevated', key: 'kanbans-card' }, [
							React.createElement(CardHeader, { key: 'kanbans-header' }, [
								React.createElement('h3', { key: 'kanbans-title' }, '现有看板')
							]),
							React.createElement(CardContent, { key: 'kanbans-content' }, [
								React.createElement(Stack, { spacing: 'sm', key: 'kanbans-list' }, 
									kanbans.map(kanban => 
										React.createElement('div', {
											key: kanban.id,
											style: {
												padding: '0.75rem',
												border: '1px solid var(--background-modifier-border)',
												borderRadius: '0.5rem',
												cursor: 'pointer',
												transition: 'all 0.2s ease'
											},
											onClick: () => this.renderKanban(kanban.id),
											onMouseEnter: (e: React.MouseEvent<HTMLDivElement>) => {
												e.currentTarget.style.backgroundColor = 'var(--interactive-hover)';
											},
											onMouseLeave: (e: React.MouseEvent<HTMLDivElement>) => {
												e.currentTarget.style.backgroundColor = 'transparent';
											}
										}, [
											React.createElement('h4', { 
												key: 'name',
												style: { margin: '0 0 0.25rem 0', fontSize: '1rem' }
											}, kanban.name),
											React.createElement('p', { 
												key: 'info',
												style: { 
													margin: 0, 
													fontSize: '0.875rem', 
													color: 'var(--text-muted)' 
												}
											}, `${kanban.columns.length} 列 • ${kanban.projectId ? '项目看板' : '全局看板'}`)
										])
									)
								)
							])
						]) : React.createElement(Card, { variant: 'elevated', key: 'empty-card' }, [
							React.createElement(CardContent, { key: 'empty-content' }, [
								React.createElement('div', {
									key: 'empty-state',
									style: {
										textAlign: 'center',
										padding: '2rem',
										color: 'var(--text-muted)'
									}
								}, [
									React.createElement('h3', { key: 'empty-title' }, '暂无看板'),
									React.createElement('p', { key: 'empty-description' }, '创建第一个看板开始使用')
								])
							])
						]),

						// 操作按钮
						React.createElement(Card, { variant: 'elevated', key: 'actions-card' }, [
							React.createElement(CardContent, { key: 'actions-content' }, [
								React.createElement(Stack, { spacing: 'sm', key: 'actions-stack' }, [
									React.createElement(Button, {
										key: 'create-btn',
										variant: 'primary',
										onClick: () => this.createTestKanban()
									}, '创建测试看板'),
									React.createElement(Button, {
										key: 'back-btn',
										variant: 'ghost',
										onClick: () => this.renderKanbanSelector()
									}, '返回选择')
								])
							])
						])
					])
				])
			);
		} catch (error) {
			console.error('KanbanView: Error rendering kanban selector:', error);
			this.renderError(error instanceof Error ? error.message : '渲染失败');
		}
	}

	/**
	 * 渲染看板
	 */
	private async renderKanban(kanbanId: string): Promise<void> {
		if (!this.root) return;

		try {
			const kanbanManager = this.ptmManager.getKanbanManager();
			if (!kanbanManager) {
				throw new Error('看板管理器未初始化');
			}

			this.currentKanbanId = kanbanId;

			this.root.render(
				React.createElement(KanbanBoard, {
					kanbanManager,
					kanbanId,
					onTaskSelect: (taskId: string) => {
						console.log('选中任务:', taskId);
					},
					onTaskEdit: (taskId: string) => {
						console.log('编辑任务:', taskId);
					},
					onTaskCreate: (columnId: string) => {
						console.log('创建任务，列:', columnId);
					},
					onConfigEdit: () => {
						console.log('编辑看板配置');
						this.renderKanbanSelector();
					}
				})
			);
		} catch (error) {
			console.error('KanbanView: Error rendering kanban:', error);
			this.renderError(error instanceof Error ? error.message : '渲染看板失败');
		}
	}

	/**
	 * 创建测试看板（只创建一次，避免重复数据）
	 */
	private async createTestKanban(): Promise<void> {
		try {
			const kanbanManager = this.ptmManager.getKanbanManager();
			const taskManager = this.ptmManager.getTaskManager();
			const projectManager = this.ptmManager.getProjectManager();
			
			if (!kanbanManager || !taskManager || !projectManager) {
				throw new Error('管理器未初始化');
			}

			// 检查是否已存在测试项目
			const existingProjects = await projectManager.getAllProjects();
			let testProject = existingProjects.find(p => p.name === '测试项目');

			if (!testProject) {
				// 创建测试项目
				testProject = await projectManager.createProject({
					name: '测试项目',
					description: '用于看板测试的项目',
					createPTMFile: false
				});

				// 创建测试任务
				const testTasks = [
					{
						title: '设计用户界面',
						description: '设计应用程序的用户界面原型',
						priority: 'high' as any,
						tags: ['设计', 'UI', 'Figma'],
						assignee: '张三'
					},
					{
						title: '实现登录功能',
						description: '开发用户登录和认证系统',
						priority: 'medium' as any,
						tags: ['开发', '认证', 'React'],
						assignee: '李四'
					},
					{
						title: '编写API文档',
						description: '为后端API编写详细的文档',
						priority: 'low' as any,
						tags: ['文档', 'API'],
						assignee: '王五'
					},
					{
						title: '修复登录Bug',
						description: '修复用户登录时的验证问题',
						priority: 'critical' as any,
						tags: ['Bug修复', '紧急'],
						assignee: '张三'
					}
				];

				// 创建任务
				for (const taskData of testTasks) {
					await taskManager.createTask({
						...taskData,
						projectId: testProject.id
					});
				}

				console.log('创建测试项目和任务成功:', testProject.name);
			} else {
				console.log('测试项目已存在，跳过创建:', testProject.name);
			}

			// 检查是否已存在测试看板
			const existingKanbans = await kanbanManager.getAllKanbans();
			let testKanban = existingKanbans.find(k => k.name === '测试看板' && k.projectId === testProject?.id);

			if (!testKanban) {
				// 创建测试看板
				testKanban = await kanbanManager.createKanban({
					name: '测试看板',
					projectId: testProject.id,
					enableSwimLanes: false
				});
				console.log('创建测试看板成功:', testKanban.name);
			} else {
				console.log('测试看板已存在，跳过创建:', testKanban.name);
			}

			// 切换到测试看板
			this.renderKanban(testKanban.id);
		} catch (error) {
			console.error('KanbanView: Error creating test kanban:', error);
			this.renderError(error instanceof Error ? error.message : '创建测试看板失败');
		}
	}

	/**
	 * 渲染错误状态
	 */
	private renderError(message: string): void {
		if (!this.root) return;

		this.root.render(
			React.createElement(Container, { padding: true }, [
				React.createElement(Card, { variant: 'elevated', key: 'error-card' }, [
					React.createElement(CardContent, { key: 'error-content' }, [
						React.createElement('div', {
							key: 'error-state',
							style: {
								textAlign: 'center',
								padding: '2rem',
								color: 'var(--text-error)'
							}
						}, [
							React.createElement('h3', { key: 'error-title' }, '加载失败'),
							React.createElement('p', { key: 'error-message' }, message),
							React.createElement(Button, {
								key: 'retry-btn',
								variant: 'primary',
								onClick: () => this.renderKanbanSelector()
							}, '重试')
						])
					])
				])
			])
		);
	}
}