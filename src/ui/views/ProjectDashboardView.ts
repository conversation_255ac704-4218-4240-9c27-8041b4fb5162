// Obsidian view for Project Dashboard

import React from 'react';
import { ItemView, WorkspaceLeaf } from 'obsidian';
import { ReactRenderer } from '../utils/ReactRenderer';
import { HomepageDashboard } from '../components/homepage/HomepageDashboard';
import { PTMManager } from '../../services/PTMManager';

export const PROJECT_DASHBOARD_VIEW = 'project-task-manager-dashboard';

export class ProjectDashboardView extends ItemView {
	private reactRenderer: ReactRenderer | null = null;
	private ptmManager: PTMManager;

	constructor(leaf: WorkspaceLeaf, ptmManager: PTMManager) {
		super(leaf);
		this.ptmManager = ptmManager;
	}

	getViewType(): string {
		return PROJECT_DASHBOARD_VIEW;
	}

	getDisplayText(): string {
		return '项目任务管理中心';
	}

	getIcon(): string {
		return 'layout-dashboard';
	}

	async onOpen(): Promise<void> {
		const container = this.containerEl.children[1];
		container.empty();
		container.addClass('ptm-homepage-view');

		// 处理导航
		const handleNavigate = (view: string, params?: any) => {
			console.log('导航到:', view, params);
			
			// 根据视图类型打开对应的Obsidian视图
			const viewTypeMap: Record<string, string> = {
				'projects': 'project-task-manager-dashboard',
				'tasks': 'project-task-manager-task-list',
				'kanban': 'project-task-manager-kanban',
				'gantt': 'project-task-manager-gantt',
				'sprint': 'project-task-manager-sprint',
				'independent-tasks': 'project-task-manager-independent-tasks'
			};

			const viewType = viewTypeMap[view];
			if (viewType) {
				this.app.workspace.getLeaf(false).setViewState({
					type: viewType,
					active: true
				});
			}
		};

		// Create React renderer and render the homepage dashboard
		this.reactRenderer = new ReactRenderer();
		const homepageElement = React.createElement(HomepageDashboard, {
			app: this.app,
			ptmManager: this.ptmManager,
			onNavigate: handleNavigate
		});
		
		this.reactRenderer.render(homepageElement, container as HTMLElement);
	}

	async onClose(): Promise<void> {
		if (this.reactRenderer) {
			this.reactRenderer.unmountAll();
			this.reactRenderer = null;
		}
	}
} 