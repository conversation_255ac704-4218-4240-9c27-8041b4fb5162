// 甘特图视图类

import { ItemView, WorkspaceLeaf } from 'obsidian';
import { Root, createRoot } from 'react-dom/client';
import React from 'react';
import { GanttViewComponent } from '../components/views/GanttViewComponent';
import { PTMManager } from '../../services/PTMManager';

export const GANTT_VIEW_TYPE = 'ptm-gantt-view';

export class GanttView extends ItemView {
	private root: Root | null = null;
	private ptmManager: PTMManager;

	constructor(leaf: WorkspaceLeaf, ptmManager: PTMManager) {
		super(leaf);
		this.ptmManager = ptmManager;
	}

	getViewType(): string {
		return GANTT_VIEW_TYPE;
	}

	getDisplayText(): string {
		return '甘特图';
	}

	getIcon(): string {
		return 'gantt-chart';
	}

	async onOpen(): Promise<void> {
		const container = this.containerEl.children[1];
		container.empty();
		
		// 创建React根节点
		this.root = createRoot(container);
		
		// 渲染甘特图组件
		this.renderGanttView();
	}

	async onClose(): Promise<void> {
		if (this.root) {
			this.root.unmount();
			this.root = null;
		}
	}

	/**
	 * 刷新甘特图
	 */
	refresh(): void {
		if (this.root) {
			this.renderGanttView();
		}
	}

	/**
	 * 渲染甘特图视图组件
	 */
	private renderGanttView(): void {
		if (!this.root) return;

		this.root.render(
			React.createElement(GanttViewComponent, {
				ptmManager: this.ptmManager
			})
		);
	}
}