/**
 * 设计令牌系统
 * 基于高保真原型的设计系统变量定义
 * 确保与Obsidian主题隔离，提供一致的视觉体验
 */

// 项目命名空间前缀，确保样式隔离
export const PTM_PREFIX = 'ptm';
export const PTM_CLASS_PREFIX = `${PTM_PREFIX}-`;

// CSS自定义属性前缀，避免与Obsidian主题冲突
export const CSS_VAR_PREFIX = `--${PTM_PREFIX}`;

export const designTokens = {
  // 颜色系统
  colors: {
    // 主色调
    primary: '#667eea',
    primaryGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    
    // 辅助色
    secondary: '#f7fafc',
    accent: '#48bb78',
    warning: '#ed8936',
    danger: '#f56565',
    info: '#3182ce',
    
    // 灰度色阶
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    
    // 语义化颜色
    success: '#48bb78',
    error: '#f56565',
    
    // 背景色
    background: {
      primary: '#ffffff',
      secondary: '#f7fafc',
      tertiary: '#f3f4f6',
    },
    
    // 文本颜色
    text: {
      primary: '#1f2937',
      secondary: '#4b5563',
      muted: '#6b7280',
      inverse: '#ffffff',
    },
    
    // 边框颜色
    border: {
      light: '#e5e7eb',
      normal: '#d1d5db',
      dark: '#9ca3af',
    }
  },

  // 字体系统
  typography: {
    fontFamily: {
      base: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif',
      mono: 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '32px',
      '4xl': '40px',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: '1.2',
      normal: '1.5',
      relaxed: '1.6',
    }
  },

  // 间距系统
  spacing: {
    0: '0',
    1: '4px',
    2: '8px',
    3: '12px',
    4: '16px',
    5: '20px',
    6: '24px',
    7: '28px',
    8: '32px',
    10: '40px',
    12: '48px',
    16: '64px',
    20: '80px',
    24: '96px',
  },

  // 圆角系统
  borderRadius: {
    none: '0',
    sm: '6px',
    base: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
    full: '9999px',
  },

  // 阴影系统
  shadows: {
    none: 'none',
    sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
    base: '0 4px 12px rgba(0, 0, 0, 0.08)',
    md: '0 8px 24px rgba(0, 0, 0, 0.12)',
    lg: '0 16px 40px rgba(0, 0, 0, 0.16)',
    xl: '0 24px 48px rgba(0, 0, 0, 0.2)',
  },

  // 布局系统
  layout: {
    // 侧边栏
    sidebarWidth: '280px',
    sidebarCollapsedWidth: '64px',
    
    // 右侧面板
    rightPanelWidth: '320px',
    
    // 头部高度
    headerHeight: '72px',
    
    // 容器最大宽度
    containerMaxWidth: '1200px',
  },

  // 响应式断点
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
    wide: '1200px',
  },

  // 动画系统
  animation: {
    duration: {
      fast: '0.15s',
      normal: '0.3s',
      slow: '0.5s',
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
    }
  },

  // Z-index 层级
  zIndex: {
    base: 0,
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060,
    toast: 1070,
  }
} as const;

// 类型定义
export type DesignTokens = typeof designTokens;
export type ColorScale = typeof designTokens.colors.gray;
export type SpacingScale = typeof designTokens.spacing;
export type FontSizeScale = typeof designTokens.typography.fontSize;

// 工具函数：获取颜色值
export const getColor = (path: string): string => {
  const keys = path.split('.');
  let value: any = designTokens.colors;
  
  for (const key of keys) {
    value = value?.[key];
  }
  
  return value || '#000000';
};

// 工具函数：获取间距值
export const getSpacing = (key: keyof typeof designTokens.spacing): string => {
  return designTokens.spacing[key];
};

// 工具函数：获取字体大小
export const getFontSize = (key: keyof typeof designTokens.typography.fontSize): string => {
  return designTokens.typography.fontSize[key];
};

// 工具函数：获取阴影值
export const getShadow = (key: keyof typeof designTokens.shadows): string => {
  return designTokens.shadows[key];
};