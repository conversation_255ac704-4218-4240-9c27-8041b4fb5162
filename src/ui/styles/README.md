# PTM 样式系统文档

## 概述

本文档描述了Obsidian Project Task Manager (PTM) 插件的样式系统架构，重点关注与Obsidian主题的隔离策略，确保插件在任何主题下都能保持一致的视觉体验。

## 样式隔离策略

### 1. 项目命名空间

所有PTM相关的样式都使用统一的前缀 `ptm-` 来避免与Obsidian主题的样式冲突：

```typescript
// 项目前缀常量
export const PTM_PREFIX = 'ptm';
export const PTM_CLASS_PREFIX = `${PTM_PREFIX}-`;
export const CSS_VAR_PREFIX = `--${PTM_PREFIX}`;
```

### 2. CSS自定义属性

使用CSS自定义属性（CSS Variables）定义设计令牌，确保样式的一致性和可维护性：

```css
.ptm-app {
  --ptm-primary: #667eea;
  --ptm-text-primary: #1f2937;
  --ptm-bg-primary: #ffffff;
  /* ... 更多变量 */
}
```

### 3. 容器隔离

所有PTM组件都必须包含在 `.ptm-app` 容器内，通过容器作用域限制样式影响范围：

```html
<div class="ptm-app">
  <!-- PTM组件内容 -->
</div>
```

### 4. 强制样式重置

使用 `!important` 声明确保关键样式不被Obsidian主题覆盖：

```css
.ptm-app {
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-secondary) !important;
}
```

## 文件结构

```
src/ui/styles/
├── tokens.ts           # 设计令牌定义
├── globals.css         # 全局样式和重置
├── components.css      # 通用组件样式
└── README.md          # 本文档
```

## 工具函数

### 类名生成

```typescript
import { cn, classNames, bem } from '../utils/styles';

// 单个类名
const buttonClass = cn('button'); // 'ptm-button'

// 多个类名
const combinedClass = classNames('button', 'primary'); // 'ptm-button ptm-primary'

// BEM风格
const bemClass = bem('card', 'header', 'large'); // 'ptm-card__header--large'
```

### CSS变量使用

```typescript
import { getCSSVar } from '../utils/styles';

const primaryColor = getCSSVar('primary'); // 'var(--ptm-primary)'
```

## CSS Modules集成

### 组件样式文件

```css
/* Button.module.css */
.button {
  font-family: var(--ptm-font-family);
  color: var(--ptm-text-primary);
  background: var(--ptm-bg-primary);
}

.primary {
  background: var(--ptm-primary-gradient) !important;
  color: var(--ptm-text-inverse) !important;
}
```

### React组件使用

```typescript
import React from 'react';
import { classNames } from '../../utils/styles';
import styles from './Button.module.css';

export const Button: React.FC<ButtonProps> = ({ variant, className, ...props }) => {
  const buttonClassName = classNames(
    styles.button,
    styles[variant],
    className
  );

  return <button className={buttonClassName} {...props} />;
};
```

## 主题管理

### PTMThemeManager

主题管理器负责：
- 注入CSS变量
- 应用样式隔离
- 监听Obsidian主题变化
- 强制应用PTM样式

```typescript
import { PTMThemeManager } from '../utils/theme';

const themeManager = new PTMThemeManager(app);
themeManager.initialize();

// 应用PTM主题到元素
themeManager.applyPTMTheme(element);
```

## 响应式设计

### 断点系统

```typescript
export const breakpoints = {
  mobile: '480px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1200px',
};
```

### 响应式Hook

```typescript
import { useResponsive } from '../hooks/useResponsive';

const { isMobile, isTablet, screenSize } = useResponsive();
```

## 测试策略

### 样式隔离测试

```typescript
import { checkStyleIsolation } from '../utils/styles';

test('应该正确应用样式隔离', () => {
  const element = render(<Component />);
  expect(checkStyleIsolation(element)).toBe(true);
});
```

### CSS Modules测试

```typescript
// 模拟CSS Modules
jest.mock('./Component.module.css', () => ({
  component: 'ptm-component',
  primary: 'ptm-primary',
}));
```

## 最佳实践

### 1. 始终使用项目前缀

```typescript
// ✅ 正确
const className = cn('button');

// ❌ 错误
const className = 'button';
```

### 2. 使用CSS变量

```css
/* ✅ 正确 */
.button {
  color: var(--ptm-text-primary);
}

/* ❌ 错误 */
.button {
  color: #1f2937;
}
```

### 3. 容器包装

```tsx
// ✅ 正确
<div className="ptm-app">
  <Button>Click me</Button>
</div>

// ❌ 错误
<Button>Click me</Button>
```

### 4. 重要样式使用!important

```css
/* ✅ 正确 - 关键样式 */
.ptm-app {
  font-family: var(--ptm-font-family) !important;
}

/* ✅ 正确 - 普通样式 */
.button {
  padding: var(--ptm-spacing-3);
}
```

## 常见问题

### Q: 为什么需要使用!important？

A: Obsidian的主题样式具有很高的特异性，使用!important确保PTM的关键样式不被覆盖。

### Q: 如何调试样式冲突？

A: 使用浏览器开发者工具检查元素，确认：
1. 元素是否在`.ptm-app`容器内
2. 类名是否有`ptm-`前缀
3. CSS变量是否正确定义

### Q: 如何添加新的设计令牌？

A: 在`tokens.ts`中添加新的令牌，然后在`globals.css`中定义对应的CSS变量。

## 更新日志

- **v1.0.0**: 初始样式系统实现
- 完整的样式隔离策略
- CSS Modules集成
- 主题管理器
- 响应式设计支持
- 完整的测试覆盖