/**
 * 组件样式模块
 * 基于设计令牌的组件样式定义
 */

/* ==========================================================================
   工具类
   ========================================================================== */

/* 间距工具类 */
.ptm-p-0 { padding: 0 !important; }
.ptm-p-1 { padding: var(--ptm-spacing-1) !important; }
.ptm-p-2 { padding: var(--ptm-spacing-2) !important; }
.ptm-p-3 { padding: var(--ptm-spacing-3) !important; }
.ptm-p-4 { padding: var(--ptm-spacing-4) !important; }
.ptm-p-5 { padding: var(--ptm-spacing-5) !important; }
.ptm-p-6 { padding: var(--ptm-spacing-6) !important; }
.ptm-p-8 { padding: var(--ptm-spacing-8) !important; }

.ptm-m-0 { margin: 0 !important; }
.ptm-m-1 { margin: var(--ptm-spacing-1) !important; }
.ptm-m-2 { margin: var(--ptm-spacing-2) !important; }
.ptm-m-3 { margin: var(--ptm-spacing-3) !important; }
.ptm-m-4 { margin: var(--ptm-spacing-4) !important; }
.ptm-m-5 { margin: var(--ptm-spacing-5) !important; }
.ptm-m-6 { margin: var(--ptm-spacing-6) !important; }
.ptm-m-8 { margin: var(--ptm-spacing-8) !important; }

/* 文本工具类 */
.ptm-text-xs { font-size: var(--ptm-text-xs) !important; }
.ptm-text-sm { font-size: var(--ptm-text-sm) !important; }
.ptm-text-base { font-size: var(--ptm-text-base) !important; }
.ptm-text-lg { font-size: var(--ptm-text-lg) !important; }
.ptm-text-xl { font-size: var(--ptm-text-xl) !important; }
.ptm-text-2xl { font-size: var(--ptm-text-2xl) !important; }
.ptm-text-3xl { font-size: var(--ptm-text-3xl) !important; }

.ptm-font-normal { font-weight: 400 !important; }
.ptm-font-medium { font-weight: 500 !important; }
.ptm-font-semibold { font-weight: 600 !important; }
.ptm-font-bold { font-weight: 700 !important; }

.ptm-text-primary { color: var(--ptm-text-primary) !important; }
.ptm-text-secondary { color: var(--ptm-text-secondary) !important; }
.ptm-text-muted { color: var(--ptm-text-muted) !important; }
.ptm-text-inverse { color: var(--ptm-text-inverse) !important; }

/* 布局工具类 */
.ptm-flex { display: flex !important; }
.ptm-inline-flex { display: inline-flex !important; }
.ptm-grid { display: grid !important; }
.ptm-block { display: block !important; }
.ptm-inline-block { display: inline-block !important; }
.ptm-hidden { display: none !important; }

.ptm-flex-col { flex-direction: column !important; }
.ptm-flex-row { flex-direction: row !important; }

.ptm-items-start { align-items: flex-start !important; }
.ptm-items-center { align-items: center !important; }
.ptm-items-end { align-items: flex-end !important; }
.ptm-items-stretch { align-items: stretch !important; }

.ptm-justify-start { justify-content: flex-start !important; }
.ptm-justify-center { justify-content: center !important; }
.ptm-justify-end { justify-content: flex-end !important; }
.ptm-justify-between { justify-content: space-between !important; }

.ptm-flex-1 { flex: 1 !important; }
.ptm-flex-none { flex: none !important; }

.ptm-w-full { width: 100% !important; }
.ptm-h-full { height: 100% !important; }

/* 圆角工具类 */
.ptm-rounded-none { border-radius: 0 !important; }
.ptm-rounded-sm { border-radius: var(--ptm-radius-sm) !important; }
.ptm-rounded { border-radius: var(--ptm-radius-base) !important; }
.ptm-rounded-md { border-radius: var(--ptm-radius-md) !important; }
.ptm-rounded-lg { border-radius: var(--ptm-radius-lg) !important; }
.ptm-rounded-xl { border-radius: var(--ptm-radius-xl) !important; }
.ptm-rounded-full { border-radius: var(--ptm-radius-full) !important; }

/* 阴影工具类 */
.ptm-shadow-none { box-shadow: none !important; }
.ptm-shadow-sm { box-shadow: var(--ptm-shadow-sm) !important; }
.ptm-shadow { box-shadow: var(--ptm-shadow-base) !important; }
.ptm-shadow-md { box-shadow: var(--ptm-shadow-md) !important; }
.ptm-shadow-lg { box-shadow: var(--ptm-shadow-lg) !important; }

/* 边框工具类 */
.ptm-border { border: 1px solid var(--ptm-border-normal) !important; }
.ptm-border-light { border-color: var(--ptm-border-light) !important; }
.ptm-border-normal { border-color: var(--ptm-border-normal) !important; }
.ptm-border-dark { border-color: var(--ptm-border-dark) !important; }

/* 背景工具类 */
.ptm-bg-primary { background-color: var(--ptm-bg-primary) !important; }
.ptm-bg-secondary { background-color: var(--ptm-bg-secondary) !important; }
.ptm-bg-tertiary { background-color: var(--ptm-bg-tertiary) !important; }

/* 过渡动画工具类 */
.ptm-transition { transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth) !important; }
.ptm-transition-fast { transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth) !important; }
.ptm-transition-slow { transition: all var(--ptm-duration-slow) var(--ptm-easing-smooth) !important; }

/* ==========================================================================
   按钮组件
   ========================================================================== */

.ptm-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--ptm-radius-base);
  font-weight: 500;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  font-family: var(--ptm-font-family);
  outline: none;
}

.ptm-button:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

.ptm-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮尺寸 */
.ptm-button--sm {
  height: 32px;
  padding: 0 var(--ptm-spacing-3);
  font-size: var(--ptm-text-sm);
  gap: var(--ptm-spacing-1);
}

.ptm-button--md {
  height: 40px;
  padding: 0 var(--ptm-spacing-4);
  font-size: var(--ptm-text-sm);
  gap: var(--ptm-spacing-2);
}

.ptm-button--lg {
  height: 48px;
  padding: 0 var(--ptm-spacing-6);
  font-size: var(--ptm-text-base);
  gap: var(--ptm-spacing-2);
}

/* 按钮变体 */
.ptm-button--primary {
  background: var(--ptm-primary-gradient);
  color: var(--ptm-text-inverse);
  box-shadow: var(--ptm-shadow-sm);
}

.ptm-button--primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--ptm-shadow-md);
}

.ptm-button--secondary {
  background: var(--ptm-bg-primary);
  color: var(--ptm-text-secondary);
  border-color: var(--ptm-border-normal);
}

.ptm-button--secondary:hover:not(:disabled) {
  background: var(--ptm-bg-secondary);
  border-color: var(--ptm-border-dark);
}

.ptm-button--ghost {
  background: transparent;
  color: var(--ptm-text-primary);
}

.ptm-button--ghost:hover:not(:disabled) {
  background: var(--ptm-bg-secondary);
}

.ptm-button--icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: var(--ptm-radius-base);
}

.ptm-button--icon.ptm-button--sm {
  width: 32px;
  height: 32px;
}

.ptm-button--icon.ptm-button--lg {
  width: 48px;
  height: 48px;
}

/* ==========================================================================
   卡片组件
   ========================================================================== */

.ptm-card {
  background: var(--ptm-bg-primary);
  border-radius: var(--ptm-radius-lg);
  border: 1px solid var(--ptm-border-light);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  overflow: hidden;
}

.ptm-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ptm-shadow-md);
  border-color: var(--ptm-border-normal);
}

.ptm-card--elevated {
  box-shadow: var(--ptm-shadow-base);
}

.ptm-card--elevated:hover {
  box-shadow: var(--ptm-shadow-lg);
}

/* ==========================================================================
   输入框组件
   ========================================================================== */

.ptm-input {
  width: 100%;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
  border: 1px solid var(--ptm-border-normal);
  border-radius: var(--ptm-radius-md);
  font-size: var(--ptm-text-sm);
  background: var(--ptm-bg-secondary);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  font-family: var(--ptm-font-family);
  color: var(--ptm-text-primary);
}

.ptm-input:focus {
  outline: none;
  border-color: var(--ptm-primary);
  background: var(--ptm-bg-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ptm-input::placeholder {
  color: var(--ptm-text-muted);
}

/* 搜索框 */
.ptm-search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.ptm-search-input {
  padding-left: var(--ptm-spacing-10);
}

.ptm-search-icon {
  position: absolute;
  left: var(--ptm-spacing-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--ptm-text-muted);
  font-size: var(--ptm-text-sm);
  pointer-events: none;
}

/* ==========================================================================
   头像组件
   ========================================================================== */

.ptm-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--ptm-radius-full);
  background: var(--ptm-primary-gradient);
  color: var(--ptm-text-inverse);
  font-weight: 600;
  flex-shrink: 0;
}

.ptm-avatar--sm {
  width: 24px;
  height: 24px;
  font-size: var(--ptm-text-xs);
}

.ptm-avatar--md {
  width: 32px;
  height: 32px;
  font-size: var(--ptm-text-sm);
}

.ptm-avatar--lg {
  width: 48px;
  height: 48px;
  font-size: var(--ptm-text-lg);
}

/* ==========================================================================
   进度条组件
   ========================================================================== */

.ptm-progress {
  width: 100%;
  height: 8px;
  background: var(--ptm-gray-200);
  border-radius: var(--ptm-radius-sm);
  overflow: hidden;
}

.ptm-progress-fill {
  height: 100%;
  background: var(--ptm-primary-gradient);
  border-radius: var(--ptm-radius-sm);
  transition: width var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* ==========================================================================
   状态指示器
   ========================================================================== */

.ptm-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--ptm-radius-full);
  display: inline-block;
  margin-right: var(--ptm-spacing-2);
}

.ptm-status-indicator--success {
  background: var(--ptm-success);
}

.ptm-status-indicator--warning {
  background: var(--ptm-warning);
}

.ptm-status-indicator--danger {
  background: var(--ptm-danger);
}

.ptm-status-indicator--info {
  background: var(--ptm-info);
}

/* ==========================================================================
   标签组件
   ========================================================================== */

.ptm-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--ptm-spacing-1) var(--ptm-spacing-2);
  background: var(--ptm-bg-secondary);
  color: var(--ptm-text-muted);
  border-radius: var(--ptm-radius-sm);
  font-size: var(--ptm-text-xs);
  font-weight: 500;
  border: 1px solid var(--ptm-border-light);
  gap: var(--ptm-spacing-1);
}

.ptm-tag:hover {
  background: var(--ptm-bg-tertiary);
  color: var(--ptm-text-primary);
}

/* 优先级标签 */
.ptm-priority-high {
  background: var(--ptm-danger);
  color: var(--ptm-text-inverse);
  border-color: var(--ptm-danger);
}

.ptm-priority-medium {
  background: var(--ptm-warning);
  color: var(--ptm-text-inverse);
  border-color: var(--ptm-warning);
}

.ptm-priority-low {
  background: var(--ptm-success);
  color: var(--ptm-text-inverse);
  border-color: var(--ptm-success);
}

/* ==========================================================================
   加载动画
   ========================================================================== */

.ptm-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--ptm-gray-200);
  border-radius: 50%;
  border-top-color: var(--ptm-primary);
  animation: ptm-spin 1s linear infinite;
}

.ptm-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-8);
  gap: var(--ptm-spacing-4);
}

.ptm-loading-text {
  color: var(--ptm-text-muted);
  font-size: var(--ptm-text-sm);
}

/* ==========================================================================
   工具提示
   ========================================================================== */

.ptm-tooltip {
  position: relative;
  cursor: help;
}

.ptm-tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--ptm-gray-800);
  color: var(--ptm-text-inverse);
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  border-radius: var(--ptm-radius-sm);
  font-size: var(--ptm-text-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  z-index: var(--ptm-z-tooltip);
  margin-bottom: var(--ptm-spacing-2);
}

.ptm-tooltip:hover::before {
  opacity: 1;
  visibility: visible;
}

/* ==========================================================================
   响应式设计
   ========================================================================== */

@media (max-width: 768px) {
  .ptm-button--md {
    height: 36px;
    padding: 0 var(--ptm-spacing-3);
    font-size: var(--ptm-text-sm);
  }
  
  .ptm-search-container {
    max-width: 200px;
  }
}