/**
 * 样式系统入口文件
 * 导出所有样式相关的模块和工具
 */

// 设计令牌
export { designTokens, getColor, getSpacing, getFontSize, getShadow } from './tokens';
export type { DesignTokens, ColorScale, SpacingScale, FontSizeScale } from './tokens';

// 样式管理器
export { styleManager, StyleManager } from '../utils/styleManager';

// Hooks
export { useResponsive, useMediaQuery, useIsMobile, useIsTablet, useIsDesktop, useIsWide, useSidebarState, useRightPanelState, useResponsiveGrid, useContainerQuery } from '../hooks/useResponsive';
export type { ResponsiveInfo } from '../hooks/useResponsive';

export { useLocalStorage, useSessionStorage, useSidebarStorage, useRightPanelStorage, useCurrentPageStorage, useSearchHistoryStorage, useUserPreferencesStorage, useKanbanSettingsStorage, useProjectFiltersStorage, useTaskFiltersStorage, useWindowSizesStorage } from '../hooks/useLocalStorage';
export type { UserPreferences, KanbanColumnSettings, ProjectFilters, TaskFilters, WindowSizes } from '../hooks/useLocalStorage';

export { useSearch, useAdvancedSearch, useRealtimeSearch, useSearchSuggestions, useProjectSearch, useTaskSearch, useGlobalSearch } from '../hooks/useSearch';
export type { SearchOptions, SearchResult } from '../hooks/useSearch';

// CSS类名常量
export const CSS_CLASSES = {
  // 应用容器
  APP: 'ptm-app',
  
  // 布局
  FLEX: 'ptm-flex',
  FLEX_COL: 'ptm-flex-col',
  FLEX_ROW: 'ptm-flex-row',
  ITEMS_CENTER: 'ptm-items-center',
  JUSTIFY_BETWEEN: 'ptm-justify-between',
  JUSTIFY_CENTER: 'ptm-justify-center',
  
  // 间距
  P_4: 'ptm-p-4',
  P_6: 'ptm-p-6',
  M_4: 'ptm-m-4',
  M_6: 'ptm-m-6',
  
  // 文本
  TEXT_PRIMARY: 'ptm-text-primary',
  TEXT_SECONDARY: 'ptm-text-secondary',
  TEXT_MUTED: 'ptm-text-muted',
  FONT_MEDIUM: 'ptm-font-medium',
  FONT_SEMIBOLD: 'ptm-font-semibold',
  
  // 按钮
  BUTTON: 'ptm-button',
  BUTTON_PRIMARY: 'ptm-button--primary',
  BUTTON_SECONDARY: 'ptm-button--secondary',
  BUTTON_GHOST: 'ptm-button--ghost',
  BUTTON_SM: 'ptm-button--sm',
  BUTTON_MD: 'ptm-button--md',
  BUTTON_LG: 'ptm-button--lg',
  
  // 卡片
  CARD: 'ptm-card',
  CARD_ELEVATED: 'ptm-card--elevated',
  
  // 输入框
  INPUT: 'ptm-input',
  SEARCH_CONTAINER: 'ptm-search-container',
  SEARCH_INPUT: 'ptm-search-input',
  SEARCH_ICON: 'ptm-search-icon',
  
  // 头像
  AVATAR: 'ptm-avatar',
  AVATAR_SM: 'ptm-avatar--sm',
  AVATAR_MD: 'ptm-avatar--md',
  AVATAR_LG: 'ptm-avatar--lg',
  
  // 进度条
  PROGRESS: 'ptm-progress',
  PROGRESS_FILL: 'ptm-progress-fill',
  
  // 标签
  TAG: 'ptm-tag',
  PRIORITY_HIGH: 'ptm-priority-high',
  PRIORITY_MEDIUM: 'ptm-priority-medium',
  PRIORITY_LOW: 'ptm-priority-low',
  
  // 状态指示器
  STATUS_INDICATOR: 'ptm-status-indicator',
  STATUS_SUCCESS: 'ptm-status-indicator--success',
  STATUS_WARNING: 'ptm-status-indicator--warning',
  STATUS_DANGER: 'ptm-status-indicator--danger',
  STATUS_INFO: 'ptm-status-indicator--info',
  
  // 加载动画
  SPINNER: 'ptm-spinner',
  LOADING_CONTAINER: 'ptm-loading-container',
  LOADING_TEXT: 'ptm-loading-text',
  
  // 工具类
  ROUNDED: 'ptm-rounded',
  ROUNDED_LG: 'ptm-rounded-lg',
  SHADOW: 'ptm-shadow',
  SHADOW_MD: 'ptm-shadow-md',
  TRANSITION: 'ptm-transition',
  
  // 响应式
  GRID: 'ptm-grid',
  GRID_COLS_1: 'ptm-grid-cols-1',
  GRID_COLS_2: 'ptm-grid-cols-2',
  GRID_COLS_3: 'ptm-grid-cols-3',
  GRID_COLS_4: 'ptm-grid-cols-4',
  GAP_4: 'ptm-gap-4',
  GAP_6: 'ptm-gap-6',
} as const;

// 工具函数
export const createClassName = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

export const conditionalClass = (condition: boolean, trueClass: string, falseClass?: string): string => {
  return condition ? trueClass : (falseClass || '');
};

// 样式工具函数
export const getResponsiveClass = (base: string, breakpoint: 'sm' | 'md' | 'lg' | 'xl'): string => {
  return `${breakpoint}:${base}`;
};

export const getSpacingClass = (type: 'p' | 'm', size: number): string => {
  return `ptm-${type}-${size}`;
};

export const getTextSizeClass = (size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'): string => {
  return `ptm-text-${size}`;
};

// 主题相关工具
export const isDarkTheme = (): boolean => {
  return document.body.classList.contains('theme-dark');
};

export const getThemeClass = (lightClass: string, darkClass: string): string => {
  return isDarkTheme() ? darkClass : lightClass;
};

// 样式初始化函数
export const initializeStyles = async (): Promise<void> => {
  await styleManager.initialize();
};

// 清理样式函数
export const cleanupStyles = (): void => {
  styleManager.cleanup();
};