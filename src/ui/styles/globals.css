/**
 * 全局样式文件 - 确保与Obsidian主题隔离
 * 所有样式都使用项目前缀，避免与Obsidian主题冲突
 */

/* 定义CSS自定义属性，使用项目前缀 */
.ptm-app {
  /* 颜色系统 */
  --ptm-primary: #667eea;
  --ptm-primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ptm-secondary: #f7fafc;
  --ptm-accent: #48bb78;
  --ptm-warning: #ed8936;
  --ptm-danger: #f56565;
  --ptm-info: #3182ce;
  --ptm-success: #48bb78;
  --ptm-error: #f56565;

  /* 灰度色阶 */
  --ptm-gray-50: #f9fafb;
  --ptm-gray-100: #f3f4f6;
  --ptm-gray-200: #e5e7eb;
  --ptm-gray-300: #d1d5db;
  --ptm-gray-400: #9ca3af;
  --ptm-gray-500: #6b7280;
  --ptm-gray-600: #4b5563;
  --ptm-gray-700: #374151;
  --ptm-gray-800: #1f2937;
  --ptm-gray-900: #111827;

  /* 背景色 */
  --ptm-bg-primary: #ffffff;
  --ptm-bg-secondary: #f7fafc;
  --ptm-bg-tertiary: #f3f4f6;

  /* 文本颜色 */
  --ptm-text-primary: #1f2937;
  --ptm-text-secondary: #4b5563;
  --ptm-text-muted: #6b7280;
  --ptm-text-inverse: #ffffff;

  /* 边框颜色 */
  --ptm-border-light: #e5e7eb;
  --ptm-border-normal: #d1d5db;
  --ptm-border-dark: #9ca3af;

  /* 字体系统 */
  --ptm-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  --ptm-font-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* 字体大小 */
  --ptm-text-xs: 12px;
  --ptm-text-sm: 14px;
  --ptm-text-base: 16px;
  --ptm-text-lg: 18px;
  --ptm-text-xl: 20px;
  --ptm-text-2xl: 24px;
  --ptm-text-3xl: 32px;
  --ptm-text-4xl: 40px;

  /* 字体粗细 */
  --ptm-font-normal: 400;
  --ptm-font-medium: 500;
  --ptm-font-semibold: 600;
  --ptm-font-bold: 700;

  /* 行高 */
  --ptm-leading-tight: 1.2;
  --ptm-leading-normal: 1.5;
  --ptm-leading-relaxed: 1.6;

  /* 间距系统 */
  --ptm-spacing-0: 0;
  --ptm-spacing-1: 4px;
  --ptm-spacing-2: 8px;
  --ptm-spacing-3: 12px;
  --ptm-spacing-4: 16px;
  --ptm-spacing-5: 20px;
  --ptm-spacing-6: 24px;
  --ptm-spacing-7: 28px;
  --ptm-spacing-8: 32px;
  --ptm-spacing-10: 40px;
  --ptm-spacing-12: 48px;
  --ptm-spacing-16: 64px;
  --ptm-spacing-20: 80px;
  --ptm-spacing-24: 96px;

  /* 圆角系统 */
  --ptm-radius-none: 0;
  --ptm-radius-sm: 6px;
  --ptm-radius-base: 8px;
  --ptm-radius-md: 12px;
  --ptm-radius-lg: 16px;
  --ptm-radius-xl: 20px;
  --ptm-radius-full: 9999px;

  /* 阴影系统 */
  --ptm-shadow-none: none;
  --ptm-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --ptm-shadow-base: 0 4px 12px rgba(0, 0, 0, 0.08);
  --ptm-shadow-md: 0 8px 24px rgba(0, 0, 0, 0.12);
  --ptm-shadow-lg: 0 16px 40px rgba(0, 0, 0, 0.16);
  --ptm-shadow-xl: 0 24px 48px rgba(0, 0, 0, 0.2);

  /* 布局系统 */
  --ptm-sidebar-width: 280px;
  --ptm-sidebar-collapsed-width: 64px;
  --ptm-right-panel-width: 320px;
  --ptm-header-height: 72px;
  --ptm-container-max-width: 1200px;

  /* 动画系统 */
  --ptm-duration-fast: 0.15s;
  --ptm-duration-normal: 0.3s;
  --ptm-duration-slow: 0.5s;
  --ptm-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index 层级 */
  --ptm-z-base: 0;
  --ptm-z-dropdown: 1000;
  --ptm-z-sticky: 1020;
  --ptm-z-fixed: 1030;
  --ptm-z-modal: 1040;
  --ptm-z-popover: 1050;
  --ptm-z-tooltip: 1060;
  --ptm-z-toast: 1070;
}

/* 重置Obsidian的全局样式影响 */
.ptm-app {
  /* 确保盒模型一致性 */
  * {
    box-sizing: border-box !important;
  }
  
  /* 重置字体，确保不被Obsidian主题覆盖 */
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-secondary) !important;
  line-height: var(--ptm-leading-normal) !important;
  
  /* 重置标题样式，避免被Obsidian主题影响 */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    margin: 0 !important;
    padding: 0 !important;
    font-weight: var(--ptm-font-semibold) !important;
    line-height: var(--ptm-leading-tight) !important;
  }
  
  /* 重置段落样式 */
  p {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: var(--ptm-leading-normal) !important;
  }
  
  /* 重置按钮样式 */
  button {
    font-family: var(--ptm-font-family) !important;
    border: none !important;
    background: none !important;
    cursor: pointer !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* 重置输入框样式 */
  input, textarea, select {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    background: var(--ptm-bg-primary) !important;
    border: 1px solid var(--ptm-border-normal) !important;
  }
  
  /* 重置链接样式 */
  a {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-primary) !important;
    text-decoration: none !important;
  }
  
  /* 重置列表样式 */
  ul, ol, li {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
  }
  
  /* 重置表格样式 */
  table, th, td {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    border-collapse: collapse !important;
  }
  
  /* 重置代码样式 */
  code, pre {
    font-family: var(--ptm-font-mono) !important;
    color: var(--ptm-text-primary) !important;
    background: var(--ptm-bg-tertiary) !important;
  }
}

/* 滚动条样式，确保在所有主题下一致 */
.ptm-app ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ptm-app ::-webkit-scrollbar-track {
  background: var(--ptm-gray-100);
  border-radius: var(--ptm-radius-sm);
}

.ptm-app ::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.ptm-app ::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* 焦点样式，确保可访问性 */
.ptm-app *:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

/* 选择文本样式 */
.ptm-app ::selection {
  background: rgba(102, 126, 234, 0.2) !important;
  color: var(--ptm-text-primary) !important;
}/**
 * 全局样式和CSS样式隔离
 * 确保PTM插件样式不受Obsidian主题影响
 */

/* CSS自定义属性定义 */
.ptm-app {
  /* 颜色系统 */
  --ptm-primary: #667eea;
  --ptm-primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ptm-secondary: #f7fafc;
  --ptm-accent: #48bb78;
  --ptm-warning: #ed8936;
  --ptm-danger: #f56565;
  --ptm-info: #3182ce;
  
  /* 灰度色阶 */
  --ptm-gray-50: #f9fafb;
  --ptm-gray-100: #f3f4f6;
  --ptm-gray-200: #e5e7eb;
  --ptm-gray-300: #d1d5db;
  --ptm-gray-400: #9ca3af;
  --ptm-gray-500: #6b7280;
  --ptm-gray-600: #4b5563;
  --ptm-gray-700: #374151;
  --ptm-gray-800: #1f2937;
  --ptm-gray-900: #111827;
  
  /* 语义化颜色 */
  --ptm-success: #48bb78;
  --ptm-error: #f56565;
  
  /* 背景色 */
  --ptm-bg-primary: #ffffff;
  --ptm-bg-secondary: #f7fafc;
  --ptm-bg-tertiary: #f3f4f6;
  
  /* 文本颜色 */
  --ptm-text-primary: #1f2937;
  --ptm-text-secondary: #4b5563;
  --ptm-text-muted: #6b7280;
  --ptm-text-inverse: #ffffff;
  
  /* 边框颜色 */
  --ptm-border-light: #e5e7eb;
  --ptm-border-normal: #d1d5db;
  --ptm-border-dark: #9ca3af;
  
  /* 字体系统 */
  --ptm-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  --ptm-font-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  /* 字体大小 */
  --ptm-text-xs: 12px;
  --ptm-text-sm: 14px;
  --ptm-text-base: 16px;
  --ptm-text-lg: 18px;
  --ptm-text-xl: 20px;
  --ptm-text-2xl: 24px;
  --ptm-text-3xl: 32px;
  --ptm-text-4xl: 40px;
  
  /* 间距系统 */
  --ptm-spacing-1: 4px;
  --ptm-spacing-2: 8px;
  --ptm-spacing-3: 12px;
  --ptm-spacing-4: 16px;
  --ptm-spacing-5: 20px;
  --ptm-spacing-6: 24px;
  --ptm-spacing-7: 28px;
  --ptm-spacing-8: 32px;
  --ptm-spacing-10: 40px;
  --ptm-spacing-12: 48px;
  --ptm-spacing-16: 64px;
  --ptm-spacing-20: 80px;
  --ptm-spacing-24: 96px;
  
  /* 圆角系统 */
  --ptm-radius-sm: 6px;
  --ptm-radius-base: 8px;
  --ptm-radius-md: 12px;
  --ptm-radius-lg: 16px;
  --ptm-radius-xl: 20px;
  --ptm-radius-full: 9999px;
  
  /* 阴影系统 */
  --ptm-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --ptm-shadow-base: 0 4px 12px rgba(0, 0, 0, 0.08);
  --ptm-shadow-md: 0 8px 24px rgba(0, 0, 0, 0.12);
  --ptm-shadow-lg: 0 16px 40px rgba(0, 0, 0, 0.16);
  --ptm-shadow-xl: 0 24px 48px rgba(0, 0, 0, 0.2);
  
  /* 布局系统 */
  --ptm-sidebar-width: 280px;
  --ptm-sidebar-collapsed-width: 64px;
  --ptm-right-panel-width: 320px;
  --ptm-header-height: 72px;
  
  /* 动画系统 */
  --ptm-duration-fast: 0.15s;
  --ptm-duration-normal: 0.3s;
  --ptm-duration-slow: 0.5s;
  --ptm-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index层级 */
  --ptm-z-dropdown: 1000;
  --ptm-z-modal: 1040;
  --ptm-z-tooltip: 1060;
}

/* 基础重置和样式隔离 */
.ptm-app {
  /* 重置所有子元素的盒模型 */
  * {
    box-sizing: border-box !important;
  }
  
  /* 确保字体不被Obsidian主题覆盖 */
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-primary) !important;
  line-height: 1.6 !important;
  font-size: var(--ptm-text-base) !important;
  
  /* 重置标题样式 */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    margin: 0 !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
  }
  
  h1 { font-size: var(--ptm-text-3xl) !important; }
  h2 { font-size: var(--ptm-text-2xl) !important; }
  h3 { font-size: var(--ptm-text-xl) !important; }
  h4 { font-size: var(--ptm-text-lg) !important; }
  h5 { font-size: var(--ptm-text-base) !important; }
  h6 { font-size: var(--ptm-text-sm) !important; }
  
  /* 重置段落样式 */
  p {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    margin: 0 !important;
    line-height: 1.6 !important;
  }
  
  /* 重置按钮样式 */
  button {
    font-family: var(--ptm-font-family) !important;
    border: none !important;
    background: none !important;
    cursor: pointer !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* 重置输入框样式 */
  input, textarea, select {
    font-family: var(--ptm-font-family) !important;
    color: var(--ptm-text-primary) !important;
    border: 1px solid var(--ptm-border-normal) !important;
    background: var(--ptm-bg-primary) !important;
    outline: none !important;
  }
  
  input:focus, textarea:focus, select:focus {
    border-color: var(--ptm-primary) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  }
  
  /* 重置链接样式 */
  a {
    color: var(--ptm-primary) !important;
    text-decoration: none !important;
  }
  
  a:hover {
    text-decoration: underline !important;
  }
  
  /* 重置列表样式 */
  ul, ol {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* 重置表格样式 */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
  }
  
  th, td {
    text-align: left !important;
    padding: var(--ptm-spacing-2) !important;
    border-bottom: 1px solid var(--ptm-border-light) !important;
  }
  
  th {
    font-weight: 600 !important;
    color: var(--ptm-text-primary) !important;
    background: var(--ptm-bg-secondary) !important;
  }
}

/* 滚动条样式 */
.ptm-app ::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
}

.ptm-app ::-webkit-scrollbar-track {
  background: var(--ptm-gray-100) !important;
  border-radius: var(--ptm-radius-sm) !important;
}

.ptm-app ::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300) !important;
  border-radius: var(--ptm-radius-sm) !important;
}

.ptm-app ::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400) !important;
}

/* Firefox滚动条 */
.ptm-app * {
  scrollbar-width: thin !important;
  scrollbar-color: var(--ptm-gray-300) var(--ptm-gray-100) !important;
}

/* 焦点样式 */
.ptm-app *:focus-visible {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

/* 选择文本样式 */
.ptm-app ::selection {
  background: rgba(102, 126, 234, 0.2) !important;
  color: var(--ptm-text-primary) !important;
}

/* 加载动画 */
@keyframes ptm-spin {
  to { transform: rotate(360deg); }
}

@keyframes ptm-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes ptm-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes ptm-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes ptm-slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes ptm-slide-in-left {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 工具类 */
.ptm-app .ptm-sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.ptm-app .ptm-not-sr-only {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* 响应式工具类 */
@media (max-width: 480px) {
  .ptm-app {
    --ptm-sidebar-width: 64px;
    --ptm-right-panel-width: 0px;
  }
}

@media (max-width: 768px) {
  .ptm-app {
    --ptm-right-panel-width: 280px;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .ptm-app * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-app {
    --ptm-border-light: #000000;
    --ptm-border-normal: #000000;
    --ptm-border-dark: #000000;
  }
}