// 简化的甘特图测试组件

import React, { useState, useEffect } from 'react';
import { Gantt, Task, ViewMode } from 'gantt-task-react';
import 'gantt-task-react/dist/index.css';

export const SimpleGanttTest: React.FC = () => {
	const [tasks, setTasks] = useState<Task[]>([]);

	useEffect(() => {
		// 创建简单的测试数据
		const testTasks: Task[] = [
			{
				id: '1',
				name: '项目规划',
				start: new Date(2025, 0, 1), // 2025年1月1日
				end: new Date(2025, 0, 5),   // 2025年1月5日
				progress: 100,
				type: 'task'
			},
			{
				id: '2',
				name: '需求分析',
				start: new Date(2025, 0, 6),
				end: new Date(2025, 0, 15),
				progress: 75,
				type: 'task',
				dependencies: ['1']
			},
			{
				id: '3',
				name: '系统设计',
				start: new Date(2025, 0, 16),
				end: new Date(2025, 0, 25),
				progress: 50,
				type: 'task',
				dependencies: ['2']
			},
			{
				id: '4',
				name: '开发实现',
				start: new Date(2025, 0, 26),
				end: new Date(2025, 1, 15),
				progress: 25,
				type: 'task',
				dependencies: ['3']
			},
			{
				id: '5',
				name: '测试验证',
				start: new Date(2025, 1, 16),
				end: new Date(2025, 1, 28),
				progress: 0,
				type: 'task',
				dependencies: ['4']
			}
		];

		console.log('简单甘特图测试数据:', testTasks);
		setTasks(testTasks);
	}, []);

	const handleDateChange = (task: Task, children: Task[]) => {
		console.log('日期变更:', task.id, task.start, task.end);
	};

	const handleProgressChange = (task: Task, children: Task[]) => {
		console.log('进度变更:', task.id, task.progress);
	};

	const handleTaskSelect = (task: Task, isSelected: boolean) => {
		console.log('任务选择:', task.id, isSelected);
	};

	return (
		<div style={{ 
			width: '100%', 
			height: '500px', 
			padding: '20px',
			boxSizing: 'border-box'
		}}>
			<h3 style={{ 
				margin: '0 0 20px 0'
			}}>
				简单甘特图测试 ({tasks.length} 个任务)
			</h3>
			
			{tasks.length > 0 ? (
				<div style={{ 
					height: '400px'
				}}>
					<Gantt
						tasks={tasks}
						viewMode={ViewMode.Day}
						onDateChange={handleDateChange}
						onProgressChange={handleProgressChange}
						onSelect={handleTaskSelect}
						listCellWidth="200px"
						columnWidth={80}
						rowHeight={40}
						headerHeight={50}
						ganttHeight={350}
						barCornerRadius={3}
						handleWidth={8}
						fontFamily="Arial, sans-serif"
						fontSize="12px"
						barFill={60}
						barProgressColor="#4CAF50"
						barProgressSelectedColor="#45a049"
						barBackgroundColor="#e0e0e0"
						barBackgroundSelectedColor="#d0d0d0"
						projectProgressColor="#2196F3"
						projectProgressSelectedColor="#1976D2"
						projectBackgroundColor="#FFC107"
						projectBackgroundSelectedColor="#FF9800"
						milestoneBackgroundColor="#9C27B0"
						milestoneBackgroundSelectedColor="#7B1FA2"
						rtl={false}
					/>
				</div>
			) : (
				<div style={{ 
					textAlign: 'center',
					padding: '50px'
				}}>
					正在加载测试数据...
				</div>
			)}
		</div>
	);
};