// 甘特图主组件

import React, { useState, useEffect, useCallback } from 'react';
import { Gantt, Task, ViewMode, EventOption } from 'gantt-task-react';
import 'gantt-task-react/dist/index.css';
import { GanttTask, GanttViewMode, CriticalPathAnalysis, ResourceConflict } from '../../../models/Gantt';
import { GanttManager } from '../../../services/GanttManager';
import { GanttToolbar } from './GanttToolbar';
import { GanttSidebar } from './GanttSidebar';
import { GanttDebugInfo } from './GanttDebugInfo';
import { GanttDiagnostic } from './GanttDiagnostic';
import { Notice } from 'obsidian';

// 错误边界组件
class GanttErrorBoundary extends React.Component<
	{ children: React.ReactNode },
	{ hasError: boolean; error?: Error }
> {
	constructor(props: { children: React.ReactNode }) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error) {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		console.error('甘特图组件错误:', error, errorInfo);
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="gantt-error-boundary">
					<h3>甘特图加载失败</h3>
					<p>请检查任务数据是否完整，特别是日期字段。</p>
					<details>
						<summary>错误详情</summary>
						<pre>{this.state.error?.message}</pre>
					</details>
					<button onClick={() => this.setState({ hasError: false })}>
						重试
					</button>
				</div>
			);
		}

		return this.props.children;
	}
}

interface GanttChartProps {
	projectId: string;
	ganttManager: GanttManager;
	onTaskSelect?: (taskId: string) => void;
	onTaskUpdate?: (taskId: string) => void;
}

export const GanttChart: React.FC<GanttChartProps> = ({
	projectId,
	ganttManager,
	onTaskSelect,
	onTaskUpdate
}) => {
	const [tasks, setTasks] = useState<Task[]>([]);
	const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.Day);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [criticalPath, setCriticalPath] = useState<CriticalPathAnalysis | null>(null);
	const [conflicts, setConflicts] = useState<ResourceConflict[]>([]);
	const [showCriticalPath, setShowCriticalPath] = useState(false);
	const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
	const [showDebugInfo, setShowDebugInfo] = useState(false);
	const [showDiagnostic, setShowDiagnostic] = useState(false);
	const [totalTaskCount, setTotalTaskCount] = useState(0);

	// 加载甘特图数据
	const loadGanttData = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			const ganttTasks = await ganttManager.getProjectGanttData(projectId);
			
			// 验证和清理数据
			const validTasks = ganttTasks.filter(task => {
				// 检查必需字段
				if (!task.id || !task.name) {
					console.warn('跳过无效任务:', task);
					return false;
				}
				
				// 检查日期字段
				if (!task.start || !task.end || isNaN(task.start.getTime()) || isNaN(task.end.getTime())) {
					console.warn('跳过日期无效的任务:', task);
					return false;
				}
				
				return true;
			});

			// 限制任务数量以避免性能问题
			const maxTasks = 50; // 限制最多显示50个任务
			const limitedTasks = validTasks.slice(0, maxTasks);
			
			if (validTasks.length > maxTasks) {
				console.warn(`任务数量过多 (${validTasks.length})，只显示前 ${maxTasks} 个任务`);
			}

			// 转换为gantt-task-react所需的格式
			const formattedTasks: Task[] = limitedTasks.map(task => ({
				id: task.id,
				name: task.name,
				start: task.start,
				end: task.end,
				progress: Math.max(0, Math.min(100, task.progress || 0)), // 确保进度在0-100之间
				type: task.type as any,
				project: task.project,
				dependencies: task.dependencies || [],
				hideChildren: task.hideChildren,
				displayOrder: task.displayOrder,
				styles: task.styles
			}));

			console.log(`甘特图数据加载成功: ${formattedTasks.length} 个任务 (总共 ${validTasks.length} 个有效任务)`);
			setTasks(formattedTasks);
			setTotalTaskCount(validTasks.length);
		} catch (err) {
			console.error('加载甘特图数据失败:', err);
			setError(err instanceof Error ? err.message : '加载数据失败');
			new Notice('加载甘特图数据失败');
		} finally {
			setIsLoading(false);
		}
	}, [projectId, ganttManager]);

	// 加载关键路径分析
	const loadCriticalPath = useCallback(async () => {
		try {
			const analysis = await ganttManager.calculateCriticalPath(projectId);
			setCriticalPath(analysis);
		} catch (err) {
			console.error('计算关键路径失败:', err);
		}
	}, [projectId, ganttManager]);

	// 加载资源冲突检测
	const loadConflicts = useCallback(async () => {
		try {
			const detectedConflicts = await ganttManager.detectResourceConflicts(projectId);
			setConflicts(detectedConflicts);
		} catch (err) {
			console.error('检测资源冲突失败:', err);
		}
	}, [projectId, ganttManager]);

	// 初始化加载
	useEffect(() => {
		loadGanttData();
		loadCriticalPath();
		loadConflicts();
	}, [loadGanttData, loadCriticalPath, loadConflicts]);

	// 处理任务日期变更
	const handleDateChange = async (task: Task, children: Task[]) => {
		try {
			await ganttManager.updateTaskTiming(task.id, task.start, task.end);
			
			// 重新加载数据
			await loadGanttData();
			await loadCriticalPath();
			
			onTaskUpdate?.(task.id);
			new Notice('任务时间已更新');
		} catch (err) {
			console.error('更新任务时间失败:', err);
			new Notice('更新任务时间失败');
		}
	};

	// 处理任务进度变更
	const handleProgressChange = async (task: Task, children: Task[]) => {
		try {
			await ganttManager.updateTaskProgress(task.id, task.progress);
			
			// 重新加载数据
			await loadGanttData();
			
			onTaskUpdate?.(task.id);
			new Notice('任务进度已更新');
		} catch (err) {
			console.error('更新任务进度失败:', err);
			new Notice('更新任务进度失败');
		}
	};

	// 处理任务选择
	const handleTaskSelect = (task: Task, isSelected: boolean) => {
		if (isSelected) {
			setSelectedTaskId(task.id);
			onTaskSelect?.(task.id);
		} else {
			setSelectedTaskId(null);
		}
	};

	// 处理视图模式切换
	const handleViewModeChange = (mode: GanttViewMode) => {
		const viewModeMap: Record<GanttViewMode, ViewMode> = {
			[GanttViewMode.QUARTER_DAY]: ViewMode.QuarterDay,
			[GanttViewMode.HALF_DAY]: ViewMode.HalfDay,
			[GanttViewMode.DAY]: ViewMode.Day,
			[GanttViewMode.WEEK]: ViewMode.Week,
			[GanttViewMode.MONTH]: ViewMode.Month,
			[GanttViewMode.YEAR]: ViewMode.Year
		};
		
		setViewMode(viewModeMap[mode]);
		ganttManager.setViewMode(mode);
	};

	// 切换关键路径显示
	const toggleCriticalPath = () => {
		setShowCriticalPath(!showCriticalPath);
	};

	// 切换调试信息显示
	const toggleDebugInfo = () => {
		setShowDebugInfo(!showDebugInfo);
	};

	// 切换诊断工具显示
	const toggleDiagnostic = () => {
		setShowDiagnostic(!showDiagnostic);
	};

	// 导出甘特图数据
	const handleExport = async (format: 'json' | 'csv') => {
		try {
			const data = await ganttManager.exportGanttData(projectId, format);
			
			// 创建下载链接
			const blob = new Blob([data], { 
				type: format === 'json' ? 'application/json' : 'text/csv' 
			});
			const url = URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `gantt-${projectId}.${format}`;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
			URL.revokeObjectURL(url);
			
			new Notice(`甘特图数据已导出为 ${format.toUpperCase()} 格式`);
		} catch (err) {
			console.error('导出失败:', err);
			new Notice('导出失败');
		}
	};

	// 应用关键路径样式
	const getTaskStyles = (task: Task) => {
		if (showCriticalPath && criticalPath?.criticalTasks.includes(task.id)) {
			return {
				...task.styles,
				backgroundColor: '#ff6b6b',
				backgroundSelectedColor: '#ff5252'
			};
		}
		return task.styles;
	};

	// 渲染加载状态
	if (isLoading) {
		return (
			<div className="gantt-loading">
				<div className="loading-spinner"></div>
				<p>正在加载甘特图...</p>
			</div>
		);
	}

	// 渲染错误状态
	if (error) {
		return (
			<div className="gantt-error">
				<p>加载甘特图失败: {error}</p>
				<button onClick={loadGanttData}>重试</button>
			</div>
		);
	}

	return (
		<GanttErrorBoundary>
			<div style={{ width: '100%', height: '100%' }}>
				<GanttToolbar
					viewMode={viewMode}
					onViewModeChange={handleViewModeChange}
					showCriticalPath={showCriticalPath}
					onToggleCriticalPath={toggleCriticalPath}
					onExport={handleExport}
					onRefresh={loadGanttData}
					showDebugInfo={showDebugInfo}
					onToggleDebugInfo={toggleDebugInfo}
					taskCount={tasks.length}
					totalTaskCount={totalTaskCount}
					showDiagnostic={showDiagnostic}
					onToggleDiagnostic={toggleDiagnostic}
				/>
				
				<div style={{ display: 'flex', height: 'calc(100% - 60px)' }}>
					<div style={{ flex: 1, overflow: 'hidden' }}>
						{tasks.length > 0 ? (
							<Gantt
								tasks={tasks.map(task => ({
									...task,
									styles: getTaskStyles(task)
								}))}
								viewMode={viewMode}
								onDateChange={handleDateChange}
								onProgressChange={handleProgressChange}
								onSelect={handleTaskSelect}
								listCellWidth="250px"
								columnWidth={80}
								rowHeight={40}
								headerHeight={50}
								ganttHeight={400}
								barCornerRadius={3}
								handleWidth={8}
								fontFamily="Arial, sans-serif"
								fontSize="12px"
								barFill={60}
								barProgressColor="#4CAF50"
								barProgressSelectedColor="#45a049"
								barBackgroundColor="#e0e0e0"
								barBackgroundSelectedColor="#d0d0d0"
								projectProgressColor="#2196F3"
								projectProgressSelectedColor="#1976D2"
								projectBackgroundColor="#FFC107"
								projectBackgroundSelectedColor="#FF9800"
								milestoneBackgroundColor="#9C27B0"
								milestoneBackgroundSelectedColor="#7B1FA2"
								rtl={false}
							/>
						) : (
							<div style={{ 
								textAlign: 'center', 
								padding: '40px'
							}}>
								<p>当前项目没有任务数据</p>
								<p>请先创建一些任务以查看甘特图</p>
							</div>
						)}
					</div>
					
					<GanttSidebar
						selectedTaskId={selectedTaskId}
						criticalPath={criticalPath}
						conflicts={conflicts}
						onTaskSelect={onTaskSelect}
					/>
				</div>

				<GanttDebugInfo
					tasks={tasks.map(task => ({
						id: task.id,
						name: task.name,
						start: task.start,
						end: task.end,
						progress: task.progress,
						type: task.type as any,
						dependencies: task.dependencies
					}))}
					criticalPath={criticalPath}
					conflicts={conflicts}
					isVisible={showDebugInfo}
				/>

				<GanttDiagnostic
					isVisible={showDiagnostic}
					tasks={tasks}
				/>
			</div>
		</GanttErrorBoundary>
	);
};