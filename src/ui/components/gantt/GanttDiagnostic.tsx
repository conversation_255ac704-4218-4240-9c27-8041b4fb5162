// 甘特图诊断工具

import React, { useEffect, useState } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react';

interface GanttDiagnosticProps {
	isVisible: boolean;
	tasks: any[];
}

interface DiagnosticResult {
	category: string;
	status: 'success' | 'warning' | 'error' | 'info';
	message: string;
	details?: string;
}

export const GanttDiagnostic: React.FC<GanttDiagnosticProps> = ({
	isVisible,
	tasks
}) => {
	const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);

	useEffect(() => {
		if (isVisible) {
			runDiagnostics();
		}
	}, [isVisible, tasks]);

	const runDiagnostics = () => {
		const results: DiagnosticResult[] = [];

		// 检查任务数据
		results.push({
			category: '数据检查',
			status: tasks.length > 0 ? 'success' : 'error',
			message: `任务数量: ${tasks.length}`,
			details: tasks.length === 0 ? '没有任务数据' : `共有 ${tasks.length} 个任务`
		});

		// 检查任务字段
		if (tasks.length > 0) {
			const firstTask = tasks[0];
			const requiredFields = ['id', 'name', 'start', 'end', 'progress', 'type'];
			const missingFields = requiredFields.filter(field => !firstTask[field]);
			
			results.push({
				category: '字段检查',
				status: missingFields.length === 0 ? 'success' : 'error',
				message: `必需字段检查`,
				details: missingFields.length === 0 ? 
					'所有必需字段都存在' : 
					`缺少字段: ${missingFields.join(', ')}`
			});
		}

		// 检查日期有效性
		if (tasks.length > 0) {
			const invalidDates = tasks.filter(task => 
				!task.start || !task.end || 
				isNaN(task.start.getTime()) || 
				isNaN(task.end.getTime())
			);

			results.push({
				category: '日期检查',
				status: invalidDates.length === 0 ? 'success' : 'warning',
				message: `日期有效性检查`,
				details: invalidDates.length === 0 ? 
					'所有任务日期都有效' : 
					`${invalidDates.length} 个任务有无效日期`
			});
		}

		// 检查甘特图库
		const ganttLibraryLoaded = typeof window !== 'undefined' && 
			document.querySelector('.gantt-task-react') !== null;

		results.push({
			category: '库检查',
			status: ganttLibraryLoaded ? 'success' : 'warning',
			message: '甘特图库加载状态',
			details: ganttLibraryLoaded ? 
				'甘特图库已加载' : 
				'甘特图库可能未正确加载'
		});

		// 检查CSS样式
		const ganttContainer = document.querySelector('.gantt-main');
		const hasStyles = ganttContainer && 
			window.getComputedStyle(ganttContainer).minHeight !== 'auto';

		results.push({
			category: '样式检查',
			status: hasStyles ? 'success' : 'warning',
			message: 'CSS样式应用状态',
			details: hasStyles ? 
				'样式已正确应用' : 
				'样式可能未正确应用'
		});

		// 检查控制台错误
		const hasConsoleErrors = window.console && 
			typeof window.console.error === 'function';

		results.push({
			category: '错误检查',
			status: 'info',
			message: '控制台错误检查',
			details: '请检查浏览器控制台是否有相关错误信息'
		});

		setDiagnostics(results);
	};

	const getStatusIcon = (status: DiagnosticResult['status']) => {
		switch (status) {
			case 'success':
				return <CheckCircle size={16} className="status-success" />;
			case 'warning':
				return <AlertTriangle size={16} className="status-warning" />;
			case 'error':
				return <XCircle size={16} className="status-error" />;
			case 'info':
			default:
				return <Info size={16} className="status-info" />;
		}
	};

	if (!isVisible) return null;

	return (
		<div className="gantt-diagnostic">
			<div className="gantt-diagnostic-header">
				<h4>甘特图诊断报告</h4>
				<button onClick={runDiagnostics} className="diagnostic-refresh">
					刷新诊断
				</button>
			</div>
			
			<div className="gantt-diagnostic-content">
				{diagnostics.map((result, index) => (
					<div key={index} className={`diagnostic-item diagnostic-${result.status}`}>
						<div className="diagnostic-header">
							{getStatusIcon(result.status)}
							<span className="diagnostic-category">{result.category}</span>
							<span className="diagnostic-message">{result.message}</span>
						</div>
						{result.details && (
							<div className="diagnostic-details">
								{result.details}
							</div>
						)}
					</div>
				))}
			</div>

			<div className="gantt-diagnostic-actions">
				<button onClick={() => {
					console.log('甘特图诊断结果:', diagnostics);
					console.log('任务数据:', tasks);
				}}>
					输出到控制台
				</button>
			</div>
		</div>
	);
};