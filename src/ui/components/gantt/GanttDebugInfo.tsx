// 甘特图调试信息组件

import React, { useState } from 'react';
import { GanttTask, CriticalPathAnalysis, ResourceConflict } from '../../../models/Gantt';
import { ChevronDown, ChevronRight, Info } from 'lucide-react';

interface GanttDebugInfoProps {
	tasks: GanttTask[];
	criticalPath: CriticalPathAnalysis | null;
	conflicts: ResourceConflict[];
	isVisible?: boolean;
}

export const GanttDebugInfo: React.FC<GanttDebugInfoProps> = ({
	tasks,
	criticalPath,
	conflicts,
	isVisible = false
}) => {
	const [expanded, setExpanded] = useState(false);
	const [activeTab, setActiveTab] = useState<'tasks' | 'critical' | 'conflicts'>('tasks');

	if (!isVisible) {
		return null;
	}

	const validTasks = tasks.filter(task => 
		task.start && task.end && 
		!isNaN(task.start.getTime()) && 
		!isNaN(task.end.getTime())
	);

	const invalidTasks = tasks.filter(task => 
		!task.start || !task.end || 
		isNaN(task.start.getTime()) || 
		isNaN(task.end.getTime())
	);

	return (
		<div className="gantt-debug-info">
			<div 
				className="gantt-debug-header"
				onClick={() => setExpanded(!expanded)}
			>
				{expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
				<Info size={16} />
				<span>调试信息</span>
				<span className="gantt-debug-badge">
					{validTasks.length}/{tasks.length} 有效任务
				</span>
			</div>

			{expanded && (
				<div className="gantt-debug-content">
					<div className="gantt-debug-tabs">
						<button 
							className={`gantt-debug-tab ${activeTab === 'tasks' ? 'active' : ''}`}
							onClick={() => setActiveTab('tasks')}
						>
							任务数据 ({tasks.length})
						</button>
						<button 
							className={`gantt-debug-tab ${activeTab === 'critical' ? 'active' : ''}`}
							onClick={() => setActiveTab('critical')}
						>
							关键路径 ({criticalPath?.criticalTasks.length || 0})
						</button>
						<button 
							className={`gantt-debug-tab ${activeTab === 'conflicts' ? 'active' : ''}`}
							onClick={() => setActiveTab('conflicts')}
						>
							冲突检测 ({conflicts.length})
						</button>
					</div>

					<div className="gantt-debug-panel">
						{activeTab === 'tasks' && (
							<div className="gantt-debug-tasks">
								<div className="gantt-debug-summary">
									<div className="gantt-debug-stat">
										<span className="label">总任务数:</span>
										<span className="value">{tasks.length}</span>
									</div>
									<div className="gantt-debug-stat">
										<span className="label">有效任务:</span>
										<span className="value success">{validTasks.length}</span>
									</div>
									<div className="gantt-debug-stat">
										<span className="label">无效任务:</span>
										<span className="value error">{invalidTasks.length}</span>
									</div>
								</div>

								{invalidTasks.length > 0 && (
									<div className="gantt-debug-invalid">
										<h4>⚠️ 无效任务数据:</h4>
										{invalidTasks.map(task => (
											<div key={task.id} className="gantt-debug-task-item error">
												<div className="task-name">{task.name}</div>
												<div className="task-issues">
													{!task.start && <span className="issue">缺少开始时间</span>}
													{!task.end && <span className="issue">缺少结束时间</span>}
													{task.start && isNaN(task.start.getTime()) && <span className="issue">开始时间无效</span>}
													{task.end && isNaN(task.end.getTime()) && <span className="issue">结束时间无效</span>}
												</div>
											</div>
										))}
									</div>
								)}

								<div className="gantt-debug-valid">
									<h4>✅ 有效任务数据:</h4>
									{validTasks.slice(0, 5).map(task => (
										<div key={task.id} className="gantt-debug-task-item">
											<div className="task-name">{task.name}</div>
											<div className="task-details">
												<span>开始: {task.start.toLocaleDateString()}</span>
												<span>结束: {task.end.toLocaleDateString()}</span>
												<span>进度: {task.progress}%</span>
												<span>依赖: {task.dependencies?.length || 0}</span>
											</div>
										</div>
									))}
									{validTasks.length > 5 && (
										<div className="gantt-debug-more">
											...还有 {validTasks.length - 5} 个任务
										</div>
									)}
								</div>
							</div>
						)}

						{activeTab === 'critical' && (
							<div className="gantt-debug-critical">
								{criticalPath ? (
									<>
										<div className="gantt-debug-summary">
											<div className="gantt-debug-stat">
												<span className="label">项目工期:</span>
												<span className="value">{criticalPath.totalDuration} 天</span>
											</div>
											<div className="gantt-debug-stat">
												<span className="label">关键任务:</span>
												<span className="value">{criticalPath.criticalTasks.length}</span>
											</div>
										</div>
										<div className="gantt-debug-critical-tasks">
											{criticalPath.criticalTasks.map(taskId => (
												<div key={taskId} className="gantt-debug-critical-task">
													<span className="task-id">{taskId}</span>
													<span className="slack">松弛: {criticalPath.slack.get(taskId) || 0} 天</span>
												</div>
											))}
										</div>
									</>
								) : (
									<div className="gantt-debug-empty">暂无关键路径数据</div>
								)}
							</div>
						)}

						{activeTab === 'conflicts' && (
							<div className="gantt-debug-conflicts">
								{conflicts.length > 0 ? (
									conflicts.map((conflict, index) => (
										<div key={index} className="gantt-debug-conflict">
											<div className="conflict-header">
												<span className={`conflict-severity ${conflict.severity}`}>
													{conflict.severity}
												</span>
												<span className="conflict-resource">{conflict.resource}</span>
											</div>
											<div className="conflict-tasks">
												<span>任务: {conflict.taskId}</span>
												<span>冲突: {conflict.conflictingTaskIds.join(', ')}</span>
											</div>
											<div className="conflict-type">{conflict.conflictType}</div>
										</div>
									))
								) : (
									<div className="gantt-debug-empty">暂无资源冲突</div>
								)}
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
};