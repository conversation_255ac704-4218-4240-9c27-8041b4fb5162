// 甘特图侧边栏组件

import React, { useState } from 'react';
import { CriticalPathAnalysis, ResourceConflict, ConflictSeverity } from '../../../models/Gantt';
import { 
	AlertTriangle, 
	Clock, 
	Navigation, 
	Users, 
	Calendar,
	TrendingUp,
	ChevronDown,
	ChevronRight
} from 'lucide-react';

interface GanttSidebarProps {
	selectedTaskId: string | null;
	criticalPath: CriticalPathAnalysis | null;
	conflicts: ResourceConflict[];
	onTaskSelect?: (taskId: string) => void;
}

export const GanttSidebar: React.FC<GanttSidebarProps> = ({
	selectedTaskId,
	criticalPath,
	conflicts,
	onTaskSelect
}) => {
	const [expandedSections, setExpandedSections] = useState<Set<string>>(
		new Set(['critical-path', 'conflicts'])
	);

	const toggleSection = (sectionId: string) => {
		const newExpanded = new Set(expandedSections);
		if (newExpanded.has(sectionId)) {
			newExpanded.delete(sectionId);
		} else {
			newExpanded.add(sectionId);
		}
		setExpandedSections(newExpanded);
	};

	const getSeverityColor = (severity: ConflictSeverity): string => {
		switch (severity) {
			case ConflictSeverity.CRITICAL: return '#ff4757';
			case ConflictSeverity.HIGH: return '#ff6b35';
			case ConflictSeverity.MEDIUM: return '#ffa502';
			case ConflictSeverity.LOW: return '#f1c40f';
			default: return '#95a5a6';
		}
	};

	const getSeverityLabel = (severity: ConflictSeverity): string => {
		switch (severity) {
			case ConflictSeverity.CRITICAL: return '严重';
			case ConflictSeverity.HIGH: return '高';
			case ConflictSeverity.MEDIUM: return '中';
			case ConflictSeverity.LOW: return '低';
			default: return '未知';
		}
	};

	const formatDuration = (days: number): string => {
		if (days < 1) {
			return `${Math.round(days * 24)}小时`;
		} else if (days < 7) {
			return `${Math.round(days)}天`;
		} else if (days < 30) {
			return `${Math.round(days / 7)}周`;
		} else {
			return `${Math.round(days / 30)}个月`;
		}
	};

	return (
		<div className="gantt-sidebar">
			{/* 项目统计 */}
			{criticalPath && (
				<div className="gantt-sidebar-section">
					<div 
						className="gantt-sidebar-header"
						onClick={() => toggleSection('project-stats')}
					>
						{expandedSections.has('project-stats') ? 
							<ChevronDown size={16} /> : 
							<ChevronRight size={16} />
						}
						<TrendingUp size={16} />
						<span>项目统计</span>
					</div>
					
					{expandedSections.has('project-stats') && (
						<div className="gantt-sidebar-content">
							<div className="gantt-stat-item">
								<Clock size={14} />
								<span>总工期: {formatDuration(criticalPath.totalDuration)}</span>
							</div>
							<div className="gantt-stat-item">
								<Calendar size={14} />
								<span>开始: {criticalPath.earliestStart.toLocaleDateString()}</span>
							</div>
							<div className="gantt-stat-item">
								<Calendar size={14} />
								<span>结束: {criticalPath.latestFinish.toLocaleDateString()}</span>
							</div>
							<div className="gantt-stat-item">
								<Navigation size={14} />
								<span>关键任务: {criticalPath.criticalTasks.length}个</span>
							</div>
						</div>
					)}
				</div>
			)}

			{/* 关键路径 */}
			{criticalPath && criticalPath.criticalTasks.length > 0 && (
				<div className="gantt-sidebar-section">
					<div 
						className="gantt-sidebar-header"
						onClick={() => toggleSection('critical-path')}
					>
						{expandedSections.has('critical-path') ? 
							<ChevronDown size={16} /> : 
							<ChevronRight size={16} />
						}
						<Navigation size={16} />
						<span>关键路径</span>
						<span className="gantt-badge">{criticalPath.criticalTasks.length}</span>
					</div>
					
					{expandedSections.has('critical-path') && (
						<div className="gantt-sidebar-content">
							{criticalPath.criticalTasks.map(taskId => (
								<div 
									key={taskId}
									className={`gantt-task-item ${selectedTaskId === taskId ? 'selected' : ''}`}
									onClick={() => onTaskSelect?.(taskId)}
								>
									<div className="gantt-task-indicator critical"></div>
									<span className="gantt-task-id">{taskId}</span>
									<span className="gantt-task-slack">
										松弛: {criticalPath.slack.get(taskId) || 0}天
									</span>
								</div>
							))}
						</div>
					)}
				</div>
			)}

			{/* 资源冲突 */}
			{conflicts.length > 0 && (
				<div className="gantt-sidebar-section">
					<div 
						className="gantt-sidebar-header"
						onClick={() => toggleSection('conflicts')}
					>
						{expandedSections.has('conflicts') ? 
							<ChevronDown size={16} /> : 
							<ChevronRight size={16} />
						}
						<AlertTriangle size={16} />
						<span>资源冲突</span>
						<span className="gantt-badge error">{conflicts.length}</span>
					</div>
					
					{expandedSections.has('conflicts') && (
						<div className="gantt-sidebar-content">
							{conflicts.map((conflict, index) => (
								<div key={index} className="gantt-conflict-item">
									<div className="gantt-conflict-header">
										<div 
											className="gantt-conflict-severity"
											style={{ backgroundColor: getSeverityColor(conflict.severity) }}
										>
											{getSeverityLabel(conflict.severity)}
										</div>
										<Users size={14} />
										<span className="gantt-conflict-resource">{conflict.resource}</span>
									</div>
									
									<div className="gantt-conflict-tasks">
										<div 
											className="gantt-conflict-task"
											onClick={() => onTaskSelect?.(conflict.taskId)}
										>
											{conflict.taskId}
										</div>
										{conflict.conflictingTaskIds.map(taskId => (
											<div 
												key={taskId}
												className="gantt-conflict-task"
												onClick={() => onTaskSelect?.(taskId)}
											>
												{taskId}
											</div>
										))}
									</div>
									
									<div className="gantt-conflict-type">
										{conflict.conflictType === 'time_overlap' && '时间重叠'}
										{conflict.conflictType === 'resource_overallocation' && '资源过度分配'}
										{conflict.conflictType === 'dependency_violation' && '依赖关系冲突'}
									</div>
								</div>
							))}
						</div>
					)}
				</div>
			)}

			{/* 空状态 */}
			{(!criticalPath || criticalPath.criticalTasks.length === 0) && conflicts.length === 0 && (
				<div className="gantt-sidebar-empty">
					<Navigation size={48} />
					<p>暂无关键路径或冲突信息</p>
					<p>添加任务依赖关系以查看分析结果</p>
				</div>
			)}
		</div>
	);
};