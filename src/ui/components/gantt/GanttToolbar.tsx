// 甘特图工具栏组件

import React from 'react';
import { ViewMode } from 'gantt-task-react';
import { GanttViewMode } from '../../../models/Gantt';
import { 
	Calendar, 
	Download, 
	RefreshCw, 
	Navigation, 
	ZoomIn, 
	ZoomOut,
	Eye,
	EyeOff,
	Bug,
	Stethoscope
} from 'lucide-react';

interface GanttToolbarProps {
	viewMode: ViewMode;
	onViewModeChange: (mode: GanttViewMode) => void;
	showCriticalPath: boolean;
	onToggleCriticalPath: () => void;
	onExport: (format: 'json' | 'csv') => void;
	onRefresh: () => void;
	showDebugInfo?: boolean;
	onToggleDebugInfo?: () => void;
	taskCount?: number;
	totalTaskCount?: number;
	showDiagnostic?: boolean;
	onToggleDiagnostic?: () => void;
}

export const GanttToolbar: React.FC<GanttToolbarProps> = ({
	viewMode,
	onViewModeChange,
	showCriticalPath,
	onToggleCriticalPath,
	onExport,
	onRefresh,
	showDebugInfo = false,
	onToggleDebugInfo,
	taskCount = 0,
	totalTaskCount = 0,
	showDiagnostic = false,
	onToggleDiagnostic
}) => {
	const viewModeOptions = [
		{ value: GanttViewMode.DAY, label: '日视图', icon: Calendar },
		{ value: GanttViewMode.WEEK, label: '周视图', icon: Calendar },
		{ value: GanttViewMode.MONTH, label: '月视图', icon: Calendar },
		{ value: GanttViewMode.YEAR, label: '年视图', icon: Calendar }
	];

	const currentViewMode = (() => {
		switch (viewMode) {
			case ViewMode.Day: return GanttViewMode.DAY;
			case ViewMode.Week: return GanttViewMode.WEEK;
			case ViewMode.Month: return GanttViewMode.MONTH;
			case ViewMode.Year: return GanttViewMode.YEAR;
			default: return GanttViewMode.DAY;
		}
	})();

	return (
		<div className="gantt-toolbar">
			<div className="gantt-toolbar-section">
				<h3 className="gantt-toolbar-title">甘特图</h3>
				{totalTaskCount > 0 && (
					<div className="gantt-toolbar-task-count">
						{taskCount === totalTaskCount ? (
							<span>{taskCount} 个任务</span>
						) : (
							<span className="task-count-limited">
								显示 {taskCount} / {totalTaskCount} 个任务
								{totalTaskCount > 50 && (
									<span className="task-count-warning"> (已限制显示数量)</span>
								)}
							</span>
						)}
					</div>
				)}
			</div>

			<div className="gantt-toolbar-section">
				<div className="gantt-toolbar-group">
					<label className="gantt-toolbar-label">视图模式:</label>
					<select 
						className="gantt-toolbar-select"
						value={currentViewMode}
						onChange={(e) => onViewModeChange(e.target.value as GanttViewMode)}
					>
						{viewModeOptions.map(option => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
				</div>
			</div>

			<div className="gantt-toolbar-section">
				<button
					className={`gantt-toolbar-btn ${showCriticalPath ? 'active' : ''}`}
					onClick={onToggleCriticalPath}
					title={showCriticalPath ? '隐藏关键路径' : '显示关键路径'}
				>
					<Navigation size={16} />
					关键路径
				</button>
			</div>

			<div className="gantt-toolbar-section">
				<div className="gantt-toolbar-group">
					<button
						className="gantt-toolbar-btn"
						onClick={() => onExport('json')}
						title="导出为JSON"
					>
						<Download size={16} />
						JSON
					</button>
					<button
						className="gantt-toolbar-btn"
						onClick={() => onExport('csv')}
						title="导出为CSV"
					>
						<Download size={16} />
						CSV
					</button>
				</div>
			</div>

			<div className="gantt-toolbar-section">
				{onToggleDebugInfo && (
					<button
						className={`gantt-toolbar-btn ${showDebugInfo ? 'active' : ''}`}
						onClick={onToggleDebugInfo}
						title={showDebugInfo ? '隐藏调试信息' : '显示调试信息'}
					>
						<Bug size={16} />
						调试
					</button>
				)}
				{onToggleDiagnostic && (
					<button
						className={`gantt-toolbar-btn ${showDiagnostic ? 'active' : ''}`}
						onClick={onToggleDiagnostic}
						title={showDiagnostic ? '隐藏诊断工具' : '显示诊断工具'}
					>
						<Stethoscope size={16} />
						诊断
					</button>
				)}
				<button
					className="gantt-toolbar-btn"
					onClick={onRefresh}
					title="刷新数据"
				>
					<RefreshCw size={16} />
					刷新
				</button>
			</div>
		</div>
	);
};