// 甘特图测试组件

import React, { useState, useEffect } from 'react';
import { GanttChart } from './GanttChart';
import { GanttManager } from '../../../services/GanttManager';
import { TaskManager } from '../../../services/TaskManager';
import { ProjectManager } from '../../../services/ProjectManager';
import { Task, TaskStatus } from '../../../models/Task';
import { Priority } from '../../../types/core';

interface GanttTestProps {
	taskManager: TaskManager;
	projectManager: ProjectManager;
}

export const GanttTest: React.FC<GanttTestProps> = ({
	taskManager,
	projectManager
}) => {
	const [ganttManager, setGanttManager] = useState<GanttManager | null>(null);
	const [testProjectId, setTestProjectId] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		initializeTest();
	}, []);

	const initializeTest = async () => {
		try {
			setIsLoading(true);

			// 创建甘特图管理器
			const manager = new GanttManager(taskManager, projectManager);
			setGanttManager(manager);

			// 创建测试项目
			const testProject = await projectManager.createProject({
				name: '甘特图测试项目',
				description: '用于测试甘特图功能的项目',
				priority: Priority.HIGH,
				tags: ['test', 'gantt']
			});

			setTestProjectId(testProject.id);

			// 创建测试任务
			await createTestTasks(testProject.id);

		} catch (error) {
			console.error('初始化甘特图测试失败:', error);
		} finally {
			setIsLoading(false);
		}
	};

	const createTestTasks = async (projectId: string) => {
		const now = new Date();
		const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
		const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
		const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

		// 创建一系列有依赖关系的任务
		const task1 = await taskManager.createTask({
			title: '需求分析',
			description: '分析项目需求和功能规格',
			projectId,
			priority: Priority.HIGH,
			startDate: now,
			dueDate: tomorrow,
			assignee: '产品经理',
			tags: ['analysis', 'requirements']
		});

		// 更新任务状态
		await taskManager.updateTask(task1.id, {
			status: TaskStatus.COMPLETED
		});

		const task2 = await taskManager.createTask({
			title: '系统设计',
			description: '设计系统架构和数据库结构',
			projectId,
			priority: Priority.HIGH,
			startDate: tomorrow,
			dueDate: new Date(tomorrow.getTime() + 3 * 24 * 60 * 60 * 1000),
			assignee: '架构师',
			tags: ['design', 'architecture']
		});

		await taskManager.updateTask(task2.id, {
			status: TaskStatus.IN_PROGRESS
		});

		const task3 = await taskManager.createTask({
			title: '前端开发',
			description: '开发用户界面和交互功能',
			projectId,
			priority: Priority.MEDIUM,
			startDate: new Date(tomorrow.getTime() + 3 * 24 * 60 * 60 * 1000),
			dueDate: nextWeek,
			assignee: '前端工程师',
			tags: ['frontend', 'ui']
		});

		const task4 = await taskManager.createTask({
			title: '后端开发',
			description: '开发API和业务逻辑',
			projectId,
			priority: Priority.MEDIUM,
			startDate: new Date(tomorrow.getTime() + 3 * 24 * 60 * 60 * 1000),
			dueDate: nextWeek,
			assignee: '后端工程师',
			tags: ['backend', 'api']
		});

		const task5 = await taskManager.createTask({
			title: '集成测试',
			description: '进行系统集成和功能测试',
			projectId,
			priority: Priority.HIGH,
			startDate: nextWeek,
			dueDate: new Date(nextWeek.getTime() + 5 * 24 * 60 * 60 * 1000),
			assignee: '测试工程师',
			tags: ['testing', 'integration']
		});

		const task6 = await taskManager.createTask({
			title: '部署上线',
			description: '部署到生产环境并上线',
			projectId,
			priority: Priority.HIGH,
			startDate: new Date(nextWeek.getTime() + 5 * 24 * 60 * 60 * 1000),
			dueDate: nextMonth,
			assignee: '运维工程师',
			tags: ['deployment', 'production']
		});

		// 添加任务依赖关系
		await taskManager.addDependency(task2.id, task1.id);
		await taskManager.addDependency(task3.id, task2.id);
		await taskManager.addDependency(task4.id, task2.id);
		await taskManager.addDependency(task5.id, task3.id);
		await taskManager.addDependency(task5.id, task4.id);
		await taskManager.addDependency(task6.id, task5.id);

		console.log('测试任务创建完成，包含依赖关系');
	};

	if (isLoading) {
		return (
			<div className="gantt-test-loading">
				<div className="loading-spinner"></div>
				<p>正在初始化甘特图测试...</p>
			</div>
		);
	}

	if (!ganttManager || !testProjectId) {
		return (
			<div className="gantt-test-error">
				<p>甘特图测试初始化失败</p>
				<button onClick={initializeTest}>重试</button>
			</div>
		);
	}

	return (
		<div className="gantt-test-container">
			<div className="gantt-test-header">
				<h2>甘特图功能测试</h2>
				<p>这是一个包含依赖关系的测试项目，用于验证甘特图的各项功能。</p>
			</div>
			
			<GanttChart
				projectId={testProjectId}
				ganttManager={ganttManager}
				onTaskSelect={(taskId) => {
					console.log('选择任务:', taskId);
				}}
				onTaskUpdate={(taskId) => {
					console.log('任务已更新:', taskId);
				}}
			/>
		</div>
	);
};