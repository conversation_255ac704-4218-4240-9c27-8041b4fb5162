// 统计概览组件

import React, { useMemo } from 'react';
import { Grid } from '../layout/Layout';
import { Card, CardContent } from '../common/Card';
import { Project, Task, Sprint, TaskStatus, Priority } from '../../../models';

export interface StatisticsOverviewProps {
  projects: Project[];
  tasks: Task[];
  sprints: Sprint[];
  onStatClick?: (type: string, filter?: any) => void;
}

interface StatisticCardProps {
  title: string;
  value: number | string;
  icon: string;
  color?: 'primary' | 'success' | 'warning' | 'error';
  trend?: {
    direction: 'up' | 'down' | 'stable';
    value: number;
  };
  onClick?: () => void;
}

const StatisticCard: React.FC<StatisticCardProps> = ({
  title,
  value,
  icon,
  color = 'primary',
  trend,
  onClick
}) => {
  const colorMap = {
    primary: 'var(--interactive-accent)',
    success: 'var(--text-success)',
    warning: 'var(--text-warning)',
    error: 'var(--text-error)'
  };

  const trendColorMap = {
    up: 'var(--text-success)',
    down: 'var(--text-error)',
    stable: 'var(--text-muted)'
  };

  const trendIconMap = {
    up: '📈',
    down: '📉',
    stable: '➡️'
  };

  return (
    <Card 
      variant="outlined" 
      className="ptm-stat-card"
      style={{ cursor: onClick ? 'pointer' : 'default' }}
      onClick={onClick}
    >
      <CardContent>
        <div style={{ textAlign: 'center' }}>
          {/* 图标 */}
          <div style={{ 
            fontSize: '1.5rem', 
            marginBottom: '0.5rem',
            color: colorMap[color]
          }}>
            {icon}
          </div>
          
          {/* 数值 */}
          <div style={{ 
            fontSize: '2rem', 
            fontWeight: 'bold', 
            color: colorMap[color],
            marginBottom: '0.25rem'
          }}>
            {value}
          </div>
          
          {/* 标题 */}
          <div style={{ 
            fontSize: '0.875rem', 
            color: 'var(--text-muted)',
            marginBottom: trend ? '0.5rem' : 0
          }}>
            {title}
          </div>

          {/* 趋势指示器 */}
          {trend && (
            <div style={{
              fontSize: '0.75rem',
              color: trendColorMap[trend.direction],
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.25rem'
            }}>
              <span>{trendIconMap[trend.direction]}</span>
              <span>{trend.value}%</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export const StatisticsOverview: React.FC<StatisticsOverviewProps> = ({
  projects,
  tasks,
  sprints,
  onStatClick
}) => {
  // 计算统计数据
  const statistics = useMemo(() => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // 项目统计
    const totalProjects = projects.length;
    const activeProjects = projects.filter(p => p.status === 'active').length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;
    
    // 任务统计
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
    const inProgressTasks = tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
    const todoTasks = tasks.filter(t => t.status === TaskStatus.TODO).length;
    
    // 逾期任务
    const overdueTasks = tasks.filter(t => {
      if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
      return now > t.dueDate;
    }).length;
    
    // 即将到期任务（7天内）
    const upcomingTasks = tasks.filter(t => {
      if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
      return t.dueDate > now && t.dueDate <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    }).length;
    
    // 高优先级任务
    const highPriorityTasks = tasks.filter(t => 
      (t.priority === Priority.HIGH || t.priority === Priority.CRITICAL) && 
      t.status !== TaskStatus.COMPLETED
    ).length;
    
    // 最近创建的任务
    const recentTasks = tasks.filter(t => t.createdAt >= oneWeekAgo).length;
    
    // 完成率
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    // 项目完成率
    const projectCompletionRate = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0;
    
    // Sprint统计
    const activeSprints = sprints.filter(s => s.status === 'active').length;
    const completedSprints = sprints.filter(s => s.status === 'completed').length;
    
    // 趋势计算（简化版，实际应该基于历史数据）
    const taskTrend = recentTasks > totalTasks * 0.3 ? 'up' : recentTasks < totalTasks * 0.1 ? 'down' : 'stable';
    const completionTrend = completionRate >= 80 ? 'up' : completionRate <= 50 ? 'down' : 'stable';
    
    return {
      // 基础统计
      totalProjects,
      activeProjects,
      completedProjects,
      totalTasks,
      completedTasks,
      inProgressTasks,
      todoTasks,
      overdueTasks,
      upcomingTasks,
      highPriorityTasks,
      recentTasks,
      activeSprints,
      completedSprints,
      
      // 比率
      completionRate,
      projectCompletionRate,
      
      // 趋势
      taskTrend,
      completionTrend,
      
      // 计算的趋势值（模拟）
      taskTrendValue: Math.floor(Math.random() * 20) + 5,
      completionTrendValue: Math.floor(Math.random() * 15) + 3
    };
  }, [projects, tasks, sprints]);

  return (
    <div className="ptm-statistics-overview">
      <Grid cols={4} gap="md" responsive>
        {/* 活跃项目 */}
        <StatisticCard
          title="活跃项目"
          value={statistics.activeProjects}
          icon="📋"
          color="primary"
          trend={{
            direction: statistics.activeProjects > statistics.totalProjects * 0.6 ? 'up' : 'stable',
            value: Math.round((statistics.activeProjects / Math.max(statistics.totalProjects, 1)) * 100)
          }}
          onClick={() => onStatClick?.('projects', { status: 'active' })}
        />

        {/* 总任务数 */}
        <StatisticCard
          title="总任务数"
          value={statistics.totalTasks}
          icon="📝"
          color="primary"
          trend={{
            direction: statistics.taskTrend as 'up' | 'down' | 'stable',
            value: statistics.taskTrendValue
          }}
          onClick={() => onStatClick?.('tasks')}
        />

        {/* 完成率 */}
        <StatisticCard
          title="完成率"
          value={`${statistics.completionRate}%`}
          icon="✅"
          color={statistics.completionRate >= 80 ? 'success' : statistics.completionRate >= 60 ? 'warning' : 'error'}
          trend={{
            direction: statistics.completionTrend as 'up' | 'down' | 'stable',
            value: statistics.completionTrendValue
          }}
          onClick={() => onStatClick?.('tasks', { status: 'completed' })}
        />

        {/* 逾期任务 */}
        <StatisticCard
          title="逾期任务"
          value={statistics.overdueTasks}
          icon="⚠️"
          color={statistics.overdueTasks > 0 ? 'error' : 'success'}
          trend={statistics.overdueTasks > 0 ? {
            direction: 'down',
            value: Math.round((statistics.overdueTasks / Math.max(statistics.totalTasks, 1)) * 100)
          } : undefined}
          onClick={() => onStatClick?.('tasks', { overdue: true })}
        />
      </Grid>

      {/* 详细统计信息（可选显示） */}
      <div className="ptm-detailed-stats" style={{ marginTop: 'var(--ptm-spacing-md)' }}>
        <Grid cols={6} gap="sm" responsive>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.inProgressTasks}</div>
            <div className="ptm-mini-stat-label">进行中</div>
          </div>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.todoTasks}</div>
            <div className="ptm-mini-stat-label">待办</div>
          </div>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.upcomingTasks}</div>
            <div className="ptm-mini-stat-label">即将到期</div>
          </div>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.highPriorityTasks}</div>
            <div className="ptm-mini-stat-label">高优先级</div>
          </div>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.activeSprints}</div>
            <div className="ptm-mini-stat-label">活跃Sprint</div>
          </div>
          <div className="ptm-mini-stat">
            <div className="ptm-mini-stat-value">{statistics.recentTasks}</div>
            <div className="ptm-mini-stat-label">本周新增</div>
          </div>
        </Grid>
      </div>

      {/* 统计卡片样式 */}
      {/* jsx样式已移除 */}
      {/*
        .ptm-stat-card {
          transition: all 0.2s ease;
        }

        .ptm-stat-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-s);
        }

        .ptm-stat-card:active {
          transform: translateY(0);
        }

        .ptm-detailed-stats {
          background: var(--background-secondary);
          border-radius: 8px;
          padding: var(--ptm-spacing-md);
        }

        .ptm-mini-stat {
          text-align: center;
          padding: var(--ptm-spacing-sm);
        }

        .ptm-mini-stat-value {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--text-normal);
          margin-bottom: 0.25rem;
        }

        .ptm-mini-stat-label {
          font-size: 0.75rem;
          color: var(--text-muted);
          line-height: 1.2;
        }

        @media (max-width: 1024px) {
          .ptm-statistics-overview :global(.ptm-grid) {
            grid-template-columns: repeat(2, 1fr);
          }
          
          .ptm-detailed-stats :global(.ptm-grid) {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        @media (max-width: 768px) {
          .ptm-statistics-overview :global(.ptm-grid) {
            grid-template-columns: 1fr;
          }
          
          .ptm-detailed-stats :global(.ptm-grid) {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (max-width: 480px) {
          .ptm-detailed-stats :global(.ptm-grid) {
            grid-template-columns: 1fr;
          }
        }
      */}
    </div>
  );
};