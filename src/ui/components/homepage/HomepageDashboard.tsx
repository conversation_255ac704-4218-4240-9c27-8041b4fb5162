// 首页Dashboard主组件

import React, { useState, useEffect, useCallback } from 'react';
import { App as ObsidianApp } from 'obsidian';
import { PTMManager } from '../../../services/PTMManager';
import { Container, Grid, Stack, Flex } from '../layout/Layout';
import { Card, CardHeader, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { DashboardErrorBoundary } from './DashboardErrorBoundary';
import { DashboardSkeleton } from './DashboardSkeleton';
import { StatisticsOverview } from './StatisticsOverview';
import { QuickAccessPanel } from './QuickAccessPanel';
import { GlobalSearch } from './GlobalSearch';
import { NotificationCenter } from './NotificationCenter';
import { Project, Task, Sprint } from '../../../models';

export interface HomepageDashboardProps {
  app: ObsidianApp;
  ptmManager: PTMManager;
  onNavigate?: (view: string, params?: any) => void;
}

interface DashboardState {
  projects: Project[];
  tasks: Task[];
  sprints: Sprint[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export const HomepageDashboard: React.FC<HomepageDashboardProps> = ({
  app,
  ptmManager,
  onNavigate
}) => {
  const [state, setState] = useState<DashboardState>({
    projects: [],
    tasks: [],
    sprints: [],
    loading: true,
    error: null,
    lastUpdated: null
  });

  // 加载仪表板数据
  const loadDashboardData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const [projects, tasks, sprints] = await Promise.all([
        ptmManager.getProjectManager().getAllProjects(),
        ptmManager.getTaskRepository().findAll(),
        Promise.resolve([])
      ]);

      setState(prev => ({
        ...prev,
        projects,
        tasks,
        sprints,
        loading: false,
        lastUpdated: new Date()
      }));
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '加载数据失败',
        loading: false
      }));
    }
  }, [ptmManager]);

  // 初始化数据加载
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // 处理导航
  const handleNavigate = useCallback((view: string, params?: any) => {
    if (onNavigate) {
      onNavigate(view, params);
    } else {
      // 默认导航逻辑
      console.log('导航到:', view, params);
    }
  }, [onNavigate]);

  // 处理功能模块点击
  const handleModuleClick = useCallback((moduleId: string) => {
    const moduleViewMap: Record<string, string> = {
      'project-dashboard': 'project-task-manager-dashboard',
      'task-list': 'project-task-manager-task-list',
      'kanban': 'project-task-manager-kanban',
      'gantt': 'project-task-manager-gantt',
      'sprint': 'project-task-manager-sprint',
      'independent-tasks': 'project-task-manager-independent-tasks'
    };

    const viewType = moduleViewMap[moduleId];
    if (viewType) {
      // 打开对应的Obsidian视图
      app.workspace.getLeaf(false).setViewState({
        type: viewType,
        active: true
      });
    }
  }, [app]);

  // 处理快速创建
  const handleQuickCreate = useCallback(async (type: 'project' | 'task' | 'sprint') => {
    try {
      switch (type) {
        case 'project':
          const { ProjectCreateModal } = await import('../common/ProjectCreateModal');
          const projectModal = new ProjectCreateModal(app, ptmManager);
          projectModal.open();
          break;
        case 'task':
          const { TaskCreateModal } = await import('../common/TaskCreateModal');
          const taskModal = new TaskCreateModal(app, ptmManager);
          taskModal.open();
          break;
        case 'sprint':
          // TODO: 实现Sprint创建模态框
          console.log('创建Sprint功能待实现');
          break;
      }
    } catch (error) {
      console.error(`创建${type}失败:`, error);
    }
  }, [app, ptmManager]);

  // 处理搜索
  const handleSearch = useCallback((query: string) => {
    // TODO: 实现搜索逻辑
    console.log('搜索:', query);
  }, []);

  // 处理搜索结果选择
  const handleSearchResultSelect = useCallback((result: any) => {
    // TODO: 实现搜索结果选择逻辑
    console.log('选择搜索结果:', result);
  }, []);

  // 如果正在加载，显示骨架屏
  if (state.loading) {
    return <DashboardSkeleton />;
  }

  // 如果有错误，显示错误状态
  if (state.error) {
    return (
      <Container padding>
        <Card variant="outlined">
          <CardContent>
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>⚠️</div>
              <h3 style={{ margin: '0 0 0.5rem 0', color: 'var(--text-error)' }}>
                加载失败
              </h3>
              <p style={{ margin: '0 0 1.5rem 0', color: 'var(--text-muted)' }}>
                {state.error}
              </p>
              <Button variant="primary" onClick={loadDashboardData}>
                重新加载
              </Button>
            </div>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <DashboardErrorBoundary>
      <div className="ptm-homepage">
        <div className="container" style={{ maxWidth: '1200px', margin: '0 auto', padding: 'var(--ptm-spacing-lg)' }}>
          {/* 页面头部 */}
          <div className="header" style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 'var(--ptm-spacing-lg)',
            flexWrap: 'wrap',
            gap: 'var(--ptm-spacing-md)'
          }}>
            <div>
              <h1 style={{
                fontSize: '1.75rem',
                fontWeight: 600,
                background: 'linear-gradient(135deg, var(--interactive-accent), #8b5cf6)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                margin: 0
              }}>
                项目任务管理中心
              </h1>
              <p style={{
                margin: '0.25rem 0 0 0',
                color: 'var(--text-muted)',
                fontSize: '0.875rem'
              }}>
                最后更新: <span>{state.lastUpdated?.toLocaleTimeString() || '--'}</span>
              </p>
            </div>
            <div className="header-actions" style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--ptm-spacing-md)'
            }}>
              {/* 全局搜索 */}
              <div className="search-box" style={{
                display: 'flex',
                alignItems: 'center',
                background: 'var(--background-secondary)',
                border: '1px solid var(--background-modifier-border)',
                borderRadius: '6px',
                padding: '0.5rem',
                minWidth: '250px'
              }}>
                <span>🔍</span>
                <input
                  type="text"
                  placeholder="搜索项目、任务..."
                  onChange={(e) => handleSearch(e.target.value)}
                  style={{
                    flex: 1,
                    border: 'none',
                    outline: 'none',
                    background: 'transparent',
                    color: 'var(--text-normal)',
                    fontSize: '0.875rem',
                    marginLeft: '0.5rem'
                  }}
                />
              </div>
              
              {/* 通知中心 */}
              <button
                className="notification-btn"
                onClick={() => console.log('通知中心')}
                style={{
                  position: 'relative',
                  background: 'none',
                  border: 'none',
                  fontSize: '1.25rem',
                  cursor: 'pointer',
                  padding: '0.5rem'
                }}
                title="通知中心"
              >
                🔔
                <span style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  background: 'var(--text-error)',
                  color: 'white',
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  borderRadius: '50%',
                  minWidth: '18px',
                  height: '18px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  3
                </span>
              </button>
              
              {/* 刷新按钮 */}
              <button
                className="btn btn-secondary"
                onClick={loadDashboardData}
                title="刷新数据"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0.5rem 1rem',
                  border: '1px solid var(--background-modifier-border)',
                  borderRadius: '6px',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  background: 'var(--background-secondary)',
                  color: 'var(--text-normal)'
                }}
              >
                🔄
              </button>
            </div>
          </div>

          {/* 统计概览 */}
          <div className="card mb-lg" style={{
            background: 'var(--background-primary)',
            border: '1px solid var(--background-modifier-border)',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: 'var(--shadow-s)',
            marginBottom: 'var(--ptm-spacing-lg)'
          }}>
            <div className="card-content" style={{ padding: 'var(--ptm-spacing-md)' }}>
              <div className="grid grid-4" style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(4, 1fr)',
                gap: 'var(--ptm-spacing-md)'
              }}>
                <div className="stat-card" style={{
                  textAlign: 'center',
                  padding: 'var(--ptm-spacing-lg)',
                  transition: 'transform 0.2s ease',
                  cursor: 'pointer'
                }}>
                  <div className="stat-icon" style={{ fontSize: '2rem', marginBottom: 'var(--ptm-spacing-sm)' }}>📋</div>
                  <div className="stat-value" style={{
                    fontSize: '2.5rem',
                    fontWeight: 'bold',
                    color: 'var(--interactive-accent)',
                    marginBottom: 'var(--ptm-spacing-xs)'
                  }}>
                    {state.projects.filter(p => p.status === 'active').length}
                  </div>
                  <div className="stat-title" style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>活跃项目</div>
                </div>
                
                <div className="stat-card" style={{
                  textAlign: 'center',
                  padding: 'var(--ptm-spacing-lg)',
                  transition: 'transform 0.2s ease',
                  cursor: 'pointer'
                }}>
                  <div className="stat-icon" style={{ fontSize: '2rem', marginBottom: 'var(--ptm-spacing-sm)' }}>📝</div>
                  <div className="stat-value" style={{
                    fontSize: '2.5rem',
                    fontWeight: 'bold',
                    color: 'var(--interactive-accent)',
                    marginBottom: 'var(--ptm-spacing-xs)'
                  }}>
                    {state.tasks.length}
                  </div>
                  <div className="stat-title" style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>总任务数</div>
                </div>
                
                <div className="stat-card" style={{
                  textAlign: 'center',
                  padding: 'var(--ptm-spacing-lg)',
                  transition: 'transform 0.2s ease',
                  cursor: 'pointer'
                }}>
                  <div className="stat-icon" style={{ fontSize: '2rem', marginBottom: 'var(--ptm-spacing-sm)' }}>✅</div>
                  <div className="stat-value" style={{
                    fontSize: '2.5rem',
                    fontWeight: 'bold',
                    color: 'var(--interactive-accent)',
                    marginBottom: 'var(--ptm-spacing-xs)'
                  }}>
                    {state.tasks.length > 0 ? Math.round((state.tasks.filter(t => t.status === 'completed').length / state.tasks.length) * 100) : 0}%
                  </div>
                  <div className="stat-title" style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>完成率</div>
                </div>
                
                <div className="stat-card" style={{
                  textAlign: 'center',
                  padding: 'var(--ptm-spacing-lg)',
                  transition: 'transform 0.2s ease',
                  cursor: 'pointer'
                }}>
                  <div className="stat-icon" style={{ fontSize: '2rem', marginBottom: 'var(--ptm-spacing-sm)' }}>⚠️</div>
                  <div className="stat-value" style={{
                    fontSize: '2.5rem',
                    fontWeight: 'bold',
                    color: 'var(--interactive-accent)',
                    marginBottom: 'var(--ptm-spacing-xs)'
                  }}>
                    {state.tasks.filter(t => t.dueDate && new Date() > t.dueDate && t.status !== 'completed').length}
                  </div>
                  <div className="stat-title" style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>逾期任务</div>
                </div>
              </div>
            </div>
          </div>

          {/* 快速访问面板 */}
          <div className="card mb-lg" style={{
            background: 'var(--background-primary)',
            border: '1px solid var(--background-modifier-border)',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: 'var(--shadow-s)',
            marginBottom: 'var(--ptm-spacing-lg)'
          }}>
            <div className="card-header" style={{
              padding: 'var(--ptm-spacing-md)',
              borderBottom: '1px solid var(--background-modifier-border)',
              background: 'var(--background-secondary)'
            }}>
              <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: 600 }}>功能模块</h3>
            </div>
            <div className="card-content" style={{ padding: 'var(--ptm-spacing-md)' }}>
              <div className="module-grid" style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gap: 'var(--ptm-spacing-sm)'
              }}>
                {[
                  { id: 'project-dashboard', name: '项目仪表板', icon: '📊', desc: '查看项目概览和进度' },
                  { id: 'task-list', name: '任务列表', icon: '📋', desc: '管理和查看所有任务' },
                  { id: 'kanban', name: '看板视图', icon: '🗂️', desc: '拖拽式任务管理' },
                  { id: 'gantt', name: '甘特图', icon: '📈', desc: '项目时间线和依赖' },
                  { id: 'sprint', name: 'Sprint管理', icon: '🏃', desc: '敏捷开发冲刺管理' },
                  { id: 'independent-tasks', name: '独立任务', icon: '📝', desc: '无项目任务管理' }
                ].map(module => (
                  <div
                    key={module.id}
                    className="module-btn"
                    onClick={() => handleModuleClick(module.id)}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      padding: 'var(--ptm-spacing-lg)',
                      background: 'var(--background-primary)',
                      border: '1px solid var(--background-modifier-border)',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      textDecoration: 'none',
                      color: 'var(--text-normal)'
                    }}
                  >
                    <div className="module-icon" style={{ fontSize: '2rem', marginBottom: 'var(--ptm-spacing-sm)' }}>
                      {module.icon}
                    </div>
                    <div className="module-name" style={{ fontWeight: 600, marginBottom: 'var(--ptm-spacing-xs)' }}>
                      {module.name}
                    </div>
                    <div className="module-desc" style={{
                      fontSize: '0.75rem',
                      color: 'var(--text-muted)',
                      textAlign: 'center'
                    }}>
                      {module.desc}
                    </div>
                  </div>
                ))}
              </div>
              
              <hr style={{
                margin: 'var(--ptm-spacing-lg) 0',
                border: 'none',
                height: '1px',
                background: 'var(--background-modifier-border)'
              }} />
              
              <div style={{ textAlign: 'center' }}>
                <h4 style={{
                  margin: '0 0 0.75rem 0',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                  color: 'var(--text-muted)'
                }}>
                  快速创建
                </h4>
                <div className="flex" style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: 'var(--ptm-spacing-sm)',
                  flexWrap: 'wrap'
                }}>
                  <button
                    className="btn btn-primary"
                    onClick={() => handleQuickCreate('project')}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0.5rem 1rem',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      background: 'var(--interactive-accent)',
                      color: 'white',
                      gap: '0.5rem'
                    }}
                  >
                    📋 新建项目
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleQuickCreate('task')}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0.5rem 1rem',
                      border: '1px solid var(--background-modifier-border)',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      background: 'var(--background-secondary)',
                      color: 'var(--text-normal)',
                      gap: '0.5rem'
                    }}
                  >
                    📝 新建任务
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={() => handleQuickCreate('sprint')}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0.5rem 1rem',
                      border: '1px solid var(--background-modifier-border)',
                      borderRadius: '6px',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      background: 'var(--background-secondary)',
                      color: 'var(--text-normal)',
                      gap: '0.5rem'
                    }}
                  >
                    🏃 新建Sprint
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="grid grid-3" style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: 'var(--ptm-spacing-md)'
          }}>
            {/* 活跃项目 */}
            <div className="card" style={{
              background: 'var(--background-primary)',
              border: '1px solid var(--background-modifier-border)',
              borderRadius: '8px',
              overflow: 'hidden',
              boxShadow: 'var(--shadow-s)'
            }}>
              <div className="card-header" style={{
                padding: 'var(--ptm-spacing-md)',
                borderBottom: '1px solid var(--background-modifier-border)',
                background: 'var(--background-secondary)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <h3 style={{ margin: 0 }}>活跃项目</h3>
                <button
                  className="btn btn-secondary"
                  onClick={() => handleNavigate('projects')}
                  style={{
                    fontSize: '0.75rem',
                    padding: '0.25rem 0.5rem',
                    border: '1px solid var(--background-modifier-border)',
                    borderRadius: '6px',
                    background: 'var(--background-secondary)',
                    color: 'var(--text-normal)',
                    cursor: 'pointer'
                  }}
                >
                  查看全部
                </button>
              </div>
              <div className="card-content" style={{ padding: 'var(--ptm-spacing-md)' }}>
                {state.projects.filter(p => p.status === 'active').length > 0 ? (
                  state.projects
                    .filter(p => p.status === 'active')
                    .slice(0, 3)
                    .map(project => (
                      <div
                        key={project.id}
                        className="item"
                        onClick={() => handleNavigate('project', { id: project.id })}
                        style={{
                          padding: 'var(--ptm-spacing-sm)',
                          background: 'var(--background-secondary)',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          marginBottom: 'var(--ptm-spacing-sm)'
                        }}
                      >
                        <div className="item-title" style={{ fontWeight: 500, marginBottom: '0.25rem' }}>
                          {project.name}
                        </div>
                        <div className="item-desc" style={{ fontSize: '0.75rem', color: 'var(--text-muted)' }}>
                          {project.description || '无描述'}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="empty-state" style={{
                    textAlign: 'center',
                    padding: '2rem 1rem',
                    color: 'var(--text-muted)'
                  }}>
                    <div className="empty-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📋</div>
                    <p>暂无活跃项目</p>
                    <button
                      className="btn btn-primary"
                      onClick={() => handleQuickCreate('project')}
                      style={{
                        marginTop: '1rem',
                        padding: '0.5rem 1rem',
                        background: 'var(--interactive-accent)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer'
                      }}
                    >
                      创建项目
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* 关键任务 */}
            <div className="card" style={{
              background: 'var(--background-primary)',
              border: '1px solid var(--background-modifier-border)',
              borderRadius: '8px',
              overflow: 'hidden',
              boxShadow: 'var(--shadow-s)'
            }}>
              <div className="card-header" style={{
                padding: 'var(--ptm-spacing-md)',
                borderBottom: '1px solid var(--background-modifier-border)',
                background: 'var(--background-secondary)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <h3 style={{ margin: 0 }}>关键任务</h3>
                <button
                  className="btn btn-secondary"
                  onClick={() => handleNavigate('tasks', { filter: 'critical' })}
                  style={{
                    fontSize: '0.75rem',
                    padding: '0.25rem 0.5rem',
                    border: '1px solid var(--background-modifier-border)',
                    borderRadius: '6px',
                    background: 'var(--background-secondary)',
                    color: 'var(--text-normal)',
                    cursor: 'pointer'
                  }}
                >
                  查看全部
                </button>
              </div>
              <div className="card-content" style={{ padding: 'var(--ptm-spacing-md)' }}>
                {state.tasks.filter(t => t.priority === 'high' || t.priority === 'critical').length > 0 ? (
                  state.tasks
                    .filter(t => t.priority === 'high' || t.priority === 'critical')
                    .slice(0, 3)
                    .map(task => (
                      <div
                        key={task.id}
                        className="item"
                        onClick={() => handleNavigate('task', { id: task.id })}
                        style={{
                          padding: 'var(--ptm-spacing-sm)',
                          background: 'var(--background-secondary)',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          marginBottom: 'var(--ptm-spacing-sm)'
                        }}
                      >
                        <div className="item-title" style={{ fontWeight: 500, marginBottom: '0.25rem' }}>
                          {task.title}
                        </div>
                        <div className="item-desc" style={{
                          fontSize: '0.75rem',
                          color: task.priority === 'critical' ? 'var(--text-error)' : 'var(--text-warning)'
                        }}>
                          {task.priority === 'critical' ? '🔴 紧急' : '🟡 重要'}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="empty-state" style={{
                    textAlign: 'center',
                    padding: '2rem 1rem',
                    color: 'var(--text-muted)'
                  }}>
                    <div className="empty-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>✅</div>
                    <p>暂无关键任务</p>
                  </div>
                )}
              </div>
            </div>

            {/* 最近活动 */}
            <div className="card" style={{
              background: 'var(--background-primary)',
              border: '1px solid var(--background-modifier-border)',
              borderRadius: '8px',
              overflow: 'hidden',
              boxShadow: 'var(--shadow-s)'
            }}>
              <div className="card-header" style={{
                padding: 'var(--ptm-spacing-md)',
                borderBottom: '1px solid var(--background-modifier-border)',
                background: 'var(--background-secondary)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <h3 style={{ margin: 0 }}>最近活动</h3>
                <button
                  className="btn btn-secondary"
                  onClick={() => handleNavigate('activities')}
                  style={{
                    fontSize: '0.75rem',
                    padding: '0.25rem 0.5rem',
                    border: '1px solid var(--background-modifier-border)',
                    borderRadius: '6px',
                    background: 'var(--background-secondary)',
                    color: 'var(--text-normal)',
                    cursor: 'pointer'
                  }}
                >
                  查看全部
                </button>
              </div>
              <div className="card-content" style={{ padding: 'var(--ptm-spacing-md)' }}>
                <div className="empty-state" style={{
                  textAlign: 'center',
                  padding: '2rem 1rem',
                  color: 'var(--text-muted)'
                }}>
                  <div className="empty-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📈</div>
                  <p>活动记录功能开发中</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardErrorBoundary>
  );
};