// 统计数据计算工具类

import { Project, Task, Sprint, TaskStatus, Priority, ProjectStatus } from '../../../models';

export interface StatisticsData {
  // 基础统计
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  onHoldProjects: number;
  
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  todoTasks: number;
  blockedTasks: number;
  cancelledTasks: number;
  
  totalSprints: number;
  activeSprints: number;
  completedSprints: number;
  
  // 时间相关统计
  overdueTasks: number;
  upcomingTasks: number;
  todayTasks: number;
  thisWeekTasks: number;
  
  // 优先级统计
  criticalTasks: number;
  highPriorityTasks: number;
  mediumPriorityTasks: number;
  lowPriorityTasks: number;
  
  // 比率和百分比
  completionRate: number;
  projectCompletionRate: number;
  overdueRate: number;
  
  // 趋势数据
  recentTasks: number;
  recentProjects: number;
  taskVelocity: number;
  
  // 工作负载
  averageTasksPerProject: number;
  averageTaskDuration: number;
  
  // 质量指标
  reopenedTasks: number;
  averageCompletionTime: number;
}

export class StatisticsCalculator {
  private projects: Project[];
  private tasks: Task[];
  private sprints: Sprint[];
  private now: Date;

  constructor(projects: Project[], tasks: Task[], sprints: Sprint[] = []) {
    this.projects = projects;
    this.tasks = tasks;
    this.sprints = sprints;
    this.now = new Date();
  }

  /**
   * 计算所有统计数据
   */
  calculateAll(): StatisticsData {
    return {
      // 基础统计
      ...this.calculateBasicStats(),
      
      // 时间相关统计
      ...this.calculateTimeStats(),
      
      // 优先级统计
      ...this.calculatePriorityStats(),
      
      // 比率和百分比
      ...this.calculateRates(),
      
      // 趋势数据
      ...this.calculateTrends(),
      
      // 工作负载
      ...this.calculateWorkload(),
      
      // 质量指标
      ...this.calculateQualityMetrics()
    };
  }

  /**
   * 计算基础统计数据
   */
  private calculateBasicStats() {
    const totalProjects = this.projects.length;
    const activeProjects = this.projects.filter(p => p.status === ProjectStatus.ACTIVE).length;
    const completedProjects = this.projects.filter(p => p.status === ProjectStatus.COMPLETED).length;
    const onHoldProjects = this.projects.filter(p => p.status === ProjectStatus.ON_HOLD).length;

    const totalTasks = this.tasks.length;
    const completedTasks = this.tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
    const inProgressTasks = this.tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
    const todoTasks = this.tasks.filter(t => t.status === TaskStatus.TODO).length;
    const blockedTasks = this.tasks.filter(t => t.status === TaskStatus.BLOCKED).length;
    const cancelledTasks = this.tasks.filter(t => t.status === TaskStatus.CANCELLED).length;

    const totalSprints = this.sprints.length;
    const activeSprints = this.sprints.filter(s => s.status === 'active').length;
    const completedSprints = this.sprints.filter(s => s.status === 'completed').length;

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      onHoldProjects,
      totalTasks,
      completedTasks,
      inProgressTasks,
      todoTasks,
      blockedTasks,
      cancelledTasks,
      totalSprints,
      activeSprints,
      completedSprints
    };
  }

  /**
   * 计算时间相关统计
   */
  private calculateTimeStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const oneWeekFromNow = new Date(this.now);
    oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
    
    const thisWeekStart = new Date(today);
    thisWeekStart.setDate(thisWeekStart.getDate() - thisWeekStart.getDay());

    const overdueTasks = this.tasks.filter(t => {
      if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
      return this.now > t.dueDate;
    }).length;

    const upcomingTasks = this.tasks.filter(t => {
      if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
      return t.dueDate > this.now && t.dueDate <= oneWeekFromNow;
    }).length;

    const todayTasks = this.tasks.filter(t => {
      if (!t.dueDate) return false;
      return t.dueDate >= today && t.dueDate < tomorrow;
    }).length;

    const thisWeekTasks = this.tasks.filter(t => {
      if (!t.dueDate) return false;
      return t.dueDate >= thisWeekStart && t.dueDate < oneWeekFromNow;
    }).length;

    return {
      overdueTasks,
      upcomingTasks,
      todayTasks,
      thisWeekTasks
    };
  }

  /**
   * 计算优先级统计
   */
  private calculatePriorityStats() {
    const criticalTasks = this.tasks.filter(t => 
      t.priority === Priority.CRITICAL && t.status !== TaskStatus.COMPLETED
    ).length;

    const highPriorityTasks = this.tasks.filter(t => 
      t.priority === Priority.HIGH && t.status !== TaskStatus.COMPLETED
    ).length;

    const mediumPriorityTasks = this.tasks.filter(t => 
      t.priority === Priority.MEDIUM && t.status !== TaskStatus.COMPLETED
    ).length;

    const lowPriorityTasks = this.tasks.filter(t => 
      t.priority === Priority.LOW && t.status !== TaskStatus.COMPLETED
    ).length;

    return {
      criticalTasks,
      highPriorityTasks,
      mediumPriorityTasks,
      lowPriorityTasks
    };
  }

  /**
   * 计算比率和百分比
   */
  private calculateRates() {
    const totalTasks = this.tasks.length;
    const completedTasks = this.tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
    const overdueTasks = this.tasks.filter(t => {
      if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
      return this.now > t.dueDate;
    }).length;

    const totalProjects = this.projects.length;
    const completedProjects = this.projects.filter(p => p.status === 'completed').length;

    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    const projectCompletionRate = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0;
    const overdueRate = totalTasks > 0 ? Math.round((overdueTasks / totalTasks) * 100) : 0;

    return {
      completionRate,
      projectCompletionRate,
      overdueRate
    };
  }

  /**
   * 计算趋势数据
   */
  private calculateTrends() {
    const oneWeekAgo = new Date(this.now);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentTasks = this.tasks.filter(t => t.createdAt >= oneWeekAgo).length;
    const recentProjects = this.projects.filter(p => p.createdAt >= oneWeekAgo).length;

    // 简化的任务速度计算（每周完成的任务数）
    const recentCompletedTasks = this.tasks.filter(t => 
      t.status === TaskStatus.COMPLETED && 
      t.completedDate && 
      t.completedDate >= oneWeekAgo
    ).length;

    const taskVelocity = recentCompletedTasks;

    return {
      recentTasks,
      recentProjects,
      taskVelocity
    };
  }

  /**
   * 计算工作负载
   */
  private calculateWorkload() {
    const totalProjects = this.projects.length;
    const totalTasks = this.tasks.length;

    const averageTasksPerProject = totalProjects > 0 ? 
      Math.round((totalTasks / totalProjects) * 10) / 10 : 0;

    // 简化的平均任务持续时间计算（天）
    const completedTasksWithDuration = this.tasks.filter(t => 
      t.status === TaskStatus.COMPLETED && 
      t.completedDate && 
      t.createdAt
    );

    const averageTaskDuration = completedTasksWithDuration.length > 0 ?
      completedTasksWithDuration.reduce((sum, task) => {
        const duration = (task.completedDate!.getTime() - task.createdAt.getTime()) / (1000 * 60 * 60 * 24);
        return sum + duration;
      }, 0) / completedTasksWithDuration.length : 0;

    return {
      averageTasksPerProject,
      averageTaskDuration: Math.round(averageTaskDuration * 10) / 10
    };
  }

  /**
   * 计算质量指标
   */
  private calculateQualityMetrics() {
    // 简化的重新打开任务计算（基于更新时间）
    const reopenedTasks = this.tasks.filter(t => 
      t.status !== TaskStatus.COMPLETED && 
      t.updatedAt.getTime() - t.createdAt.getTime() > 24 * 60 * 60 * 1000 // 超过1天的更新
    ).length;

    // 简化的平均完成时间计算
    const completedTasks = this.tasks.filter(t => 
      t.status === TaskStatus.COMPLETED && t.completedDate
    );

    const averageCompletionTime = completedTasks.length > 0 ?
      completedTasks.reduce((sum, task) => {
        const duration = (task.completedDate!.getTime() - task.createdAt.getTime()) / (1000 * 60 * 60 * 24);
        return sum + duration;
      }, 0) / completedTasks.length : 0;

    return {
      reopenedTasks,
      averageCompletionTime: Math.round(averageCompletionTime * 10) / 10
    };
  }

  /**
   * 获取趋势方向
   */
  getTrendDirection(current: number, previous: number): 'up' | 'down' | 'stable' {
    const threshold = 0.05; // 5%的变化阈值
    const change = previous > 0 ? (current - previous) / previous : 0;
    
    if (Math.abs(change) < threshold) return 'stable';
    return change > 0 ? 'up' : 'down';
  }

  /**
   * 获取趋势百分比
   */
  getTrendPercentage(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(Math.abs((current - previous) / previous) * 100);
  }

  /**
   * 获取健康度评分（0-100）
   */
  getHealthScore(): number {
    const stats = this.calculateAll();
    let score = 100;

    // 完成率影响（权重：30%）
    if (stats.completionRate < 50) score -= 30;
    else if (stats.completionRate < 70) score -= 15;
    else if (stats.completionRate < 90) score -= 5;

    // 逾期率影响（权重：25%）
    if (stats.overdueRate > 20) score -= 25;
    else if (stats.overdueRate > 10) score -= 15;
    else if (stats.overdueRate > 5) score -= 8;

    // 阻塞任务影响（权重：20%）
    const blockedRate = stats.totalTasks > 0 ? (stats.blockedTasks / stats.totalTasks) * 100 : 0;
    if (blockedRate > 15) score -= 20;
    else if (blockedRate > 10) score -= 12;
    else if (blockedRate > 5) score -= 6;

    // 工作负载平衡影响（权重：15%）
    if (stats.averageTasksPerProject > 50) score -= 15;
    else if (stats.averageTasksPerProject > 30) score -= 8;

    // 活跃度影响（权重：10%）
    const activeRate = stats.totalProjects > 0 ? (stats.activeProjects / stats.totalProjects) * 100 : 0;
    if (activeRate < 30) score -= 10;
    else if (activeRate < 50) score -= 5;

    return Math.max(0, Math.min(100, score));
  }
}