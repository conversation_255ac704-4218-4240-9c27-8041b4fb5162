// 模块状态管理器

import { PTMManager } from '../../../services/PTMManager';

export interface ModuleStatus {
  enabled: boolean;
  loading?: boolean;
  error?: string;
  badge?: number;
  lastUsed?: Date;
  dataCount?: number;
}

export interface ModuleUsageStats {
  moduleId: string;
  usageCount: number;
  lastUsed: Date;
  averageSessionTime: number;
  errorCount: number;
}

export class ModuleStatusManager {
  private ptmManager: PTMManager;
  private statusCache: Map<string, ModuleStatus> = new Map();
  private usageStats: Map<string, ModuleUsageStats> = new Map();
  private updateCallbacks: Set<(statuses: Record<string, ModuleStatus>) => void> = new Set();

  constructor(ptmManager: PTMManager) {
    this.ptmManager = ptmManager;
    this.loadUsageStats();
  }

  /**
   * 获取所有模块状态
   */
  async getAllModuleStatuses(): Promise<Record<string, ModuleStatus>> {
    const moduleIds = [
      'project-dashboard',
      'task-list',
      'kanban',
      'gantt',
      'sprint',
      'independent-tasks',
      'reports',
      'calendar'
    ];

    const statuses: Record<string, ModuleStatus> = {};

    for (const moduleId of moduleIds) {
      statuses[moduleId] = await this.getModuleStatus(moduleId);
    }

    return statuses;
  }

  /**
   * 获取单个模块状态
   */
  async getModuleStatus(moduleId: string): Promise<ModuleStatus> {
    // 检查缓存
    if (this.statusCache.has(moduleId)) {
      return this.statusCache.get(moduleId)!;
    }

    const status = await this.calculateModuleStatus(moduleId);
    this.statusCache.set(moduleId, status);
    
    return status;
  }

  /**
   * 计算模块状态
   */
  private async calculateModuleStatus(moduleId: string): Promise<ModuleStatus> {
    const baseStatus: ModuleStatus = {
      enabled: true,
      loading: false,
      error: undefined,
      badge: 0,
      lastUsed: this.usageStats.get(moduleId)?.lastUsed
    };

    try {
      switch (moduleId) {
        case 'project-dashboard':
          return await this.getProjectDashboardStatus(baseStatus);
        case 'task-list':
          return await this.getTaskListStatus(baseStatus);
        case 'kanban':
          return await this.getKanbanStatus(baseStatus);
        case 'gantt':
          return await this.getGanttStatus(baseStatus);
        case 'sprint':
          return await this.getSprintStatus(baseStatus);
        case 'independent-tasks':
          return await this.getIndependentTasksStatus(baseStatus);
        case 'reports':
          return await this.getReportsStatus(baseStatus);
        case 'calendar':
          return await this.getCalendarStatus(baseStatus);
        default:
          return baseStatus;
      }
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: error instanceof Error ? error.message : '模块加载失败'
      };
    }
  }

  /**
   * 项目仪表板状态
   */
  private async getProjectDashboardStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const projects = await this.ptmManager.getProjectManager().getAllProjects();
      const activeProjects = projects.filter(p => p.status === 'active').length;
      
      return {
        ...baseStatus,
        enabled: true,
        badge: activeProjects,
        dataCount: projects.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '无法加载项目数据'
      };
    }
  }

  /**
   * 任务列表状态
   */
  private async getTaskListStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const tasks = await this.ptmManager.getTaskRepository().findAll();
      const pendingTasks = tasks.filter(t => t.status === 'todo' || t.status === 'in-progress').length;
      
      return {
        ...baseStatus,
        enabled: true,
        badge: pendingTasks,
        dataCount: tasks.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '无法加载任务数据'
      };
    }
  }

  /**
   * 看板状态
   */
  private async getKanbanStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const kanbanManager = this.ptmManager.getKanbanManager();
      if (!kanbanManager) {
        return {
          ...baseStatus,
          enabled: false,
          error: '看板管理器未初始化'
        };
      }

      const kanbans = await kanbanManager.getAllKanbans();
      const activeTasks = await this.ptmManager.getTaskRepository().findAll();
      const inProgressTasks = activeTasks.filter(t => t.status === 'in-progress').length;
      
      return {
        ...baseStatus,
        enabled: true,
        badge: inProgressTasks,
        dataCount: kanbans.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '看板功能不可用'
      };
    }
  }

  /**
   * 甘特图状态
   */
  private async getGanttStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const ganttManager = this.ptmManager.getGanttManager();
      if (!ganttManager) {
        return {
          ...baseStatus,
          enabled: false,
          error: '甘特图管理器未初始化'
        };
      }

      const tasks = await this.ptmManager.getTaskRepository().findAll();
      const tasksWithDates = tasks.filter(t => t.startDate && t.dueDate).length;
      
      if (tasksWithDates === 0) {
        return {
          ...baseStatus,
          enabled: true,
          error: '需要设置任务时间才能使用甘特图'
        };
      }
      
      return {
        ...baseStatus,
        enabled: true,
        dataCount: tasksWithDates
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '甘特图功能不可用'
      };
    }
  }

  /**
   * Sprint状态
   */
  private async getSprintStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const sprintManager = this.ptmManager.getSprintManager();
      if (!sprintManager) {
        return {
          ...baseStatus,
          enabled: false,
          error: 'Sprint管理器未初始化'
        };
      }

      const sprints = await sprintManager.getAllSprints();
      const activeSprints = sprints.filter(s => s.status === 'active').length;
      
      return {
        ...baseStatus,
        enabled: true,
        badge: activeSprints,
        dataCount: sprints.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: 'Sprint功能不可用'
      };
    }
  }

  /**
   * 独立任务状态
   */
  private async getIndependentTasksStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const independentTaskService = this.ptmManager.getIndependentTaskService();
      if (!independentTaskService) {
        return {
          ...baseStatus,
          enabled: false,
          error: '独立任务服务未初始化'
        };
      }

      const independentTasks = await independentTaskService.getAllTasks();
      const pendingTasks = independentTasks.filter(t => t.status !== 'completed').length;
      
      return {
        ...baseStatus,
        enabled: true,
        badge: pendingTasks,
        dataCount: independentTasks.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '独立任务功能不可用'
      };
    }
  }

  /**
   * 报告状态
   */
  private async getReportsStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    try {
      const projects = await this.ptmManager.getProjectManager().getAllProjects();
      const tasks = await this.ptmManager.getTaskRepository().findAll();
      
      if (projects.length === 0 && tasks.length === 0) {
        return {
          ...baseStatus,
          enabled: true,
          error: '需要项目或任务数据才能生成报告'
        };
      }
      
      return {
        ...baseStatus,
        enabled: true,
        dataCount: projects.length + tasks.length
      };
    } catch (error) {
      return {
        ...baseStatus,
        enabled: false,
        error: '报告功能不可用'
      };
    }
  }

  /**
   * 日历状态（实验功能）
   */
  private async getCalendarStatus(baseStatus: ModuleStatus): Promise<ModuleStatus> {
    return {
      ...baseStatus,
      enabled: false,
      error: '日历功能正在开发中'
    };
  }

  /**
   * 记录模块使用
   */
  recordModuleUsage(moduleId: string): void {
    const now = new Date();
    const existing = this.usageStats.get(moduleId);
    
    if (existing) {
      existing.usageCount++;
      existing.lastUsed = now;
    } else {
      this.usageStats.set(moduleId, {
        moduleId,
        usageCount: 1,
        lastUsed: now,
        averageSessionTime: 0,
        errorCount: 0
      });
    }
    
    this.saveUsageStats();
    this.invalidateCache(moduleId);
  }

  /**
   * 记录模块错误
   */
  recordModuleError(moduleId: string, error: string): void {
    const stats = this.usageStats.get(moduleId);
    if (stats) {
      stats.errorCount++;
    }
    
    // 更新状态缓存
    const status = this.statusCache.get(moduleId);
    if (status) {
      status.error = error;
      status.enabled = false;
    }
    
    this.notifyStatusUpdate();
  }

  /**
   * 获取最近使用的模块
   */
  getRecentModules(limit: number = 6): string[] {
    return Array.from(this.usageStats.values())
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())
      .slice(0, limit)
      .map(stats => stats.moduleId);
  }

  /**
   * 获取使用统计
   */
  getUsageStats(): ModuleUsageStats[] {
    return Array.from(this.usageStats.values());
  }

  /**
   * 订阅状态更新
   */
  onStatusUpdate(callback: (statuses: Record<string, ModuleStatus>) => void): () => void {
    this.updateCallbacks.add(callback);
    return () => this.updateCallbacks.delete(callback);
  }

  /**
   * 刷新所有模块状态
   */
  async refreshAllStatuses(): Promise<void> {
    this.statusCache.clear();
    const statuses = await this.getAllModuleStatuses();
    this.notifyStatusUpdate(statuses);
  }

  /**
   * 使缓存失效
   */
  private invalidateCache(moduleId?: string): void {
    if (moduleId) {
      this.statusCache.delete(moduleId);
    } else {
      this.statusCache.clear();
    }
  }

  /**
   * 通知状态更新
   */
  private async notifyStatusUpdate(statuses?: Record<string, ModuleStatus>): Promise<void> {
    const currentStatuses = statuses || await this.getAllModuleStatuses();
    this.updateCallbacks.forEach(callback => callback(currentStatuses));
  }

  /**
   * 加载使用统计
   */
  private loadUsageStats(): void {
    try {
      const stored = localStorage.getItem('ptm-module-usage-stats');
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([moduleId, stats]: [string, any]) => {
          this.usageStats.set(moduleId, {
            ...stats,
            lastUsed: new Date(stats.lastUsed)
          });
        });
      }
    } catch (error) {
      console.warn('Failed to load module usage stats:', error);
    }
  }

  /**
   * 保存使用统计
   */
  private saveUsageStats(): void {
    try {
      const data: Record<string, any> = {};
      this.usageStats.forEach((stats, moduleId) => {
        data[moduleId] = stats;
      });
      localStorage.setItem('ptm-module-usage-stats', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save module usage stats:', error);
    }
  }
}