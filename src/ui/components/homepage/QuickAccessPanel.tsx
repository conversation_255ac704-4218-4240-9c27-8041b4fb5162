// 快速访问面板组件

import React, { useState, useEffect } from 'react';
import { Grid, Stack, Flex } from '../layout/Layout';
import { Card, CardHeader, CardContent } from '../common/Card';
import { Button } from '../common/Button';

export interface QuickAccessPanelProps {
  onModuleClick: (moduleId: string) => void;
  onCreateClick: (type: 'project' | 'task' | 'sprint') => void;
  moduleStatuses?: Record<string, ModuleStatus>;
  recentModules?: string[];
  favoriteModules?: string[];
  onModuleFavorite?: (moduleId: string, isFavorite: boolean) => void;
}

export interface ModuleStatus {
  enabled: boolean;
  loading?: boolean;
  error?: string;
  badge?: number;
  lastUsed?: Date;
}

interface ModuleConfig {
  id: string;
  name: string;
  icon: string;
  description: string;
  enabled: boolean;
  badge?: number;
  color?: string;
  category?: 'core' | 'advanced' | 'experimental';
  shortcut?: string;
  requiresData?: boolean;
}

const ModuleButton: React.FC<{
  module: ModuleConfig;
  status?: ModuleStatus;
  isFavorite?: boolean;
  isRecent?: boolean;
  onClick: () => void;
  onFavorite?: (isFavorite: boolean) => void;
}> = ({ module, status, isFavorite = false, isRecent = false, onClick, onFavorite }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const isEnabled = status?.enabled ?? module.enabled;
  const isLoading = status?.loading ?? false;
  const hasError = !!status?.error;
  const badge = status?.badge ?? module.badge;
  return (
    <div
      className={`ptm-module-button-wrapper ${isRecent ? 'ptm-module-recent' : ''} ${isFavorite ? 'ptm-module-favorite' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ position: 'relative' }}
    >
      <Button
        variant="ghost"
        className={`ptm-module-button ${hasError ? 'ptm-module-error' : ''} ${isLoading ? 'ptm-module-loading' : ''}`}
        onClick={onClick}
        disabled={!isEnabled || isLoading}
        style={{
          height: 'auto',
          padding: '1rem',
          flexDirection: 'column',
          gap: '0.5rem',
          position: 'relative',
          width: '100%',
          opacity: isEnabled ? 1 : 0.6
        }}
      >
        {/* 状态指示器 */}
        <div className="ptm-module-status-indicators" style={{
          position: 'absolute',
          top: '0.5rem',
          left: '0.5rem',
          display: 'flex',
          gap: '0.25rem'
        }}>
          {isRecent && (
            <span style={{ fontSize: '0.75rem' }} title="最近使用">🕒</span>
          )}
          {isFavorite && (
            <span style={{ fontSize: '0.75rem' }} title="收藏">⭐</span>
          )}
          {hasError && (
            <span style={{ fontSize: '0.75rem', color: 'var(--text-error)' }} title={status?.error}>⚠️</span>
          )}
        </div>

        {/* 收藏按钮 */}
        {onFavorite && isHovered && (
          <button
            className="ptm-module-favorite-btn"
            onClick={(e) => {
              e.stopPropagation();
              onFavorite(!isFavorite);
            }}
            style={{
              position: 'absolute',
              top: '0.5rem',
              right: '0.5rem',
              background: 'none',
              border: 'none',
              fontSize: '0.875rem',
              cursor: 'pointer',
              color: isFavorite ? 'var(--text-warning)' : 'var(--text-muted)',
              transition: 'color 0.2s ease'
            }}
            title={isFavorite ? '取消收藏' : '添加收藏'}
          >
            {isFavorite ? '⭐' : '☆'}
          </button>
        )}
        
        {/* 模块图标 */}
        <div 
          className="ptm-module-icon"
          style={{ 
            fontSize: '2rem',
            color: hasError ? 'var(--text-error)' : (module.color || 'var(--interactive-accent)'),
            position: 'relative',
            opacity: isLoading ? 0.5 : 1
          }}
        >
          {isLoading ? '⏳' : module.icon}
          
          {/* 徽章 */}
          {badge && badge > 0 && (
            <span 
              className="ptm-module-badge"
              style={{
                position: 'absolute',
                top: '-4px',
                right: '-4px',
                backgroundColor: 'var(--text-error)',
                color: 'white',
                fontSize: '0.75rem',
                fontWeight: 'bold',
                borderRadius: '50%',
                minWidth: '18px',
                height: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                lineHeight: 1,
                animation: badge > 0 ? 'ptm-badge-pulse 2s infinite' : 'none'
              }}
            >
              {badge > 99 ? '99+' : badge}
            </span>
          )}
        </div>
        
        {/* 模块名称 */}
        <div 
          className="ptm-module-name"
          style={{ 
            fontWeight: 600,
            fontSize: '0.875rem',
            color: hasError ? 'var(--text-error)' : (isEnabled ? 'var(--text-normal)' : 'var(--text-muted)'),
            textAlign: 'center'
          }}
        >
          {module.name}
          {module.shortcut && (
            <span style={{
              fontSize: '0.75rem',
              color: 'var(--text-muted)',
              marginLeft: '0.25rem'
            }}>
              {module.shortcut}
            </span>
          )}
        </div>
        
        {/* 模块描述 */}
        <div 
          className="ptm-module-desc"
          style={{ 
            fontSize: '0.75rem',
            color: 'var(--text-muted)',
            textAlign: 'center',
            lineHeight: 1.2
          }}
        >
          {hasError ? status?.error : module.description}
        </div>

        {/* 最后使用时间 */}
        {status?.lastUsed && isHovered && (
          <div style={{
            fontSize: '0.7rem',
            color: 'var(--text-muted)',
            marginTop: '0.25rem'
          }}>
            上次使用: {status.lastUsed.toLocaleDateString()}
          </div>
        )}
      </Button>

      {/* 类别标签 */}
      {module.category && module.category !== 'core' && (
        <div 
          className="ptm-module-category"
          style={{
            position: 'absolute',
            bottom: '0.5rem',
            right: '0.5rem',
            fontSize: '0.7rem',
            padding: '0.125rem 0.25rem',
            borderRadius: '0.25rem',
            backgroundColor: module.category === 'experimental' ? 'var(--text-warning)' : 'var(--interactive-accent)',
            color: 'white',
            opacity: 0.8
          }}
        >
          {module.category === 'experimental' ? 'Beta' : 'Pro'}
        </div>
      )}
    </div>
  );
};

export const QuickAccessPanel: React.FC<QuickAccessPanelProps> = ({
  onModuleClick,
  onCreateClick,
  moduleStatuses = {},
  recentModules = [],
  favoriteModules = [],
  onModuleFavorite
}) => {
  const [viewMode, setViewMode] = useState<'all' | 'favorites' | 'recent'>('all');
  // 功能模块配置
  const modules: ModuleConfig[] = [
    {
      id: 'project-dashboard',
      name: '项目仪表板',
      icon: '📊',
      description: '查看项目概览和进度',
      enabled: true,
      color: 'var(--interactive-accent)',
      category: 'core',
      shortcut: 'Ctrl+D',
      requiresData: true
    },
    {
      id: 'task-list',
      name: '任务列表',
      icon: '📋',
      description: '管理和查看所有任务',
      enabled: true,
      color: 'var(--text-success)',
      category: 'core',
      shortcut: 'Ctrl+T'
    },
    {
      id: 'kanban',
      name: '看板视图',
      icon: '🗂️',
      description: '拖拽式任务管理',
      enabled: true,
      color: 'var(--text-warning)',
      category: 'core',
      shortcut: 'Ctrl+K'
    },
    {
      id: 'gantt',
      name: '甘特图',
      icon: '📈',
      description: '项目时间线和依赖',
      enabled: true,
      color: 'var(--text-error)',
      category: 'advanced',
      shortcut: 'Ctrl+G',
      requiresData: true
    },
    {
      id: 'sprint',
      name: 'Sprint管理',
      icon: '🏃',
      description: '敏捷开发冲刺管理',
      enabled: true,
      color: 'var(--interactive-accent)',
      category: 'advanced',
      shortcut: 'Ctrl+S'
    },
    {
      id: 'independent-tasks',
      name: '独立任务',
      icon: '📝',
      description: '无项目任务管理',
      enabled: true,
      color: 'var(--text-muted)',
      category: 'core'
    },
    {
      id: 'reports',
      name: '报告分析',
      icon: '📈',
      description: '生成项目和任务报告',
      enabled: true,
      color: 'var(--text-info)',
      category: 'advanced',
      requiresData: true
    },
    {
      id: 'calendar',
      name: '日历视图',
      icon: '📅',
      description: '基于时间的任务视图',
      enabled: false,
      color: 'var(--text-warning)',
      category: 'experimental'
    }
  ];

  // 根据视图模式过滤模块
  const getFilteredModules = () => {
    let filtered = modules;
    
    switch (viewMode) {
      case 'favorites':
        filtered = modules.filter(m => favoriteModules.includes(m.id));
        break;
      case 'recent':
        filtered = modules.filter(m => recentModules.includes(m.id));
        break;
      default:
        filtered = modules;
    }
    
    // 按优先级排序：收藏 > 最近使用 > 核心功能 > 高级功能 > 实验功能
    return filtered.sort((a, b) => {
      const aIsFavorite = favoriteModules.includes(a.id);
      const bIsFavorite = favoriteModules.includes(b.id);
      const aIsRecent = recentModules.includes(a.id);
      const bIsRecent = recentModules.includes(b.id);
      
      if (aIsFavorite && !bIsFavorite) return -1;
      if (!aIsFavorite && bIsFavorite) return 1;
      if (aIsRecent && !bIsRecent) return -1;
      if (!aIsRecent && bIsRecent) return 1;
      
      const categoryOrder = { core: 0, advanced: 1, experimental: 2 };
      const aOrder = categoryOrder[a.category || 'core'];
      const bOrder = categoryOrder[b.category || 'core'];
      
      return aOrder - bOrder;
    });
  };

  const filteredModules = getFilteredModules();

  return (
    <Card variant="outlined" className="ptm-quick-access">
      <CardHeader>
        <Flex justify="between" align="center">
          <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: 600 }}>
            功能模块
          </h3>
          
          {/* 视图切换按钮 */}
          <div className="ptm-view-toggle" style={{ display: 'flex', gap: '0.25rem' }}>
            <Button
              variant={viewMode === 'all' ? 'primary' : 'ghost'}
              size="small"
              onClick={() => setViewMode('all')}
            >
              全部
            </Button>
            <Button
              variant={viewMode === 'favorites' ? 'primary' : 'ghost'}
              size="small"
              onClick={() => setViewMode('favorites')}
              disabled={favoriteModules.length === 0}
            >
              收藏 {favoriteModules.length > 0 && `(${favoriteModules.length})`}
            </Button>
            <Button
              variant={viewMode === 'recent' ? 'primary' : 'ghost'}
              size="small"
              onClick={() => setViewMode('recent')}
              disabled={recentModules.length === 0}
            >
              最近 {recentModules.length > 0 && `(${recentModules.length})`}
            </Button>
          </div>
        </Flex>
      </CardHeader>
      <CardContent>
        <Stack spacing="lg">
          {/* 功能模块网格 */}
          {filteredModules.length > 0 ? (
            <Grid cols={3} gap="sm" responsive>
              {filteredModules.map(module => (
                <ModuleButton
                  key={module.id}
                  module={module}
                  status={moduleStatuses[module.id]}
                  isFavorite={favoriteModules.includes(module.id)}
                  isRecent={recentModules.includes(module.id)}
                  onClick={() => onModuleClick(module.id)}
                  onFavorite={onModuleFavorite ? (isFavorite) => onModuleFavorite(module.id, isFavorite) : undefined}
                />
              ))}
            </Grid>
          ) : (
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              color: 'var(--text-muted)'
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                {viewMode === 'favorites' ? '⭐' : '🕒'}
              </div>
              <p>
                {viewMode === 'favorites' ? '暂无收藏的模块' : '暂无最近使用的模块'}
              </p>
            </div>
          )}
          
          {/* 分隔线 */}
          <div style={{
            height: '1px',
            backgroundColor: 'var(--background-modifier-border)',
            margin: '0.5rem 0'
          }} />
          
          {/* 快速创建按钮 */}
          <div>
            <h4 style={{ 
              margin: '0 0 0.75rem 0', 
              fontSize: '0.875rem', 
              fontWeight: 600,
              color: 'var(--text-muted)'
            }}>
              快速创建
            </h4>
            <Flex gap="sm" justify="center" wrap>
              <Button 
                variant="primary" 
                size="small"
                onClick={() => onCreateClick('project')}
                style={{ minWidth: '100px' }}
              >
                <span style={{ marginRight: '0.5rem' }}>📋</span>
                新建项目
              </Button>
              <Button 
                variant="secondary" 
                size="small"
                onClick={() => onCreateClick('task')}
                style={{ minWidth: '100px' }}
              >
                <span style={{ marginRight: '0.5rem' }}>📝</span>
                新建任务
              </Button>
              <Button 
                variant="secondary" 
                size="small"
                onClick={() => onCreateClick('sprint')}
                style={{ minWidth: '100px' }}
              >
                <span style={{ marginRight: '0.5rem' }}>🏃</span>
                新建Sprint
              </Button>
            </Flex>
          </div>
        </Stack>
      </CardContent>

      {/* 快速访问面板样式 */}
      {/* jsx样式已移除 */}
      {/*
        .ptm-module-button-wrapper {
          position: relative;
        }

        .ptm-module-button {
          transition: all 0.2s ease;
          border: 1px solid transparent;
        }

        .ptm-module-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: var(--shadow-s);
          border-color: var(--background-modifier-border);
        }

        .ptm-module-button:active:not(:disabled) {
          transform: translateY(0);
        }

        .ptm-module-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .ptm-module-button.ptm-module-error {
          border-color: var(--text-error);
          background-color: rgba(var(--text-error-rgb), 0.05);
        }

        .ptm-module-button.ptm-module-loading {
          pointer-events: none;
        }

        .ptm-module-recent::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: var(--text-info);
          border-radius: 1px;
        }

        .ptm-module-favorite::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 0;
          height: 0;
          border-left: 12px solid transparent;
          border-top: 12px solid var(--text-warning);
        }

        .ptm-module-favorite-btn:hover {
          color: var(--text-warning) !important;
        }

        @keyframes ptm-badge-pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
        }

        .ptm-view-toggle button {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }

        @media (max-width: 1024px) {
          .ptm-quick-access :global(.ptm-grid) {
            grid-template-columns: repeat(2, 1fr);
          }
          
          .ptm-view-toggle {
            flex-direction: column;
            gap: 0.125rem !important;
          }
        }

        @media (max-width: 768px) {
          .ptm-quick-access :global(.ptm-grid) {
            grid-template-columns: 1fr;
          }
          
          .ptm-quick-access :global(.ptm-flex) {
            flex-direction: column;
            gap: var(--ptm-spacing-sm);
          }
          
          .ptm-view-toggle {
            align-self: stretch;
            justify-content: center;
          }
        }
      */}
    </Card>
  );
};