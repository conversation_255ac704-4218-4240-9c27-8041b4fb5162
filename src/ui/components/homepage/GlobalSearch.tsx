// 全局搜索组件

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '../common/Button';

export interface GlobalSearchProps {
  onSearch: (query: string) => void;
  onResultSelect: (result: SearchResult) => void;
  placeholder?: string;
}

export interface SearchResult {
  id: string;
  type: 'project' | 'task' | 'sprint';
  title: string;
  subtitle?: string;
  icon: string;
  path: string;
  relevance: number;
}

// 防抖Hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({
  onSearch,
  onResultSelect,
  placeholder = "搜索项目、任务..."
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  
  // 防抖搜索查询
  const debouncedQuery = useDebounce(query, 300);

  // 执行搜索
  useEffect(() => {
    if (debouncedQuery.trim()) {
      onSearch(debouncedQuery);
      // TODO: 这里应该从搜索结果中更新results
      // 现在先用模拟数据
      setResults([
        {
          id: '1',
          type: 'project',
          title: '示例项目',
          subtitle: '项目描述',
          icon: '📋',
          path: '/project/1',
          relevance: 0.9
        },
        {
          id: '2',
          type: 'task',
          title: '示例任务',
          subtitle: '任务描述',
          icon: '📝',
          path: '/task/2',
          relevance: 0.8
        }
      ]);
    } else {
      setResults([]);
    }
  }, [debouncedQuery, onSearch]);

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setSelectedIndex(-1);
  }, []);

  // 处理键盘导航
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : results.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleResultSelect(results[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  }, [isOpen, results, selectedIndex]);

  // 处理结果选择
  const handleResultSelect = useCallback((result: SearchResult) => {
    onResultSelect(result);
    setQuery('');
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  }, [onResultSelect]);

  // 处理焦点
  const handleFocus = useCallback(() => {
    setIsOpen(true);
  }, []);

  const handleBlur = useCallback(() => {
    // 延迟关闭，允许点击结果
    setTimeout(() => {
      setIsOpen(false);
      setSelectedIndex(-1);
    }, 200);
  }, []);

  // 清除搜索
  const handleClear = useCallback(() => {
    setQuery('');
    setResults([]);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  }, []);

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project': return '📋';
      case 'task': return '📝';
      case 'sprint': return '🏃';
      default: return '📄';
    }
  };

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'project': return '项目';
      case 'task': return '任务';
      case 'sprint': return 'Sprint';
      default: return '其他';
    }
  };

  return (
    <div className="ptm-global-search" style={{ position: 'relative' }}>
      {/* 搜索输入框 */}
      <div 
        className="ptm-search-input-wrapper"
        style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          backgroundColor: 'var(--background-secondary)',
          border: '1px solid var(--background-modifier-border)',
          borderRadius: '6px',
          padding: '0.5rem',
          minWidth: '250px',
          transition: 'border-color 0.2s ease'
        }}
      >
        {/* 搜索图标 */}
        <div 
          className="ptm-search-icon"
          style={{
            color: 'var(--text-muted)',
            marginRight: '0.5rem',
            fontSize: '1rem'
          }}
        >
          🔍
        </div>
        
        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          className="ptm-search-input"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={{
            flex: 1,
            border: 'none',
            outline: 'none',
            backgroundColor: 'transparent',
            color: 'var(--text-normal)',
            fontSize: '0.875rem'
          }}
        />
        
        {/* 清除按钮 */}
        {query && (
          <Button
            variant="ghost"
            size="small"
            className="ptm-search-clear"
            onClick={handleClear}
            style={{
              padding: '0.25rem',
              minWidth: 'auto',
              height: 'auto'
            }}
          >
            ❌
          </Button>
        )}
      </div>
      
      {/* 搜索结果 */}
      {isOpen && results.length > 0 && (
        <div
          ref={resultsRef}
          className="ptm-search-results"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: 'var(--background-primary)',
            border: '1px solid var(--background-modifier-border)',
            borderRadius: '6px',
            boxShadow: 'var(--shadow-s)',
            zIndex: 1000,
            maxHeight: '300px',
            overflowY: 'auto',
            marginTop: '0.25rem'
          }}
        >
          {results.map((result, index) => (
            <div
              key={result.id}
              className="ptm-search-result"
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '0.75rem',
                cursor: 'pointer',
                backgroundColor: index === selectedIndex ? 'var(--background-modifier-hover)' : 'transparent',
                borderBottom: index < results.length - 1 ? '1px solid var(--background-modifier-border)' : 'none'
              }}
              onClick={() => handleResultSelect(result)}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              {/* 结果图标 */}
              <div 
                className="ptm-search-result-icon"
                style={{
                  fontSize: '1.25rem',
                  marginRight: '0.75rem'
                }}
              >
                {getTypeIcon(result.type)}
              </div>
              
              {/* 结果内容 */}
              <div className="ptm-search-result-content" style={{ flex: 1 }}>
                <div 
                  className="ptm-search-result-title"
                  style={{
                    fontWeight: 500,
                    color: 'var(--text-normal)',
                    fontSize: '0.875rem',
                    marginBottom: '0.25rem'
                  }}
                >
                  {result.title}
                </div>
                {result.subtitle && (
                  <div 
                    className="ptm-search-result-subtitle"
                    style={{
                      fontSize: '0.75rem',
                      color: 'var(--text-muted)',
                      lineHeight: 1.2
                    }}
                  >
                    {result.subtitle}
                  </div>
                )}
              </div>
              
              {/* 类型标签 */}
              <div 
                className="ptm-search-result-type"
                style={{
                  fontSize: '0.75rem',
                  color: 'var(--text-muted)',
                  backgroundColor: 'var(--background-secondary)',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '12px',
                  marginLeft: '0.5rem'
                }}
              >
                {getTypeLabel(result.type)}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 无结果提示 */}
      {isOpen && query.trim() && results.length === 0 && (
        <div
          className="ptm-search-no-results"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: 'var(--background-primary)',
            border: '1px solid var(--background-modifier-border)',
            borderRadius: '6px',
            boxShadow: 'var(--shadow-s)',
            zIndex: 1000,
            padding: '1rem',
            textAlign: 'center',
            color: 'var(--text-muted)',
            fontSize: '0.875rem',
            marginTop: '0.25rem'
          }}
        >
          未找到相关结果
        </div>
      )}
    </div>
  );
};