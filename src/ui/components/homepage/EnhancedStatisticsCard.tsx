// 增强的统计卡片组件

import React from 'react';
import { Card, CardContent } from '../common/Card';

export interface EnhancedStatisticsCardProps {
  title: string;
  value: number | string;
  icon: string;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    direction: 'up' | 'down' | 'stable';
    value: number;
    label?: string;
  };
  subtitle?: string;
  loading?: boolean;
  onClick?: () => void;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  progressValue?: number;
  maxValue?: number;
}

export const EnhancedStatisticsCard: React.FC<EnhancedStatisticsCardProps> = ({
  title,
  value,
  icon,
  color = 'primary',
  trend,
  subtitle,
  loading = false,
  onClick,
  size = 'medium',
  showProgress = false,
  progressValue = 0,
  maxValue = 100
}) => {
  const colorMap = {
    primary: 'var(--interactive-accent)',
    success: 'var(--text-success)',
    warning: 'var(--text-warning)',
    error: 'var(--text-error)',
    info: 'var(--text-info)'
  };

  const trendColorMap = {
    up: 'var(--text-success)',
    down: 'var(--text-error)',
    stable: 'var(--text-muted)'
  };

  const trendIconMap = {
    up: '↗️',
    down: '↘️',
    stable: '➡️'
  };

  const sizeMap = {
    small: {
      iconSize: '1.25rem',
      valueSize: '1.5rem',
      titleSize: '0.75rem',
      padding: 'var(--ptm-spacing-sm)'
    },
    medium: {
      iconSize: '1.5rem',
      valueSize: '2rem',
      titleSize: '0.875rem',
      padding: 'var(--ptm-spacing-md)'
    },
    large: {
      iconSize: '2rem',
      valueSize: '2.5rem',
      titleSize: '1rem',
      padding: 'var(--ptm-spacing-lg)'
    }
  };

  const currentSize = sizeMap[size];
  const progressPercentage = maxValue > 0 ? (progressValue / maxValue) * 100 : 0;

  if (loading) {
    return (
      <Card 
        variant="outlined" 
        className="ptm-enhanced-stat-card ptm-enhanced-stat-card--loading"
      >
        <CardContent style={{ padding: currentSize.padding }}>
          <div style={{ textAlign: 'center' }}>
            <div className="ptm-skeleton" style={{
              width: '2rem',
              height: currentSize.iconSize,
              margin: '0 auto 0.5rem auto',
              borderRadius: '4px'
            }} />
            <div className="ptm-skeleton" style={{
              width: '3rem',
              height: currentSize.valueSize,
              margin: '0 auto 0.25rem auto',
              borderRadius: '4px'
            }} />
            <div className="ptm-skeleton" style={{
              width: '4rem',
              height: currentSize.titleSize,
              margin: '0 auto',
              borderRadius: '4px'
            }} />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      variant="outlined" 
      className={`ptm-enhanced-stat-card ptm-enhanced-stat-card--${color} ptm-enhanced-stat-card--${size}`}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
      onClick={onClick}
    >
      <CardContent style={{ padding: currentSize.padding }}>
        <div style={{ textAlign: 'center', position: 'relative' }}>
          {/* 图标 */}
          <div style={{ 
            fontSize: currentSize.iconSize, 
            marginBottom: '0.5rem',
            color: colorMap[color]
          }}>
            {icon}
          </div>
          
          {/* 数值 */}
          <div style={{ 
            fontSize: currentSize.valueSize, 
            fontWeight: 'bold', 
            color: colorMap[color],
            marginBottom: '0.25rem',
            lineHeight: 1
          }}>
            {value}
          </div>
          
          {/* 标题 */}
          <div style={{ 
            fontSize: currentSize.titleSize, 
            color: 'var(--text-muted)',
            marginBottom: subtitle || trend || showProgress ? '0.5rem' : 0,
            fontWeight: 500
          }}>
            {title}
          </div>

          {/* 副标题 */}
          {subtitle && (
            <div style={{
              fontSize: '0.75rem',
              color: 'var(--text-muted)',
              marginBottom: trend || showProgress ? '0.5rem' : 0,
              lineHeight: 1.2
            }}>
              {subtitle}
            </div>
          )}

          {/* 进度条 */}
          {showProgress && (
            <div style={{
              width: '100%',
              height: '4px',
              backgroundColor: 'var(--background-modifier-border)',
              borderRadius: '2px',
              overflow: 'hidden',
              marginBottom: trend ? '0.5rem' : 0
            }}>
              <div
                style={{
                  width: `${Math.min(progressPercentage, 100)}%`,
                  height: '100%',
                  backgroundColor: colorMap[color],
                  transition: 'width 0.3s ease',
                  borderRadius: '2px'
                }}
              />
            </div>
          )}

          {/* 趋势指示器 */}
          {trend && (
            <div style={{
              fontSize: '0.75rem',
              color: trendColorMap[trend.direction],
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.25rem'
            }}>
              <span>{trendIconMap[trend.direction]}</span>
              <span>{trend.value}%</span>
              {trend.label && (
                <span style={{ color: 'var(--text-muted)' }}>
                  {trend.label}
                </span>
              )}
            </div>
          )}

          {/* 悬停效果指示器 */}
          {onClick && (
            <div 
              className="ptm-stat-card-hover-indicator"
              style={{
                position: 'absolute',
                top: '0.5rem',
                right: '0.5rem',
                opacity: 0,
                transition: 'opacity 0.2s ease',
                fontSize: '0.75rem',
                color: 'var(--text-muted)'
              }}
            >
              👆
            </div>
          )}
        </div>
      </CardContent>

      {/* jsx样式已移除 */}
      {/*
        .ptm-enhanced-stat-card {
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;
        }

        .ptm-enhanced-stat-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: ${colorMap[color]};
          opacity: 0.8;
        }

        .ptm-enhanced-stat-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-s);
        }

        .ptm-enhanced-stat-card:hover .ptm-stat-card-hover-indicator {
          opacity: 1;
        }

        .ptm-enhanced-stat-card:active {
          transform: translateY(0);
        }

        .ptm-enhanced-stat-card--loading {
          pointer-events: none;
        }

        .ptm-enhanced-stat-card--small {
          min-height: 120px;
        }

        .ptm-enhanced-stat-card--medium {
          min-height: 140px;
        }

        .ptm-enhanced-stat-card--large {
          min-height: 160px;
        }

        .ptm-enhanced-stat-card--primary::before {
          background: var(--interactive-accent);
        }

        .ptm-enhanced-stat-card--success::before {
          background: var(--text-success);
        }

        .ptm-enhanced-stat-card--warning::before {
          background: var(--text-warning);
        }

        .ptm-enhanced-stat-card--error::before {
          background: var(--text-error);
      */}
    </Card>
  );
};