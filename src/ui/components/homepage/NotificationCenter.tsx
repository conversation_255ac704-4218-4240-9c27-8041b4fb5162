// 通知中心组件

import React, { useState, useCallback } from 'react';
import { Button } from '../common/Button';
import { Card, CardHeader, CardContent } from '../common/Card';
import { Stack, Flex } from '../layout/Layout';

export interface NotificationCenterProps {
  notifications?: Notification[];
  onNotificationClick?: (notification: Notification) => void;
  onMarkAsRead?: (notificationId: string) => void;
  onMarkAllAsRead?: () => void;
  onClearAll?: () => void;
}

export interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionable?: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary';
  handler: () => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  notifications = [],
  onNotificationClick,
  onMarkAsRead,
  onMarkAllAsRead,
  onClearAll
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // 计算未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length;

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info': return 'ℹ️';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'success': return '✅';
      default: return '📢';
    }
  };

  // 获取通知颜色
  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'info': return 'var(--interactive-accent)';
      case 'warning': return 'var(--text-warning)';
      case 'error': return 'var(--text-error)';
      case 'success': return 'var(--text-success)';
      default: return 'var(--text-normal)';
    }
  };

  // 处理通知点击
  const handleNotificationClick = useCallback((notification: Notification) => {
    if (!notification.read) {
      onMarkAsRead?.(notification.id);
    }
    onNotificationClick?.(notification);
  }, [onNotificationClick, onMarkAsRead]);

  // 格式化时间
  const formatTime = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return timestamp.toLocaleDateString();
  };

  return (
    <div className="ptm-notification-center" style={{ position: 'relative' }}>
      {/* 通知按钮 */}
      <Button
        variant="ghost"
        size="small"
        onClick={() => setIsOpen(!isOpen)}
        style={{
          position: 'relative',
          padding: '0.5rem',
          minWidth: 'auto'
        }}
        title="通知中心"
      >
        <span style={{ fontSize: '1.25rem' }}>🔔</span>
        
        {/* 未读徽章 */}
        {unreadCount > 0 && (
          <span
            className="ptm-notification-badge"
            style={{
              position: 'absolute',
              top: '0',
              right: '0',
              backgroundColor: 'var(--text-error)',
              color: 'white',
              fontSize: '0.75rem',
              fontWeight: 'bold',
              borderRadius: '50%',
              minWidth: '18px',
              height: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              lineHeight: 1
            }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </Button>

      {/* 通知面板 */}
      {isOpen && (
        <div
          className="ptm-notification-panel"
          style={{
            position: 'absolute',
            top: '100%',
            right: 0,
            width: '350px',
            maxHeight: '400px',
            backgroundColor: 'var(--background-primary)',
            border: '1px solid var(--background-modifier-border)',
            borderRadius: '8px',
            boxShadow: 'var(--shadow-s)',
            zIndex: 1000,
            marginTop: '0.5rem'
          }}
        >
          <Card variant="default" style={{ border: 'none', boxShadow: 'none' }}>
            {/* 头部 */}
            <CardHeader style={{ borderBottom: '1px solid var(--background-modifier-border)' }}>
              <Flex justify="between" align="center">
                <h3 style={{ margin: 0, fontSize: '1rem', fontWeight: 600 }}>
                  通知中心
                </h3>
                <Flex gap="sm">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      size="small"
                      onClick={onMarkAllAsRead}
                    >
                      全部已读
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="small"
                    onClick={onClearAll}
                  >
                    清空
                  </Button>
                  <Button
                    variant="ghost"
                    size="small"
                    onClick={() => setIsOpen(false)}
                  >
                    ❌
                  </Button>
                </Flex>
              </Flex>
            </CardHeader>

            {/* 通知列表 */}
            <CardContent style={{ padding: 0, maxHeight: '300px', overflowY: 'auto' }}>
              {notifications.length > 0 ? (
                <Stack spacing="none">
                  {notifications.map((notification, index) => (
                    <div
                      key={notification.id}
                      className="ptm-notification-item"
                      style={{
                        padding: '0.75rem',
                        borderBottom: index < notifications.length - 1 ? '1px solid var(--background-modifier-border)' : 'none',
                        backgroundColor: notification.read ? 'transparent' : 'var(--background-secondary)',
                        cursor: 'pointer',
                        transition: 'background-color 0.2s ease'
                      }}
                      onClick={() => handleNotificationClick(notification)}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--background-modifier-hover)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = notification.read ? 'transparent' : 'var(--background-secondary)';
                      }}
                    >
                      <Flex gap="sm" align="start">
                        {/* 通知图标 */}
                        <div
                          style={{
                            fontSize: '1.25rem',
                            color: getNotificationColor(notification.type),
                            flexShrink: 0
                          }}
                        >
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* 通知内容 */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div
                            style={{
                              fontWeight: notification.read ? 400 : 600,
                              fontSize: '0.875rem',
                              color: 'var(--text-normal)',
                              marginBottom: '0.25rem'
                            }}
                          >
                            {notification.title}
                          </div>
                          <div
                            style={{
                              fontSize: '0.75rem',
                              color: 'var(--text-muted)',
                              lineHeight: 1.3,
                              marginBottom: '0.5rem'
                            }}
                          >
                            {notification.message}
                          </div>
                          
                          {/* 操作按钮 */}
                          {notification.actions && notification.actions.length > 0 && (
                            <Flex gap="sm">
                              {notification.actions.map(action => (
                                <Button
                                  key={action.id}
                                  variant={action.type}
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    action.handler();
                                  }}
                                >
                                  {action.label}
                                </Button>
                              ))}
                            </Flex>
                          )}
                          
                          {/* 时间戳 */}
                          <div
                            style={{
                              fontSize: '0.75rem',
                              color: 'var(--text-muted)',
                              marginTop: '0.5rem'
                            }}
                          >
                            {formatTime(notification.timestamp)}
                          </div>
                        </div>

                        {/* 未读指示器 */}
                        {!notification.read && (
                          <div
                            style={{
                              width: '8px',
                              height: '8px',
                              backgroundColor: 'var(--interactive-accent)',
                              borderRadius: '50%',
                              flexShrink: 0,
                              marginTop: '0.25rem'
                            }}
                          />
                        )}
                      </Flex>
                    </div>
                  ))}
                </Stack>
              ) : (
                <div
                  style={{
                    padding: '2rem',
                    textAlign: 'center',
                    color: 'var(--text-muted)'
                  }}
                >
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📭</div>
                  <p style={{ margin: 0 }}>暂无通知</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* 点击外部关闭 */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999
          }}
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};