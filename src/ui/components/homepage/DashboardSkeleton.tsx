// Dashboard骨架屏组件

import React from 'react';
import { Container, Grid, Stack, Flex } from '../layout/Layout';
import { <PERSON>, CardHeader, CardContent } from '../common/Card';

export const DashboardSkeleton: React.FC = () => {
  return (
    <div className="ptm-homepage ptm-homepage--loading">
      <Container padding>
        <Stack spacing="lg">
          {/* 页面头部骨架 */}
          <div className="ptm-homepage-header">
            <Flex justify="between" align="center">
              <div>
                <div className="ptm-skeleton ptm-skeleton--title" style={{
                  width: '300px',
                  height: '2rem',
                  marginBottom: '0.5rem'
                }} />
                <div className="ptm-skeleton ptm-skeleton--text" style={{
                  width: '200px',
                  height: '1rem'
                }} />
              </div>
              <div className="ptm-skeleton ptm-skeleton--button" style={{
                width: '200px',
                height: '2.5rem'
              }} />
            </Flex>
          </div>

          {/* 统计卡片骨架 */}
          <Grid cols={4} gap="md" responsive>
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} variant="outlined">
                <CardContent>
                  <div style={{ textAlign: 'center' }}>
                    <div className="ptm-skeleton ptm-skeleton--number" style={{
                      width: '60px',
                      height: '2.5rem',
                      margin: '0 auto 0.5rem auto'
                    }} />
                    <div className="ptm-skeleton ptm-skeleton--text" style={{
                      width: '80px',
                      height: '1rem',
                      margin: '0 auto'
                    }} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </Grid>

          {/* 快速访问面板骨架 */}
          <Card variant="outlined">
            <CardHeader>
              <div className="ptm-skeleton ptm-skeleton--text" style={{
                width: '120px',
                height: '1.25rem'
              }} />
            </CardHeader>
            <CardContent>
              <Grid cols={3} gap="sm" responsive>
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="ptm-skeleton ptm-skeleton--button" style={{
                    height: '80px',
                    borderRadius: '8px'
                  }} />
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* 内容区域骨架 */}
          <Grid cols={3} gap="md" responsive>
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i} variant="outlined">
                <CardHeader>
                  <Flex justify="between" align="center">
                    <div className="ptm-skeleton ptm-skeleton--text" style={{
                      width: '100px',
                      height: '1.25rem'
                    }} />
                    <div className="ptm-skeleton ptm-skeleton--button" style={{
                      width: '60px',
                      height: '1.5rem'
                    }} />
                  </Flex>
                </CardHeader>
                <CardContent>
                  <Stack spacing="sm">
                    {Array.from({ length: 3 }).map((_, j) => (
                      <div key={j} className="ptm-skeleton ptm-skeleton--item" style={{
                        height: '60px',
                        borderRadius: '4px'
                      }} />
                    ))}
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Grid>
        </Stack>
      </Container>

      {/* 骨架屏样式 */}
      {/* jsx样式已移除 */}
      {/*
        .ptm-skeleton {
          background: linear-gradient(
            90deg,
            var(--background-secondary) 25%,
            var(--background-modifier-hover) 50%,
            var(--background-secondary) 75%
          );
          background-size: 200% 100%;
          animation: ptm-skeleton-loading 1.5s infinite;
          border-radius: 4px;
        }

        .ptm-skeleton--title {
          height: 2rem;
          border-radius: 6px;
        }

        .ptm-skeleton--text {
          height: 1rem;
          border-radius: 4px;
        }

        .ptm-skeleton--number {
          height: 2.5rem;
          border-radius: 6px;
        }

        .ptm-skeleton--button {
          border-radius: 6px;
        }

        .ptm-skeleton--item {
          border-radius: 4px;
        }

        @keyframes ptm-skeleton-loading {
          0% {
            background-position: 200% 0;
          }
          100% {
            background-position: -200% 0;
          }
        }

        .ptm-homepage--loading {
          pointer-events: none;
          user-select: none;
        }
      */}
    </div>
  );
};