// HomepageDashboard组件测试

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { HomepageDashboard } from '../HomepageDashboard';
import { PTMManager } from '../../../../services/PTMManager';

// Mock dependencies
jest.mock('../../../../services/PTMManager');
jest.mock('obsidian', () => ({
  App: jest.fn(),
  Notice: jest.fn()
}));

// Mock子组件
jest.mock('../StatisticsOverview', () => ({
  StatisticsOverview: ({ projects, tasks, sprints, onStatClick }: any) => (
    <div data-testid="statistics-overview">
      <div>Projects: {projects.length}</div>
      <div>Tasks: {tasks.length}</div>
      <div>Sprints: {sprints.length}</div>
    </div>
  )
}));

jest.mock('../QuickAccessPanel', () => ({
  QuickAccessPanel: ({ onModuleClick, onCreateClick }: any) => (
    <div data-testid="quick-access-panel">
      <button onClick={() => onModuleClick('task-list')}>任务列表</button>
      <button onClick={() => onCreateClick('project')}>新建项目</button>
    </div>
  )
}));

jest.mock('../GlobalSearch', () => ({
  GlobalSearch: ({ onSearch, onResultSelect }: any) => (
    <div data-testid="global-search">
      <input 
        placeholder="搜索项目、任务..."
        onChange={(e) => onSearch(e.target.value)}
      />
    </div>
  )
}));

jest.mock('../NotificationCenter', () => ({
  NotificationCenter: () => (
    <div data-testid="notification-center">通知中心</div>
  )
}));

describe('HomepageDashboard', () => {
  let mockApp: any;
  let mockPTMManager: jest.Mocked<PTMManager>;
  let mockOnNavigate: jest.Mock;

  beforeEach(() => {
    mockApp = {
      workspace: {
        getLeaf: jest.fn().mockReturnValue({
          setViewState: jest.fn()
        })
      }
    };

    mockPTMManager = {
      getProjectManager: jest.fn().mockReturnValue({
        getAllProjects: jest.fn().mockResolvedValue([
          { id: '1', name: '测试项目', status: 'active', description: '测试描述' }
        ])
      }),
      getTaskRepository: jest.fn().mockReturnValue({
        findAll: jest.fn().mockResolvedValue([
          { id: '1', title: '测试任务', status: 'todo', priority: 'high', projectId: '1' }
        ])
      }),
      getSprintManager: jest.fn().mockReturnValue({
        getAllSprints: jest.fn().mockResolvedValue([])
      })
    } as any;

    mockOnNavigate = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染首页Dashboard', async () => {
    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    // 等待数据加载完成
    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 检查主要组件是否渲染
    expect(screen.getByTestId('statistics-overview')).toBeInTheDocument();
    expect(screen.getByTestId('quick-access-panel')).toBeInTheDocument();
    expect(screen.getByTestId('global-search')).toBeInTheDocument();
    expect(screen.getByTestId('notification-center')).toBeInTheDocument();
  });

  it('应该显示加载状态', () => {
    // Mock延迟的数据加载
    mockPTMManager.getProjectManager().getAllProjects.mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    // 应该显示骨架屏
    expect(document.querySelector('.ptm-homepage--loading')).toBeInTheDocument();
  });

  it('应该处理数据加载错误', async () => {
    // Mock数据加载失败
    mockPTMManager.getProjectManager().getAllProjects.mockRejectedValue(
      new Error('加载失败')
    );

    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('加载失败')).toBeInTheDocument();
      expect(screen.getByText('加载失败')).toBeInTheDocument();
    });

    // 应该有重新加载按钮
    const reloadButton = screen.getByText('重新加载');
    expect(reloadButton).toBeInTheDocument();
  });

  it('应该正确处理模块点击', async () => {
    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 点击任务列表按钮
    const taskListButton = screen.getByText('任务列表');
    fireEvent.click(taskListButton);

    // 应该调用Obsidian的视图切换
    expect(mockApp.workspace.getLeaf().setViewState).toHaveBeenCalledWith({
      type: 'project-task-manager-task-list',
      active: true
    });
  });

  it('应该正确处理快速创建', async () => {
    // Mock动态导入
    const mockProjectCreateModal = {
      open: jest.fn()
    };
    
    jest.doMock('../../common/ProjectCreateModal', () => ({
      ProjectCreateModal: jest.fn().mockImplementation(() => mockProjectCreateModal)
    }));

    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 点击新建项目按钮
    const createProjectButton = screen.getByText('新建项目');
    fireEvent.click(createProjectButton);

    // 由于动态导入的异步性质，这里需要等待
    await waitFor(() => {
      // 验证模态框被创建和打开的逻辑
      // 实际测试中可能需要更复杂的mock策略
    });
  });

  it('应该正确处理搜索', async () => {
    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 在搜索框中输入
    const searchInput = screen.getByPlaceholderText('搜索项目、任务...');
    fireEvent.change(searchInput, { target: { value: '测试' } });

    // 搜索功能目前是占位符，所以只验证输入是否正常
    expect(searchInput).toHaveValue('测试');
  });

  it('应该显示项目和任务数据', async () => {
    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 检查统计数据是否正确显示
    expect(screen.getByText('Projects: 1')).toBeInTheDocument();
    expect(screen.getByText('Tasks: 1')).toBeInTheDocument();
    expect(screen.getByText('Sprints: 0')).toBeInTheDocument();

    // 检查活跃项目是否显示
    expect(screen.getByText('测试项目')).toBeInTheDocument();
    expect(screen.getByText('测试描述')).toBeInTheDocument();

    // 检查关键任务是否显示
    expect(screen.getByText('测试任务')).toBeInTheDocument();
  });

  it('应该显示空状态', async () => {
    // Mock空数据
    mockPTMManager.getProjectManager().getAllProjects.mockResolvedValue([]);
    mockPTMManager.getTaskRepository().findAll.mockResolvedValue([]);

    render(
      <HomepageDashboard
        app={mockApp}
        ptmManager={mockPTMManager}
        onNavigate={mockOnNavigate}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('项目任务管理中心')).toBeInTheDocument();
    });

    // 应该显示空状态提示
    expect(screen.getByText('暂无活跃项目')).toBeInTheDocument();
    expect(screen.getByText('创建项目')).toBeInTheDocument();
  });
});