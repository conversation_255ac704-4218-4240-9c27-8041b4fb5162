// Dashboard错误边界组件

import React from 'react';
import { Card, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { Container, Stack, Flex } from '../layout/Layout';

interface DashboardErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface DashboardErrorBoundaryProps {
  children: React.ReactNode;
}

export class DashboardErrorBoundary extends React.Component<
  DashboardErrorBoundaryProps,
  DashboardErrorBoundaryState
> {
  constructor(props: DashboardErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): DashboardErrorBoundaryState {
    // 更新状态以显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    console.error('Dashboard错误边界捕获到错误:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // 这里可以添加错误报告逻辑
    // 例如发送到错误监控服务
  }

  handleReload = () => {
    // 重置错误状态
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    
    // 刷新页面
    window.location.reload();
  };

  handleReset = () => {
    // 只重置错误状态，不刷新页面
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Container padding>
          <Card variant="outlined" className="ptm-error-fallback">
            <CardContent>
              <Stack spacing="md" align="center">
                {/* 错误图标 */}
                <div style={{ 
                  fontSize: '4rem', 
                  color: 'var(--text-error)',
                  marginBottom: '1rem'
                }}>
                  ⚠️
                </div>
                
                {/* 错误标题 */}
                <h2 style={{ 
                  margin: 0, 
                  color: 'var(--text-error)',
                  fontSize: '1.5rem',
                  fontWeight: 600
                }}>
                  仪表板加载失败
                </h2>
                
                {/* 错误描述 */}
                <p style={{ 
                  margin: 0, 
                  color: 'var(--text-muted)',
                  textAlign: 'center',
                  maxWidth: '400px'
                }}>
                  抱歉，仪表板遇到了意外错误。这可能是由于数据加载问题或组件渲染异常导致的。
                </p>

                {/* 错误详情（开发模式下显示） */}
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details style={{ 
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: 'var(--background-secondary)',
                    borderRadius: '4px',
                    fontSize: '0.875rem',
                    fontFamily: 'monospace',
                    maxWidth: '600px',
                    overflow: 'auto'
                  }}>
                    <summary style={{ 
                      cursor: 'pointer',
                      fontWeight: 600,
                      marginBottom: '0.5rem'
                    }}>
                      错误详情
                    </summary>
                    <div style={{ color: 'var(--text-error)' }}>
                      <strong>错误信息:</strong> {this.state.error.message}
                    </div>
                    <div style={{ marginTop: '0.5rem' }}>
                      <strong>错误堆栈:</strong>
                      <pre style={{ 
                        margin: '0.5rem 0 0 0',
                        whiteSpace: 'pre-wrap',
                        fontSize: '0.75rem'
                      }}>
                        {this.state.error.stack}
                      </pre>
                    </div>
                    {this.state.errorInfo && (
                      <div style={{ marginTop: '0.5rem' }}>
                        <strong>组件堆栈:</strong>
                        <pre style={{ 
                          margin: '0.5rem 0 0 0',
                          whiteSpace: 'pre-wrap',
                          fontSize: '0.75rem'
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </details>
                )}

                {/* 操作按钮 */}
                <Flex gap="sm" direction="row">
                  <Button 
                    variant="primary" 
                    onClick={this.handleReload}
                  >
                    刷新页面
                  </Button>
                  <Button 
                    variant="secondary" 
                    onClick={this.handleReset}
                  >
                    重试加载
                  </Button>
                </Flex>

                {/* 帮助信息 */}
                <div style={{ 
                  marginTop: '1rem',
                  padding: '1rem',
                  backgroundColor: 'var(--background-secondary)',
                  borderRadius: '4px',
                  fontSize: '0.875rem',
                  color: 'var(--text-muted)',
                  textAlign: 'center'
                }}>
                  <p style={{ margin: '0 0 0.5rem 0' }}>
                    <strong>故障排除建议:</strong>
                  </p>
                  <ul style={{ 
                    margin: 0, 
                    paddingLeft: '1.5rem',
                    textAlign: 'left'
                  }}>
                    <li>检查是否有其他插件冲突</li>
                    <li>尝试重启Obsidian应用</li>
                    <li>查看开发者控制台的详细错误信息</li>
                    <li>如果问题持续，请联系插件开发者</li>
                  </ul>
                </div>
              </Stack>
            </CardContent>
          </Card>
        </Container>
      );
    }

    return this.props.children;
  }
}