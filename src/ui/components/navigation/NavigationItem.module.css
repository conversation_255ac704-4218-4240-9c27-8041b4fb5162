/**
 * PTM NavigationItem 组件样式
 * 完全隔离版本 - 确保不受Obsidian主题影响
 */

/* 主容器样式 - 使用双重选择器确保优先级 */
.ptm-app-container .navigationItem,
.navigationItem {
  display: flex !important;
  align-items: center !important;
  margin: 0 var(--ptm-spacing-2) !important;
  border-radius: var(--ptm-radius-base) !important;
  cursor: pointer !important;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth) !important;
  position: relative !important;
  min-height: 44px !important; /* 确保足够的点击区域 */
  
  /* 强制重置所有可能被Obsidian主题影响的属性 */
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-sm) !important;
  font-weight: var(--ptm-font-normal) !important;
  line-height: var(--ptm-leading-normal) !important;
  letter-spacing: normal !important;
  text-decoration: none !important;
  text-transform: none !important;
  text-shadow: none !important;
  
  /* 基础状态 */
  background-color: transparent !important;
  color: var(--ptm-text-secondary) !important;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4) !important;
  border: none !important;
  outline: none !important;
  box-sizing: border-box !important;
  box-shadow: none !important;
}

/* 激活状态 */
.ptm-app-container .navigationItem.active,
.navigationItem.active {
  background-color: var(--ptm-primary) !important;
  color: var(--ptm-text-inverse) !important;
  font-weight: var(--ptm-font-medium) !important;
}

/* 折叠状态 */
.ptm-app-container .navigationItem.collapsed,
.navigationItem.collapsed {
  padding: var(--ptm-spacing-3) !important;
  justify-content: center !important;
}

/* 禁用状态 */
.ptm-app-container .navigationItem.disabled,
.navigationItem.disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

/* hover效果 */
.ptm-app-container .navigationItem:not(.active):not(.disabled):hover,
.navigationItem:not(.active):not(.disabled):hover {
  background-color: var(--ptm-gray-100) !important;
  color: var(--ptm-text-primary) !important;
}

/* 焦点状态 */
.ptm-app-container .navigationItem:focus-visible,
.navigationItem:focus-visible {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

/* 图标容器 */
.ptm-app-container .icon,
.icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 20px !important;
  height: 20px !important;
  flex-shrink: 0 !important;
  position: relative !important;
  box-sizing: border-box !important;
}

.ptm-app-container .navigationItem:not(.collapsed) .icon,
.navigationItem:not(.collapsed) .icon {
  margin-right: var(--ptm-spacing-3) !important;
}

/* 图标文本样式 */
.ptm-app-container .iconText,
.iconText {
  font-size: var(--ptm-text-base) !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 20px !important;
  min-height: 20px !important;
  font-family: var(--ptm-font-family) !important;
  font-weight: inherit !important;
  color: inherit !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
}

.ptm-app-container .iconComponent,
.iconComponent {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: inherit !important;
}

/* 文本标签 */
.ptm-app-container .label,
.label {
  flex: 1 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  font-size: var(--ptm-text-sm) !important;
  line-height: var(--ptm-leading-normal) !important;
  font-family: var(--ptm-font-family) !important;
  font-weight: inherit !important;
  color: inherit !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

/* 徽章样式 */
.ptm-app-container .badge,
.badge {
  position: absolute !important;
  top: -6px !important;
  right: -6px !important;
  background-color: var(--ptm-danger) !important;
  color: var(--ptm-text-inverse) !important;
  font-size: var(--ptm-text-xs) !important;
  font-weight: var(--ptm-font-medium) !important;
  font-family: var(--ptm-font-family) !important;
  padding: 2px var(--ptm-spacing-1) !important;
  border-radius: var(--ptm-radius-full) !important;
  min-width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  z-index: 1 !important;
  border: none !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  box-sizing: border-box !important;
}

/* 激活状态指示器 */
.ptm-app-container .indicator,
.indicator {
  position: absolute !important;
  left: 0 !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 3px !important;
  height: 20px !important;
  background-color: var(--ptm-text-inverse) !important;
  border-radius: 0 2px 2px 0 !important;
  border: none !important;
  box-sizing: border-box !important;
}

/* 工具提示（折叠状态下显示） */
.ptm-app-container .tooltip,
.tooltip {
  position: absolute !important;
  left: 100% !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-left: var(--ptm-spacing-2) !important;
  background-color: var(--ptm-gray-800) !important;
  color: var(--ptm-text-inverse) !important;
  padding: var(--ptm-spacing-1) var(--ptm-spacing-2) !important;
  border-radius: var(--ptm-radius-sm) !important;
  font-size: var(--ptm-text-xs) !important;
  font-family: var(--ptm-font-family) !important;
  font-weight: var(--ptm-font-normal) !important;
  white-space: nowrap !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth) !important;
  z-index: var(--ptm-z-tooltip) !important;
  pointer-events: none !important;
  border: none !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  line-height: var(--ptm-leading-normal) !important;
  box-sizing: border-box !important;
}

/* 工具提示箭头 */
.ptm-app-container .tooltip::before,
.tooltip::before {
  content: '' !important;
  position: absolute !important;
  right: 100% !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-width: 4px !important;
  border-style: solid !important;
  border-color: transparent var(--ptm-gray-800) transparent transparent !important;
}

/* 折叠状态下hover显示工具提示 */
.ptm-app-container .navigationItem.collapsed:hover .tooltip,
.navigationItem.collapsed:hover .tooltip {
  opacity: 1 !important;
  visibility: visible !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ptm-app-container .navigationItem,
  .navigationItem {
    min-height: 48px !important; /* 移动端增加点击区域 */
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .ptm-app-container .navigationItem,
  .navigationItem,
  .ptm-app-container .tooltip,
  .tooltip {
    transition: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-app-container .navigationItem,
  .navigationItem {
    border: 1px solid var(--ptm-border-dark) !important;
  }
  
  .ptm-app-container .navigationItem.active,
  .navigationItem.active {
    border-color: var(--ptm-primary) !important;
  }
}

/* 确保在所有通用HTML元素上重置样式 */
.ptm-app-container .navigationItem *,
.navigationItem * {
  box-sizing: border-box !important;
}

/* 重置可能被Obsidian影响的通用元素 */
.ptm-app-container .navigationItem span,
.navigationItem span {
  font-family: var(--ptm-font-family) !important;
  color: inherit !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}

.ptm-app-container .navigationItem div,
.navigationItem div {
  font-family: var(--ptm-font-family) !important;
  color: inherit !important;
  text-decoration: none !important;
  text-shadow: none !important;
  letter-spacing: normal !important;
  text-transform: none !important;
}