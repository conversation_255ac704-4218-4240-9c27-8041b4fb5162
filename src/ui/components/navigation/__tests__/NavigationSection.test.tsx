import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NavigationSection } from '../NavigationSection';
import { NavigationItem } from '../NavigationItem';

// 模拟CSS模块
jest.mock('../NavigationSection.module.css', () => ({
  navigationSection: 'navigationSection',
  collapsed: 'collapsed',
  title: 'title',
  clickable: 'clickable',
  titleCollapsed: 'titleCollapsed',
  arrow: 'arrow',
  arrowExpanded: 'arrowExpanded',
  titleIcon: 'titleIcon',
  titleIconComponent: 'titleIconComponent',
  titleText: 'titleText',
  content: 'content',
  tooltip: 'tooltip'
}));

jest.mock('../NavigationItem.module.css', () => ({
  navigationItem: 'navigationItem',
  active: 'active',
  collapsed: 'collapsed',
  disabled: 'disabled',
  icon: 'icon',
  iconText: 'iconText',
  label: 'label',
  badge: 'badge',
  indicator: 'indicator',
  tooltip: 'tooltip'
}));

describe('NavigationSection', () => {
  // 使用简单的div元素作为子元素，避免与NavigationItem的button角色冲突
  const mockChildren = (
    <>
      <div>仪表板</div>
      <div>项目</div>
      <div>任务</div>
    </>
  );

  const defaultProps = {
    title: '主要功能',
    children: mockChildren
  };

  describe('基础渲染', () => {
    it('应该正确渲染导航分组', () => {
      render(<NavigationSection {...defaultProps} />);
      
      expect(screen.getByText('主要功能')).toBeInTheDocument();
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      expect(screen.getByText('项目')).toBeInTheDocument();
      expect(screen.getByText('任务')).toBeInTheDocument();
    });

    it('应该支持自定义图标', () => {
      render(<NavigationSection {...defaultProps} icon="🏠" />);
      
      expect(screen.getByText('🏠')).toBeInTheDocument();
    });

    it('应该支持React组件图标', () => {
      const IconComponent = () => <svg data-testid="section-icon">icon</svg>;
      render(<NavigationSection {...defaultProps} icon={<IconComponent />} />);
      
      expect(screen.getByTestId('section-icon')).toBeInTheDocument();
    });
  });

  describe('折叠功能', () => {
    it('默认应该展开显示内容', () => {
      render(<NavigationSection {...defaultProps} />);
      
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      expect(screen.getByRole('group')).toBeInTheDocument();
    });

    it('应该支持默认折叠状态', () => {
      render(<NavigationSection {...defaultProps} defaultExpanded={false} />);
      
      expect(screen.queryByText('仪表板')).not.toBeInTheDocument();
      expect(screen.queryByRole('group')).not.toBeInTheDocument();
    });

    it('点击标题应该切换展开状态', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const title = screen.getByLabelText(/主要功能 分组/);
      
      // 初始状态：展开
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      
      // 点击折叠
      fireEvent.click(title);
      expect(screen.queryByText('仪表板')).not.toBeInTheDocument();
      
      // 再次点击展开
      fireEvent.click(title);
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });

    it('应该支持键盘导航 - Enter键', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const title = screen.getByLabelText(/主要功能 分组/);
      
      // 初始状态：展开
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      
      // Enter键折叠
      fireEvent.keyDown(title, { key: 'Enter' });
      expect(screen.queryByText('仪表板')).not.toBeInTheDocument();
    });

    it('应该支持键盘导航 - 空格键', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const title = screen.getByLabelText(/主要功能 分组/);
      
      // 初始状态：展开
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      
      // 空格键折叠
      fireEvent.keyDown(title, { key: ' ' });
      expect(screen.queryByText('仪表板')).not.toBeInTheDocument();
    });

    it('不可折叠时不应该响应点击', () => {
      render(<NavigationSection {...defaultProps} collapsible={false} />);
      
      // 不可折叠时不应该有分组标题的button角色
      expect(screen.queryByLabelText(/主要功能 分组/)).not.toBeInTheDocument();
      
      // 内容应该始终显示
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });
  });

  describe('侧边栏折叠状态', () => {
    it('侧边栏折叠时不应该显示内容', () => {
      render(<NavigationSection {...defaultProps} collapsed />);
      
      expect(screen.queryByText('仪表板')).not.toBeInTheDocument();
      expect(screen.queryByRole('group')).not.toBeInTheDocument();
    });

    it('侧边栏折叠时不应该显示标题文本', () => {
      render(<NavigationSection {...defaultProps} collapsed />);
      
      // 标题文本不在主要位置显示，但在工具提示中显示
      const titleElements = screen.getAllByText('主要功能');
      expect(titleElements.length).toBe(1); // 只在工具提示中显示
    });

    it('侧边栏折叠时不应该显示箭头', () => {
      const { container } = render(<NavigationSection {...defaultProps} collapsed />);
      
      const arrow = container.querySelector('.arrow');
      expect(arrow).not.toBeInTheDocument();
    });

    it('侧边栏折叠时应该显示工具提示', () => {
      render(<NavigationSection {...defaultProps} collapsed />);
      
      expect(screen.getByText('主要功能')).toBeInTheDocument();
    });
  });

  describe('可访问性', () => {
    it('可折叠分组应该有正确的ARIA属性', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const title = screen.getByLabelText(/主要功能 分组/);
      expect(title).toHaveAttribute('aria-expanded', 'true');
      expect(title).toHaveAttribute('aria-label');
      expect(title).toHaveAttribute('tabIndex', '0');
    });

    it('不可折叠分组不应该有button角色', () => {
      render(<NavigationSection {...defaultProps} collapsible={false} />);
      
      expect(screen.queryByLabelText(/主要功能 分组/)).not.toBeInTheDocument();
    });

    it('分组内容应该有正确的group角色', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const content = screen.getByRole('group');
      expect(content).toHaveAttribute('aria-labelledby');
    });

    it('箭头应该有aria-hidden属性', () => {
      render(<NavigationSection {...defaultProps} />);
      
      // 查找包含箭头符号的元素
      const arrow = screen.getByText('▶');
      expect(arrow).toHaveAttribute('aria-hidden', 'true');
    });
  });

  describe('状态管理', () => {
    it('应该正确管理展开状态', () => {
      render(<NavigationSection {...defaultProps} />);
      
      const title = screen.getByLabelText(/主要功能 分组/);
      
      // 初始状态
      expect(title).toHaveAttribute('aria-expanded', 'true');
      
      // 折叠后
      fireEvent.click(title);
      expect(title).toHaveAttribute('aria-expanded', 'false');
      
      // 再次展开
      fireEvent.click(title);
      expect(title).toHaveAttribute('aria-expanded', 'true');
    });
  });

  describe('自定义样式', () => {
    it('应该支持自定义CSS类名', () => {
      const { container } = render(
        <NavigationSection {...defaultProps} className="custom-section" />
      );
      
      expect(container.firstChild).toHaveClass('custom-section');
    });
  });

  describe('边界情况', () => {
    it('应该处理空子元素', () => {
      render(<NavigationSection title="空分组" children={null} />);
      
      expect(screen.getByText('空分组')).toBeInTheDocument();
    });

    it('应该处理没有图标的情况', () => {
      render(<NavigationSection {...defaultProps} />);
      
      expect(screen.getByText('主要功能')).toBeInTheDocument();
    });
  });
});