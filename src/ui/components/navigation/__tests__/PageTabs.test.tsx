import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PageTabs, PageTab } from '../PageTabs';

// 模拟CSS模块
jest.mock('../PageTabs.module.css', () => ({
  pageTabs: 'pageTabs',
  top: 'top',
  bottom: 'bottom',
  tabsContainer: 'tabsContainer',
  tab: 'tab',
  active: 'active',
  disabled: 'disabled',
  tabContent: 'tabContent',
  tabIcon: 'tabIcon',
  tabIconComponent: 'tabIconComponent',
  tabLabel: 'tabLabel',
  tabBadge: 'tabBadge',
  closeButton: 'closeButton',
  activeIndicator: 'activeIndicator',
  scrollButton: 'scrollButton',
  scrollLeft: 'scrollLeft',
  scrollRight: 'scrollRight',
  addButton: 'addButton'
}));

describe('PageTabs', () => {
  const mockTabs: PageTab[] = [
    { id: 'dashboard', label: '仪表板', icon: '📊' },
    { id: 'projects', label: '项目', icon: '📁', badge: 5 },
    { id: 'tasks', label: '任务', icon: '✅', closable: true },
    { id: 'kanban', label: '看板', icon: '📋', disabled: true }
  ];

  const defaultProps = {
    tabs: mockTabs,
    activeTabId: 'dashboard',
    onTabChange: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基础渲染', () => {
    it('应该正确渲染标签列表', () => {
      render(<PageTabs {...defaultProps} />);
      
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      expect(screen.getByText('项目')).toBeInTheDocument();
      expect(screen.getByText('任务')).toBeInTheDocument();
      expect(screen.getByText('看板')).toBeInTheDocument();
    });

    it('应该显示标签图标', () => {
      render(<PageTabs {...defaultProps} />);
      
      expect(screen.getByText('📊')).toBeInTheDocument();
      expect(screen.getByText('📁')).toBeInTheDocument();
      expect(screen.getByText('✅')).toBeInTheDocument();
      expect(screen.getByText('📋')).toBeInTheDocument();
    });

    it('应该支持React组件图标', () => {
      const tabsWithComponentIcon: PageTab[] = [
        { 
          id: 'custom', 
          label: '自定义', 
          icon: <svg data-testid="custom-icon">icon</svg> 
        }
      ];
      
      render(
        <PageTabs 
          tabs={tabsWithComponentIcon} 
          activeTabId="custom" 
          onTabChange={jest.fn()} 
        />
      );
      
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('应该显示徽章', () => {
      render(<PageTabs {...defaultProps} />);
      
      expect(screen.getByText('5')).toBeInTheDocument();
    });

    it('应该显示99+当徽章数字超过99', () => {
      const tabsWithLargeBadge: PageTab[] = [
        { id: 'test', label: '测试', badge: 150 }
      ];
      
      render(
        <PageTabs 
          tabs={tabsWithLargeBadge} 
          activeTabId="test" 
          onTabChange={jest.fn()} 
        />
      );
      
      expect(screen.getByText('99+')).toBeInTheDocument();
    });
  });

  describe('标签状态', () => {
    it('应该正确显示激活状态', () => {
      render(<PageTabs {...defaultProps} />);
      
      const activeTab = screen.getByRole('tab', { name: /仪表板/ });
      expect(activeTab).toHaveAttribute('aria-selected', 'true');
      expect(activeTab).toHaveClass('active');
    });

    it('应该正确显示禁用状态', () => {
      render(<PageTabs {...defaultProps} />);
      
      const disabledTab = screen.getByRole('tab', { name: /看板/ });
      expect(disabledTab).toHaveAttribute('aria-disabled', 'true');
      expect(disabledTab).toHaveAttribute('tabIndex', '-1');
      expect(disabledTab).toHaveClass('disabled');
    });
  });

  describe('交互功能', () => {
    it('应该在点击标签时调用onTabChange', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} onTabChange={onTabChange} />);
      
      const projectTab = screen.getByRole('tab', { name: /项目/ });
      fireEvent.click(projectTab);
      
      expect(onTabChange).toHaveBeenCalledWith('projects');
    });

    it('禁用的标签不应该响应点击', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} onTabChange={onTabChange} />);
      
      const disabledTab = screen.getByRole('tab', { name: /看板/ });
      fireEvent.click(disabledTab);
      
      expect(onTabChange).not.toHaveBeenCalled();
    });

    it('应该支持键盘导航 - Enter键', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} onTabChange={onTabChange} />);
      
      const projectTab = screen.getByRole('tab', { name: /项目/ });
      fireEvent.keyDown(projectTab, { key: 'Enter' });
      
      expect(onTabChange).toHaveBeenCalledWith('projects');
    });

    it('应该支持键盘导航 - 空格键', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} onTabChange={onTabChange} />);
      
      const projectTab = screen.getByRole('tab', { name: /项目/ });
      fireEvent.keyDown(projectTab, { key: ' ' });
      
      expect(onTabChange).toHaveBeenCalledWith('projects');
    });

    it('应该支持键盘导航 - 箭头键', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} onTabChange={onTabChange} />);
      
      const activeTab = screen.getByRole('tab', { name: /仪表板/ });
      fireEvent.keyDown(activeTab, { key: 'ArrowRight' });
      
      expect(onTabChange).toHaveBeenCalledWith('projects');
    });

    it('箭头键应该跳过禁用的标签', () => {
      const onTabChange = jest.fn();
      render(<PageTabs {...defaultProps} activeTabId="tasks" onTabChange={onTabChange} />);
      
      const activeTab = screen.getByRole('tab', { name: /任务/ });
      fireEvent.keyDown(activeTab, { key: 'ArrowRight' });
      
      // 应该跳过禁用的看板标签，但由于是最后一个，不会有变化
      expect(onTabChange).not.toHaveBeenCalled();
    });
  });

  describe('关闭功能', () => {
    it('应该显示关闭按钮', () => {
      const onTabClose = jest.fn();
      render(<PageTabs {...defaultProps} onTabClose={onTabClose} />);
      
      expect(screen.getByLabelText('关闭 任务 标签')).toBeInTheDocument();
    });

    it('应该在点击关闭按钮时调用onTabClose', () => {
      const onTabClose = jest.fn();
      render(<PageTabs {...defaultProps} onTabClose={onTabClose} />);
      
      const closeButton = screen.getByLabelText('关闭 任务 标签');
      fireEvent.click(closeButton);
      
      expect(onTabClose).toHaveBeenCalledWith('tasks');
    });

    it('关闭按钮点击不应该触发标签切换', () => {
      const onTabChange = jest.fn();
      const onTabClose = jest.fn();
      render(
        <PageTabs 
          {...defaultProps} 
          onTabChange={onTabChange} 
          onTabClose={onTabClose} 
        />
      );
      
      const closeButton = screen.getByLabelText('关闭 任务 标签');
      fireEvent.click(closeButton);
      
      expect(onTabClose).toHaveBeenCalledWith('tasks');
      expect(onTabChange).not.toHaveBeenCalled();
    });
  });

  describe('添加按钮', () => {
    it('应该显示添加按钮', () => {
      const onAddTab = jest.fn();
      render(
        <PageTabs 
          {...defaultProps} 
          showAddButton={true} 
          onAddTab={onAddTab} 
        />
      );
      
      expect(screen.getByLabelText('添加新标签')).toBeInTheDocument();
    });

    it('应该在点击添加按钮时调用onAddTab', () => {
      const onAddTab = jest.fn();
      render(
        <PageTabs 
          {...defaultProps} 
          showAddButton={true} 
          onAddTab={onAddTab} 
        />
      );
      
      const addButton = screen.getByLabelText('添加新标签');
      fireEvent.click(addButton);
      
      expect(onAddTab).toHaveBeenCalled();
    });
  });

  describe('位置设置', () => {
    it('应该支持顶部位置', () => {
      const { container } = render(<PageTabs {...defaultProps} position="top" />);
      
      expect(container.firstChild).toHaveClass('top');
    });

    it('应该支持底部位置', () => {
      const { container } = render(<PageTabs {...defaultProps} position="bottom" />);
      
      expect(container.firstChild).toHaveClass('bottom');
    });
  });

  describe('可访问性', () => {
    it('应该有正确的ARIA属性', () => {
      render(<PageTabs {...defaultProps} />);
      
      const tablist = screen.getByRole('tablist');
      expect(tablist).toHaveAttribute('aria-orientation', 'horizontal');
      
      const tabs = screen.getAllByRole('tab');
      tabs.forEach((tab, index) => {
        expect(tab).toHaveAttribute('aria-selected');
        expect(tab).toHaveAttribute('tabIndex');
      });
    });

    it('激活的标签应该有正确的aria-selected', () => {
      render(<PageTabs {...defaultProps} />);
      
      const activeTab = screen.getByRole('tab', { name: /仪表板/ });
      expect(activeTab).toHaveAttribute('aria-selected', 'true');
      
      const inactiveTab = screen.getByRole('tab', { name: /项目/ });
      expect(inactiveTab).toHaveAttribute('aria-selected', 'false');
    });
  });

  describe('自定义样式', () => {
    it('应该支持自定义CSS类名', () => {
      const { container } = render(
        <PageTabs {...defaultProps} className="custom-tabs" />
      );
      
      expect(container.firstChild).toHaveClass('custom-tabs');
    });

    it('应该支持最大标签宽度', () => {
      render(<PageTabs {...defaultProps} maxTabWidth={150} />);
      
      const tab = screen.getByRole('tab', { name: /仪表板/ });
      expect(tab).toHaveStyle({ maxWidth: '150px' });
    });
  });

  describe('边界情况', () => {
    it('应该处理空标签列表', () => {
      render(
        <PageTabs 
          tabs={[]} 
          activeTabId="" 
          onTabChange={jest.fn()} 
        />
      );
      
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('应该处理不存在的激活标签ID', () => {
      render(
        <PageTabs 
          {...defaultProps} 
          activeTabId="nonexistent" 
        />
      );
      
      // 不应该有激活的标签
      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected', 'false');
      });
    });

    it('应该处理没有图标的标签', () => {
      const tabsWithoutIcon: PageTab[] = [
        { id: 'simple', label: '简单标签' }
      ];
      
      render(
        <PageTabs 
          tabs={tabsWithoutIcon} 
          activeTabId="simple" 
          onTabChange={jest.fn()} 
        />
      );
      
      expect(screen.getByText('简单标签')).toBeInTheDocument();
    });
  });
});