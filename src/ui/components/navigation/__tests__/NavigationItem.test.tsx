import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { NavigationItem } from '../NavigationItem';
import { checkStyleIsolation } from '../../../utils/styleIsolationChecker';

// 模拟CSS模块
jest.mock('../NavigationItem.module.css', () => ({
  navigationItem: 'navigationItem',
  active: 'active',
  collapsed: 'collapsed',
  disabled: 'disabled',
  icon: 'icon',
  iconText: 'iconText',
  iconComponent: 'iconComponent',
  label: 'label',
  badge: 'badge',
  indicator: 'indicator',
  tooltip: 'tooltip'
}));

// 模拟样式隔离检查器
jest.mock('../../../utils/styleIsolationChecker', () => ({
  checkStyleIsolation: jest.fn(),
  devStyleIsolationWarning: jest.fn(),
}));

describe('NavigationItem', () => {
  const defaultProps = {
    icon: '📊',
    label: '仪表板',
    onClick: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基础渲染', () => {
    it('应该正确渲染导航项', () => {
      render(<NavigationItem {...defaultProps} />);
      
      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByText('📊')).toBeInTheDocument();
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });

    it('应该支持字符串图标', () => {
      render(<NavigationItem {...defaultProps} icon="🏠" />);
      
      expect(screen.getByText('🏠')).toBeInTheDocument();
    });

    it('应该支持React组件图标', () => {
      const IconComponent = () => <svg data-testid="custom-icon">icon</svg>;
      render(<NavigationItem {...defaultProps} icon={<IconComponent />} />);
      
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });
  });

  describe('交互功能', () => {
    it('应该在点击时调用onClick', () => {
      const onClick = jest.fn();
      render(<NavigationItem {...defaultProps} onClick={onClick} />);
      
      fireEvent.click(screen.getByRole('button'));
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('应该支持键盘导航 - Enter键', () => {
      const onClick = jest.fn();
      render(<NavigationItem {...defaultProps} onClick={onClick} />);
      
      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('应该支持键盘导航 - 空格键', () => {
      const onClick = jest.fn();
      render(<NavigationItem {...defaultProps} onClick={onClick} />);
      
      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: ' ' });
      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('禁用状态下不应该响应点击', () => {
      const onClick = jest.fn();
      render(<NavigationItem {...defaultProps} onClick={onClick} disabled />);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      expect(onClick).not.toHaveBeenCalled();
    });

    it('禁用状态下不应该响应键盘事件', () => {
      const onClick = jest.fn();
      render(<NavigationItem {...defaultProps} onClick={onClick} disabled />);
      
      const button = screen.getByRole('button');
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(onClick).not.toHaveBeenCalled();
    });
  });

  describe('状态管理', () => {
    it('应该正确显示激活状态', () => {
      render(<NavigationItem {...defaultProps} active />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-pressed', 'true');
      expect(button).toHaveClass('active');
    });

    it('应该正确显示禁用状态', () => {
      render(<NavigationItem {...defaultProps} disabled />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-disabled', 'true');
      expect(button).toHaveAttribute('tabIndex', '-1');
      expect(button).toHaveClass('disabled');
    });

    it('应该正确显示折叠状态', () => {
      render(<NavigationItem {...defaultProps} collapsed />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('collapsed');
      // 折叠状态下文本标签不在主要位置显示，但在工具提示中显示
      const labelElements = screen.getAllByText('仪表板');
      expect(labelElements.length).toBe(1); // 只在工具提示中显示
    });
  });

  describe('徽章功能', () => {
    it('应该显示徽章数字', () => {
      render(<NavigationItem {...defaultProps} badge={5} />);
      
      expect(screen.getByText('5')).toBeInTheDocument();
    });

    it('应该显示99+当数字超过99', () => {
      render(<NavigationItem {...defaultProps} badge={150} />);
      
      expect(screen.getByText('99+')).toBeInTheDocument();
    });

    it('不应该显示0或负数徽章', () => {
      const { container } = render(<NavigationItem {...defaultProps} badge={0} />);
      
      // 检查徽章元素是否不存在
      const badgeElement = container.querySelector('.badge');
      expect(badgeElement).not.toBeInTheDocument();
    });
  });

  describe('可访问性', () => {
    it('应该有正确的ARIA属性', () => {
      render(<NavigationItem {...defaultProps} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', '仪表板');
      expect(button).toHaveAttribute('aria-pressed', 'false');
      expect(button).toHaveAttribute('tabIndex', '0');
    });

    it('激活状态应该有正确的ARIA属性', () => {
      render(<NavigationItem {...defaultProps} active />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-pressed', 'true');
    });

    it('禁用状态应该有正确的ARIA属性', () => {
      render(<NavigationItem {...defaultProps} disabled />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-disabled', 'true');
      expect(button).toHaveAttribute('tabIndex', '-1');
    });
  });

  describe('自定义样式', () => {
    it('应该支持自定义CSS类名', () => {
      render(<NavigationItem {...defaultProps} className="custom-class" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });
  });

  describe('工具提示', () => {
    it('折叠状态下应该显示工具提示', () => {
      render(<NavigationItem {...defaultProps} collapsed />);
      
      // 工具提示应该存在但默认隐藏
      expect(screen.getByText('仪表板')).toBeInTheDocument();
    });
  });

  describe('样式隔离测试', () => {
    beforeEach(() => {
      // 重置mock
      (checkStyleIsolation as jest.Mock).mockClear();
    });

    it('应该应用正确的CSS模块类名', () => {
      render(<NavigationItem {...defaultProps} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('navigationItem');
      
      // 检查子元素的类名
      const iconElement = button.querySelector('.icon');
      const labelElement = button.querySelector('.label');
      
      expect(iconElement).toBeInTheDocument();
      expect(labelElement).toBeInTheDocument();
    });

    it('应该为不同状态应用正确的类名', () => {
      const { rerender } = render(<NavigationItem {...defaultProps} />);
      
      let button = screen.getByRole('button');
      expect(button).toHaveClass('navigationItem');
      expect(button).not.toHaveClass('active');
      expect(button).not.toHaveClass('collapsed');
      expect(button).not.toHaveClass('disabled');
      
      // 测试激活状态
      rerender(<NavigationItem {...defaultProps} active />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('navigationItem', 'active');
      
      // 测试折叠状态
      rerender(<NavigationItem {...defaultProps} collapsed />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('navigationItem', 'collapsed');
      
      // 测试禁用状态
      rerender(<NavigationItem {...defaultProps} disabled />);
      button = screen.getByRole('button');
      expect(button).toHaveClass('navigationItem', 'disabled');
    });

    it('应该在激活状态下显示指示器', () => {
      render(<NavigationItem {...defaultProps} active />);
      
      const button = screen.getByRole('button');
      const indicator = button.querySelector('.indicator');
      
      expect(indicator).toBeInTheDocument();
    });

    it('应该为徽章应用正确的类名', () => {
      render(<NavigationItem {...defaultProps} badge={5} />);
      
      const button = screen.getByRole('button');
      const badge = button.querySelector('.badge');
      
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('badge');
      expect(badge).toHaveTextContent('5');
    });

    it('应该通过样式隔离检查', () => {
      const mockCheckResult = {
        passed: true,
        details: {
          hasProjectPrefix: true,
          hasContainerClass: true,
          hasProperCSSVars: true,
          hasGlobalStyleReset: true,
        },
        suggestions: [],
      };
      
      (checkStyleIsolation as jest.Mock).mockReturnValue(mockCheckResult);
      
      render(<NavigationItem {...defaultProps} />);
      
      const button = screen.getByRole('button');
      const result = checkStyleIsolation(button);
      
      expect(checkStyleIsolation).toHaveBeenCalledWith(button);
      expect(result.passed).toBe(true);
      expect(result.suggestions).toHaveLength(0);
    });

    it('应该检测样式隔离问题', () => {
      const mockCheckResult = {
        passed: false,
        details: {
          hasProjectPrefix: false,
          hasContainerClass: false,
          hasProperCSSVars: false,
          hasGlobalStyleReset: false,
        },
        suggestions: [
          '元素缺少项目前缀类名',
          '元素不在 .ptm-app-container 容器内',
          '建议使用项目CSS变量'
        ],
      };
      
      (checkStyleIsolation as jest.Mock).mockReturnValue(mockCheckResult);
      
      render(<NavigationItem {...defaultProps} />);
      
      const button = screen.getByRole('button');
      const result = checkStyleIsolation(button);
      
      expect(result.passed).toBe(false);
      expect(result.suggestions).toHaveLength(3);
      expect(result.suggestions).toContain('元素缺少项目前缀类名');
    });

    it('应该正确处理图标文本的样式类', () => {
      render(<NavigationItem {...defaultProps} icon="📊" />);
      
      const button = screen.getByRole('button');
      const iconText = button.querySelector('.iconText');
      
      expect(iconText).toBeInTheDocument();
      expect(iconText).toHaveClass('iconText');
      expect(iconText).toHaveTextContent('📊');
    });

    it('应该正确处理React组件图标的样式类', () => {
      const IconComponent = () => <svg data-testid="custom-icon">icon</svg>;
      render(<NavigationItem {...defaultProps} icon={<IconComponent />} />);
      
      const button = screen.getByRole('button');
      const iconComponent = button.querySelector('.iconComponent');
      
      expect(iconComponent).toBeInTheDocument();
      expect(iconComponent).toHaveClass('iconComponent');
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('应该在折叠状态下正确显示工具提示样式', () => {
      render(<NavigationItem {...defaultProps} collapsed />);
      
      const button = screen.getByRole('button');
      const tooltip = button.querySelector('.tooltip');
      
      expect(tooltip).toBeInTheDocument();
      expect(tooltip).toHaveClass('tooltip');
      expect(tooltip).toHaveTextContent('仪表板');
    });
  });

  describe('CSS变量使用测试', () => {
    it('应该使用PTM CSS变量前缀', () => {
      render(<NavigationItem {...defaultProps} />);
      
      const button = screen.getByRole('button');
      
      // 这里我们测试组件是否正确应用了CSS模块
      // 实际的CSS变量使用会在CSS文件中定义
      expect(button).toHaveClass('navigationItem');
      
      // 在实际应用中，这些样式会通过CSS模块和CSS变量系统应用
      // 我们主要测试类名的正确应用
    });
  });
});