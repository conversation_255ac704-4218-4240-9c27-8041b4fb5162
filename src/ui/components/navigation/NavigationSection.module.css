/**
 * NavigationSection 组件样式
 * 使用CSS模块确保样式隔离，避免与Obsidian主题冲突
 */

.navigationSection {
  margin-bottom: var(--ptm-spacing-4);
  position: relative;
}

/* 分组标题 */
.title {
  display: flex;
  align-items: center;
  padding: var(--ptm-spacing-2) var(--ptm-spacing-4);
  margin: 0 var(--ptm-spacing-2);
  border-radius: var(--ptm-radius-sm);
  
  /* 确保字体不被Obsidian主题覆盖 */
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-xs) !important;
  font-weight: var(--ptm-font-semibold) !important;
  color: var(--ptm-text-muted) !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: var(--ptm-leading-tight);
  
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* 可点击的标题 */
.title.clickable {
  cursor: pointer;
  user-select: none;
}

.title.clickable:hover {
  background-color: var(--ptm-gray-100);
  color: var(--ptm-text-secondary);
}

.title.clickable:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

/* 折叠状态下的标题 */
.title.titleCollapsed {
  opacity: 0.7;
}

/* 展开/折叠箭头 */
.arrow {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: var(--ptm-spacing-2);
  font-size: 8px;
  line-height: 1;
  transition: transform var(--ptm-duration-normal) var(--ptm-easing-smooth);
  transform-origin: center;
  color: var(--ptm-text-muted);
}

.arrow.arrowExpanded {
  transform: rotate(90deg);
}

/* 标题图标 */
.titleIcon,
.titleIconComponent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: var(--ptm-spacing-2);
  flex-shrink: 0;
}

.titleIcon {
  font-size: var(--ptm-text-sm);
  line-height: 1;
}

/* 标题文本 */
.titleText {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分组内容 */
.content {
  padding-left: var(--ptm-spacing-2);
  animation: ptm-fade-in var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* 折叠状态 */
.navigationSection.collapsed {
  margin-bottom: var(--ptm-spacing-2);
}

.navigationSection.collapsed .title {
  padding: var(--ptm-spacing-3);
  justify-content: center;
  margin: 0 var(--ptm-spacing-2);
}

/* 工具提示（折叠状态下显示） */
.tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: var(--ptm-spacing-2);
  background-color: var(--ptm-gray-800);
  color: var(--ptm-text-inverse);
  padding: var(--ptm-spacing-1) var(--ptm-spacing-2);
  border-radius: var(--ptm-radius-sm);
  font-size: var(--ptm-text-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth);
  z-index: var(--ptm-z-tooltip);
  pointer-events: none;
}

/* 工具提示箭头 */
.tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 4px;
  border-style: solid;
  border-color: transparent var(--ptm-gray-800) transparent transparent;
}

/* 折叠状态下hover显示工具提示 */
.navigationSection.collapsed:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* 分隔线（可选） */
.navigationSection + .navigationSection::before {
  content: '';
  display: block;
  height: 1px;
  background-color: var(--ptm-border-light);
  margin: var(--ptm-spacing-4) var(--ptm-spacing-4) 0;
}

/* 第一个分组不需要分隔线 */
.navigationSection:first-child::before {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    min-height: 40px; /* 移动端增加点击区域 */
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .title,
  .arrow,
  .content,
  .tooltip {
    transition: none;
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .title {
    border: 1px solid var(--ptm-border-dark);
  }
  
  .title.clickable:hover {
    border-color: var(--ptm-primary);
  }
}

/* 深色主题适配（如果需要） */
@media (prefers-color-scheme: dark) {
  .tooltip {
    background-color: var(--ptm-gray-700);
  }
  
  .tooltip::before {
    border-color: transparent var(--ptm-gray-700) transparent transparent;
  }
}