import React from 'react';
import styles from './NavigationItem.module.css';

/**
 * NavigationItem 组件属性接口
 */
export interface NavigationItemProps {
  /** 图标名称或图标组件 */
  icon: string | React.ReactNode;
  /** 导航项文本标签 */
  label: string;
  /** 是否为激活状态 */
  active?: boolean;
  /** 是否为折叠状态 */
  collapsed?: boolean;
  /** 点击事件处理函数 */
  onClick: () => void;
  /** 自定义CSS类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 徽章数字（如未读消息数） */
  badge?: number;
}

/**
 * NavigationItem 导航项组件
 * 
 * 功能特性：
 * - 支持图标和文本显示
 * - 激活状态视觉反馈
 * - 折叠状态下的简化显示
 * - hover和点击效果
 * - 样式隔离，不受Obsidian主题影响
 */
export const NavigationItem: React.FC<NavigationItemProps> = ({
  icon,
  label,
  active = false,
  collapsed = false,
  onClick,
  className = '',
  disabled = false,
  badge
}) => {
  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (!disabled) {
        onClick();
      }
    }
  };

  // 渲染图标
  const renderIcon = () => {
    if (typeof icon === 'string') {
      return (
        <span className={styles.iconText}>
          {icon}
        </span>
      );
    }
    return (
      <span className={styles.iconComponent}>
        {icon}
      </span>
    );
  };

  // 构建CSS类名
  const itemClasses = [
    styles.navigationItem,
    active && styles.active,
    collapsed && styles.collapsed,
    disabled && styles.disabled,
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      className={itemClasses}
      onClick={disabled ? undefined : onClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-label={label}
      aria-pressed={active}
      aria-disabled={disabled}
    >
      {/* 图标容器 */}
      <div className={styles.icon}>
        {renderIcon()}
        
        {/* 徽章 */}
        {badge !== undefined && badge > 0 && (
          <span className={styles.badge}>
            {badge > 99 ? '99+' : badge}
          </span>
        )}
      </div>

      {/* 文本标签 */}
      {!collapsed && (
        <span className={styles.label}>
          {label}
        </span>
      )}

      {/* 激活状态指示器 */}
      {active && (
        <div className={styles.indicator} />
      )}

      {/* 折叠状态下的工具提示 */}
      {collapsed && (
        <div className={styles.tooltip}>
          {label}
        </div>
      )}
    </div>
  );
};

export default NavigationItem;