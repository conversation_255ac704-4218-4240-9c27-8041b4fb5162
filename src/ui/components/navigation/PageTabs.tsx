import React, { useRef, useEffect, useState } from 'react';
import { designTokens } from '../../styles/tokens';
import styles from './PageTabs.module.css';

/**
 * 页面标签项接口
 */
export interface PageTab {
  /** 标签唯一标识 */
  id: string;
  /** 标签显示文本 */
  label: string;
  /** 标签图标 */
  icon?: string | React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
  /** 徽章数字 */
  badge?: number;
  /** 是否可关闭 */
  closable?: boolean;
}

/**
 * PageTabs 组件属性接口
 */
export interface PageTabsProps {
  /** 标签列表 */
  tabs: PageTab[];
  /** 当前激活的标签ID */
  activeTabId: string;
  /** 标签切换回调 */
  onTabChange: (tabId: string) => void;
  /** 标签关闭回调 */
  onTabClose?: (tabId: string) => void;
  /** 自定义CSS类名 */
  className?: string;
  /** 是否显示添加按钮 */
  showAddButton?: boolean;
  /** 添加标签回调 */
  onAddTab?: () => void;
  /** 标签栏位置 */
  position?: 'top' | 'bottom';
  /** 是否可滚动 */
  scrollable?: boolean;
  /** 最大标签宽度 */
  maxTabWidth?: number;
}

/**
 * PageTabs 页面标签组件
 * 
 * 功能特性：
 * - 支持标签的激活状态管理
 * - 响应式标签布局
 * - 可滚动的标签栏
 * - 支持标签关闭功能
 * - 样式隔离，不受Obsidian主题影响
 */
export const PageTabs: React.FC<PageTabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  onTabClose,
  className = '',
  showAddButton = false,
  onAddTab,
  position = 'top',
  scrollable = true,
  maxTabWidth = 200
}) => {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // 检查是否需要滚动按钮
  const checkScrollButtons = () => {
    if (!scrollable || !tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const needsScroll = container.scrollWidth > container.clientWidth;
    setShowScrollButtons(needsScroll);

    if (needsScroll) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  };

  // 监听容器大小变化和滚动事件
  useEffect(() => {
    checkScrollButtons();
    
    const container = tabsContainerRef.current;
    if (!container) return;

    const handleScroll = () => checkScrollButtons();
    const handleResize = () => checkScrollButtons();

    container.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [tabs, scrollable]);

  // 滚动到指定位置
  const scrollTo = (direction: 'left' | 'right') => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const scrollAmount = container.clientWidth * 0.8;
    const newScrollLeft = direction === 'left' 
      ? container.scrollLeft - scrollAmount
      : container.scrollLeft + scrollAmount;

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  };

  // 滚动到激活的标签
  const scrollToActiveTab = () => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const activeTab = container.querySelector(`[data-tab-id="${activeTabId}"]`) as HTMLElement;
    
    if (activeTab) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = activeTab.getBoundingClientRect();
      
      if (tabRect.left < containerRect.left || tabRect.right > containerRect.right) {
        activeTab.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      }
    }
  };

  // 当激活标签改变时，滚动到该标签
  useEffect(() => {
    scrollToActiveTab();
  }, [activeTabId]);

  // 处理标签点击
  const handleTabClick = (tabId: string, disabled?: boolean) => {
    if (!disabled) {
      onTabChange(tabId);
    }
  };

  // 处理标签关闭
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose?.(tabId);
  };

  // 处理键盘导航
  const handleKeyDown = (e: React.KeyboardEvent, tabId: string, disabled?: boolean) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleTabClick(tabId, disabled);
    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      e.preventDefault();
      const currentIndex = tabs.findIndex(tab => tab.id === tabId);
      const nextIndex = e.key === 'ArrowLeft' 
        ? Math.max(0, currentIndex - 1)
        : Math.min(tabs.length - 1, currentIndex + 1);
      
      if (nextIndex !== currentIndex && !tabs[nextIndex].disabled) {
        onTabChange(tabs[nextIndex].id);
      }
    }
  };

  // 渲染标签图标
  const renderTabIcon = (icon?: string | React.ReactNode) => {
    if (!icon) return null;

    if (typeof icon === 'string') {
      return (
        <span className={styles.tabIcon}>
          {icon}
        </span>
      );
    }
    return (
      <span className={styles.tabIconComponent}>
        {icon}
      </span>
    );
  };

  // 构建CSS类名
  const containerClasses = [
    styles.pageTabs,
    styles[position],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {/* 左滚动按钮 */}
      {showScrollButtons && canScrollLeft && (
        <button
          className={`${styles.scrollButton} ${styles.scrollLeft}`}
          onClick={() => scrollTo('left')}
          aria-label="向左滚动标签"
          type="button"
        >
          ◀
        </button>
      )}

      {/* 标签容器 */}
      <div
        ref={tabsContainerRef}
        className={styles.tabsContainer}
        role="tablist"
        aria-orientation="horizontal"
      >
        {tabs.map((tab) => {
          const isActive = tab.id === activeTabId;
          
          const tabClasses = [
            styles.tab,
            isActive && styles.active,
            tab.disabled && styles.disabled
          ].filter(Boolean).join(' ');

          return (
            <div
              key={tab.id}
              data-tab-id={tab.id}
              className={tabClasses}
              role="tab"
              tabIndex={tab.disabled ? -1 : 0}
              aria-selected={isActive}
              aria-disabled={tab.disabled}
              onClick={() => handleTabClick(tab.id, tab.disabled)}
              onKeyDown={(e) => handleKeyDown(e, tab.id, tab.disabled)}
              style={{
                maxWidth: maxTabWidth ? `${maxTabWidth}px` : undefined
              }}
            >
              {/* 标签内容 */}
              <div className={styles.tabContent}>
                {/* 图标 */}
                {renderTabIcon(tab.icon)}
                
                {/* 标签文本 */}
                <span className={styles.tabLabel}>
                  {tab.label}
                </span>
                
                {/* 徽章 */}
                {tab.badge !== undefined && tab.badge > 0 && (
                  <span className={styles.tabBadge}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </span>
                )}
              </div>

              {/* 关闭按钮 */}
              {tab.closable && onTabClose && (
                <button
                  className={styles.closeButton}
                  onClick={(e) => handleTabClose(e, tab.id)}
                  aria-label={`关闭 ${tab.label} 标签`}
                  type="button"
                >
                  ×
                </button>
              )}

              {/* 激活指示器 */}
              {isActive && (
                <div className={styles.activeIndicator} />
              )}
            </div>
          );
        })}
      </div>

      {/* 右滚动按钮 */}
      {showScrollButtons && canScrollRight && (
        <button
          className={`${styles.scrollButton} ${styles.scrollRight}`}
          onClick={() => scrollTo('right')}
          aria-label="向右滚动标签"
          type="button"
        >
          ▶
        </button>
      )}

      {/* 添加标签按钮 */}
      {showAddButton && onAddTab && (
        <button
          className={styles.addButton}
          onClick={onAddTab}
          aria-label="添加新标签"
          type="button"
        >
          +
        </button>
      )}
    </div>
  );
};

export default PageTabs;