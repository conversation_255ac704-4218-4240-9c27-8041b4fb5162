/**
 * PageTabs 组件样式
 * 使用CSS模块确保样式隔离，避免与Obsidian主题冲突
 */

.pageTabs {
  display: flex;
  align-items: center;
  position: relative;
  background: var(--ptm-bg-primary);
  border-bottom: 1px solid var(--ptm-border-light);
  
  /* 确保字体不被Obsidian主题覆盖 */
  font-family: var(--ptm-font-family) !important;
}

/* 底部位置 */
.pageTabs.bottom {
  border-bottom: none;
  border-top: 1px solid var(--ptm-border-light);
}

/* 标签容器 */
.tabsContainer {
  display: flex;
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* 隐藏滚动条 */
.tabsContainer::-webkit-scrollbar {
  display: none;
}

/* 单个标签 */
.tab {
  display: flex;
  align-items: center;
  position: relative;
  min-width: 120px;
  height: 48px;
  padding: 0 var(--ptm-spacing-4);
  cursor: pointer;
  user-select: none;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  flex-shrink: 0;
  
  /* 基础样式 */
  background: transparent;
  color: var(--ptm-text-secondary);
  border: none;
  border-radius: 0;
  
  /* 确保样式不被Obsidian主题覆盖 */
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-sm) !important;
  font-weight: var(--ptm-font-normal) !important;
}

/* 标签hover效果 */
.tab:hover:not(.disabled):not(.active) {
  background: var(--ptm-gray-100);
  color: var(--ptm-text-primary);
}

/* 激活状态 */
.tab.active {
  background: var(--ptm-bg-primary);
  color: var(--ptm-primary);
  font-weight: var(--ptm-font-medium);
}

/* 禁用状态 */
.tab.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 焦点状态 */
.tab:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: -2px;
  z-index: 1;
}

/* 标签内容 */
.tabContent {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 允许文本截断 */
}

/* 标签图标 */
.tabIcon,
.tabIconComponent {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: var(--ptm-spacing-2);
  flex-shrink: 0;
}

.tabIcon {
  font-size: var(--ptm-text-sm);
  line-height: 1;
}

/* 标签文本 */
.tabLabel {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--ptm-text-sm);
  line-height: var(--ptm-leading-normal);
}

/* 标签徽章 */
.tabBadge {
  margin-left: var(--ptm-spacing-2);
  background: var(--ptm-danger);
  color: var(--ptm-text-inverse);
  font-size: var(--ptm-text-xs);
  font-weight: var(--ptm-font-medium);
  padding: 2px var(--ptm-spacing-1);
  border-radius: var(--ptm-radius-full);
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  flex-shrink: 0;
}

/* 关闭按钮 */
.closeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: var(--ptm-spacing-2);
  background: transparent;
  border: none;
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  color: var(--ptm-text-muted);
  font-size: 16px;
  line-height: 1;
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth);
  flex-shrink: 0;
  
  /* 确保字体不被覆盖 */
  font-family: var(--ptm-font-family) !important;
}

.closeButton:hover {
  background: var(--ptm-gray-200);
  color: var(--ptm-text-primary);
}

.closeButton:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 1px;
}

/* 激活指示器 */
.activeIndicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--ptm-primary);
  border-radius: 1px 1px 0 0;
}

/* 底部位置的激活指示器 */
.pageTabs.bottom .activeIndicator {
  bottom: auto;
  top: 0;
  border-radius: 0 0 1px 1px;
}

/* 滚动按钮 */
.scrollButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 48px;
  background: var(--ptm-bg-primary);
  border: none;
  border-right: 1px solid var(--ptm-border-light);
  cursor: pointer;
  color: var(--ptm-text-secondary);
  font-size: var(--ptm-text-sm);
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth);
  flex-shrink: 0;
  z-index: 1;
  
  /* 确保字体不被覆盖 */
  font-family: var(--ptm-font-family) !important;
}

.scrollButton:hover {
  background: var(--ptm-gray-100);
  color: var(--ptm-text-primary);
}

.scrollButton:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: -2px;
}

.scrollButton.scrollLeft {
  border-right: 1px solid var(--ptm-border-light);
  border-left: none;
}

.scrollButton.scrollRight {
  border-left: 1px solid var(--ptm-border-light);
  border-right: none;
}

/* 添加按钮 */
.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 48px;
  background: var(--ptm-bg-primary);
  border: none;
  border-left: 1px solid var(--ptm-border-light);
  cursor: pointer;
  color: var(--ptm-text-secondary);
  font-size: 18px;
  font-weight: var(--ptm-font-normal);
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth);
  flex-shrink: 0;
  
  /* 确保字体不被覆盖 */
  font-family: var(--ptm-font-family) !important;
}

.addButton:hover {
  background: var(--ptm-gray-100);
  color: var(--ptm-primary);
}

.addButton:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: -2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab {
    min-width: 100px;
    padding: 0 var(--ptm-spacing-3);
  }
  
  .tabLabel {
    font-size: var(--ptm-text-xs);
  }
  
  .scrollButton,
  .addButton {
    width: 28px;
  }
}

@media (max-width: 480px) {
  .tab {
    min-width: 80px;
    padding: 0 var(--ptm-spacing-2);
  }
  
  .tabIcon,
  .tabIconComponent {
    width: 14px;
    height: 14px;
    margin-right: var(--ptm-spacing-1);
  }
  
  .closeButton {
    width: 18px;
    height: 18px;
    margin-left: var(--ptm-spacing-1);
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .tab,
  .closeButton,
  .scrollButton,
  .addButton {
    transition: none;
  }
  
  .tabsContainer {
    scroll-behavior: auto;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .tab {
    border: 1px solid var(--ptm-border-dark);
  }
  
  .tab.active {
    border-color: var(--ptm-primary);
    background: var(--ptm-primary);
    color: var(--ptm-text-inverse);
  }
  
  .activeIndicator {
    height: 3px;
  }
}

/* 深色主题适配（如果需要） */
@media (prefers-color-scheme: dark) {
  .pageTabs {
    background: var(--ptm-gray-800);
    border-color: var(--ptm-gray-700);
  }
  
  .tab.active {
    background: var(--ptm-gray-800);
  }
  
  .scrollButton,
  .addButton {
    background: var(--ptm-gray-800);
    border-color: var(--ptm-gray-700);
  }
}