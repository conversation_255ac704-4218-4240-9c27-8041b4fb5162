import React, { useState } from 'react';
import { designTokens } from '../../styles/tokens';
import styles from './NavigationSection.module.css';

/**
 * NavigationSection 组件属性接口
 */
export interface NavigationSectionProps {
  /** 分组标题 */
  title: string;
  /** 子导航项 */
  children: React.ReactNode;
  /** 是否为折叠状态（侧边栏折叠） */
  collapsed?: boolean;
  /** 是否可折叠分组内容 */
  collapsible?: boolean;
  /** 默认是否展开分组内容 */
  defaultExpanded?: boolean;
  /** 自定义CSS类名 */
  className?: string;
  /** 分组图标 */
  icon?: string | React.ReactNode;
}

/**
 * NavigationSection 导航分组组件
 * 
 * 功能特性：
 * - 支持分组标题和子项管理
 * - 可折叠的分组内容
 * - 侧边栏折叠状态下的简化显示
 * - 样式隔离，不受Obsidian主题影响
 */
export const NavigationSection: React.FC<NavigationSectionProps> = ({
  title,
  children,
  collapsed = false,
  collapsible = true,
  defaultExpanded = true,
  className = '',
  icon
}) => {
  // 分组内容展开/折叠状态
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  // 切换展开状态
  const toggleExpanded = () => {
    if (collapsible && !collapsed) {
      setIsExpanded(!isExpanded);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleExpanded();
    }
  };

  // 渲染标题图标
  const renderIcon = () => {
    if (!icon) return null;
    
    if (typeof icon === 'string') {
      return (
        <span className={styles.titleIcon}>
          {icon}
        </span>
      );
    }
    return (
      <span className={styles.titleIconComponent}>
        {icon}
      </span>
    );
  };

  // 渲染展开/折叠箭头
  const renderArrow = () => {
    if (!collapsible || collapsed) return null;
    
    return (
      <span 
        className={`${styles.arrow} ${isExpanded ? styles.arrowExpanded : ''}`}
        aria-hidden="true"
      >
        ▶
      </span>
    );
  };

  // 构建CSS类名
  const sectionClasses = [
    styles.navigationSection,
    collapsed && styles.collapsed,
    className
  ].filter(Boolean).join(' ');

  const titleClasses = [
    styles.title,
    collapsible && !collapsed && styles.clickable,
    !isExpanded && styles.titleCollapsed
  ].filter(Boolean).join(' ');

  return (
    <div className={sectionClasses}>
      {/* 分组标题 */}
      <div
        className={titleClasses}
        onClick={toggleExpanded}
        onKeyDown={handleKeyDown}
        role={collapsible && !collapsed ? 'button' : undefined}
        tabIndex={collapsible && !collapsed ? 0 : undefined}
        aria-expanded={collapsible ? isExpanded : undefined}
        aria-label={collapsible ? `${title} 分组，${isExpanded ? '已展开' : '已折叠'}` : title}
      >
        {/* 展开/折叠箭头 */}
        {renderArrow()}
        
        {/* 标题图标 */}
        {renderIcon()}
        
        {/* 标题文本 */}
        {!collapsed && (
          <span className={styles.titleText}>
            {title}
          </span>
        )}
      </div>

      {/* 分组内容 */}
      {(!collapsed && isExpanded) && (
        <div 
          className={styles.content}
          role="group"
          aria-labelledby={`nav-section-${title}`}
        >
          {children}
        </div>
      )}

      {/* 折叠状态下的工具提示 */}
      {collapsed && (
        <div className={styles.tooltip}>
          {title}
        </div>
      )}
    </div>
  );
};

export default NavigationSection;