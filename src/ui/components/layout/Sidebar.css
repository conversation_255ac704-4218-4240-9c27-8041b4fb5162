/**
 * Sidebar 侧边栏样式
 * 可折叠的左侧导航栏样式定义
 */

/* 侧边栏主容器 */
.ptm-sidebar {
  width: var(--ptm-sidebar-width);
  background: var(--ptm-bg-primary);
  border-right: 1px solid var(--ptm-border-light);
  display: flex;
  flex-direction: column;
  box-shadow: var(--ptm-shadow-base);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  position: relative;
  z-index: var(--ptm-z-fixed);
  height: 100vh;
  overflow: hidden;
}

/* 折叠状态 */
.ptm-sidebar.ptm-collapsed {
  width: var(--ptm-sidebar-collapsed-width);
}

/* 侧边栏头部 */
.ptm-sidebar-header {
  padding: var(--ptm-spacing-6);
  border-bottom: 1px solid var(--ptm-border-light);
  position: relative;
  flex-shrink: 0;
  min-height: var(--ptm-header-height);
  display: flex;
  align-items: center;
}

/* Logo区域 */
.ptm-logo {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-3);
  font-size: var(--ptm-text-lg);
  font-weight: 600;
  color: var(--ptm-text-primary);
  cursor: pointer;
  transition: all var(--ptm-duration-fast);
  flex: 1;
}

.ptm-logo:hover {
  color: var(--ptm-primary);
}

.ptm-logo-icon {
  width: var(--ptm-spacing-8);
  height: var(--ptm-spacing-8);
  background: var(--ptm-primary-gradient);
  border-radius: var(--ptm-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ptm-text-inverse);
  font-weight: 700;
  font-size: var(--ptm-text-sm);
  flex-shrink: 0;
}

.ptm-logo-text {
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* 折叠按钮 */
.ptm-sidebar-toggle {
  position: absolute;
  right: var(--ptm-spacing-4);
  top: 50%;
  transform: translateY(-50%);
  width: var(--ptm-spacing-6);
  height: var(--ptm-spacing-6);
  border: none;
  background: var(--ptm-gray-100);
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ptm-gray-600);
  transition: all var(--ptm-duration-fast);
}

.ptm-sidebar-toggle:hover {
  background: var(--ptm-gray-200);
  color: var(--ptm-gray-800);
}

.ptm-sidebar-toggle:focus {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

.ptm-toggle-icon {
  transition: transform var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

.ptm-collapsed .ptm-toggle-icon {
  transform: rotate(180deg);
}

/* 导航区域 */
.ptm-sidebar-nav {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--ptm-spacing-4) 0;
}

/* 导航项 */
.ptm-nav-item {
  margin: var(--ptm-spacing-1) var(--ptm-spacing-4);
}

.ptm-nav-item.ptm-nav-item--child {
  margin-left: var(--ptm-spacing-8);
}

.ptm-nav-link {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-3);
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
  border-radius: var(--ptm-radius-base);
  color: var(--ptm-text-secondary);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--ptm-duration-fast);
  width: 100%;
  text-align: left;
  font-size: var(--ptm-text-sm);
  font-weight: 500;
  position: relative;
}

.ptm-nav-link:hover {
  background: var(--ptm-gray-100);
  color: var(--ptm-text-primary);
}

.ptm-nav-link:focus {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

.ptm-nav-link.ptm-active {
  background: rgba(102, 126, 234, 0.1);
  color: var(--ptm-primary);
  font-weight: 600;
}

.ptm-nav-link.ptm-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--ptm-primary);
  border-radius: 0 var(--ptm-radius-sm) var(--ptm-radius-sm) 0;
}

/* 折叠状态下的导航项 */
.ptm-collapsed .ptm-nav-link {
  justify-content: center;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
}

.ptm-collapsed .ptm-nav-link.ptm-active::before {
  display: none;
}

/* 导航图标 */
.ptm-nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--ptm-spacing-5);
  height: var(--ptm-spacing-5);
  flex-shrink: 0;
  font-size: var(--ptm-text-base);
}

/* 导航文本 */
.ptm-nav-text {
  flex: 1;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  white-space: nowrap;
  overflow: hidden;
}

/* 侧边栏文本通用样式（用于折叠时隐藏） */
.ptm-sidebar-text {
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

.ptm-collapsed .ptm-sidebar-text {
  opacity: 0;
  visibility: hidden;
  width: 0;
}

/* 子导航项 */
.ptm-nav-children {
  margin-top: var(--ptm-spacing-2);
  padding-left: var(--ptm-spacing-6);
  border-left: 1px solid var(--ptm-border-light);
  margin-left: var(--ptm-spacing-6);
}

.ptm-nav-children .ptm-nav-link {
  font-size: var(--ptm-text-xs);
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ptm-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: var(--ptm-z-modal);
    transform: translateX(-100%);
  }
  
  .ptm-sidebar:not(.ptm-collapsed) {
    transform: translateX(0);
  }
  
  /* 移动设备上的遮罩层 */
  .ptm-sidebar:not(.ptm-collapsed)::after {
    content: '';
    position: fixed;
    top: 0;
    left: var(--ptm-sidebar-width);
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .ptm-sidebar {
    width: 240px;
  }
  
  .ptm-collapsed .ptm-sidebar {
    width: var(--ptm-sidebar-collapsed-width);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-sidebar {
    border-right: 2px solid var(--ptm-border-dark);
  }
  
  .ptm-nav-link.ptm-active {
    background: var(--ptm-primary);
    color: var(--ptm-text-inverse);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .ptm-sidebar,
  .ptm-sidebar-text,
  .ptm-nav-text,
  .ptm-toggle-icon {
    transition: none !important;
  }
}

/* 打印样式 */
@media print {
  .ptm-sidebar {
    display: none;
  }
}

/* 滚动条样式 */
.ptm-sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.ptm-sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.ptm-sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.ptm-sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* 无障碍支持 */
.ptm-sidebar[aria-hidden="true"] {
  visibility: hidden;
}

/* 键盘导航支持 */
.ptm-nav-link:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

/* 工具提示支持（折叠状态） */
.ptm-collapsed .ptm-nav-link[title] {
  position: relative;
}

.ptm-collapsed .ptm-nav-link[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--ptm-gray-900);
  color: var(--ptm-text-inverse);
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  border-radius: var(--ptm-radius-sm);
  font-size: var(--ptm-text-xs);
  white-space: nowrap;
  z-index: var(--ptm-z-tooltip);
  margin-left: var(--ptm-spacing-2);
  pointer-events: none;
}

.ptm-collapsed .ptm-nav-link[title]:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: var(--ptm-gray-900);
  margin-left: -2px;
  z-index: var(--ptm-z-tooltip);
  pointer-events: none;
}