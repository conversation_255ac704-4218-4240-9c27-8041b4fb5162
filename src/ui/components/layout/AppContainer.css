/**
 * AppContainer 样式
 * 主应用容器的样式定义，确保与Obsidian主题隔离
 */

/* 主容器样式 */
.ptm-app-container {
  /* 基础布局 */
  display: flex;
  height: 100vh;
  overflow: hidden;
  
  /* 样式隔离 - 重置可能被Obsidian主题影响的属性 */
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-base) !important;
  line-height: 1.6 !important;
  color: var(--ptm-text-primary) !important;
  background: var(--ptm-bg-secondary) !important;
  
  /* 确保盒模型正确 */
  box-sizing: border-box;
  
  /* 重置可能的外边距和内边距 */
  margin: 0 !important;
  padding: 0 !important;
  
  /* 确保定位正确 */
  position: relative;
  
  /* 防止文本选择影响拖拽 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 确保所有子元素都使用正确的盒模型 */
.ptm-app-container *,
.ptm-app-container *::before,
.ptm-app-container *::after {
  box-sizing: border-box;
}

/* 重置可能被Obsidian主题影响的标题样式 */
.ptm-app-container h1,
.ptm-app-container h2,
.ptm-app-container h3,
.ptm-app-container h4,
.ptm-app-container h5,
.ptm-app-container h6 {
  font-family: var(--ptm-font-family) !important;
  color: var(--ptm-text-primary) !important;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: 600 !important;
}

/* 重置按钮样式 */
.ptm-app-container button {
  font-family: var(--ptm-font-family) !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 重置输入框样式 */
.ptm-app-container input,
.ptm-app-container textarea,
.ptm-app-container select {
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-base) !important;
  color: var(--ptm-text-primary) !important;
}

/* 重置链接样式 */
.ptm-app-container a {
  color: var(--ptm-primary) !important;
  text-decoration: none !important;
}

.ptm-app-container a:hover {
  text-decoration: underline !important;
}

/* 响应式布局 - 移动设备 */
.ptm-app-container.ptm-mobile-layout {
  flex-direction: column;
}

/* 响应式布局 - 平板设备 */
.ptm-app-container.ptm-tablet-layout {
  /* 平板设备保持水平布局但可能需要调整间距 */
}

/* 响应式断点样式 */
@media (max-width: 480px) {
  .ptm-app-container.ptm-responsive-mobile {
    flex-direction: column;
    height: 100vh;
    overflow-x: hidden;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .ptm-app-container.ptm-responsive-tablet {
    /* 平板特定样式 */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .ptm-app-container.ptm-responsive-desktop {
    /* 桌面特定样式 */
  }
}

@media (min-width: 1025px) {
  .ptm-app-container.ptm-responsive-wide {
    /* 宽屏特定样式 */
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* 加载状态 */
.ptm-app-container.ptm-loading {
  pointer-events: none;
  opacity: 0.7;
}

/* 错误状态 */
.ptm-app-container.ptm-error {
  background: var(--ptm-bg-primary) !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ptm-app-container {
    border: 2px solid var(--ptm-border-dark);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .ptm-app-container,
  .ptm-app-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .ptm-app-container {
    height: auto !important;
    overflow: visible !important;
    background: white !important;
    color: black !important;
  }
}

/* 焦点管理 */
.ptm-app-container:focus-within {
  /* 当容器内有焦点元素时的样式 */
}

/* 确保滚动条样式一致 */
.ptm-app-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ptm-app-container ::-webkit-scrollbar-track {
  background: var(--ptm-gray-100);
  border-radius: var(--ptm-radius-sm);
}

.ptm-app-container ::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.ptm-app-container ::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* Firefox 滚动条样式 */
.ptm-app-container * {
  scrollbar-width: thin;
  scrollbar-color: var(--ptm-gray-300) var(--ptm-gray-100);
}