/**
 * MainContent 主内容区样式
 * 包含搜索栏、头部操作区和内容区域的样式定义
 */

/* 主内容区容器 */
.ptm-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: var(--ptm-bg-secondary);
}

/* 主内容头部 */
.ptm-main-header {
  background: var(--ptm-bg-primary);
  border-bottom: 1px solid var(--ptm-border-light);
  padding: var(--ptm-spacing-4) var(--ptm-spacing-6);
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-4);
  flex-shrink: 0;
  min-height: var(--ptm-header-height);
  box-shadow: var(--ptm-shadow-sm);
}

/* 搜索区域 */
.ptm-search-section {
  flex: 1;
  max-width: 400px;
}

.ptm-search-bar {
  width: 100%;
}

.ptm-search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--ptm-bg-secondary);
  border: 1px solid var(--ptm-border-normal);
  border-radius: var(--ptm-radius-base);
  transition: all var(--ptm-duration-fast);
}

.ptm-search-input-wrapper:focus-within {
  border-color: var(--ptm-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ptm-search-icon {
  position: absolute;
  left: var(--ptm-spacing-3);
  color: var(--ptm-text-muted);
  font-size: var(--ptm-text-sm);
  pointer-events: none;
  z-index: 1;
}

.ptm-search-input {
  width: 100%;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-10) var(--ptm-spacing-3) var(--ptm-spacing-10);
  border: none;
  background: transparent;
  font-size: var(--ptm-text-sm);
  color: var(--ptm-text-primary);
  outline: none;
}

.ptm-search-input::placeholder {
  color: var(--ptm-text-muted);
}

.ptm-search-clear {
  position: absolute;
  right: var(--ptm-spacing-3);
  width: var(--ptm-spacing-5);
  height: var(--ptm-spacing-5);
  border: none;
  background: transparent;
  color: var(--ptm-text-muted);
  cursor: pointer;
  border-radius: var(--ptm-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ptm-duration-fast);
}

.ptm-search-clear:hover {
  background: var(--ptm-gray-200);
  color: var(--ptm-text-primary);
}

.ptm-search-clear:focus {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

/* 头部操作区域 */
.ptm-header-actions-section {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-4);
  flex-shrink: 0;
}

.ptm-page-title {
  font-size: var(--ptm-text-xl);
  font-weight: 600;
  color: var(--ptm-text-primary);
  margin: 0;
  white-space: nowrap;
}

.ptm-header-actions {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-2);
}

/* 内容区域 */
.ptm-content-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ptm-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--ptm-spacing-6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ptm-main-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--ptm-spacing-3);
    padding: var(--ptm-spacing-4);
  }
  
  .ptm-search-section {
    max-width: none;
    order: 2;
  }
  
  .ptm-header-actions-section {
    order: 1;
    justify-content: space-between;
  }
  
  .ptm-page-title {
    font-size: var(--ptm-text-lg);
  }
  
  .ptm-content-wrapper {
    padding: var(--ptm-spacing-4);
  }
}

@media (max-width: 480px) {
  .ptm-main-header {
    padding: var(--ptm-spacing-3);
  }
  
  .ptm-page-title {
    font-size: var(--ptm-text-base);
  }
  
  .ptm-content-wrapper {
    padding: var(--ptm-spacing-3);
  }
  
  .ptm-search-input {
    padding: var(--ptm-spacing-2) var(--ptm-spacing-8) var(--ptm-spacing-2) var(--ptm-spacing-8);
    font-size: var(--ptm-text-sm);
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .ptm-search-section {
    max-width: 300px;
  }
  
  .ptm-content-wrapper {
    padding: var(--ptm-spacing-5);
  }
}

/* 无搜索栏时的布局调整 */
.ptm-main-content:not(:has(.ptm-search-section)) .ptm-main-header {
  justify-content: space-between;
}

/* 无头部时的内容区域 */
.ptm-main-content:not(:has(.ptm-main-header)) .ptm-content-area {
  height: 100vh;
}

/* 滚动条样式 */
.ptm-content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.ptm-content-wrapper::-webkit-scrollbar-track {
  background: var(--ptm-gray-100);
  border-radius: var(--ptm-radius-sm);
}

.ptm-content-wrapper::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.ptm-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* 加载状态 */
.ptm-main-content.ptm-loading {
  pointer-events: none;
}

.ptm-main-content.ptm-loading .ptm-content-wrapper {
  opacity: 0.7;
}

/* 空状态 */
.ptm-content-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--ptm-text-muted);
  text-align: center;
  padding: var(--ptm-spacing-8);
}

.ptm-content-empty-icon {
  font-size: var(--ptm-text-4xl);
  margin-bottom: var(--ptm-spacing-4);
  opacity: 0.5;
}

.ptm-content-empty-title {
  font-size: var(--ptm-text-lg);
  font-weight: 600;
  margin-bottom: var(--ptm-spacing-2);
  color: var(--ptm-text-secondary);
}

.ptm-content-empty-description {
  font-size: var(--ptm-text-sm);
  max-width: 400px;
  line-height: var(--ptm-leading-relaxed);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-search-input-wrapper {
    border: 2px solid var(--ptm-border-dark);
  }
  
  .ptm-search-input-wrapper:focus-within {
    border-color: var(--ptm-primary);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .ptm-search-input-wrapper,
  .ptm-search-clear {
    transition: none !important;
  }
}

/* 打印样式 */
@media print {
  .ptm-main-header {
    display: none;
  }
  
  .ptm-content-wrapper {
    padding: 0;
    overflow: visible;
  }
}

/* 焦点管理 */
.ptm-search-input:focus {
  outline: none;
}

.ptm-search-input-wrapper:focus-within {
  outline: none;
}

/* 键盘导航支持 */
.ptm-search-clear:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

/* 搜索建议（预留） */
.ptm-search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--ptm-bg-primary);
  border: 1px solid var(--ptm-border-normal);
  border-top: none;
  border-radius: 0 0 var(--ptm-radius-base) var(--ptm-radius-base);
  box-shadow: var(--ptm-shadow-md);
  z-index: var(--ptm-z-dropdown);
  max-height: 200px;
  overflow-y: auto;
}

.ptm-search-suggestion {
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
  cursor: pointer;
  border-bottom: 1px solid var(--ptm-border-light);
  transition: background-color var(--ptm-duration-fast);
}

.ptm-search-suggestion:hover,
.ptm-search-suggestion:focus {
  background: var(--ptm-gray-100);
}

.ptm-search-suggestion:last-child {
  border-bottom: none;
}

/* 搜索快捷键提示 */
.ptm-search-shortcut {
  position: absolute;
  right: var(--ptm-spacing-10);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--ptm-text-xs);
  color: var(--ptm-text-muted);
  background: var(--ptm-gray-100);
  padding: 2px var(--ptm-spacing-2);
  border-radius: var(--ptm-radius-sm);
  pointer-events: none;
}

/* 搜索状态指示器 */
.ptm-search-loading {
  position: absolute;
  right: var(--ptm-spacing-3);
  top: 50%;
  transform: translateY(-50%);
  width: var(--ptm-spacing-4);
  height: var(--ptm-spacing-4);
  border: 2px solid var(--ptm-gray-300);
  border-top: 2px solid var(--ptm-primary);
  border-radius: 50%;
  animation: ptm-spin 1s linear infinite;
}

@keyframes ptm-spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}