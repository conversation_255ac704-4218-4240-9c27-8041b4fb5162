/**
 * Sidebar 侧边栏组件
 * 可折叠的左侧导航栏，支持导航项激活状态管理和动画效果
 */

import React, { useState } from 'react';
import { classNames, bem } from '../../utils/styles';
import { useSidebarState } from '../../hooks/useResponsive';
import './Sidebar.css';

export interface SidebarItem {
  /** 唯一标识 */
  id: string;
  /** 显示文本 */
  label: string;
  /** 图标类名或React组件 */
  icon: string | React.ReactNode;
  /** 是否激活 */
  active?: boolean;
  /** 点击回调 */
  onClick?: () => void;
  /** 子项目 */
  children?: SidebarItem[];
}

export interface SidebarProps {
  /** 导航项列表 */
  items: SidebarItem[];
  /** 是否默认折叠 */
  defaultCollapsed?: boolean;
  /** 折叠状态变化回调 */
  onCollapseChange?: (collapsed: boolean) => void;
  /** 额外的CSS类名 */
  className?: string;
  /** Logo配置 */
  logo?: {
    icon: string | React.ReactNode;
    text: string;
    onClick?: () => void;
  };
}

/**
 * 侧边栏组件
 */
export const Sidebar: React.FC<SidebarProps> = ({
  items,
  defaultCollapsed = false,
  onCollapseChange,
  className,
  logo,
}) => {
  const { isCollapsed, toggleSidebar } = useSidebarState(defaultCollapsed);
  const [activeItem, setActiveItem] = useState<string | null>(null);

  // 处理折叠切换
  const handleToggle = () => {
    toggleSidebar();
    onCollapseChange?.(isCollapsed);
  };

  // 处理导航项点击
  const handleItemClick = (item: SidebarItem) => {
    setActiveItem(item.id);
    item.onClick?.();
  };

  // 渲染导航项
  const renderNavItem = (item: SidebarItem, level = 0) => {
    const isActive = activeItem === item.id || item.active;
    
    return (
      <div key={item.id} className={bem('nav-item', undefined, level > 0 ? 'child' : undefined)}>
        <button
          className={classNames(
            'nav-link',
            isActive && 'active',
            isCollapsed && 'collapsed'
          )}
          onClick={() => handleItemClick(item)}
          title={isCollapsed ? item.label : undefined}
        >
          <span className={classNames('nav-icon')}>
            {typeof item.icon === 'string' ? (
              <i className={item.icon} />
            ) : (
              item.icon
            )}
          </span>
          {!isCollapsed && (
            <span className={classNames('nav-text', 'sidebar-text')}>
              {item.label}
            </span>
          )}
        </button>
        
        {/* 子项目 */}
        {item.children && !isCollapsed && (
          <div className={classNames('nav-children')}>
            {item.children.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={classNames(
        'sidebar',
        isCollapsed && 'collapsed',
        className
      )}
    >
      {/* 侧边栏头部 */}
      <div className={classNames('sidebar-header')}>
        {logo && (
          <div
            className={classNames('logo')}
            onClick={logo.onClick}
            role={logo.onClick ? 'button' : undefined}
            tabIndex={logo.onClick ? 0 : undefined}
          >
            <div className={classNames('logo-icon')}>
              {typeof logo.icon === 'string' ? (
                <i className={logo.icon} />
              ) : (
                logo.icon
              )}
            </div>
            {!isCollapsed && (
              <span className={classNames('logo-text', 'sidebar-text')}>
                {logo.text}
              </span>
            )}
          </div>
        )}
        
        {/* 折叠按钮 */}
        <button
          className={classNames('sidebar-toggle')}
          onClick={handleToggle}
          title={isCollapsed ? '展开侧边栏' : '折叠侧边栏'}
          aria-label={isCollapsed ? '展开侧边栏' : '折叠侧边栏'}
        >
          <i className={classNames('toggle-icon', isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left')} />
        </button>
      </div>

      {/* 导航区域 */}
      <div className={classNames('sidebar-nav')}>
        {items.map(item => renderNavItem(item))}
      </div>
    </div>
  );
};

export default Sidebar;