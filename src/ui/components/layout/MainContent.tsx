/**
 * MainContent 主内容区组件
 * 包含搜索栏、头部操作区和主要内容区域
 */

import React, { useState, useCallback } from 'react';
import { classNames } from '../../utils/styles';
import './MainContent.css';

export interface MainContentProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 页面标题 */
  title?: string;
  /** 搜索配置 */
  searchConfig?: {
    placeholder?: string;
    onSearch?: (query: string) => void;
    defaultValue?: string;
  };
  /** 头部操作按钮 */
  headerActions?: React.ReactNode;
  /** 额外的CSS类名 */
  className?: string;
  /** 是否显示搜索栏 */
  showSearch?: boolean;
  /** 是否显示头部 */
  showHeader?: boolean;
}

/**
 * 主内容区组件
 */
export const MainContent: React.FC<MainContentProps> = ({
  children,
  title,
  searchConfig,
  headerActions,
  className,
  showSearch = true,
  showHeader = true,
}) => {
  const [searchQuery, setSearchQuery] = useState(searchConfig?.defaultValue || '');

  // 处理搜索输入
  const handleSearchInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    try {
      searchConfig?.onSearch?.(value);
    } catch (error) {
      console.error('搜索回调执行失败:', error);
    }
  }, [searchConfig]);

  // 处理搜索提交
  const handleSearchSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    try {
      searchConfig?.onSearch?.(searchQuery);
    } catch (error) {
      console.error('搜索提交失败:', error);
    }
  }, [searchConfig, searchQuery]);

  // 处理清除搜索
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    searchConfig?.onSearch?.('');
  }, [searchConfig]);

  return (
    <div className={classNames('ptm-main-content', className)}>
      {/* 主内容头部 */}
      {showHeader && (
        <div className={classNames('ptm-main-header')}>
          {/* 搜索栏 */}
          {showSearch && (
            <div className={classNames('ptm-search-section')}>
              <form 
                className={classNames('ptm-search-bar')}
                onSubmit={handleSearchSubmit}
              >
                <div className={classNames('ptm-search-input-wrapper')}>
                  <i className={classNames('ptm-search-icon', 'fa-search')} />
                  <input
                    type="search"
                    role="searchbox"
                    className={classNames('ptm-search-input')}
                    placeholder={searchConfig?.placeholder || '搜索...'}
                    value={searchQuery}
                    onChange={handleSearchInput}
                  />
                  {searchQuery && (
                    <button
                      type="button"
                      className={classNames('ptm-search-clear')}
                      onClick={handleClearSearch}
                      title="清除搜索"
                    >
                      <i className={classNames('fa-times')} />
                    </button>
                  )}
                </div>
              </form>
            </div>
          )}

          {/* 页面标题和操作区 */}
          <div className={classNames('ptm-header-actions-section')}>
            {title && (
              <h1 className={classNames('ptm-page-title')}>
                {title}
              </h1>
            )}
            
            {headerActions && (
              <div className={classNames('ptm-header-actions')}>
                {headerActions}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主内容区域 */}
      <div className={classNames('ptm-content-area')}>
        <div className={classNames('ptm-content-wrapper')}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default MainContent;