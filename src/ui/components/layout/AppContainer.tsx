/**
 * AppContainer 主应用容器组件
 * 负责整体布局结构、响应式处理和CSS样式隔离
 */

import React from 'react';
import { classNames, createContainer } from '../../utils/styles';
import { useResponsive } from '../../hooks/useResponsive';
import './AppContainer.css';

export interface AppContainerProps {
  /** 子组件 */
  children: React.ReactNode;
  /** 额外的CSS类名 */
  className?: string;
  /** 是否启用响应式布局 */
  responsive?: boolean;
}

/**
 * 主应用容器组件
 * 提供整体布局结构和样式隔离
 */
export const AppContainer: React.FC<AppContainerProps> = ({
  children,
  className,
  responsive = true,
}) => {
  const { current, isMobile, isTablet } = useResponsive();

  // 生成容器类名
  const containerClass = classNames(
    createContainer('app'),
    responsive && `responsive-${current}`,
    isMobile && 'mobile-layout',
    isTablet && 'tablet-layout',
    className
  );

  return (
    <div className={containerClass}>
      {children}
    </div>
  );
};

export default AppContainer;