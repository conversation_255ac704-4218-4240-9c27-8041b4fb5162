import { render, screen } from '@testing-library/react';
import { AppContainer } from '../AppContainer';

jest.mock('../../../hooks/useResponsive', () => ({
  useResponsive: () => ({
    current: 'desktop',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isWide: false,
    width: 1024,
    height: 768,
  }),
}));

describe('AppContainer', () => {
  it('应该正确渲染', () => {
    render(
      <AppContainer>
        <div data-testid="test-content">测试内容</div>
      </AppContainer>
    );

    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('应该应用正确的样式隔离类名', () => {
    const { container } = render(
      <AppContainer>
        <div>测试内容</div>
      </AppContainer>
    );

    const appContainer = container.firstChild as HTMLElement;
    expect(appContainer).toHaveClass('ptm-app-container');
    expect(appContainer.className).toMatch(/ptm-/);
  });

  it('应该支持响应式布局', () => {
    const { container } = render(
      <AppContainer responsive={true}>
        <div>测试内容</div>
      </AppContainer>
    );

    const appContainer = container.firstChild as HTMLElement;
    expect(appContainer).toHaveClass('ptm-responsive-desktop');
  });

  it('应该支持自定义类名', () => {
    const { container } = render(
      <AppContainer className="custom-class">
        <div>测试内容</div>
      </AppContainer>
    );

    const appContainer = container.firstChild as HTMLElement;
    expect(appContainer).toHaveClass('ptm-custom-class');
  });
});