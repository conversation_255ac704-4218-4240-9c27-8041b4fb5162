import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Sidebar, SidebarItem } from '../Sidebar';

// Mock hooks
jest.mock('../../../hooks/useResponsive', () => ({
  useSidebarState: jest.fn(() => ({
    isCollapsed: false,
    toggleSidebar: jest.fn(),
  })),
}));

const mockItems: SidebarItem[] = [
  {
    id: 'dashboard',
    label: '仪表板',
    icon: 'fa-dashboard',
    onClick: jest.fn(),
  },
  {
    id: 'projects',
    label: '项目',
    icon: 'fa-folder',
    onClick: jest.fn(),
    children: [
      {
        id: 'project-1',
        label: '项目1',
        icon: 'fa-file',
        onClick: jest.fn(),
      },
    ],
  },
];

describe('Sidebar', () => {
  it('应该正确渲染导航项', () => {
    render(<Sidebar items={mockItems} />);
    
    expect(screen.getByText('仪表板')).toBeInTheDocument();
    expect(screen.getByText('项目')).toBeInTheDocument();
  });

  it('应该支持Logo配置', () => {
    const logo = {
      icon: 'fa-logo',
      text: 'PTM',
      onClick: jest.fn(),
    };

    render(<Sidebar items={mockItems} logo={logo} />);
    
    expect(screen.getByText('PTM')).toBeInTheDocument();
  });

  it('应该处理导航项点击', () => {
    render(<Sidebar items={mockItems} />);
    
    const dashboardItem = screen.getByText('仪表板');
    fireEvent.click(dashboardItem);
    
    expect(mockItems[0].onClick).toHaveBeenCalled();
  });
});