import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { RightPanel, StatItem, TeamMember, PanelTab } from '../RightPanel';

// Mock hooks
jest.mock('../../../hooks/useResponsive', () => ({
  useRightPanelState: jest.fn(() => ({
    isCollapsed: false,
    togglePanel: jest.fn(),
  })),
}));

const mockTabs: PanelTab[] = [
  {
    id: 'stats',
    title: '统计',
    icon: 'fa-chart-bar',
    content: <div data-testid="stats-content">统计内容</div>,
  },
  {
    id: 'team',
    title: '团队',
    icon: 'fa-users',
    content: <div data-testid="team-content">团队内容</div>,
  },
];

describe('RightPanel', () => {
  it('应该正确渲染基本结构', () => {
    render(
      <RightPanel title="测试面板">
        <div data-testid="panel-content">面板内容</div>
      </RightPanel>
    );

    expect(screen.getByText('测试面板')).toBeInTheDocument();
    expect(screen.getByTestId('panel-content')).toBeInTheDocument();
  });

  it('应该支持标签页', () => {
    render(<RightPanel tabs={mockTabs} />);

    expect(screen.getByText('统计')).toBeInTheDocument();
    expect(screen.getByText('团队')).toBeInTheDocument();
    expect(screen.getByTestId('stats-content')).toBeInTheDocument();
  });

  it('应该支持标签切换', () => {
    render(<RightPanel tabs={mockTabs} />);

    const teamTab = screen.getByText('团队');
    fireEvent.click(teamTab);

    expect(screen.getByTestId('team-content')).toBeInTheDocument();
  });
});

describe('StatItem', () => {
  it('应该正确渲染统计项', () => {
    render(
      <StatItem
        title="总任务"
        value={42}
        icon="fa-tasks"
        trend={{ value: 12, type: 'up' }}
      />
    );

    expect(screen.getByText('总任务')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('12%')).toBeInTheDocument();
  });
});

describe('TeamMember', () => {
  it('应该正确渲染团队成员', () => {
    render(
      <TeamMember
        id="1"
        name="张三"
        status="online"
        role="开发者"
      />
    );

    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('开发者')).toBeInTheDocument();
  });
});