/**
 * MainContent 组件测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MainContent } from '../MainContent';

describe('MainContent', () => {
  const defaultProps = {
    children: <div data-testid="test-content">测试内容</div>,
  };

  it('应该正确渲染基本内容', () => {
    render(<MainContent {...defaultProps} />);
    
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByRole('searchbox')).toBeInTheDocument();
  });

  it('应该显示页面标题', () => {
    const title = '测试页面';
    render(<MainContent {...defaultProps} title={title} />);
    
    expect(screen.getByRole('heading', { name: title })).toBeInTheDocument();
  });

  it('应该处理搜索输入', async () => {
    const user = userEvent.setup();
    const onSearch = jest.fn();
    const searchConfig = {
      placeholder: '搜索测试',
      onSearch,
    };

    render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    
    const searchInput = screen.getByPlaceholderText('搜索测试');
    await user.type(searchInput, '测试查询');
    
    expect(searchInput).toHaveValue('测试查询');
    expect(onSearch).toHaveBeenCalledWith('测试查询');
  });

  it('应该显示清除搜索按钮', async () => {
    const user = userEvent.setup();
    const onSearch = jest.fn();
    const searchConfig = { onSearch };

    render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    
    const searchInput = screen.getByRole('searchbox');
    await user.type(searchInput, '测试');
    
    const clearButton = screen.getByTitle('清除搜索');
    expect(clearButton).toBeInTheDocument();
    
    await user.click(clearButton);
    expect(searchInput).toHaveValue('');
    expect(onSearch).toHaveBeenCalledWith('');
  });

  it('应该处理搜索表单提交', async () => {
    const user = userEvent.setup();
    const onSearch = jest.fn();
    const searchConfig = { onSearch };

    render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    
    const searchInput = screen.getByRole('searchbox');
    await user.type(searchInput, '测试查询');
    
    fireEvent.submit(searchInput.closest('form')!);
    
    expect(onSearch).toHaveBeenCalledWith('测试查询');
  });

  it('应该支持隐藏搜索栏', () => {
    render(<MainContent {...defaultProps} showSearch={false} />);
    
    expect(screen.queryByRole('searchbox')).not.toBeInTheDocument();
  });

  it('应该支持隐藏头部', () => {
    const title = '测试标题';
    render(<MainContent {...defaultProps} title={title} showHeader={false} />);
    
    expect(screen.queryByRole('heading', { name: title })).not.toBeInTheDocument();
    expect(screen.queryByRole('searchbox')).not.toBeInTheDocument();
  });

  it('应该渲染头部操作按钮', () => {
    const headerActions = (
      <button data-testid="action-button">操作按钮</button>
    );

    render(<MainContent {...defaultProps} headerActions={headerActions} />);
    
    expect(screen.getByTestId('action-button')).toBeInTheDocument();
  });

  it('应该应用自定义类名', () => {
    const { container } = render(
      <MainContent {...defaultProps} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('ptm-main-content', 'ptm-custom-class');
  });

  it('应该使用默认搜索值', () => {
    const searchConfig = {
      defaultValue: '默认搜索',
      onSearch: jest.fn(),
    };

    render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    
    const searchInput = screen.getByRole('searchbox');
    expect(searchInput).toHaveValue('默认搜索');
  });

  it('应该正确处理搜索配置为空的情况', () => {
    render(<MainContent {...defaultProps} searchConfig={undefined} />);
    
    const searchInput = screen.getByRole('searchbox');
    expect(searchInput).toHaveAttribute('placeholder', '搜索...');
  });

  it('应该在移动端正确显示', () => {
    // 模拟移动端视口
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    const { container } = render(<MainContent {...defaultProps} title="移动端测试" />);
    
    // 验证响应式类名是否正确应用
    expect(container.querySelector('.ptm-main-content')).toBeInTheDocument();
  });

  it('应该支持键盘导航', async () => {
    const user = userEvent.setup();
    const onSearch = jest.fn();
    const searchConfig = { onSearch };

    render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    
    const searchInput = screen.getByRole('searchbox');
    
    // 测试Tab键导航
    await user.tab();
    expect(searchInput).toHaveFocus();
    
    // 测试输入
    await user.type(searchInput, '键盘测试');
    expect(onSearch).toHaveBeenCalledWith('键盘测试');
  });

  it('应该正确处理搜索回调错误', async () => {
    const user = userEvent.setup();
    const onSearch = jest.fn().mockImplementation(() => {
      throw new Error('搜索错误');
    });
    const searchConfig = { onSearch };

    // 不应该因为回调错误而崩溃
    expect(() => {
      render(<MainContent {...defaultProps} searchConfig={searchConfig} />);
    }).not.toThrow();

    const searchInput = screen.getByRole('searchbox');
    
    // 输入不应该因为回调错误而失败
    await expect(user.type(searchInput, '测试')).resolves.not.toThrow();
  });
});