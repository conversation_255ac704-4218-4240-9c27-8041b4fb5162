/**
 * RightPanel 右侧面板组件
 * 可折叠的右侧信息面板，显示统计信息和团队成员
 */

import React, { useState } from 'react';
import { classNames, bem } from '../../utils/styles';
import { useRightPanelState } from '../../hooks/useResponsive';
import './RightPanel.css';

export interface PanelTab {
  /** 标签ID */
  id: string;
  /** 标签标题 */
  title: string;
  /** 标签图标 */
  icon?: string | React.ReactNode;
  /** 标签内容 */
  content: React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
}

export interface RightPanelProps {
  /** 面板标签页 */
  tabs?: PanelTab[];
  /** 是否默认折叠 */
  defaultCollapsed?: boolean;
  /** 折叠状态变化回调 */
  onCollapseChange?: (collapsed: boolean) => void;
  /** 额外的CSS类名 */
  className?: string;
  /** 面板标题 */
  title?: string;
  /** 直接内容（不使用标签页时） */
  children?: React.ReactNode;
}

/**
 * 右侧面板组件
 */
export const RightPanel: React.FC<RightPanelProps> = ({
  tabs = [],
  defaultCollapsed = false,
  onCollapseChange,
  className,
  title,
  children,
}) => {
  const { isCollapsed, togglePanel } = useRightPanelState(defaultCollapsed);
  const [activeTab, setActiveTab] = useState<string>(tabs[0]?.id || '');

  // 处理折叠切换
  const handleToggle = () => {
    togglePanel();
    onCollapseChange?.(!isCollapsed);
  };

  // 处理标签切换
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // 获取当前激活的标签内容
  const getActiveTabContent = () => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    return activeTabData?.content || null;
  };

  // 如果面板被折叠，只显示切换按钮
  if (isCollapsed) {
    return (
      <div className={classNames('right-panel', 'collapsed', className)}>
        <button
          className={classNames('panel-toggle')}
          onClick={handleToggle}
          title="展开右侧面板"
          aria-label="展开右侧面板"
        >
          <i className={classNames('toggle-icon', 'fa-chevron-left')} />
        </button>
      </div>
    );
  }

  return (
    <div className={classNames('right-panel', className)}>
      {/* 面板头部 */}
      <div className={classNames('panel-header')}>
        {title && (
          <h2 className={classNames('panel-title')}>
            {title}
          </h2>
        )}
        
        {/* 标签页导航 */}
        {tabs.length > 0 && (
          <div className={classNames('panel-tabs')}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={classNames(
                  'tab-button',
                  activeTab === tab.id && 'active',
                  tab.disabled && 'disabled'
                )}
                onClick={() => !tab.disabled && handleTabChange(tab.id)}
                disabled={tab.disabled}
                title={tab.title}
              >
                {tab.icon && (
                  <span className={classNames('tab-icon')}>
                    {typeof tab.icon === 'string' ? (
                      <i className={tab.icon} />
                    ) : (
                      tab.icon
                    )}
                  </span>
                )}
                <span className={classNames('tab-title')}>
                  {tab.title}
                </span>
              </button>
            ))}
          </div>
        )}
        
        {/* 折叠按钮 */}
        <button
          className={classNames('panel-toggle')}
          onClick={handleToggle}
          title="折叠右侧面板"
          aria-label="折叠右侧面板"
        >
          <i className={classNames('toggle-icon', 'fa-chevron-right')} />
        </button>
      </div>

      {/* 面板内容 */}
      <div className={classNames('panel-content')}>
        {tabs.length > 0 ? (
          <div className={classNames('tab-content')}>
            {getActiveTabContent()}
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

/**
 * 统计信息组件
 */
export interface StatItemProps {
  /** 统计标题 */
  title: string;
  /** 统计值 */
  value: string | number;
  /** 图标 */
  icon?: string | React.ReactNode;
  /** 变化趋势 */
  trend?: {
    value: number;
    type: 'up' | 'down' | 'neutral';
  };
  /** 点击回调 */
  onClick?: () => void;
}

export const StatItem: React.FC<StatItemProps> = ({
  title,
  value,
  icon,
  trend,
  onClick,
}) => {
  return (
    <div
      className={classNames('stat-item', onClick && 'clickable')}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {icon && (
        <div className={classNames('stat-icon')}>
          {typeof icon === 'string' ? (
            <i className={icon} />
          ) : (
            icon
          )}
        </div>
      )}
      
      <div className={classNames('stat-content')}>
        <div className={classNames('stat-value')}>
          {value}
        </div>
        <div className={classNames('stat-title')}>
          {title}
        </div>
        
        {trend && (
          <div className={classNames('stat-trend', `trend-${trend.type}`)}>
            <i className={classNames(
              'trend-icon',
              trend.type === 'up' ? 'fa-arrow-up' :
              trend.type === 'down' ? 'fa-arrow-down' :
              'fa-minus'
            )} />
            <span className={classNames('trend-value')}>
              {Math.abs(trend.value)}%
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 团队成员组件
 */
export interface TeamMemberProps {
  /** 成员ID */
  id: string;
  /** 成员姓名 */
  name: string;
  /** 头像URL */
  avatar?: string;
  /** 在线状态 */
  status: 'online' | 'offline' | 'away' | 'busy';
  /** 角色 */
  role?: string;
  /** 点击回调 */
  onClick?: () => void;
}

export const TeamMember: React.FC<TeamMemberProps> = ({
  name,
  avatar,
  status,
  role,
  onClick,
}) => {
  return (
    <div
      className={classNames('team-member', onClick && 'clickable')}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      <div className={classNames('member-avatar')}>
        {avatar ? (
          <img
            src={avatar}
            alt={name}
            className={classNames('avatar-image')}
          />
        ) : (
          <div className={classNames('avatar-placeholder')}>
            {name.charAt(0).toUpperCase()}
          </div>
        )}
        <div className={classNames('status-indicator', `status-${status}`)} />
      </div>
      
      <div className={classNames('member-info')}>
        <div className={classNames('member-name')}>
          {name}
        </div>
        {role && (
          <div className={classNames('member-role')}>
            {role}
          </div>
        )}
      </div>
    </div>
  );
};

export default RightPanel;