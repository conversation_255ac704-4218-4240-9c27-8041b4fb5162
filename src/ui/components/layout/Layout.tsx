// Layout components for responsive design
// 使用标准化的组件接口规范

import React from 'react';
import clsx from 'clsx';
import { 
  StandardContainerProps, 
  StandardFlexProps, 
  StandardGridProps,
  createStandardProps,
  normalizeSpacing 
} from '../../types/components';

export interface ContainerProps extends StandardContainerProps, React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const Container: React.FC<ContainerProps> = (props) => {
	const standardProps = createStandardProps(props, {
		maxWidth: 'full',
		centered: false,
		padding: 'medium',
	});

	const {
		maxWidth,
		centered,
		padding,
		children,
		className,
		...restProps
	} = standardProps;

	const baseClasses = 'ptm-container';
	
	const maxWidthClasses = {
		sm: 'ptm-container--sm',
		md: 'ptm-container--md',
		lg: 'ptm-container--lg',
		xl: 'ptm-container--xl',
		full: 'ptm-container--full'
	};

	const normalizedPadding = normalizeSpacing(padding);
	const paddingClasses = {
		none: '',
		small: 'ptm-container--padding-sm',
		medium: 'ptm-container--padding-md',
		large: 'ptm-container--padding-lg'
	};

	const classes = clsx(
		baseClasses,
		maxWidthClasses[maxWidth],
		paddingClasses[normalizedPadding],
		{
			'ptm-container--centered': centered
		},
		className
	);

	return (
		<div className={classes} {...restProps}>
			{children}
		</div>
	);
};

export interface GridProps extends StandardGridProps, React.HTMLAttributes<HTMLDivElement> {
	cols?: 1 | 2 | 3 | 4 | 6 | 12;
	responsive?: boolean;
	children?: React.ReactNode;
}

export const Grid: React.FC<GridProps> = (props) => {
	const standardProps = createStandardProps(props, {
		cols: 1,
		gap: 'medium',
		responsive: true,
	});

	const {
		cols,
		gap,
		responsive,
		children,
		className,
		...restProps
	} = standardProps;

	const normalizedGap = normalizeSpacing(gap);

	const baseClasses = 'ptm-grid';
	
	const colClasses = {
		1: 'ptm-grid--cols-1',
		2: 'ptm-grid--cols-2',
		3: 'ptm-grid--cols-3',
		4: 'ptm-grid--cols-4',
		6: 'ptm-grid--cols-6',
		12: 'ptm-grid--cols-12'
	};

	const gapClasses = {
		none: 'ptm-grid--gap-none',
		small: 'ptm-grid--gap-sm',
		medium: 'ptm-grid--gap-md',
		large: 'ptm-grid--gap-lg'
	};

	const classes = clsx(
		baseClasses,
		colClasses[cols],
		gapClasses[normalizedGap],
		{
			'ptm-grid--responsive': responsive
		},
		className
	);

	return (
		<div className={classes} {...restProps}>
			{children}
		</div>
	);
};

export interface FlexProps extends StandardFlexProps, React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const Flex: React.FC<FlexProps> = (props) => {
	const standardProps = createStandardProps(props, {
		direction: 'row',
		align: 'start',
		justify: 'start',
		wrap: false,
		gap: 'none',
	});

	const {
		direction,
		align,
		justify,
		wrap,
		gap,
		children,
		className,
		...restProps
	} = standardProps;

	const normalizedGap = normalizeSpacing(gap);

	const baseClasses = 'ptm-flex';
	
	const directionClasses = {
		row: 'ptm-flex--row',
		'row-reverse': 'ptm-flex--row-reverse',
		column: 'ptm-flex--column',
		'column-reverse': 'ptm-flex--column-reverse'
	};

	const alignClasses = {
		start: 'ptm-flex--align-start',
		center: 'ptm-flex--align-center',
		end: 'ptm-flex--align-end',
		stretch: 'ptm-flex--align-stretch',
		baseline: 'ptm-flex--align-baseline'
	};

	const justifyClasses = {
		start: 'ptm-flex--justify-start',
		center: 'ptm-flex--justify-center',
		end: 'ptm-flex--justify-end',
		between: 'ptm-flex--justify-between',
		around: 'ptm-flex--justify-around',
		evenly: 'ptm-flex--justify-evenly'
	};

	const gapClasses = {
		none: '',
		small: 'ptm-flex--gap-sm',
		medium: 'ptm-flex--gap-md',
		large: 'ptm-flex--gap-lg'
	};

	const classes = clsx(
		baseClasses,
		directionClasses[direction],
		alignClasses[align],
		justifyClasses[justify],
		gapClasses[normalizedGap],
		{
			'ptm-flex--wrap': wrap
		},
		className
	);

	return (
		<div className={classes} {...restProps}>
			{children}
		</div>
	);
};

export interface StackProps extends React.HTMLAttributes<HTMLDivElement> {
	spacing?: 'none' | 'small' | 'medium' | 'large';
	align?: 'start' | 'center' | 'end' | 'stretch';
	children?: React.ReactNode;
}

export const Stack: React.FC<StackProps> = (props) => {
	const {
		spacing = 'medium',
		align = 'stretch',
		children,
		className,
		...restProps
	} = props;

	const normalizedSpacing = normalizeSpacing(spacing);

	return (
		<Flex
			direction="column"
			align={align}
			gap={normalizedSpacing}
			className={clsx('ptm-stack', className)}
			{...restProps}
		>
			{children}
		</Flex>
	);
};