/**
 * RightPanel 右侧面板样式
 * 可折叠的右侧信息面板样式定义
 */

/* 右侧面板主容器 */
.ptm-right-panel {
  width: var(--ptm-right-panel-width);
  background: var(--ptm-bg-primary);
  border-left: 1px solid var(--ptm-border-light);
  display: flex;
  flex-direction: column;
  box-shadow: var(--ptm-shadow-base);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  position: relative;
  z-index: var(--ptm-z-fixed);
  height: 100vh;
  overflow: hidden;
}

/* 折叠状态 */
.ptm-right-panel.ptm-collapsed {
  width: var(--ptm-spacing-12);
  min-width: var(--ptm-spacing-12);
}

/* 面板头部 */
.ptm-panel-header {
  padding: var(--ptm-spacing-4) var(--ptm-spacing-6);
  border-bottom: 1px solid var(--ptm-border-light);
  display: flex;
  flex-direction: column;
  gap: var(--ptm-spacing-4);
  flex-shrink: 0;
  position: relative;
}

.ptm-panel-title {
  font-size: var(--ptm-text-lg);
  font-weight: 600;
  color: var(--ptm-text-primary);
  margin: 0;
}

/* 标签页导航 */
.ptm-panel-tabs {
  display: flex;
  gap: var(--ptm-spacing-1);
  background: var(--ptm-bg-secondary);
  border-radius: var(--ptm-radius-base);
  padding: var(--ptm-spacing-1);
}

.ptm-tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--ptm-spacing-2);
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  border: none;
  background: transparent;
  color: var(--ptm-text-secondary);
  font-size: var(--ptm-text-sm);
  font-weight: 500;
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  transition: all var(--ptm-duration-fast);
}

.ptm-tab-button:hover:not(.ptm-disabled) {
  background: var(--ptm-bg-primary);
  color: var(--ptm-text-primary);
}

.ptm-tab-button.ptm-active {
  background: var(--ptm-bg-primary);
  color: var(--ptm-primary);
  font-weight: 600;
  box-shadow: var(--ptm-shadow-sm);
}

.ptm-tab-button.ptm-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ptm-tab-icon {
  font-size: var(--ptm-text-sm);
}

.ptm-tab-title {
  white-space: nowrap;
}

/* 折叠按钮 */
.ptm-panel-toggle {
  position: absolute;
  right: var(--ptm-spacing-4);
  top: var(--ptm-spacing-4);
  width: var(--ptm-spacing-6);
  height: var(--ptm-spacing-6);
  border: none;
  background: var(--ptm-gray-100);
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ptm-gray-600);
  transition: all var(--ptm-duration-fast);
}

.ptm-panel-toggle:hover {
  background: var(--ptm-gray-200);
  color: var(--ptm-gray-800);
}

.ptm-panel-toggle:focus {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

.ptm-collapsed .ptm-panel-toggle {
  position: static;
  margin: var(--ptm-spacing-3) auto;
}

.ptm-toggle-icon {
  transition: transform var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

.ptm-collapsed .ptm-toggle-icon {
  transform: rotate(180deg);
}

/* 面板内容 */
.ptm-panel-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--ptm-spacing-4) var(--ptm-spacing-6);
}

.ptm-tab-content {
  height: 100%;
}

/* 统计项组件 */
.ptm-stat-item {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-3);
  padding: var(--ptm-spacing-4);
  background: var(--ptm-bg-secondary);
  border-radius: var(--ptm-radius-base);
  margin-bottom: var(--ptm-spacing-3);
  transition: all var(--ptm-duration-fast);
}

.ptm-stat-item.ptm-clickable {
  cursor: pointer;
}

.ptm-stat-item.ptm-clickable:hover {
  background: var(--ptm-gray-100);
  transform: translateY(-1px);
  box-shadow: var(--ptm-shadow-sm);
}

.ptm-stat-icon {
  width: var(--ptm-spacing-10);
  height: var(--ptm-spacing-10);
  background: var(--ptm-primary-gradient);
  border-radius: var(--ptm-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ptm-text-inverse);
  font-size: var(--ptm-text-lg);
  flex-shrink: 0;
}

.ptm-stat-content {
  flex: 1;
  min-width: 0;
}

.ptm-stat-value {
  font-size: var(--ptm-text-xl);
  font-weight: 700;
  color: var(--ptm-text-primary);
  line-height: 1.2;
}

.ptm-stat-title {
  font-size: var(--ptm-text-sm);
  color: var(--ptm-text-secondary);
  margin-top: var(--ptm-spacing-1);
}

.ptm-stat-trend {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-1);
  margin-top: var(--ptm-spacing-2);
  font-size: var(--ptm-text-xs);
  font-weight: 600;
}

.ptm-stat-trend.ptm-trend-up {
  color: var(--ptm-success);
}

.ptm-stat-trend.ptm-trend-down {
  color: var(--ptm-error);
}

.ptm-stat-trend.ptm-trend-neutral {
  color: var(--ptm-text-muted);
}

.ptm-trend-icon {
  font-size: var(--ptm-text-xs);
}

/* 团队成员组件 */
.ptm-team-member {
  display: flex;
  align-items: center;
  gap: var(--ptm-spacing-3);
  padding: var(--ptm-spacing-3);
  border-radius: var(--ptm-radius-base);
  transition: all var(--ptm-duration-fast);
  margin-bottom: var(--ptm-spacing-2);
}

.ptm-team-member.ptm-clickable {
  cursor: pointer;
}

.ptm-team-member.ptm-clickable:hover {
  background: var(--ptm-bg-secondary);
}

.ptm-member-avatar {
  position: relative;
  width: var(--ptm-spacing-8);
  height: var(--ptm-spacing-8);
  flex-shrink: 0;
}

.ptm-avatar-image {
  width: 100%;
  height: 100%;
  border-radius: var(--ptm-radius-full);
  object-fit: cover;
}

.ptm-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--ptm-primary-gradient);
  border-radius: var(--ptm-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ptm-text-inverse);
  font-weight: 600;
  font-size: var(--ptm-text-sm);
}

.ptm-status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: var(--ptm-spacing-3);
  height: var(--ptm-spacing-3);
  border-radius: var(--ptm-radius-full);
  border: 2px solid var(--ptm-bg-primary);
}

.ptm-status-indicator.ptm-status-online {
  background: var(--ptm-success);
}

.ptm-status-indicator.ptm-status-offline {
  background: var(--ptm-gray-400);
}

.ptm-status-indicator.ptm-status-away {
  background: var(--ptm-warning);
}

.ptm-status-indicator.ptm-status-busy {
  background: var(--ptm-error);
}

.ptm-member-info {
  flex: 1;
  min-width: 0;
}

.ptm-member-name {
  font-size: var(--ptm-text-sm);
  font-weight: 600;
  color: var(--ptm-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ptm-member-role {
  font-size: var(--ptm-text-xs);
  color: var(--ptm-text-muted);
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .ptm-right-panel {
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--ptm-z-modal);
    transform: translateX(100%);
    transition: transform var(--ptm-duration-normal) var(--ptm-easing-smooth);
  }
  
  .ptm-right-panel:not(.ptm-collapsed) {
    transform: translateX(0);
  }
  
  /* 平板和移动设备上的遮罩层 */
  .ptm-right-panel:not(.ptm-collapsed)::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: var(--ptm-right-panel-width);
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

@media (max-width: 768px) {
  .ptm-right-panel {
    width: 280px;
  }
  
  .ptm-panel-header {
    padding: var(--ptm-spacing-4);
  }
  
  .ptm-panel-content {
    padding: var(--ptm-spacing-4);
  }
  
  .ptm-tab-button {
    padding: var(--ptm-spacing-2);
  }
  
  .ptm-tab-title {
    display: none;
  }
}

/* 滚动条样式 */
.ptm-panel-content::-webkit-scrollbar {
  width: 4px;
}

.ptm-panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.ptm-panel-content::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.ptm-panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ptm-right-panel {
    border-left: 2px solid var(--ptm-border-dark);
  }
  
  .ptm-tab-button.ptm-active {
    background: var(--ptm-primary);
    color: var(--ptm-text-inverse);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .ptm-right-panel,
  .ptm-toggle-icon,
  .ptm-stat-item,
  .ptm-team-member {
    transition: none !important;
  }
}

/* 打印样式 */
@media print {
  .ptm-right-panel {
    display: none;
  }
}

/* 无障碍支持 */
.ptm-right-panel[aria-hidden="true"] {
  visibility: hidden;
}

/* 键盘导航支持 */
.ptm-tab-button:focus-visible,
.ptm-stat-item:focus-visible,
.ptm-team-member:focus-visible {
  outline: 2px solid var(--ptm-primary);
  outline-offset: 2px;
}

/* 空状态 */
.ptm-panel-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--ptm-text-muted);
  text-align: center;
}

.ptm-panel-empty-icon {
  font-size: var(--ptm-text-3xl);
  margin-bottom: var(--ptm-spacing-3);
  opacity: 0.5;
}

.ptm-panel-empty-text {
  font-size: var(--ptm-text-sm);
}

/* 加载状态 */
.ptm-panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--ptm-text-muted);
}

.ptm-panel-loading-spinner {
  width: var(--ptm-spacing-6);
  height: var(--ptm-spacing-6);
  border: 2px solid var(--ptm-gray-300);
  border-top: 2px solid var(--ptm-primary);
  border-radius: 50%;
  animation: ptm-spin 1s linear infinite;
  margin-right: var(--ptm-spacing-3);
}

/* 分组标题 */
.ptm-panel-section-title {
  font-size: var(--ptm-text-sm);
  font-weight: 600;
  color: var(--ptm-text-secondary);
  margin: var(--ptm-spacing-6) 0 var(--ptm-spacing-3);
  padding-bottom: var(--ptm-spacing-2);
  border-bottom: 1px solid var(--ptm-border-light);
}

.ptm-panel-section-title:first-child {
  margin-top: 0;
}