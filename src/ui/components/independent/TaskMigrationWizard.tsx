// 任务迁移向导组件

import React, { useState, useEffect } from 'react';
import { Task, Project } from '../../../models';
import { 
	TaskMigrationService, 
	TaskMigrationOptions, 
	MigrationValidationResult,
	MigrationPreview,
	TaskMigrationResult
} from '../../../services/TaskMigrationService';
import { Card } from '../common/Card';

interface TaskMigrationWizardProps {
	migrationService: TaskMigrationService;
	selectedTasks: Task[];
	onClose: () => void;
	onMigrationComplete: (result: TaskMigrationResult) => void;
}

type WizardStep = 'select-project' | 'configure-options' | 'preview' | 'migrate' | 'complete';

interface WizardState {
	currentStep: WizardStep;
	targetProjectId: string;
	availableProjects: Project[];
	migrationOptions: TaskMigrationOptions;
	validation: MigrationValidationResult | null;
	preview: MigrationPreview | null;
	migrationResult: TaskMigrationResult | null;
	loading: boolean;
	error: string | null;
}

/**
 * 任务迁移向导组件
 * 提供分步骤的任务迁移流程
 */
export const TaskMigrationWizard: React.FC<TaskMigrationWizardProps> = ({
	migrationService,
	selectedTasks,
	onClose,
	onMigrationComplete
}) => {
	const [state, setState] = useState<WizardState>({
		currentStep: 'select-project',
		targetProjectId: '',
		availableProjects: [],
		migrationOptions: {
			taskIds: selectedTasks.map(t => t.id),
			targetProjectId: '',
			preserveStatus: true,
			preservePriority: true,
			preserveAssignee: true,
			preserveTags: true,
			preserveDates: true,
			addMigrationTag: true,
			migrationTagName: 'migrated'
		},
		validation: null,
		preview: null,
		migrationResult: null,
		loading: false,
		error: null
	});

	// 加载可用项目
	useEffect(() => {
		loadAvailableProjects();
	}, []);

	const loadAvailableProjects = async () => {
		try {
			setState(prev => ({ ...prev, loading: true, error: null }));
			const projects = await migrationService.getAvailableTargetProjects();
			setState(prev => ({ 
				...prev, 
				availableProjects: projects,
				loading: false 
			}));
		} catch (error) {
			setState(prev => ({ 
				...prev, 
				error: error instanceof Error ? error.message : '加载项目失败',
				loading: false 
			}));
		}
	};

	// 更新迁移选项
	const updateMigrationOptions = (updates: Partial<TaskMigrationOptions>) => {
		setState(prev => ({
			...prev,
			migrationOptions: { ...prev.migrationOptions, ...updates }
		}));
	};

	// 验证迁移
	const validateMigration = async () => {
		try {
			setState(prev => ({ ...prev, loading: true, error: null }));
			const validation = await migrationService.validateMigration(state.migrationOptions);
			setState(prev => ({ 
				...prev, 
				validation,
				loading: false 
			}));
			return validation.isValid;
		} catch (error) {
			setState(prev => ({ 
				...prev, 
				error: error instanceof Error ? error.message : '验证失败',
				loading: false 
			}));
			return false;
		}
	};

	// 生成预览
	const generatePreview = async () => {
		try {
			setState(prev => ({ ...prev, loading: true, error: null }));
			const preview = await migrationService.generateMigrationPreview(state.migrationOptions);
			setState(prev => ({ 
				...prev, 
				preview,
				loading: false 
			}));
		} catch (error) {
			setState(prev => ({ 
				...prev, 
				error: error instanceof Error ? error.message : '生成预览失败',
				loading: false 
			}));
		}
	};

	// 执行迁移
	const executeMigration = async () => {
		try {
			setState(prev => ({ ...prev, loading: true, error: null }));
			const result = await migrationService.migrateTasks(state.migrationOptions);
			setState(prev => ({ 
				...prev, 
				migrationResult: result,
				loading: false,
				currentStep: 'complete'
			}));
			onMigrationComplete(result);
		} catch (error) {
			setState(prev => ({ 
				...prev, 
				error: error instanceof Error ? error.message : '迁移失败',
				loading: false 
			}));
		}
	};

	// 下一步
	const nextStep = async () => {
		switch (state.currentStep) {
			case 'select-project':
				if (!state.targetProjectId) {
					setState(prev => ({ ...prev, error: '请选择目标项目' }));
					return;
				}
				updateMigrationOptions({ targetProjectId: state.targetProjectId });
				setState(prev => ({ ...prev, currentStep: 'configure-options', error: null }));
				break;

			case 'configure-options':
				const isValid = await validateMigration();
				if (isValid) {
					setState(prev => ({ ...prev, currentStep: 'preview' }));
					await generatePreview();
				}
				break;

			case 'preview':
				setState(prev => ({ ...prev, currentStep: 'migrate' }));
				await executeMigration();
				break;

			default:
				break;
		}
	};

	// 上一步
	const prevStep = () => {
		const stepOrder: WizardStep[] = ['select-project', 'configure-options', 'preview', 'migrate'];
		const currentIndex = stepOrder.indexOf(state.currentStep);
		if (currentIndex > 0) {
			setState(prev => ({ 
				...prev, 
				currentStep: stepOrder[currentIndex - 1],
				error: null 
			}));
		}
	};

	// 获取步骤标题
	const getStepTitle = (step: WizardStep): string => {
		const titles = {
			'select-project': '选择目标项目',
			'configure-options': '配置迁移选项',
			'preview': '预览迁移',
			'migrate': '执行迁移',
			'complete': '迁移完成'
		};
		return titles[step];
	};

	// 渲染步骤指示器
	const renderStepIndicator = () => {
		const steps: WizardStep[] = ['select-project', 'configure-options', 'preview', 'migrate', 'complete'];
		const currentIndex = steps.indexOf(state.currentStep);

		return (
			<div style={{ 
				display: 'flex', 
				justifyContent: 'center', 
				marginBottom: '24px',
				padding: '0 20px'
			}}>
				{steps.map((step, index) => (
					<div key={step} style={{ display: 'flex', alignItems: 'center' }}>
						<div style={{
							width: '32px',
							height: '32px',
							borderRadius: '50%',
							background: index <= currentIndex ? '#3b82f6' : '#e5e7eb',
							color: index <= currentIndex ? 'white' : '#6b7280',
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'center',
							fontSize: '14px',
							fontWeight: 'bold'
						}}>
							{index + 1}
						</div>
						{index < steps.length - 1 && (
							<div style={{
								width: '60px',
								height: '2px',
								background: index < currentIndex ? '#3b82f6' : '#e5e7eb',
								margin: '0 8px'
							}} />
						)}
					</div>
				))}
			</div>
		);
	};

	// 渲染项目选择步骤
	const renderSelectProject = () => (
		<div>
			<h3>选择目标项目</h3>
			<p style={{ color: '#6b7280', marginBottom: '20px' }}>
				选择要将 {selectedTasks.length} 个独立任务迁移到的项目
			</p>

			{state.availableProjects.length === 0 ? (
				<div style={{ 
					textAlign: 'center', 
					padding: '40px', 
					color: '#6b7280' 
				}}>
					没有可用的项目，请先创建项目
				</div>
			) : (
				<div style={{ display: 'grid', gap: '12px' }}>
					{state.availableProjects.map(project => (
						<div
							key={project.id}
							onClick={() => setState(prev => ({ ...prev, targetProjectId: project.id }))}
							style={{
								border: `2px solid ${state.targetProjectId === project.id ? '#3b82f6' : '#e5e7eb'}`,
								borderRadius: '8px',
								padding: '16px',
								cursor: 'pointer',
								background: state.targetProjectId === project.id ? '#f0f9ff' : 'white'
							}}
						>
							<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
								<div>
									<h4 style={{ margin: '0 0 8px 0' }}>{project.name}</h4>
									{project.description && (
										<p style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px' }}>
											{project.description}
										</p>
									)}
									<div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#6b7280' }}>
										<span>状态: {project.status}</span>
										<span>优先级: {project.priority}</span>
										{project.endDate && (
											<span>截止: {new Date(project.endDate).toLocaleDateString()}</span>
										)}
									</div>
								</div>
								{state.targetProjectId === project.id && (
									<div style={{
										width: '20px',
										height: '20px',
										borderRadius: '50%',
										background: '#3b82f6',
										color: 'white',
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center',
										fontSize: '12px'
									}}>
										✓
									</div>
								)}
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);

	// 渲染配置选项步骤
	const renderConfigureOptions = () => (
		<div>
			<h3>配置迁移选项</h3>
			<p style={{ color: '#6b7280', marginBottom: '20px' }}>
				选择在迁移过程中要保留的任务属性
			</p>

			<div style={{ display: 'grid', gap: '16px' }}>
				<label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
					<input
						type="checkbox"
						checked={state.migrationOptions.preserveStatus}
						onChange={(e) => updateMigrationOptions({ preserveStatus: e.target.checked })}
					/>
					<span>保留任务状态</span>
					<span style={{ fontSize: '12px', color: '#6b7280' }}>
						(否则重置为待办)
					</span>
				</label>

				<label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
					<input
						type="checkbox"
						checked={state.migrationOptions.preservePriority}
						onChange={(e) => updateMigrationOptions({ preservePriority: e.target.checked })}
					/>
					<span>保留优先级</span>
					<span style={{ fontSize: '12px', color: '#6b7280' }}>
						(否则重置为中等)
					</span>
				</label>

				<label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
					<input
						type="checkbox"
						checked={state.migrationOptions.preserveAssignee}
						onChange={(e) => updateMigrationOptions({ preserveAssignee: e.target.checked })}
					/>
					<span>保留负责人</span>
					<span style={{ fontSize: '12px', color: '#6b7280' }}>
						(否则清除负责人)
					</span>
				</label>

				<label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
					<input
						type="checkbox"
						checked={state.migrationOptions.preserveTags}
						onChange={(e) => updateMigrationOptions({ preserveTags: e.target.checked })}
					/>
					<span>保留标签</span>
					<span style={{ fontSize: '12px', color: '#6b7280' }}>
						(否则清除所有标签)
					</span>
				</label>

				<label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
					<input
						type="checkbox"
						checked={state.migrationOptions.preserveDates}
						onChange={(e) => updateMigrationOptions({ preserveDates: e.target.checked })}
					/>
					<span>保留日期</span>
					<span style={{ fontSize: '12px', color: '#6b7280' }}>
						(开始日期、截止日期等)
					</span>
				</label>

				<div style={{ borderTop: '1px solid #e5e7eb', paddingTop: '16px' }}>
					<label style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
						<input
							type="checkbox"
							checked={state.migrationOptions.addMigrationTag}
							onChange={(e) => updateMigrationOptions({ addMigrationTag: e.target.checked })}
						/>
						<span>添加迁移标签</span>
					</label>

					{state.migrationOptions.addMigrationTag && (
						<div style={{ marginLeft: '24px' }}>
							<input
								type="text"
								value={state.migrationOptions.migrationTagName || ''}
								onChange={(e) => updateMigrationOptions({ migrationTagName: e.target.value })}
								placeholder="迁移标签名称"
								style={{
									padding: '6px 12px',
									border: '1px solid #d1d5db',
									borderRadius: '4px',
									fontSize: '14px'
								}}
							/>
						</div>
					)}
				</div>
			</div>

			{state.validation && (
				<div style={{ marginTop: '20px' }}>
					{state.validation.errors.length > 0 && (
						<div style={{ 
							background: '#fee2e2', 
							color: '#dc2626', 
							padding: '12px', 
							borderRadius: '6px',
							marginBottom: '12px'
						}}>
							<h5 style={{ margin: '0 0 8px 0' }}>错误:</h5>
							<ul style={{ margin: 0, paddingLeft: '20px' }}>
								{state.validation.errors.map((error, index) => (
									<li key={index}>{error}</li>
								))}
							</ul>
						</div>
					)}

					{state.validation.warnings.length > 0 && (
						<div style={{ 
							background: '#fef3c7', 
							color: '#d97706', 
							padding: '12px', 
							borderRadius: '6px'
						}}>
							<h5 style={{ margin: '0 0 8px 0' }}>警告:</h5>
							<ul style={{ margin: 0, paddingLeft: '20px' }}>
								{state.validation.warnings.map((warning, index) => (
									<li key={index}>{warning}</li>
								))}
							</ul>
						</div>
					)}
				</div>
			)}
		</div>
	);

	// 渲染预览步骤
	const renderPreview = () => (
		<div>
			<h3>迁移预览</h3>
			<p style={{ color: '#6b7280', marginBottom: '20px' }}>
				检查迁移详情，确认无误后执行迁移
			</p>

			{state.preview && (
				<div>
					{/* 摘要信息 */}
					<div style={{ 
						background: '#f9fafb', 
						padding: '16px', 
						borderRadius: '8px',
						marginBottom: '20px'
					}}>
						<h4 style={{ margin: '0 0 12px 0' }}>迁移摘要</h4>
						<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' }}>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#3b82f6' }}>
									{state.preview.summary.totalTasks}
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>总任务数</div>
							</div>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ef4444' }}>
									{state.preview.summary.tasksWithConflicts}
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>有冲突</div>
							</div>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f59e0b' }}>
									{state.preview.summary.tasksWithWarnings}
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>有警告</div>
							</div>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
									{state.preview.summary.estimatedDuration}s
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>预估时间</div>
							</div>
						</div>
					</div>

					{/* 任务详情 */}
					<div style={{ maxHeight: '400px', overflowY: 'auto' }}>
						{state.preview.tasks.map(taskPreview => (
							<div
								key={taskPreview.task.id}
								style={{
									border: '1px solid #e5e7eb',
									borderRadius: '6px',
									padding: '12px',
									marginBottom: '8px',
									background: taskPreview.conflicts.length > 0 ? '#fef2f2' : 
									          taskPreview.warnings.length > 0 ? '#fffbeb' : 'white'
								}}
							>
								<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
									<div style={{ flex: 1 }}>
										<h5 style={{ margin: '0 0 8px 0' }}>{taskPreview.task.title}</h5>
										
										{/* 变更信息 */}
										{Object.keys(taskPreview.changes).length > 1 && (
											<div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '8px' }}>
												变更: {Object.keys(taskPreview.changes).filter(k => k !== 'projectId').join(', ')}
											</div>
										)}

										{/* 冲突 */}
										{taskPreview.conflicts.length > 0 && (
											<div style={{ marginBottom: '8px' }}>
												{taskPreview.conflicts.map((conflict, index) => (
													<div key={index} style={{ 
														fontSize: '12px', 
														color: '#dc2626',
														background: '#fee2e2',
														padding: '4px 8px',
														borderRadius: '4px',
														marginBottom: '4px'
													}}>
														⚠️ {conflict}
													</div>
												))}
											</div>
										)}

										{/* 警告 */}
										{taskPreview.warnings.length > 0 && (
											<div>
												{taskPreview.warnings.map((warning, index) => (
													<div key={index} style={{ 
														fontSize: '12px', 
														color: '#d97706',
														background: '#fef3c7',
														padding: '4px 8px',
														borderRadius: '4px',
														marginBottom: '4px'
													}}>
														⚠️ {warning}
													</div>
												))}
											</div>
										)}
									</div>

									<div style={{ 
										fontSize: '12px', 
										color: taskPreview.conflicts.length > 0 ? '#dc2626' : 
										        taskPreview.warnings.length > 0 ? '#d97706' : '#10b981',
										fontWeight: 'bold'
									}}>
										{taskPreview.conflicts.length > 0 ? '冲突' : 
										 taskPreview.warnings.length > 0 ? '警告' : '正常'}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			)}
		</div>
	);

	// 渲染迁移执行步骤
	const renderMigrate = () => (
		<div style={{ textAlign: 'center', padding: '40px' }}>
			<div style={{ 
				width: '64px', 
				height: '64px', 
				border: '4px solid #e5e7eb',
				borderTop: '4px solid #3b82f6',
				borderRadius: '50%',
				animation: 'spin 1s linear infinite',
				margin: '0 auto 20px'
			}} />
			<h3>正在迁移任务...</h3>
			<p style={{ color: '#6b7280' }}>
				请稍候，正在将 {selectedTasks.length} 个任务迁移到目标项目
			</p>
		</div>
	);

	// 渲染完成步骤
	const renderComplete = () => (
		<div>
			<h3>迁移完成</h3>
			
			{state.migrationResult && (
				<div>
					{/* 结果摘要 */}
					<div style={{ 
						background: state.migrationResult.failed > 0 ? '#fef3c7' : '#f0fdf4', 
						padding: '16px', 
						borderRadius: '8px',
						marginBottom: '20px'
					}}>
						<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '12px' }}>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
									{state.migrationResult.success}
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>成功迁移</div>
							</div>
							<div>
								<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ef4444' }}>
									{state.migrationResult.failed}
								</div>
								<div style={{ fontSize: '14px', color: '#6b7280' }}>迁移失败</div>
							</div>
						</div>
					</div>

					{/* 错误信息 */}
					{state.migrationResult.errors.length > 0 && (
						<div style={{ 
							background: '#fee2e2', 
							padding: '12px', 
							borderRadius: '6px',
							marginBottom: '12px'
						}}>
							<h5 style={{ margin: '0 0 8px 0', color: '#dc2626' }}>迁移错误:</h5>
							<div style={{ maxHeight: '150px', overflowY: 'auto' }}>
								{state.migrationResult.errors.map((error, index) => (
									<div key={index} style={{ fontSize: '14px', color: '#dc2626', marginBottom: '4px' }}>
										• {error.taskTitle}: {error.error}
									</div>
								))}
							</div>
						</div>
					)}

					{/* 警告信息 */}
					{state.migrationResult.warnings.length > 0 && (
						<div style={{ 
							background: '#fef3c7', 
							padding: '12px', 
							borderRadius: '6px'
						}}>
							<h5 style={{ margin: '0 0 8px 0', color: '#d97706' }}>迁移警告:</h5>
							<div style={{ maxHeight: '150px', overflowY: 'auto' }}>
								{state.migrationResult.warnings.map((warning, index) => (
									<div key={index} style={{ fontSize: '14px', color: '#d97706', marginBottom: '4px' }}>
										• {warning.taskTitle}: {warning.warning}
									</div>
								))}
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);

	return (
		<div style={{
			position: 'fixed',
			top: 0,
			left: 0,
			right: 0,
			bottom: 0,
			background: 'rgba(0, 0, 0, 0.5)',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			zIndex: 1000
		}}>
			<div style={{
				background: 'white',
				borderRadius: '12px',
				width: '90%',
				maxWidth: '800px',
				maxHeight: '90vh',
				overflow: 'hidden',
				display: 'flex',
				flexDirection: 'column'
			}}>
				{/* 头部 */}
				<div style={{ 
					padding: '20px 24px', 
					borderBottom: '1px solid #e5e7eb',
					display: 'flex',
					justifyContent: 'space-between',
					alignItems: 'center'
				}}>
					<h2 style={{ margin: 0 }}>{getStepTitle(state.currentStep)}</h2>
					<button
						onClick={onClose}
						style={{
							background: 'none',
							border: 'none',
							fontSize: '24px',
							cursor: 'pointer',
							color: '#6b7280'
						}}
					>
						×
					</button>
				</div>

				{/* 步骤指示器 */}
				{renderStepIndicator()}

				{/* 内容区域 */}
				<div style={{ 
					flex: 1, 
					padding: '0 24px', 
					overflowY: 'auto' 
				}}>
					{state.error && (
						<div style={{ 
							background: '#fee2e2', 
							color: '#dc2626', 
							padding: '12px', 
							borderRadius: '6px',
							marginBottom: '20px'
						}}>
							{state.error}
						</div>
					)}

					{state.loading && (
						<div style={{ textAlign: 'center', padding: '20px' }}>
							<div>加载中...</div>
						</div>
					)}

					{!state.loading && (
						<div>
							{state.currentStep === 'select-project' && renderSelectProject()}
							{state.currentStep === 'configure-options' && renderConfigureOptions()}
							{state.currentStep === 'preview' && renderPreview()}
							{state.currentStep === 'migrate' && renderMigrate()}
							{state.currentStep === 'complete' && renderComplete()}
						</div>
					)}
				</div>

				{/* 底部按钮 */}
				<div style={{ 
					padding: '20px 24px', 
					borderTop: '1px solid #e5e7eb',
					display: 'flex',
					justifyContent: 'space-between'
				}}>
					<div>
						{state.currentStep !== 'select-project' && state.currentStep !== 'migrate' && state.currentStep !== 'complete' && (
							<button
								onClick={prevStep}
								disabled={state.loading}
								style={{
									background: '#f3f4f6',
									border: '1px solid #d1d5db',
									padding: '8px 16px',
									borderRadius: '6px',
									cursor: state.loading ? 'not-allowed' : 'pointer'
								}}
							>
								上一步
							</button>
						)}
					</div>

					<div style={{ display: 'flex', gap: '12px' }}>
						{state.currentStep === 'complete' ? (
							<button
								onClick={onClose}
								style={{
									background: '#3b82f6',
									color: 'white',
									border: 'none',
									padding: '8px 16px',
									borderRadius: '6px',
									cursor: 'pointer'
								}}
							>
								完成
							</button>
						) : state.currentStep !== 'migrate' && (
							<button
								onClick={nextStep}
								disabled={state.loading || (state.currentStep === 'select-project' && !state.targetProjectId)}
								style={{
									background: '#3b82f6',
									color: 'white',
									border: 'none',
									padding: '8px 16px',
									borderRadius: '6px',
									cursor: (state.loading || (state.currentStep === 'select-project' && !state.targetProjectId)) ? 'not-allowed' : 'pointer'
								}}
							>
								{state.currentStep === 'preview' ? '开始迁移' : '下一步'}
							</button>
						)}
					</div>
				</div>
			</div>

			<style>
				{`
					@keyframes spin {
						0% { transform: rotate(0deg); }
						100% { transform: rotate(360deg); }
					}
				`}
			</style>
		</div>
	);
};