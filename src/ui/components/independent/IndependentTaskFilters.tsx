// 独立任务高级筛选组件

import React, { useState, useEffect } from 'react';
import { TaskStatus, Priority } from '../../../models';
import { TaskSearchOptions } from '../../../services/IndependentTaskService';
import { Card } from '../common/Card';

interface IndependentTaskFiltersProps {
	onFiltersChange: (filters: TaskSearchOptions) => void;
	availableTags: string[];
	availableAssignees: string[];
	initialFilters?: TaskSearchOptions;
}

interface FilterState {
	searchText: string;
	status: TaskStatus[];
	priority: Priority[];
	assignee: string[];
	tags: string[];
	dateRange: {
		start: string;
		end: string;
		field: 'dueDate' | 'startDate' | 'createdAt' | 'updatedAt';
	};
	sortBy: 'title' | 'priority' | 'status' | 'dueDate' | 'createdAt' | 'updatedAt';
	sortOrder: 'asc' | 'desc';
}

/**
 * 独立任务高级筛选组件
 * 提供丰富的筛选和排序选项
 */
export const IndependentTaskFilters: React.FC<IndependentTaskFiltersProps> = ({
	onFiltersChange,
	availableTags,
	availableAssignees,
	initialFilters = {}
}) => {
	const [filters, setFilters] = useState<FilterState>({
		searchText: initialFilters.searchText || '',
		status: initialFilters.status || [],
		priority: initialFilters.priority || [],
		assignee: initialFilters.assignee || [],
		tags: initialFilters.tags || [],
		dateRange: {
			start: initialFilters.dateRange?.start?.toISOString().split('T')[0] || '',
			end: initialFilters.dateRange?.end?.toISOString().split('T')[0] || '',
			field: initialFilters.dateRange?.field || 'dueDate'
		},
		sortBy: initialFilters.sortBy || 'updatedAt',
		sortOrder: initialFilters.sortOrder || 'desc'
	});

	const [isExpanded, setIsExpanded] = useState(false);
	const [showPresets, setShowPresets] = useState(false);

	// 预设筛选器
	const filterPresets = [
		{
			name: '今日任务',
			filters: {
				dateRange: {
					start: new Date(),
					end: new Date(),
					field: 'dueDate' as const
				},
				status: [TaskStatus.TODO, TaskStatus.IN_PROGRESS]
			}
		},
		{
			name: '本周任务',
			filters: {
				dateRange: {
					start: new Date(),
					end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
					field: 'dueDate' as const
				},
				status: [TaskStatus.TODO, TaskStatus.IN_PROGRESS]
			}
		},
		{
			name: '高优先级',
			filters: {
				priority: [Priority.HIGH, Priority.CRITICAL],
				status: [TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.BLOCKED]
			}
		},
		{
			name: '逾期任务',
			filters: {
				dateRange: {
					start: new Date(0),
					end: new Date(Date.now() - 24 * 60 * 60 * 1000),
					field: 'dueDate' as const
				},
				status: [TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.BLOCKED]
			}
		},
		{
			name: '已完成',
			filters: {
				status: [TaskStatus.COMPLETED]
			}
		}
	];

	// 当筛选器改变时通知父组件
	useEffect(() => {
		const searchOptions: TaskSearchOptions = {
			searchText: filters.searchText || undefined,
			status: filters.status.length > 0 ? filters.status : undefined,
			priority: filters.priority.length > 0 ? filters.priority : undefined,
			assignee: filters.assignee.length > 0 ? filters.assignee : undefined,
			tags: filters.tags.length > 0 ? filters.tags : undefined,
			dateRange: (filters.dateRange.start || filters.dateRange.end) ? {
				start: filters.dateRange.start ? new Date(filters.dateRange.start) : undefined,
				end: filters.dateRange.end ? new Date(filters.dateRange.end) : undefined,
				field: filters.dateRange.field
			} : undefined,
			sortBy: filters.sortBy,
			sortOrder: filters.sortOrder
		};

		onFiltersChange(searchOptions);
	}, [filters, onFiltersChange]);

	// 更新筛选器状态
	const updateFilters = (updates: Partial<FilterState>) => {
		setFilters(prev => ({ ...prev, ...updates }));
	};

	// 应用预设筛选器
	const applyPreset = (preset: typeof filterPresets[0]) => {
		const newFilters: Partial<FilterState> = {
			status: preset.filters.status || [],
			priority: preset.filters.priority || [],
			assignee: preset.filters.assignee || [],
			tags: preset.filters.tags || []
		};

		if (preset.filters.dateRange) {
			newFilters.dateRange = {
				start: preset.filters.dateRange.start.toISOString().split('T')[0],
				end: preset.filters.dateRange.end.toISOString().split('T')[0],
				field: preset.filters.dateRange.field
			};
		}

		updateFilters(newFilters);
		setShowPresets(false);
	};

	// 清除所有筛选器
	const clearAllFilters = () => {
		setFilters({
			searchText: '',
			status: [],
			priority: [],
			assignee: [],
			tags: [],
			dateRange: {
				start: '',
				end: '',
				field: 'dueDate'
			},
			sortBy: 'updatedAt',
			sortOrder: 'desc'
		});
	};

	// 检查是否有活跃的筛选器
	const hasActiveFilters = () => {
		return filters.searchText ||
			filters.status.length > 0 ||
			filters.priority.length > 0 ||
			filters.assignee.length > 0 ||
			filters.tags.length > 0 ||
			filters.dateRange.start ||
			filters.dateRange.end;
	};

	// 多选处理函数
	const handleMultiSelect = <T,>(
		currentValues: T[],
		value: T,
		field: keyof FilterState
	) => {
		const newValues = currentValues.includes(value)
			? currentValues.filter(v => v !== value)
			: [...currentValues, value];
		
		updateFilters({ [field]: newValues });
	};

	return (
		<Card>
			{/* 筛选器头部 */}
			<div style={{ 
				display: 'flex', 
				justifyContent: 'space-between', 
				alignItems: 'center', 
				marginBottom: '16px' 
			}}>
				<div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
					<h4 style={{ margin: 0 }}>筛选和搜索</h4>
					{hasActiveFilters() && (
						<span style={{
							background: '#3b82f6',
							color: 'white',
							padding: '2px 8px',
							borderRadius: '12px',
							fontSize: '12px'
						}}>
							已筛选
						</span>
					)}
				</div>
				
				<div style={{ display: 'flex', gap: '8px' }}>
					<button
						onClick={() => setShowPresets(!showPresets)}
						style={{
							background: '#f3f4f6',
							border: '1px solid #d1d5db',
							padding: '6px 12px',
							borderRadius: '4px',
							cursor: 'pointer',
							fontSize: '14px'
						}}
					>
						预设
					</button>
					<button
						onClick={() => setIsExpanded(!isExpanded)}
						style={{
							background: '#f3f4f6',
							border: '1px solid #d1d5db',
							padding: '6px 12px',
							borderRadius: '4px',
							cursor: 'pointer',
							fontSize: '14px'
						}}
					>
						{isExpanded ? '收起' : '展开'}
					</button>
					{hasActiveFilters() && (
						<button
							onClick={clearAllFilters}
							style={{
								background: '#fee2e2',
								color: '#dc2626',
								border: '1px solid #fecaca',
								padding: '6px 12px',
								borderRadius: '4px',
								cursor: 'pointer',
								fontSize: '14px'
							}}
						>
							清除
						</button>
					)}
				</div>
			</div>

			{/* 预设筛选器 */}
			{showPresets && (
				<div style={{ 
					marginBottom: '16px', 
					padding: '12px', 
					background: '#f9fafb', 
					borderRadius: '6px' 
				}}>
					<div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
						{filterPresets.map(preset => (
							<button
								key={preset.name}
								onClick={() => applyPreset(preset)}
								style={{
									background: 'white',
									border: '1px solid #d1d5db',
									padding: '6px 12px',
									borderRadius: '4px',
									cursor: 'pointer',
									fontSize: '14px'
								}}
							>
								{preset.name}
							</button>
						))}
					</div>
				</div>
			)}

			{/* 基础搜索 */}
			<div style={{ marginBottom: '16px' }}>
				<input
					type="text"
					placeholder="搜索任务标题、描述或标签..."
					value={filters.searchText}
					onChange={(e) => updateFilters({ searchText: e.target.value })}
					style={{
						width: '100%',
						padding: '10px 12px',
						border: '1px solid #d1d5db',
						borderRadius: '6px',
						fontSize: '14px'
					}}
				/>
			</div>

			{/* 快速筛选 */}
			<div style={{ 
				display: 'grid', 
				gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
				gap: '12px',
				marginBottom: isExpanded ? '16px' : '0'
			}}>
				{/* 状态筛选 */}
				<div>
					<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
						状态
					</label>
					<div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
						{Object.values(TaskStatus).map(status => (
							<button
								key={status}
								onClick={() => handleMultiSelect(filters.status, status, 'status')}
								style={{
									background: filters.status.includes(status) ? '#3b82f6' : '#f3f4f6',
									color: filters.status.includes(status) ? 'white' : '#374151',
									border: 'none',
									padding: '4px 8px',
									borderRadius: '4px',
									cursor: 'pointer',
									fontSize: '12px'
								}}
							>
								{status === TaskStatus.TODO ? '待办' :
								 status === TaskStatus.IN_PROGRESS ? '进行中' :
								 status === TaskStatus.BLOCKED ? '阻塞' :
								 status === TaskStatus.REVIEW ? '审核' :
								 status === TaskStatus.COMPLETED ? '已完成' : '已取消'}
							</button>
						))}
					</div>
				</div>

				{/* 优先级筛选 */}
				<div>
					<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
						优先级
					</label>
					<div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
						{Object.values(Priority).map(priority => (
							<button
								key={priority}
								onClick={() => handleMultiSelect(filters.priority, priority, 'priority')}
								style={{
									background: filters.priority.includes(priority) ? '#3b82f6' : '#f3f4f6',
									color: filters.priority.includes(priority) ? 'white' : '#374151',
									border: 'none',
									padding: '4px 8px',
									borderRadius: '4px',
									cursor: 'pointer',
									fontSize: '12px'
								}}
							>
								{priority === Priority.LOW ? '低' :
								 priority === Priority.MEDIUM ? '中' :
								 priority === Priority.HIGH ? '高' : '紧急'}
							</button>
						))}
					</div>
				</div>

				{/* 排序 */}
				<div>
					<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
						排序
					</label>
					<div style={{ display: 'flex', gap: '4px' }}>
						<select
							value={filters.sortBy}
							onChange={(e) => updateFilters({ sortBy: e.target.value as any })}
							style={{
								flex: 1,
								padding: '4px 8px',
								border: '1px solid #d1d5db',
								borderRadius: '4px',
								fontSize: '12px'
							}}
						>
							<option value="title">标题</option>
							<option value="priority">优先级</option>
							<option value="status">状态</option>
							<option value="dueDate">截止日期</option>
							<option value="createdAt">创建时间</option>
							<option value="updatedAt">更新时间</option>
						</select>
						<button
							onClick={() => updateFilters({ 
								sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' 
							})}
							style={{
								background: '#f3f4f6',
								border: '1px solid #d1d5db',
								padding: '4px 8px',
								borderRadius: '4px',
								cursor: 'pointer',
								fontSize: '12px'
							}}
						>
							{filters.sortOrder === 'asc' ? '↑' : '↓'}
						</button>
					</div>
				</div>
			</div>

			{/* 高级筛选 */}
			{isExpanded && (
				<div style={{ 
					borderTop: '1px solid #e5e7eb', 
					paddingTop: '16px',
					display: 'grid',
					gap: '16px'
				}}>
					{/* 负责人筛选 */}
					{availableAssignees.length > 0 && (
						<div>
							<label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
								负责人
							</label>
							<div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
								{availableAssignees.map(assignee => (
									<button
										key={assignee}
										onClick={() => handleMultiSelect(filters.assignee, assignee, 'assignee')}
										style={{
											background: filters.assignee.includes(assignee) ? '#3b82f6' : '#f3f4f6',
											color: filters.assignee.includes(assignee) ? 'white' : '#374151',
											border: 'none',
											padding: '4px 8px',
											borderRadius: '4px',
											cursor: 'pointer',
											fontSize: '12px'
										}}
									>
										{assignee}
									</button>
								))}
							</div>
						</div>
					)}

					{/* 标签筛选 */}
					{availableTags.length > 0 && (
						<div>
							<label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
								标签
							</label>
							<div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px', maxHeight: '120px', overflowY: 'auto' }}>
								{availableTags.map(tag => (
									<button
										key={tag}
										onClick={() => handleMultiSelect(filters.tags, tag, 'tags')}
										style={{
											background: filters.tags.includes(tag) ? '#3b82f6' : '#f3f4f6',
											color: filters.tags.includes(tag) ? 'white' : '#374151',
											border: 'none',
											padding: '4px 8px',
											borderRadius: '4px',
											cursor: 'pointer',
											fontSize: '12px'
										}}
									>
										#{tag}
									</button>
								))}
							</div>
						</div>
					)}

					{/* 日期范围筛选 */}
					<div>
						<label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
							日期范围
						</label>
						<div style={{ display: 'grid', gridTemplateColumns: 'auto 1fr 1fr', gap: '8px', alignItems: 'center' }}>
							<select
								value={filters.dateRange.field}
								onChange={(e) => updateFilters({ 
									dateRange: { ...filters.dateRange, field: e.target.value as any }
								})}
								style={{
									padding: '6px 8px',
									border: '1px solid #d1d5db',
									borderRadius: '4px',
									fontSize: '12px'
								}}
							>
								<option value="dueDate">截止日期</option>
								<option value="startDate">开始日期</option>
								<option value="createdAt">创建时间</option>
								<option value="updatedAt">更新时间</option>
							</select>
							<input
								type="date"
								value={filters.dateRange.start}
								onChange={(e) => updateFilters({ 
									dateRange: { ...filters.dateRange, start: e.target.value }
								})}
								style={{
									padding: '6px 8px',
									border: '1px solid #d1d5db',
									borderRadius: '4px',
									fontSize: '12px'
								}}
							/>
							<input
								type="date"
								value={filters.dateRange.end}
								onChange={(e) => updateFilters({ 
									dateRange: { ...filters.dateRange, end: e.target.value }
								})}
								style={{
									padding: '6px 8px',
									border: '1px solid #d1d5db',
									borderRadius: '4px',
									fontSize: '12px'
								}}
							/>
						</div>
					</div>
				</div>
			)}
		</Card>
	);
};