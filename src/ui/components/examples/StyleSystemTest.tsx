/**
 * 样式系统测试组件
 * 用于验证设计令牌和样式隔离是否正常工作
 */

import React from 'react';
import { designTokens } from '../../styles/tokens';

export const StyleSystemTest: React.FC = () => {
  return (
    <div className="ptm-app" style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1 className="ptm-text-3xl ptm-font-bold ptm-text-primary ptm-m-0 ptm-mb-6">
        PTM 样式系统测试
      </h1>
      
      {/* 颜色系统测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">颜色系统</h2>
        
        <div className="ptm-grid ptm-grid-cols-4 ptm-gap-4 ptm-mb-6">
          <div className="ptm-p-4 ptm-rounded-lg" style={{ background: designTokens.colors.primary }}>
            <div className="ptm-text-inverse ptm-font-medium">Primary</div>
            <div className="ptm-text-inverse ptm-text-sm">{designTokens.colors.primary}</div>
          </div>
          
          <div className="ptm-p-4 ptm-rounded-lg" style={{ background: designTokens.colors.accent }}>
            <div className="ptm-text-inverse ptm-font-medium">Accent</div>
            <div className="ptm-text-inverse ptm-text-sm">{designTokens.colors.accent}</div>
          </div>
          
          <div className="ptm-p-4 ptm-rounded-lg" style={{ background: designTokens.colors.warning }}>
            <div className="ptm-text-inverse ptm-font-medium">Warning</div>
            <div className="ptm-text-inverse ptm-text-sm">{designTokens.colors.warning}</div>
          </div>
          
          <div className="ptm-p-4 ptm-rounded-lg" style={{ background: designTokens.colors.danger }}>
            <div className="ptm-text-inverse ptm-font-medium">Danger</div>
            <div className="ptm-text-inverse ptm-text-sm">{designTokens.colors.danger}</div>
          </div>
        </div>

        <div className="ptm-grid ptm-grid-cols-8 ptm-gap-2">
          {Object.entries(designTokens.colors.gray).map(([key, value]) => (
            <div key={key} className="ptm-p-3 ptm-rounded" style={{ background: value }}>
              <div className={`ptm-text-xs ptm-font-medium ${parseInt(key) >= 500 ? 'ptm-text-inverse' : 'ptm-text-primary'}`}>
                {key}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 按钮组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">按钮组件</h2>
        
        <div className="ptm-flex ptm-gap-4 ptm-mb-4">
          <button className="ptm-button ptm-button--primary ptm-button--md">
            <span>主要按钮</span>
          </button>
          
          <button className="ptm-button ptm-button--secondary ptm-button--md">
            <span>次要按钮</span>
          </button>
          
          <button className="ptm-button ptm-button--ghost ptm-button--md">
            <span>幽灵按钮</span>
          </button>
          
          <button className="ptm-button ptm-button--icon ptm-button--md">
            <i className="fas fa-plus"></i>
          </button>
        </div>

        <div className="ptm-flex ptm-gap-4">
          <button className="ptm-button ptm-button--primary ptm-button--sm">
            <span>小按钮</span>
          </button>
          
          <button className="ptm-button ptm-button--primary ptm-button--md">
            <span>中按钮</span>
          </button>
          
          <button className="ptm-button ptm-button--primary ptm-button--lg">
            <span>大按钮</span>
          </button>
        </div>
      </section>

      {/* 卡片组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">卡片组件</h2>
        
        <div className="ptm-grid ptm-grid-cols-3 ptm-gap-6">
          <div className="ptm-card ptm-p-6">
            <h3 className="ptm-text-lg ptm-font-semibold ptm-text-primary ptm-mb-2">默认卡片</h3>
            <p className="ptm-text-secondary ptm-text-sm">这是一个默认样式的卡片组件。</p>
          </div>
          
          <div className="ptm-card ptm-card--elevated ptm-p-6">
            <h3 className="ptm-text-lg ptm-font-semibold ptm-text-primary ptm-mb-2">阴影卡片</h3>
            <p className="ptm-text-secondary ptm-text-sm">这是一个带阴影的卡片组件。</p>
          </div>
          
          <div className="ptm-card ptm-p-6" style={{ borderColor: designTokens.colors.primary }}>
            <h3 className="ptm-text-lg ptm-font-semibold ptm-text-primary ptm-mb-2">强调卡片</h3>
            <p className="ptm-text-secondary ptm-text-sm">这是一个强调边框的卡片组件。</p>
          </div>
        </div>
      </section>

      {/* 输入框组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">输入框组件</h2>
        
        <div className="ptm-grid ptm-grid-cols-2 ptm-gap-6">
          <div>
            <label className="ptm-block ptm-text-sm ptm-font-medium ptm-text-primary ptm-mb-2">
              普通输入框
            </label>
            <input 
              type="text" 
              className="ptm-input" 
              placeholder="请输入内容..."
            />
          </div>
          
          <div>
            <label className="ptm-block ptm-text-sm ptm-font-medium ptm-text-primary ptm-mb-2">
              搜索框
            </label>
            <div className="ptm-search-container">
              <input 
                type="text" 
                className="ptm-input ptm-search-input" 
                placeholder="搜索..."
              />
              <i className="ptm-search-icon fas fa-search"></i>
            </div>
          </div>
        </div>
      </section>

      {/* 头像组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">头像组件</h2>
        
        <div className="ptm-flex ptm-items-center ptm-gap-4">
          <div className="ptm-avatar ptm-avatar--sm">A</div>
          <div className="ptm-avatar ptm-avatar--md">B</div>
          <div className="ptm-avatar ptm-avatar--lg">C</div>
        </div>
      </section>

      {/* 进度条组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">进度条组件</h2>
        
        <div className="ptm-space-y-4">
          <div>
            <div className="ptm-flex ptm-justify-between ptm-mb-2">
              <span className="ptm-text-sm ptm-font-medium ptm-text-primary">进度 25%</span>
            </div>
            <div className="ptm-progress">
              <div className="ptm-progress-fill" style={{ width: '25%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="ptm-flex ptm-justify-between ptm-mb-2">
              <span className="ptm-text-sm ptm-font-medium ptm-text-primary">进度 60%</span>
            </div>
            <div className="ptm-progress">
              <div className="ptm-progress-fill" style={{ width: '60%' }}></div>
            </div>
          </div>
          
          <div>
            <div className="ptm-flex ptm-justify-between ptm-mb-2">
              <span className="ptm-text-sm ptm-font-medium ptm-text-primary">进度 90%</span>
            </div>
            <div className="ptm-progress">
              <div className="ptm-progress-fill" style={{ width: '90%' }}></div>
            </div>
          </div>
        </div>
      </section>

      {/* 标签组件测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">标签组件</h2>
        
        <div className="ptm-flex ptm-flex-wrap ptm-gap-2 ptm-mb-4">
          <span className="ptm-tag">默认标签</span>
          <span className="ptm-tag">设计</span>
          <span className="ptm-tag">开发</span>
          <span className="ptm-tag">测试</span>
        </div>
        
        <div className="ptm-flex ptm-flex-wrap ptm-gap-2">
          <span className="ptm-tag ptm-priority-high">高优先级</span>
          <span className="ptm-tag ptm-priority-medium">中优先级</span>
          <span className="ptm-tag ptm-priority-low">低优先级</span>
        </div>
      </section>

      {/* 状态指示器测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">状态指示器</h2>
        
        <div className="ptm-space-y-2">
          <div className="ptm-flex ptm-items-center">
            <span className="ptm-status-indicator ptm-status-indicator--success"></span>
            <span className="ptm-text-sm">成功状态</span>
          </div>
          
          <div className="ptm-flex ptm-items-center">
            <span className="ptm-status-indicator ptm-status-indicator--warning"></span>
            <span className="ptm-text-sm">警告状态</span>
          </div>
          
          <div className="ptm-flex ptm-items-center">
            <span className="ptm-status-indicator ptm-status-indicator--danger"></span>
            <span className="ptm-text-sm">危险状态</span>
          </div>
          
          <div className="ptm-flex ptm-items-center">
            <span className="ptm-status-indicator ptm-status-indicator--info"></span>
            <span className="ptm-text-sm">信息状态</span>
          </div>
        </div>
      </section>

      {/* 加载动画测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">加载动画</h2>
        
        <div className="ptm-loading-container">
          <div className="ptm-spinner"></div>
          <div className="ptm-loading-text">加载中...</div>
        </div>
      </section>

      {/* 响应式测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">响应式网格</h2>
        
        <div className="ptm-grid ptm-grid-cols-1 md:ptm-grid-cols-2 lg:ptm-grid-cols-3 xl:ptm-grid-cols-4 ptm-gap-4">
          {Array.from({ length: 8 }, (_, i) => (
            <div key={i} className="ptm-card ptm-p-4">
              <div className="ptm-text-sm ptm-font-medium ptm-text-primary">网格项 {i + 1}</div>
            </div>
          ))}
        </div>
      </section>

      {/* 样式隔离测试 */}
      <section className="ptm-mb-8">
        <h2 className="ptm-text-2xl ptm-font-semibold ptm-text-primary ptm-mb-4">样式隔离测试</h2>
        
        <div className="ptm-card ptm-p-6">
          <p className="ptm-text-secondary ptm-mb-4">
            这个组件应该不受Obsidian主题影响，保持一致的外观。
          </p>
          
          <div className="ptm-flex ptm-gap-4">
            <button className="ptm-button ptm-button--primary ptm-button--md">
              PTM按钮
            </button>
            
            {/* 这是一个普通的HTML按钮，应该受到Obsidian主题影响 */}
            <button style={{ padding: '8px 16px', border: '1px solid #ccc' }}>
              普通按钮
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};