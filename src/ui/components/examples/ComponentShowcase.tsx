// Component showcase for testing and documentation

import React, { useState } from 'react';
import { But<PERSON> } from '../common/Button';
import { Card, CardHeader, CardContent, CardFooter } from '../common/Card';
import { Container, Grid, Flex, Stack } from '../layout/Layout';

export const ComponentShowcase: React.FC = () => {
	const [loading, setLoading] = useState(false);

	const handleLoadingTest = () => {
		setLoading(true);
		setTimeout(() => setLoading(false), 2000);
	};

	return (
		<Container padding>
			<Stack spacing="lg">
				<h1>PTM Component Showcase</h1>
				
				{/* Button Examples */}
				<Card variant="elevated">
					<CardHeader>
						<h2>Button Components</h2>
					</CardHeader>
					<CardContent>
						<Stack spacing="md">
							<div>
								<h3>Button Variants</h3>
								<Flex gap="sm" wrap>
									<Button variant="primary">Primary</Button>
									<Button variant="secondary">Secondary</Button>
									<Button variant="ghost">Ghost</Button>
									<Button variant="danger">Destructive</Button>
								</Flex>
							</div>
							
							<div>
								<h3>Button Sizes</h3>
								<Flex gap="sm" align="center">
									<Button size="small">Small</Button>
									<Button size="medium">Medium</Button>
									<Button size="large">Large</Button>
								</Flex>
							</div>
							
							<div>
								<h3>Button States</h3>
								<Flex gap="sm">
									<Button 
										loading={loading} 
										onClick={handleLoadingTest}
									>
										{loading ? 'Loading...' : 'Test Loading'}
									</Button>
									<Button disabled>Disabled</Button>
								</Flex>
							</div>
							
							<div>
								<h3>Icon Buttons</h3>
								<Flex gap="sm">
									<Button 
										icon={<span>📝</span>}
									>
										With Icon
									</Button>
									<Button 
										icon={<span>⚙️</span>}
										variant="ghost"
									>
										Icon Only
									</Button>
								</Flex>
							</div>
						</Stack>
					</CardContent>
				</Card>

				{/* Card Examples */}
				<Card variant="elevated">
					<CardHeader>
						<h2>Card Components</h2>
					</CardHeader>
					<CardContent>
						<Grid cols={3} gap="md" responsive>
							<Card variant="default">
								<CardContent>
									<h4>Default Card</h4>
									<p>This is a default card with standard styling.</p>
								</CardContent>
							</Card>
							
							<Card variant="elevated">
								<CardContent>
									<h4>Elevated Card</h4>
									<p>This card has a shadow for elevation effect.</p>
								</CardContent>
							</Card>
							
							<Card variant="outlined">
								<CardContent>
									<h4>Outlined Card</h4>
									<p>This card has a prominent border outline.</p>
								</CardContent>
							</Card>
						</Grid>
					</CardContent>
				</Card>

				{/* Layout Examples */}
				<Card variant="elevated">
					<CardHeader>
						<h2>Layout Components</h2>
					</CardHeader>
					<CardContent>
						<Stack spacing="md">
							<div>
								<h3>Grid Layout</h3>
								<Grid cols={4} gap="sm" responsive>
									{Array.from({ length: 8 }, (_, i) => (
										<Card key={i} variant="outlined" padding="sm">
											<div style={{ textAlign: 'center', padding: '1rem' }}>
												Item {i + 1}
											</div>
										</Card>
									))}
								</Grid>
							</div>
							
							<div>
								<h3>Flex Layout</h3>
								<Flex justify="between" align="center" gap="md">
									<Card variant="outlined" padding="sm">
										<div>Left Item</div>
									</Card>
									<Card variant="outlined" padding="sm">
										<div>Center Item</div>
									</Card>
									<Card variant="outlined" padding="sm">
										<div>Right Item</div>
									</Card>
								</Flex>
							</div>
							
							<div>
								<h3>Stack Layout</h3>
								<Stack spacing="sm">
									<Card variant="outlined" padding="sm">
										<div>Stack Item 1</div>
									</Card>
									<Card variant="outlined" padding="sm">
										<div>Stack Item 2</div>
									</Card>
									<Card variant="outlined" padding="sm">
										<div>Stack Item 3</div>
									</Card>
								</Stack>
							</div>
						</Stack>
					</CardContent>
				</Card>

				{/* Theme Integration Example */}
				<Card variant="elevated">
					<CardHeader>
						<h2>Theme Integration</h2>
					</CardHeader>
					<CardContent>
						<p>
							All components automatically integrate with Obsidian's theme system.
							They respond to theme changes and use Obsidian's CSS variables for colors.
						</p>
						<Flex gap="md" wrap>
							<div style={{ 
								padding: 'var(--ptm-spacing-md)', 
								backgroundColor: 'var(--background-secondary)',
								borderRadius: 'var(--ptm-radius-md)',
								border: '1px solid var(--background-modifier-border)'
							}}>
								Background Secondary
							</div>
							<div style={{ 
								padding: 'var(--ptm-spacing-md)', 
								color: 'var(--text-accent)',
								backgroundColor: 'var(--background-primary)',
								borderRadius: 'var(--ptm-radius-md)',
								border: '1px solid var(--interactive-accent)'
							}}>
								Accent Text
							</div>
							<div style={{ 
								padding: 'var(--ptm-spacing-md)', 
								color: 'var(--text-muted)',
								backgroundColor: 'var(--background-primary)',
								borderRadius: 'var(--ptm-radius-md)',
								border: '1px solid var(--background-modifier-border)'
							}}>
								Muted Text
							</div>
						</Flex>
					</CardContent>
				</Card>

				{/* Responsive Design Example */}
				<Card variant="elevated">
					<CardHeader>
						<h2>Responsive Design</h2>
					</CardHeader>
					<CardContent>
						<p>
							Components are designed to be responsive and work well on different screen sizes.
							Try resizing your window to see the grid layout adapt.
						</p>
						<Grid cols={6} gap="sm" responsive>
							{Array.from({ length: 12 }, (_, i) => (
								<Card key={i} variant="outlined" padding="sm">
									<div style={{ textAlign: 'center', fontSize: '0.875rem' }}>
										{i + 1}
									</div>
								</Card>
							))}
						</Grid>
					</CardContent>
					<CardFooter>
						<p style={{ fontSize: '0.875rem', color: 'var(--text-muted)' }}>
							Grid automatically adjusts from 6 columns to fewer columns on smaller screens.
						</p>
					</CardFooter>
				</Card>
			</Stack>
		</Container>
	);
};