/**
 * Button组件
 * 使用CSS Modules和样式隔离确保不受Obsidian主题影响
 */

import React from 'react';
import { classNames } from '../../utils/styles';
import { StandardButtonProps, createInteractiveProps, normalizeSize, normalizeVariant } from '../../types/components';
import { validateAndWarn, validateButtonProps } from '../../utils/componentValidation';
import styles from './Button.module.css';

// 继承标准化的Button属性接口
export interface ButtonProps extends StandardButtonProps {
  children: React.ReactNode;
}

/**
 * Button组件 - 确保样式隔离的按钮组件
 * 使用标准化的组件接口规范，包含运行时属性验证
 */
export const Button: React.FC<ButtonProps> = (props) => {
  // 开发环境下进行属性验证
  validateAndWarn('Button', props, validateButtonProps);

  // 使用标准化的属性处理
  const standardProps = createInteractiveProps(props, {
    variant: 'primary',
    size: 'medium',
    type: 'button',
    iconPosition: 'left',
    iconOnly: false,
  });

  const {
    children,
    variant,
    size,
    disabled,
    loading,
    iconOnly,
    icon,
    iconPosition,
    onClick,
    onFocus,
    onBlur,
    type,
    className,
    style,
    title,
    'data-testid': testId,
  } = standardProps;

  // 标准化属性值
  const normalizedVariant = normalizeVariant(variant);
  const normalizedSize = normalizeSize(size);

  // 生成组合的类名，确保样式隔离
  const buttonClassName = classNames(
    styles.button,
    styles[normalizedVariant],
    normalizedSize !== 'medium' && styles[normalizedSize],
    iconOnly && styles.iconOnly,
    loading && styles.loading,
    className
  );

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) {
      event.preventDefault();
      return;
    }
    onClick?.(event);
  };

  // 渲染按钮内容
  const renderContent = () => {
    if (iconOnly) {
      return icon || children;
    }

    if (icon && iconPosition === 'left') {
      return (
        <>
          <span className={styles.icon}>{icon}</span>
          {children}
        </>
      );
    }

    if (icon && iconPosition === 'right') {
      return (
        <>
          {children}
          <span className={styles.icon}>{icon}</span>
        </>
      );
    }

    return children;
  };

  return (
    <button
      type={type}
      className={buttonClassName}
      style={style}
      title={title}
      disabled={disabled || loading}
      onClick={handleClick}
      onFocus={onFocus}
      onBlur={onBlur}
      data-testid={testId}
      aria-disabled={disabled || loading}
      aria-busy={loading}
    >
      {renderContent()}
    </button>
  );
};

// 导出默认组件
export default Button;