/**
 * StatusIndicator组件样式 - 使用CSS Modules确保样式隔离
 * 所有类名都会自动添加哈希后缀，避免与Obsidian主题冲突
 */

.statusIndicator {
  display: inline-flex;
  align-items: center;
  font-family: var(--ptm-font-family);
  gap: var(--ptm-spacing-2);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* 垂直布局 */
.verticalLayout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--ptm-spacing-1);
}

/* 文本位置变体 */
.text-left {
  flex-direction: row-reverse;
}

.text-right {
  flex-direction: row;
}

.text-top,
.text-bottom {
  flex-direction: column;
  align-items: center;
}

/* 指示器核心样式 */
.indicator {
  position: relative;
  border-radius: var(--ptm-radius-full);
  background-color: var(--ptm-gray-400);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
}

/* 指示器尺寸 */
.small .indicator {
  width: 6px;
  height: 6px;
}

.medium .indicator {
  width: 8px;
  height: 8px;
}

.large .indicator {
  width: 12px;
  height: 12px;
}

/* 状态颜色 */
.success {
  background-color: var(--ptm-success) !important;
}

.warning {
  background-color: var(--ptm-warning) !important;
}

.danger {
  background-color: var(--ptm-danger) !important;
}

.info {
  background-color: var(--ptm-info) !important;
}

.pending {
  background-color: var(--ptm-gray-400) !important;
}

.inactive {
  background-color: var(--ptm-gray-300) !important;
}

.processing {
  background-color: var(--ptm-primary) !important;
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.pulse::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-color: inherit;
  animation: pulseRing 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* 图标容器 */
.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--ptm-text-inverse);
  font-size: 60%;
}

.small .iconContainer {
  font-size: 50%;
}

.large .iconContainer {
  font-size: 70%;
}

/* 文本样式 */
.text {
  color: var(--ptm-text-secondary) !important;
  font-size: var(--ptm-text-sm) !important;
  font-weight: var(--ptm-font-medium) !important;
  font-family: var(--ptm-font-family) !important;
  line-height: var(--ptm-leading-normal) !important;
  margin: 0 !important;
  padding: 0 !important;
  white-space: nowrap;
}

.small .text {
  font-size: var(--ptm-text-xs) !important;
}

.large .text {
  font-size: var(--ptm-text-base) !important;
}

/* 可点击状态 */
.clickable {
  cursor: pointer;
  border-radius: var(--ptm-radius-sm);
  padding: var(--ptm-spacing-1);
  margin: calc(-1 * var(--ptm-spacing-1));
}

.clickable:hover {
  background-color: var(--ptm-bg-secondary);
}

.clickable:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

.clickable:active {
  transform: scale(0.95);
}

.clickable:hover .indicator {
  transform: scale(1.1);
}

.clickable:hover .text {
  color: var(--ptm-text-primary) !important;
}

/* 特殊状态的脉冲颜色 */
.success.pulse::before {
  background-color: var(--ptm-success);
}

.warning.pulse::before {
  background-color: var(--ptm-warning);
}

.danger.pulse::before {
  background-color: var(--ptm-danger);
}

.info.pulse::before {
  background-color: var(--ptm-info);
}

.pending.pulse::before {
  background-color: var(--ptm-gray-400);
}

.inactive.pulse::before {
  background-color: var(--ptm-gray-300);
}

.processing.pulse::before {
  background-color: var(--ptm-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statusIndicator {
    gap: var(--ptm-spacing-1);
  }
  
  .text {
    font-size: var(--ptm-text-xs) !important;
  }
  
  .large .text {
    font-size: var(--ptm-text-sm) !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .indicator {
    border: 2px solid var(--ptm-text-primary);
  }
  
  .text {
    font-weight: var(--ptm-font-bold) !important;
  }
  
  .clickable:hover {
    background-color: var(--ptm-text-primary) !important;
  }
  
  .clickable:hover .text {
    color: var(--ptm-text-inverse) !important;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .statusIndicator,
  .indicator,
  .clickable {
    transition: none !important;
  }
  
  .pulse,
  .pulse::before {
    animation: none !important;
  }
  
  .clickable:active {
    transform: none !important;
  }
  
  .clickable:hover .indicator {
    transform: none !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .text {
    color: var(--ptm-gray-300) !important;
  }
  
  .clickable:hover {
    background-color: var(--ptm-gray-700);
  }
  
  .clickable:hover .text {
    color: var(--ptm-gray-100) !important;
  }
}

/* 打印样式 */
@media print {
  .statusIndicator {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .pulse,
  .pulse::before {
    animation: none !important;
  }
  
  .clickable:hover {
    background-color: transparent !important;
  }
}