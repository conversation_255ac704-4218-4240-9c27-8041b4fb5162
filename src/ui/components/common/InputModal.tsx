// 输入对话框组件

import React, { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';

interface InputModalProps {
	isOpen: boolean;
	title: string;
	placeholder?: string;
	defaultValue?: string;
	onConfirm: (value: string) => void;
	onCancel: () => void;
}

export const InputModal: React.FC<InputModalProps> = ({
	isOpen,
	title,
	placeholder = '',
	defaultValue = '',
	onConfirm,
	onCancel
}) => {
	const [value, setValue] = useState(defaultValue);
	const inputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		if (isOpen) {
			setValue(defaultValue);
			// 延迟聚焦，确保模态框已经渲染
			setTimeout(() => {
				inputRef.current?.focus();
				inputRef.current?.select();
			}, 100);
		}
	}, [isOpen, defaultValue]);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (value.trim()) {
			onConfirm(value.trim());
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Escape') {
			onCancel();
		}
	};

	if (!isOpen) return null;

	return (
		<div className="input-modal-overlay" onClick={onCancel}>
			<div className="input-modal" onClick={(e) => e.stopPropagation()}>
				<div className="input-modal-header">
					<h3>{title}</h3>
					<button 
						className="input-modal-close"
						onClick={onCancel}
						type="button"
					>
						<X size={16} />
					</button>
				</div>
				
				<form onSubmit={handleSubmit} className="input-modal-form">
					<input
						ref={inputRef}
						type="text"
						value={value}
						onChange={(e) => setValue(e.target.value)}
						placeholder={placeholder}
						className="input-modal-input"
						onKeyDown={handleKeyDown}
					/>
					
					<div className="input-modal-actions">
						<button 
							type="button" 
							onClick={onCancel}
							className="input-modal-btn input-modal-btn-cancel"
						>
							取消
						</button>
						<button 
							type="submit"
							className="input-modal-btn input-modal-btn-confirm"
							disabled={!value.trim()}
						>
							确定
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};