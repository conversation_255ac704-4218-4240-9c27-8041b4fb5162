// 项目创建模态框组件

import React, { useState } from 'react';
import { App, Modal, Notice } from 'obsidian';
import { createRoot, Root } from 'react-dom/client';
import { PTMManager } from '../../../services/PTMManager';
import { Priority } from '../../../models';

interface ProjectCreateModalProps {
	onProjectCreated?: (projectId: string) => void;
	onCancel?: () => void;
}

const ProjectCreateForm: React.FC<ProjectCreateModalProps> = ({ onProjectCreated, onCancel }) => {
	const [formData, setFormData] = useState({
		name: '',
		description: '',
		priority: Priority.MEDIUM,
		tags: '',
		startDate: new Date().toISOString().split('T')[0],
		endDate: '',
		createPTMFile: true
	});
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!formData.name.trim()) {
			new Notice('请输入项目名称');
			return;
		}

		setIsSubmitting(true);
		try {
			// 这里需要通过某种方式访问 PTMManager
			// 我们将在模态框类中处理实际的项目创建
			if (onProjectCreated) {
				onProjectCreated(formData.name);
			}
		} catch (error) {
			console.error('Error creating project:', error);
			new Notice('创建项目失败');
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleInputChange = (field: string, value: any) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	return (
		<div className="ptm-modal-content">
			<h2 style={{ marginBottom: '1rem' }}>创建新项目</h2>
			
			<form onSubmit={handleSubmit}>
				<div className="ptm-form-group">
					<label htmlFor="project-name">项目名称 *</label>
					<input
						id="project-name"
						type="text"
						value={formData.name}
						onChange={(e) => handleInputChange('name', e.target.value)}
						placeholder="输入项目名称"
						required
						autoFocus
					/>
				</div>

				<div className="ptm-form-group">
					<label htmlFor="project-description">项目描述</label>
					<textarea
						id="project-description"
						value={formData.description}
						onChange={(e) => handleInputChange('description', e.target.value)}
						placeholder="输入项目描述（可选）"
						rows={3}
					/>
				</div>

				<div className="ptm-form-row">
					<div className="ptm-form-group">
						<label htmlFor="project-priority">优先级</label>
						<select
							id="project-priority"
							value={formData.priority}
							onChange={(e) => handleInputChange('priority', e.target.value as Priority)}
						>
							<option value={Priority.LOW}>低</option>
							<option value={Priority.MEDIUM}>中</option>
							<option value={Priority.HIGH}>高</option>
							<option value={Priority.CRITICAL}>紧急</option>
						</select>
					</div>

					<div className="ptm-form-group">
						<label htmlFor="project-tags">标签</label>
						<input
							id="project-tags"
							type="text"
							value={formData.tags}
							onChange={(e) => handleInputChange('tags', e.target.value)}
							placeholder="用逗号分隔多个标签"
						/>
					</div>
				</div>

				<div className="ptm-form-row">
					<div className="ptm-form-group">
						<label htmlFor="project-start-date">开始日期</label>
						<input
							id="project-start-date"
							type="date"
							value={formData.startDate}
							onChange={(e) => handleInputChange('startDate', e.target.value)}
						/>
					</div>

					<div className="ptm-form-group">
						<label htmlFor="project-end-date">结束日期</label>
						<input
							id="project-end-date"
							type="date"
							value={formData.endDate}
							onChange={(e) => handleInputChange('endDate', e.target.value)}
							min={formData.startDate}
						/>
					</div>
				</div>

				<div className="ptm-form-group">
					<label className="ptm-checkbox-label">
						<input
							type="checkbox"
							checked={formData.createPTMFile}
							onChange={(e) => handleInputChange('createPTMFile', e.target.checked)}
						/>
						创建 PTM 项目文件
					</label>
				</div>

				<div className="ptm-modal-actions">
					<button
						type="button"
						className="ptm-button ptm-button-secondary"
						onClick={onCancel}
						disabled={isSubmitting}
					>
						取消
					</button>
					<button
						type="submit"
						className="ptm-button ptm-button-primary"
						disabled={isSubmitting}
					>
						{isSubmitting ? '创建中...' : '创建项目'}
					</button>
				</div>
			</form>
		</div>
	);
};

export class ProjectCreateModal extends Modal {
	private root: Root | null = null;
	private ptmManager: PTMManager;

	constructor(app: App, ptmManager: PTMManager) {
		super(app);
		this.ptmManager = ptmManager;
	}

	onOpen() {
		const { contentEl } = this;
		contentEl.empty();
		contentEl.addClass('ptm-project-create-modal');

		// 创建 React 根节点
		this.root = createRoot(contentEl);
		
		// 渲染 React 组件
		this.root.render(
			<ProjectCreateForm
				onProjectCreated={this.handleProjectCreated.bind(this)}
				onCancel={this.handleCancel.bind(this)}
			/>
		);
	}

	onClose() {
		if (this.root) {
			this.root.unmount();
			this.root = null;
		}
	}

	private async handleProjectCreated(projectName: string) {
		try {
			// 获取表单数据
			const formEl = this.contentEl.querySelector('form') as HTMLFormElement;
			if (!formEl) return;

			const formData = new FormData(formEl);
			const name = (formEl.querySelector('#project-name') as HTMLInputElement)?.value || projectName;
			const description = (formEl.querySelector('#project-description') as HTMLTextAreaElement)?.value;
			const priority = (formEl.querySelector('#project-priority') as HTMLSelectElement)?.value as Priority;
			const tags = (formEl.querySelector('#project-tags') as HTMLInputElement)?.value;
			const startDate = (formEl.querySelector('#project-start-date') as HTMLInputElement)?.value;
			const endDate = (formEl.querySelector('#project-end-date') as HTMLInputElement)?.value;
			const createPTMFile = (formEl.querySelector('input[type="checkbox"]') as HTMLInputElement)?.checked;

			// 处理标签
			const tagArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

			// 创建项目
			const projectManager = this.ptmManager.getProjectManager();
			const project = await projectManager.createProject({
				name,
				description: description || undefined,
				priority,
				tags: tagArray,
				startDate: startDate ? new Date(startDate) : undefined,
				endDate: endDate ? new Date(endDate) : undefined,
				createPTMFile,
				ptmFilePath: createPTMFile ? `${name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.ptm` : undefined
			});

			new Notice(`项目 "${project.name}" 创建成功！`);
			
			// 关闭模态框
			this.close();

			// 打开项目仪表板
			this.app.workspace.getLeaf(false).setViewState({
				type: 'project-task-manager-dashboard',
				active: true
			});

		} catch (error) {
			console.error('Error creating project:', error);
			new Notice(`创建项目失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private handleCancel() {
		this.close();
	}
}