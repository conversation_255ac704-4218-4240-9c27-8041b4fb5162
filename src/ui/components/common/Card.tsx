// Base Card component with Obsidian theme integration
// 使用标准化的组件接口规范

import React from 'react';
import clsx from 'clsx';
import { StandardCardProps, createStandardProps, normalizeSpacing } from '../../types/components';
import { validateAndWarn, validateCardProps } from '../../utils/componentValidation';

export interface CardProps extends StandardCardProps, React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const Card: React.FC<CardProps> = (props) => {
	// 开发环境下进行属性验证
	validateAndWarn('Card', props, validateCardProps);

	// 使用标准化的属性处理
	const standardProps = createStandardProps(props, {
		variant: 'default',
		padding: 'medium',
	});

	const {
		variant,
		padding,
		children,
		className,
		clickable,
		onClick,
		...restProps
	} = standardProps;

	// 标准化间距属性
	const normalizedPadding = normalizeSpacing(padding);

	const baseClasses = 'ptm-card';
	
	const variantClasses = {
		default: 'ptm-card--default',
		elevated: 'ptm-card--elevated',
		outlined: 'ptm-card--outlined'
	};

	const paddingClasses = {
		none: 'ptm-card--no-padding',
		small: 'ptm-card--padding-sm',
		medium: 'ptm-card--padding-md',
		large: 'ptm-card--padding-lg'
	};

	const classes = clsx(
		baseClasses,
		variantClasses[variant],
		paddingClasses[normalizedPadding],
		clickable && 'ptm-card--clickable',
		className
	);

	return (
		<div 
			className={classes} 
			onClick={clickable ? onClick : undefined}
			role={clickable ? 'button' : undefined}
			tabIndex={clickable ? 0 : undefined}
			{...restProps}
		>
			{children}
		</div>
	);
};

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
	children,
	className,
	...props
}) => {
	return (
		<div className={clsx('ptm-card__header', className)} {...props}>
			{children}
		</div>
	);
};

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({
	children,
	className,
	...props
}) => {
	return (
		<div className={clsx('ptm-card__content', className)} {...props}>
			{children}
		</div>
	);
};

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
}

export const CardFooter: React.FC<CardFooterProps> = ({
	children,
	className,
	...props
}) => {
	return (
		<div className={clsx('ptm-card__footer', className)} {...props}>
			{children}
		</div>
	);
};