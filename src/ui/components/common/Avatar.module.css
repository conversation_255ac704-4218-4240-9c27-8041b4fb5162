/**
 * Avatar组件样式 - 使用CSS Modules确保样式隔离
 * 所有类名都会自动添加哈希后缀，避免与Obsidian主题冲突
 */

.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 500;
  background-color: var(--ptm-gray-400);
  color: #ffffff;
  overflow: hidden;
  flex-shrink: 0;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 头像尺寸 */
.small {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.medium {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.large {
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.xlarge {
  width: 64px;
  height: 64px;
  font-size: 18px;
}

/* 头像形状 */
.circle {
  border-radius: 9999px;
}

.square {
  border-radius: 8px;
}

/* 可点击状态 */
.clickable {
  cursor: pointer;
}

.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.clickable:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

.clickable:active {
  transform: scale(0.95);
}

/* 头像图片 */
.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: none !important;
  background: none !important;
}

/* 头像文字 */
.text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  font-weight: 600 !important;
  line-height: 1 !important;
  color: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 状态指示器 */
.statusIndicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25%;
  height: 25%;
  min-width: 8px;
  min-height: 8px;
  border-radius: 9999px;
  border: 2px solid #ffffff;
  background-color: var(--ptm-gray-400);
}

.statusIndicator.online {
  background-color: var(--ptm-success);
}

.statusIndicator.offline {
  background-color: var(--ptm-gray-400);
}

.statusIndicator.away {
  background-color: var(--ptm-warning);
}

.statusIndicator.busy {
  background-color: var(--ptm-danger);
}

/* 加载状态 */
.loading {
  opacity: 0.7;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: inherit;
}

.spinner {
  width: 50%;
  height: 50%;
  min-width: 12px;
  min-height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: rgba(255, 255, 255, 0.8);
  border-radius: 9999px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xlarge {
    width: 56px;
    height: 56px;
    font-size: 16px;
  }
  
  .large {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .avatar {
    border: 2px solid #1f2937;
  }
  
  .statusIndicator {
    border-width: 3px;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .avatar,
  .clickable,
  .spinner {
    transition: none !important;
    animation: none !important;
  }
  
  .clickable:hover {
    transform: none !important;
  }
  
  .clickable:active {
    transform: none !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .statusIndicator {
    border-color: #1f2937;
  }
  
  .loadingOverlay {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

/* 打印样式 */
@media print {
  .avatar {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .statusIndicator {
    display: none;
  }
  
  .loadingOverlay {
    display: none;
  }
}