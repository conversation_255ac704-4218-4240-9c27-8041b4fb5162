// 任务创建模态框组件

import React, { useState, useEffect } from 'react';
import { App, Modal, Notice } from 'obsidian';
import { createRoot, Root } from 'react-dom/client';
import { PTMManager } from '../../../services/PTMManager';
import { Priority, Project } from '../../../models';

interface TaskCreateOptions {
	initialTitle?: string;
	sourceFile?: string;
	projectId?: string;
}

interface TaskCreateModalProps {
	ptmManager: PTMManager;
	options?: TaskCreateOptions;
	onTaskCreated?: (taskId: string) => void;
	onCancel?: () => void;
}

const TaskCreateForm: React.FC<TaskCreateModalProps> = ({ 
	ptmManager, 
	options = {}, 
	onTaskCreated, 
	onCancel 
}) => {
	const [projects, setProjects] = useState<Project[]>([]);
	const [formData, setFormData] = useState({
		title: options.initialTitle || '',
		description: '',
		projectId: options.projectId || '',
		priority: Priority.MEDIUM,
		assignee: '',
		estimatedHours: '',
		startDate: new Date().toISOString().split('T')[0],
		dueDate: '',
		tags: '',
		linkedNotes: options.sourceFile ? [options.sourceFile] : []
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [loading, setLoading] = useState(true);

	// 加载项目列表
	useEffect(() => {
		loadProjects();
	}, []);

	const loadProjects = async () => {
		try {
			const projectManager = ptmManager.getProjectManager();
			const allProjects = await projectManager.getAllProjects();
			setProjects(allProjects);
			
			// 如果没有指定项目ID且有项目，选择第一个活跃项目
			if (!formData.projectId && allProjects.length > 0) {
				const activeProject = allProjects.find(p => p.status === 'active') || allProjects[0];
				setFormData(prev => ({ ...prev, projectId: activeProject.id }));
			}
		} catch (error) {
			console.error('Error loading projects:', error);
			new Notice('加载项目列表失败');
		} finally {
			setLoading(false);
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!formData.title.trim()) {
			new Notice('请输入任务标题');
			return;
		}

		if (!formData.projectId) {
			new Notice('请选择项目');
			return;
		}

		setIsSubmitting(true);
		try {
			const taskManager = ptmManager.getTaskManager();
			
			// 处理标签
			const tagArray = formData.tags ? 
				formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

			// 创建任务
			const task = await taskManager.createTask({
				title: formData.title,
				projectId: formData.projectId,
				description: formData.description || undefined,
				priority: formData.priority,
				assignee: formData.assignee || undefined,
				estimatedHours: formData.estimatedHours ? parseFloat(formData.estimatedHours) : undefined,
				startDate: formData.startDate ? new Date(formData.startDate) : undefined,
				dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
				tags: tagArray,
				linkedNotes: formData.linkedNotes.filter(note => note.trim())
			});

			new Notice(`任务 "${task.title}" 创建成功！`);
			
			if (onTaskCreated) {
				onTaskCreated(task.id);
			}

		} catch (error) {
			console.error('Error creating task:', error);
			new Notice(`创建任务失败: ${error instanceof Error ? error.message : String(error)}`);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleInputChange = (field: string, value: any) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	const addLinkedNote = () => {
		setFormData(prev => ({
			...prev,
			linkedNotes: [...prev.linkedNotes, '']
		}));
	};

	const updateLinkedNote = (index: number, value: string) => {
		setFormData(prev => ({
			...prev,
			linkedNotes: prev.linkedNotes.map((note, i) => i === index ? value : note)
		}));
	};

	const removeLinkedNote = (index: number) => {
		setFormData(prev => ({
			...prev,
			linkedNotes: prev.linkedNotes.filter((_, i) => i !== index)
		}));
	};

	if (loading) {
		return (
			<div className="ptm-modal-content">
				<div style={{ textAlign: 'center', padding: '2rem' }}>
					<div className="ptm-spinner">
						<svg viewBox="0 0 24 24" style={{ width: '2rem', height: '2rem' }}>
							<circle
								className="ptm-spinner__circle"
								cx="12"
								cy="12"
								r="10"
								fill="none"
								strokeWidth="2"
							/>
						</svg>
					</div>
					<p style={{ marginTop: '1rem', color: 'var(--text-muted)' }}>
						加载项目列表...
					</p>
				</div>
			</div>
		);
	}

	if (projects.length === 0) {
		return (
			<div className="ptm-modal-content">
				<h2 style={{ marginBottom: '1rem' }}>创建新任务</h2>
				<div style={{ textAlign: 'center', padding: '2rem' }}>
					<p style={{ color: 'var(--text-muted)', marginBottom: '1rem' }}>
						没有找到项目，请先创建一个项目
					</p>
					<div className="ptm-modal-actions">
						<button
							type="button"
							className="ptm-button ptm-button-secondary"
							onClick={onCancel}
						>
							取消
						</button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="ptm-modal-content">
			<h2 style={{ marginBottom: '1rem' }}>创建新任务</h2>
			
			<form onSubmit={handleSubmit}>
				<div className="ptm-form-group">
					<label htmlFor="task-title">任务标题 *</label>
					<input
						id="task-title"
						type="text"
						value={formData.title}
						onChange={(e) => handleInputChange('title', e.target.value)}
						placeholder="输入任务标题"
						required
						autoFocus
					/>
				</div>

				<div className="ptm-form-group">
					<label htmlFor="task-project">所属项目 *</label>
					<select
						id="task-project"
						value={formData.projectId}
						onChange={(e) => handleInputChange('projectId', e.target.value)}
						required
					>
						<option value="">选择项目</option>
						{projects.map(project => (
							<option key={project.id} value={project.id}>
								{project.name} ({project.status})
							</option>
						))}
					</select>
				</div>

				<div className="ptm-form-group">
					<label htmlFor="task-description">任务描述</label>
					<textarea
						id="task-description"
						value={formData.description}
						onChange={(e) => handleInputChange('description', e.target.value)}
						placeholder="输入任务描述（可选）"
						rows={3}
					/>
				</div>

				<div className="ptm-form-row">
					<div className="ptm-form-group">
						<label htmlFor="task-priority">优先级</label>
						<select
							id="task-priority"
							value={formData.priority}
							onChange={(e) => handleInputChange('priority', e.target.value as Priority)}
						>
							<option value={Priority.LOW}>低</option>
							<option value={Priority.MEDIUM}>中</option>
							<option value={Priority.HIGH}>高</option>
							<option value={Priority.CRITICAL}>紧急</option>
						</select>
					</div>

					<div className="ptm-form-group">
						<label htmlFor="task-assignee">负责人</label>
						<input
							id="task-assignee"
							type="text"
							value={formData.assignee}
							onChange={(e) => handleInputChange('assignee', e.target.value)}
							placeholder="输入负责人（可选）"
						/>
					</div>
				</div>

				<div className="ptm-form-row">
					<div className="ptm-form-group">
						<label htmlFor="task-estimated-hours">预估工时</label>
						<input
							id="task-estimated-hours"
							type="number"
							min="0"
							step="0.5"
							value={formData.estimatedHours}
							onChange={(e) => handleInputChange('estimatedHours', e.target.value)}
							placeholder="小时"
						/>
					</div>

					<div className="ptm-form-group">
						<label htmlFor="task-tags">标签</label>
						<input
							id="task-tags"
							type="text"
							value={formData.tags}
							onChange={(e) => handleInputChange('tags', e.target.value)}
							placeholder="用逗号分隔多个标签"
						/>
					</div>
				</div>

				<div className="ptm-form-row">
					<div className="ptm-form-group">
						<label htmlFor="task-start-date">开始日期</label>
						<input
							id="task-start-date"
							type="date"
							value={formData.startDate}
							onChange={(e) => handleInputChange('startDate', e.target.value)}
						/>
					</div>

					<div className="ptm-form-group">
						<label htmlFor="task-due-date">截止日期</label>
						<input
							id="task-due-date"
							type="date"
							value={formData.dueDate}
							onChange={(e) => handleInputChange('dueDate', e.target.value)}
							min={formData.startDate}
						/>
					</div>
				</div>

				<div className="ptm-form-group">
					<label>关联笔记</label>
					{formData.linkedNotes.map((note, index) => (
						<div key={index} className="ptm-linked-note-row">
							<input
								type="text"
								value={note}
								onChange={(e) => updateLinkedNote(index, e.target.value)}
								placeholder="笔记路径（如：folder/note.md）"
							/>
							<button
								type="button"
								className="ptm-button ptm-button-small ptm-button-danger"
								onClick={() => removeLinkedNote(index)}
							>
								删除
							</button>
						</div>
					))}
					<button
						type="button"
						className="ptm-button ptm-button-small ptm-button-secondary"
						onClick={addLinkedNote}
					>
						添加关联笔记
					</button>
				</div>

				<div className="ptm-modal-actions">
					<button
						type="button"
						className="ptm-button ptm-button-secondary"
						onClick={onCancel}
						disabled={isSubmitting}
					>
						取消
					</button>
					<button
						type="submit"
						className="ptm-button ptm-button-primary"
						disabled={isSubmitting}
					>
						{isSubmitting ? '创建中...' : '创建任务'}
					</button>
				</div>
			</form>
		</div>
	);
};

export class TaskCreateModal extends Modal {
	private root: Root | null = null;
	private ptmManager: PTMManager;
	private options: TaskCreateOptions;

	constructor(app: App, ptmManager: PTMManager, options: TaskCreateOptions = {}) {
		super(app);
		this.ptmManager = ptmManager;
		this.options = options;
	}

	onOpen() {
		const { contentEl } = this;
		contentEl.empty();
		contentEl.addClass('ptm-task-create-modal');

		// 创建 React 根节点
		this.root = createRoot(contentEl);
		
		// 渲染 React 组件
		this.root.render(
			<TaskCreateForm
				ptmManager={this.ptmManager}
				options={this.options}
				onTaskCreated={this.handleTaskCreated.bind(this)}
				onCancel={this.handleCancel.bind(this)}
			/>
		);
	}

	onClose() {
		if (this.root) {
			this.root.unmount();
			this.root = null;
		}
	}

	private handleTaskCreated(taskId: string) {
		// 关闭模态框
		this.close();

		// 打开任务列表视图
		this.app.workspace.getLeaf(false).setViewState({
			type: 'project-task-manager-task-list',
			active: true
		});
	}

	private handleCancel() {
		this.close();
	}
}