/**
 * ProgressBar组件样式 - 使用CSS Modules确保样式隔离
 * 所有类名都会自动添加哈希后缀，避免与Obsidian主题冲突
 */

.progressBar {
  font-family: var(--ptm-font-family);
  width: 100%;
}

/* 进度条容器 */
.progressContainer {
  position: relative;
  background-color: var(--ptm-gray-200);
  border-radius: var(--ptm-radius-full);
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 进度条尺寸 */
.small .progressContainer {
  height: 4px;
}

.medium .progressContainer {
  height: 8px;
}

.large .progressContainer {
  height: 16px;
}

/* 进度填充 */
.progressFill {
  height: 100%;
  background-color: var(--ptm-primary);
  border-radius: inherit;
  transition: width var(--ptm-duration-normal) var(--ptm-easing-smooth);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.progressFill.animating {
  transition: width var(--ptm-duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 颜色主题 */
.primary .progressFill {
  background: var(--ptm-primary-gradient);
}

.success .progressFill {
  background: linear-gradient(90deg, var(--ptm-success) 0%, #38a169 100%);
}

.warning .progressFill {
  background: linear-gradient(90deg, var(--ptm-warning) 0%, #d69e2e 100%);
}

.danger .progressFill {
  background: linear-gradient(90deg, var(--ptm-danger) 0%, #e53e3e 100%);
}

.info .progressFill {
  background: linear-gradient(90deg, var(--ptm-info) 0%, #2b6cb0 100%);
}

/* 条纹效果 */
.striped .progressFill {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 16px 16px;
}

.animatedStripes .progressFill {
  animation: stripeAnimation 1s linear infinite;
}

@keyframes stripeAnimation {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 16px 0;
  }
}

/* 标签样式 */
.labelInside {
  color: var(--ptm-text-inverse) !important;
  font-size: var(--ptm-text-xs) !important;
  font-weight: var(--ptm-font-medium) !important;
  font-family: var(--ptm-font-family) !important;
  line-height: 1 !important;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0 !important;
  padding: 0 var(--ptm-spacing-2) !important;
}

.labelOutside {
  color: var(--ptm-text-secondary) !important;
  font-size: var(--ptm-text-sm) !important;
  font-weight: var(--ptm-font-medium) !important;
  font-family: var(--ptm-font-family) !important;
  margin-top: var(--ptm-spacing-2) !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
  text-align: right;
}

.labelTop {
  color: var(--ptm-text-secondary) !important;
  font-size: var(--ptm-text-sm) !important;
  font-weight: var(--ptm-font-medium) !important;
  font-family: var(--ptm-font-family) !important;
  margin-bottom: var(--ptm-spacing-2) !important;
  margin-top: 0 !important;
  padding: 0 !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 小尺寸时隐藏内部标签 */
.small .labelInside {
  display: none;
}

/* 中等尺寸时调整内部标签 */
.medium .labelInside {
  font-size: 10px !important;
}

/* 大尺寸时正常显示内部标签 */
.large .labelInside {
  font-size: var(--ptm-text-xs) !important;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.disabled .progressContainer {
  background-color: var(--ptm-gray-100) !important;
}

.disabled .progressFill {
  background: var(--ptm-gray-300) !important;
}

/* 条纹图案背景 */
.stripePattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.05) 25%,
    transparent 25%,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 50%,
    rgba(0, 0, 0, 0.05) 75%,
    transparent 75%,
    transparent
  );
  background-size: 8px 8px;
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .labelInside {
    font-size: 10px !important;
  }
  
  .labelOutside,
  .labelTop {
    font-size: var(--ptm-text-xs) !important;
  }
  
  .large .progressContainer {
    height: 12px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .progressContainer {
    border: 1px solid var(--ptm-text-primary);
  }
  
  .progressFill {
    border: 1px solid var(--ptm-text-primary);
  }
  
  .labelInside {
    text-shadow: none !important;
    font-weight: var(--ptm-font-bold) !important;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .progressFill,
  .progressFill.animating {
    transition: none !important;
  }
  
  .animatedStripes .progressFill {
    animation: none !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .progressContainer {
    background-color: var(--ptm-gray-700);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  }
  
  .disabled .progressContainer {
    background-color: var(--ptm-gray-800) !important;
  }
  
  .disabled .progressFill {
    background: var(--ptm-gray-600) !important;
  }
}

/* 打印样式 */
@media print {
  .progressBar {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .animatedStripes .progressFill {
    animation: none !important;
  }
  
  .stripePattern {
    display: none;
  }
}

/* 焦点样式（用于可访问性） */
.progressFill:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

/* 悬停效果（如果需要交互） */
.progressBar:hover .progressFill {
  filter: brightness(1.1);
}

/* 脉冲动画（用于加载状态） */
.progressBar.loading .progressFill {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}