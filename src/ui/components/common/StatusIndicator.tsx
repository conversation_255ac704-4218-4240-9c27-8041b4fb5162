/**
 * StatusIndicator组件
 * 状态点显示组件，实现不同状态的颜色区分，添加状态文本提示
 * 使用CSS Modules确保样式隔离
 */

import React from 'react';
import { classNames } from '../../utils/styles';
import styles from './StatusIndicator.module.css';

export type StatusType = 
  | 'success' 
  | 'warning' 
  | 'danger' 
  | 'info' 
  | 'pending' 
  | 'inactive' 
  | 'processing';

export interface StatusIndicatorProps {
  /** 状态类型 */
  status: StatusType;
  /** 状态文本 */
  text?: string;
  /** 指示器尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示脉冲动画 */
  pulse?: boolean;
  /** 是否显示文本 */
  showText?: boolean;
  /** 文本位置 */
  textPosition?: 'right' | 'left' | 'top' | 'bottom';
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 点击事件处理 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 是否可点击 */
  clickable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 测试ID */
  'data-testid'?: string;
}

// 状态配置映射
const STATUS_CONFIG: Record<StatusType, {
  label: string;
  color: string;
  description: string;
}> = {
  success: {
    label: '成功',
    color: 'var(--ptm-success)',
    description: '操作已成功完成',
  },
  warning: {
    label: '警告',
    color: 'var(--ptm-warning)',
    description: '需要注意的状态',
  },
  danger: {
    label: '错误',
    color: 'var(--ptm-danger)',
    description: '发生错误或失败',
  },
  info: {
    label: '信息',
    color: 'var(--ptm-info)',
    description: '一般信息状态',
  },
  pending: {
    label: '等待中',
    color: 'var(--ptm-gray-400)',
    description: '等待处理或响应',
  },
  inactive: {
    label: '未激活',
    color: 'var(--ptm-gray-300)',
    description: '未激活或禁用状态',
  },
  processing: {
    label: '处理中',
    color: 'var(--ptm-primary)',
    description: '正在处理中',
  },
};

/**
 * StatusIndicator组件 - 确保样式隔离的状态指示器组件
 */
export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  text,
  size = 'medium',
  pulse = false,
  showText = false,
  textPosition = 'right',
  icon,
  onClick,
  clickable = false,
  className,
  'data-testid': testId,
}) => {
  const statusConfig = STATUS_CONFIG[status];
  const displayText = text || statusConfig.label;

  // 处理点击事件
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (clickable && onClick) {
      onClick(event);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (clickable && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      handleClick(event as any);
    }
  };

  // 生成容器类名
  const containerClassName = classNames(
    styles.statusIndicator,
    styles[size],
    styles[`text-${textPosition}`],
    clickable ? styles.clickable : undefined,
    className
  );

  // 生成指示器类名
  const indicatorClassName = classNames(
    styles.indicator,
    styles[status],
    pulse ? styles.pulse : undefined
  );

  // 渲染指示器内容
  const renderIndicator = () => (
    <div 
      className={indicatorClassName}
      style={{ backgroundColor: statusConfig.color }}
      aria-hidden="true"
    >
      {icon && (
        <div className={styles.iconContainer}>
          {icon}
        </div>
      )}
    </div>
  );

  // 渲染文本内容
  const renderText = () => {
    if (!showText && !text) return null;

    return (
      <span className={styles.text}>
        {displayText}
      </span>
    );
  };

  // 根据文本位置渲染内容
  const renderContent = () => {
    switch (textPosition) {
      case 'left':
        return (
          <>
            {renderText()}
            {renderIndicator()}
          </>
        );
      case 'top':
        return (
          <div className={styles.verticalLayout}>
            {renderText()}
            {renderIndicator()}
          </div>
        );
      case 'bottom':
        return (
          <div className={styles.verticalLayout}>
            {renderIndicator()}
            {renderText()}
          </div>
        );
      case 'right':
      default:
        return (
          <>
            {renderIndicator()}
            {renderText()}
          </>
        );
    }
  };

  return (
    <div
      className={containerClassName}
      onClick={handleClick}
      onKeyDown={clickable ? handleKeyDown : undefined}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      aria-label={`状态: ${displayText}`}
      title={statusConfig.description}
      data-testid={testId}
    >
      {renderContent()}
    </div>
  );
};

// 导出默认组件
export default StatusIndicator;