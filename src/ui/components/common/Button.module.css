/**
 * Button组件样式 - 使用CSS Modules确保样式隔离
 * 所有类名都会自动添加哈希后缀，避免与Obsidian主题冲突
 */

.button {
  /* 使用CSS变量，确保与设计令牌一致 */
  font-family: var(--ptm-font-family);
  font-size: var(--ptm-text-sm);
  font-weight: var(--ptm-font-medium);
  line-height: var(--ptm-leading-normal);
  
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--ptm-spacing-2);
  
  padding: var(--ptm-spacing-3) var(--ptm-spacing-5);
  border: none;
  border-radius: var(--ptm-radius-base);
  cursor: pointer;
  
  /* 过渡效果 */
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  
  /* 确保不被Obsidian主题覆盖 */
  background: none !important;
  color: inherit !important;
  text-decoration: none !important;
}

.button:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮变体 */
.primary {
  background: var(--ptm-primary-gradient) !important;
  color: var(--ptm-text-inverse) !important;
  box-shadow: var(--ptm-shadow-sm);
}

.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--ptm-shadow-md);
}

.secondary {
  background: var(--ptm-bg-primary) !important;
  color: var(--ptm-text-primary) !important;
  border: 1px solid var(--ptm-border-normal) !important;
}

.secondary:hover:not(:disabled) {
  background: var(--ptm-bg-secondary) !important;
  border-color: var(--ptm-border-dark) !important;
}

.ghost {
  background: transparent !important;
  color: var(--ptm-text-secondary) !important;
}

.ghost:hover:not(:disabled) {
  background: var(--ptm-bg-secondary) !important;
  color: var(--ptm-text-primary) !important;
}

.danger {
  background: var(--ptm-danger) !important;
  color: var(--ptm-text-inverse) !important;
}

.danger:hover:not(:disabled) {
  background: var(--ptm-danger) !important;
  opacity: 0.9;
}

/* 按钮尺寸 */
.small {
  padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  font-size: var(--ptm-text-xs);
}

.large {
  padding: var(--ptm-spacing-4) var(--ptm-spacing-6);
  font-size: var(--ptm-text-base);
}

/* 图标样式 */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 图标按钮 */
.iconOnly {
  width: var(--ptm-spacing-8);
  height: var(--ptm-spacing-8);
  padding: 0;
  border-radius: var(--ptm-radius-base);
}

.iconOnly.small {
  width: var(--ptm-spacing-6);
  height: var(--ptm-spacing-6);
}

.iconOnly.large {
  width: var(--ptm-spacing-10);
  height: var(--ptm-spacing-10);
}

/* 加载状态 */
.loading {
  position: relative;
  color: transparent !important;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button {
    padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
    font-size: var(--ptm-text-xs);
  }
  
  .large {
    padding: var(--ptm-spacing-3) var(--ptm-spacing-5);
    font-size: var(--ptm-text-sm);
  }
}