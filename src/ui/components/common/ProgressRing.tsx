// Progress ring component for displaying project completion

import React from 'react';
import clsx from 'clsx';

export interface ProgressRingProps {
	progress: number; // 0-100
	size?: 'sm' | 'md' | 'lg';
	strokeWidth?: number;
	showLabel?: boolean;
	label?: string;
	className?: string;
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
	progress,
	size = 'md',
	strokeWidth,
	showLabel = true,
	label,
	className
}) => {
	const sizeMap = {
		sm: { diameter: 60, defaultStroke: 4 },
		md: { diameter: 80, defaultStroke: 6 },
		lg: { diameter: 120, defaultStroke: 8 }
	};

	const { diameter, defaultStroke } = sizeMap[size];
	const stroke = strokeWidth || defaultStroke;
	const radius = (diameter - stroke) / 2;
	const circumference = 2 * Math.PI * radius;
	const strokeDasharray = circumference;
	const strokeDashoffset = circumference - (progress / 100) * circumference;

	const getProgressColor = (progress: number): string => {
		if (progress >= 80) return 'var(--text-success, #28a745)';
		if (progress >= 60) return 'var(--interactive-accent, #007acc)';
		if (progress >= 40) return 'var(--text-warning, #ffc107)';
		return 'var(--text-error, #dc3545)';
	};

	return (
		<div className={clsx('ptm-progress-ring', className)}>
			<svg
				width={diameter}
				height={diameter}
				className="ptm-progress-ring__svg"
			>
				{/* Background circle */}
				<circle
					cx={diameter / 2}
					cy={diameter / 2}
					r={radius}
					fill="none"
					stroke="var(--background-modifier-border)"
					strokeWidth={stroke}
				/>
				
				{/* Progress circle */}
				<circle
					cx={diameter / 2}
					cy={diameter / 2}
					r={radius}
					fill="none"
					stroke={getProgressColor(progress)}
					strokeWidth={stroke}
					strokeLinecap="round"
					strokeDasharray={strokeDasharray}
					strokeDashoffset={strokeDashoffset}
					className="ptm-progress-ring__progress"
					transform={`rotate(-90 ${diameter / 2} ${diameter / 2})`}
				/>
			</svg>
			
			{showLabel && (
				<div className="ptm-progress-ring__label">
					<div className="ptm-progress-ring__percentage">
						{Math.round(progress)}%
					</div>
					{label && (
						<div className="ptm-progress-ring__text">
							{label}
						</div>
					)}
				</div>
			)}
		</div>
	);
};