/**
 * ProgressBar组件
 * 进度条显示组件，实现动画效果和颜色变化，添加百分比文本显示
 * 使用CSS Modules确保样式隔离
 */

import React, { useEffect, useState, useRef } from 'react';
import { classNames } from '../../utils/styles';
import styles from './ProgressBar.module.css';

export interface ProgressBarProps {
  /** 当前进度值 (0-100) */
  value: number;
  /** 最大值，默认100 */
  max?: number;
  /** 进度条尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 进度条颜色主题 */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  /** 是否显示百分比文本 */
  showPercentage?: boolean;
  /** 是否显示动画效果 */
  animated?: boolean;
  /** 是否显示条纹效果 */
  striped?: boolean;
  /** 自定义标签文本 */
  label?: string;
  /** 标签位置 */
  labelPosition?: 'inside' | 'outside' | 'top';
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 测试ID */
  'data-testid'?: string;
  /** 进度变化回调 */
  onProgressChange?: (value: number, percentage: number) => void;
}

/**
 * ProgressBar组件 - 确保样式隔离的进度条组件
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'medium',
  color = 'primary',
  showPercentage = false,
  animated = true,
  striped = false,
  label,
  labelPosition = 'inside',
  disabled = false,
  className,
  'data-testid': testId,
  onProgressChange,
}) => {
  // 确保值在有效范围内
  const clampedValue = Math.max(0, Math.min(value, max));
  const [displayValue, setDisplayValue] = useState(animated ? 0 : clampedValue);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<number>();
  const previousValueRef = useRef(0);

  const percentage = max > 0 ? (clampedValue / max) * 100 : 0;

  // 动画效果
  useEffect(() => {
    if (!animated) {
      setDisplayValue(clampedValue);
      return;
    }

    const startValue = previousValueRef.current;
    const endValue = clampedValue;
    const duration = 500; // 动画持续时间
    const startTime = Date.now();

    setIsAnimating(true);

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
      const easedProgress = easeOutCubic(progress);
      
      const currentValue = startValue + (endValue - startValue) * easedProgress;
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
        previousValueRef.current = endValue;
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [clampedValue, animated]);

  // 进度变化回调
  useEffect(() => {
    if (onProgressChange) {
      const currentPercentage = max > 0 ? (displayValue / max) * 100 : 0;
      onProgressChange(displayValue, currentPercentage);
    }
  }, [displayValue, max, onProgressChange]);

  // 生成进度条类名
  const progressBarClassName = classNames(
    styles.progressBar,
    styles[size],
    styles[color],
    striped ? styles.striped : undefined,
    (animated && striped) ? styles.animatedStripes : undefined,
    disabled ? styles.disabled : undefined,
    className
  );

  // 生成进度填充类名
  const progressFillClassName = classNames(
    styles.progressFill,
    isAnimating ? styles.animating : undefined
  );

  // 计算显示的百分比
  const displayPercentage = max > 0 ? (displayValue / max) * 100 : 0;

  // 渲染百分比文本
  const renderPercentageText = () => {
    if (!showPercentage && !label) return null;

    const text = label || `${Math.round(displayPercentage)}%`;
    
    if (labelPosition === 'outside') {
      return (
        <div className={styles.labelOutside}>
          {text}
        </div>
      );
    }

    if (labelPosition === 'top') {
      return (
        <div className={styles.labelTop}>
          {text}
        </div>
      );
    }

    // inside position
    return (
      <div className={styles.labelInside}>
        {text}
      </div>
    );
  };

  // 渲染进度条内容
  const renderProgressContent = () => (
    <div className={styles.progressContainer}>
      <div
        className={progressFillClassName}
        style={{ width: `${displayPercentage}%` }}
        role="progressbar"
        aria-valuenow={Math.round(displayValue)}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label || `进度 ${Math.round(displayPercentage)}%`}
      >
        {labelPosition === 'inside' && renderPercentageText()}
      </div>
      
      {/* 背景网格线（可选） */}
      {striped && (
        <div className={styles.stripePattern} />
      )}
    </div>
  );

  return (
    <div className={progressBarClassName} data-testid={testId}>
      {labelPosition === 'top' && renderPercentageText()}
      
      {renderProgressContent()}
      
      {labelPosition === 'outside' && renderPercentageText()}
    </div>
  );
};

// 导出默认组件
export default ProgressBar;