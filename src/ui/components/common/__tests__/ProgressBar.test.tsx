/**
 * ProgressBar组件测试
 * 验证进度条功能、动画效果和样式隔离
 */

import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { ProgressBar } from '../ProgressBar';

// 模拟CSS Modules
jest.mock('../ProgressBar.module.css', () => ({
  progressBar: 'ptm-progress-bar',
  progressContainer: 'ptm-progress-container',
  progressFill: 'ptm-progress-fill',
  small: 'ptm-small',
  medium: 'ptm-medium',
  large: 'ptm-large',
  primary: 'ptm-primary',
  success: 'ptm-success',
  warning: 'ptm-warning',
  danger: 'ptm-danger',
  info: 'ptm-info',
  striped: 'ptm-striped',
  animatedStripes: 'ptm-animated-stripes',
  disabled: 'ptm-disabled',
  animating: 'ptm-animating',
  labelInside: 'ptm-label-inside',
  labelOutside: 'ptm-label-outside',
  labelTop: 'ptm-label-top',
  stripePattern: 'ptm-stripe-pattern',
}));

// 模拟 requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => {
  setTimeout(cb, 16);
  return 1;
});

global.cancelAnimationFrame = jest.fn();

describe('ProgressBar组件测试', () => {
  beforeEach(() => {
    // 创建PTM应用容器
    const container = document.createElement('div');
    container.className = 'ptm-app';
    document.body.appendChild(container);
    
    // 重置模拟函数
    jest.clearAllMocks();
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('基础功能测试', () => {
    test('应该正确渲染进度条', () => {
      render(<ProgressBar value={50} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '50');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    });

    test('应该正确设置进度值', () => {
      render(<ProgressBar value={75} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '75');
    });

    test('应该处理超出范围的值', () => {
      render(<ProgressBar value={150} max={100} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');
    });

    test('应该处理负值', () => {
      render(<ProgressBar value={-10} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '0');
    });

    test('应该支持自定义最大值', () => {
      render(<ProgressBar value={25} max={50} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuemax', '50');
      expect(progressBar).toHaveAttribute('aria-valuenow', '25');
    });
  });

  describe('尺寸变体测试', () => {
    test('应该应用small尺寸样式', () => {
      const { container } = render(<ProgressBar value={50} size="small" />);
      
      expect(container.firstChild).toHaveClass('ptm-small');
    });

    test('应该应用medium尺寸样式', () => {
      const { container } = render(<ProgressBar value={50} size="medium" />);
      
      expect(container.firstChild).toHaveClass('ptm-medium');
    });

    test('应该应用large尺寸样式', () => {
      const { container } = render(<ProgressBar value={50} size="large" />);
      
      expect(container.firstChild).toHaveClass('ptm-large');
    });
  });

  describe('颜色主题测试', () => {
    test('应该应用primary颜色主题', () => {
      const { container } = render(<ProgressBar value={50} color="primary" />);
      
      expect(container.firstChild).toHaveClass('ptm-primary');
    });

    test('应该应用success颜色主题', () => {
      const { container } = render(<ProgressBar value={50} color="success" />);
      
      expect(container.firstChild).toHaveClass('ptm-success');
    });

    test('应该应用warning颜色主题', () => {
      const { container } = render(<ProgressBar value={50} color="warning" />);
      
      expect(container.firstChild).toHaveClass('ptm-warning');
    });

    test('应该应用danger颜色主题', () => {
      const { container } = render(<ProgressBar value={50} color="danger" />);
      
      expect(container.firstChild).toHaveClass('ptm-danger');
    });

    test('应该应用info颜色主题', () => {
      const { container } = render(<ProgressBar value={50} color="info" />);
      
      expect(container.firstChild).toHaveClass('ptm-info');
    });
  });

  describe('百分比显示测试', () => {
    test('应该显示百分比文本', () => {
      render(<ProgressBar value={75} showPercentage animated={false} />);
      
      expect(screen.getByText('75%')).toBeInTheDocument();
    });

    test('应该支持自定义标签', () => {
      render(<ProgressBar value={50} label="进度: 50%" />);
      
      expect(screen.getByText('进度: 50%')).toBeInTheDocument();
    });

    test('应该支持标签在外部显示', () => {
      render(
        <ProgressBar 
          value={60} 
          showPercentage 
          labelPosition="outside" 
          animated={false}
        />
      );
      
      const label = screen.getByText('60%');
      expect(label).toBeInTheDocument();
      expect(label).toHaveClass('ptm-label-outside');
    });

    test('应该支持标签在顶部显示', () => {
      render(
        <ProgressBar 
          value={40} 
          showPercentage 
          labelPosition="top" 
          animated={false}
        />
      );
      
      const label = screen.getByText('40%');
      expect(label).toBeInTheDocument();
      expect(label).toHaveClass('ptm-label-top');
    });

    test('应该支持标签在内部显示', () => {
      render(
        <ProgressBar 
          value={80} 
          showPercentage 
          labelPosition="inside" 
          animated={false}
        />
      );
      
      const label = screen.getByText('80%');
      expect(label).toBeInTheDocument();
      expect(label).toHaveClass('ptm-label-inside');
    });
  });

  describe('条纹效果测试', () => {
    test('应该应用条纹样式', () => {
      const { container } = render(<ProgressBar value={50} striped />);
      
      expect(container.firstChild).toHaveClass('ptm-striped');
    });

    test('应该应用动画条纹样式', () => {
      const { container } = render(
        <ProgressBar value={50} striped animated />
      );
      
      expect(container.firstChild).toHaveClass('ptm-striped');
      expect(container.firstChild).toHaveClass('ptm-animated-stripes');
    });

    test('非动画状态不应该有动画条纹样式', () => {
      const { container } = render(
        <ProgressBar value={50} striped animated={false} />
      );
      
      expect(container.firstChild).toHaveClass('ptm-striped');
      expect(container.firstChild).not.toHaveClass('ptm-animated-stripes');
    });
  });

  describe('禁用状态测试', () => {
    test('应该应用禁用样式', () => {
      const { container } = render(<ProgressBar value={50} disabled />);
      
      expect(container.firstChild).toHaveClass('ptm-disabled');
    });

    test('禁用状态应该正确设置ARIA属性', () => {
      render(<ProgressBar value={50} disabled animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '50');
    });
  });

  describe('动画效果测试', () => {
    test('应该支持禁用动画', () => {
      render(<ProgressBar value={50} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
    });

    test('动画状态变化时应该触发回调', async () => {
      const onProgressChange = jest.fn();
      
      const { rerender } = render(
        <ProgressBar 
          value={30} 
          onProgressChange={onProgressChange} 
          animated={false}
        />
      );
      
      // 等待初始渲染完成
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      // 更改进度值
      rerender(
        <ProgressBar 
          value={70} 
          onProgressChange={onProgressChange} 
          animated={false}
        />
      );
      
      // 等待状态更新
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });
      
      expect(onProgressChange).toHaveBeenCalled();
    });
  });

  describe('样式隔离测试', () => {
    test('应该正确应用样式隔离', () => {
      const { container } = render(
        <div className="ptm-app">
          <ProgressBar value={50} />
        </div>
      );
      
      const progressBar = container.querySelector('.ptm-progress-bar');
      expect(progressBar).not.toBeNull();
      
      // 验证进度条在PTM应用容器中
      const ptmContainer = progressBar?.closest('.ptm-app');
      expect(ptmContainer).not.toBeNull();
    });

    test('应该支持自定义类名', () => {
      const { container } = render(
        <ProgressBar value={50} className="custom-progress" />
      );
      
      expect(container.firstChild).toHaveClass('ptm-progress-bar');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect((container.firstChild as HTMLElement).className).toContain('custom-progress');
    });

    test('组合多个属性应该正确工作', () => {
      const { container } = render(
        <ProgressBar 
          value={65}
          size="large"
          color="success"
          striped
          animated={true}
          showPercentage
          className="custom-class"
        />
      );
      
      const progressBar = container.firstChild;
      expect(progressBar).toHaveClass('ptm-progress-bar');
      expect(progressBar).toHaveClass('ptm-large');
      expect(progressBar).toHaveClass('ptm-success');
      expect(progressBar).toHaveClass('ptm-striped');
      expect(progressBar).toHaveClass('ptm-animated-stripes');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect((progressBar as HTMLElement).className).toContain('custom-class');
      
      // 由于动画，初始显示可能是0%，我们检查进度条存在即可
      const progressBarElement = screen.getByRole('progressbar');
      expect(progressBarElement).toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    test('应该设置正确的ARIA属性', () => {
      render(<ProgressBar value={45} max={100} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '45');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    });

    test('应该设置正确的ARIA标签', () => {
      render(<ProgressBar value={60} label="文件上传进度" />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-label', '文件上传进度');
    });

    test('没有自定义标签时应该有默认ARIA标签', () => {
      render(<ProgressBar value={30} animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-label', '进度 30%');
    });

    test('应该支持data-testid属性', () => {
      render(<ProgressBar value={50} data-testid="test-progress" />);
      
      expect(screen.getByTestId('test-progress')).toBeInTheDocument();
    });
  });

  describe('边界情况测试', () => {
    test('应该处理0%进度', () => {
      render(<ProgressBar value={0} showPercentage />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '0');
      expect(screen.getByText('0%')).toBeInTheDocument();
    });

    test('应该处理100%进度', () => {
      render(<ProgressBar value={100} showPercentage animated={false} />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuenow', '100');
      expect(screen.getByText('100%')).toBeInTheDocument();
    });

    test('应该处理最大值为0的情况', () => {
      render(<ProgressBar value={50} max={0} showPercentage />);
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toHaveAttribute('aria-valuemax', '0');
      expect(screen.getByText('0%')).toBeInTheDocument();
    });

    test('应该正确处理组件卸载', () => {
      const { unmount } = render(<ProgressBar value={50} animated />);
      
      // 应该能够正常卸载而不报错
      expect(() => unmount()).not.toThrow();
    });

    test('应该处理快速连续的值变化', async () => {
      const { rerender } = render(<ProgressBar value={10} animated />);
      
      // 快速连续更改值
      rerender(<ProgressBar value={30} animated />);
      rerender(<ProgressBar value={60} animated />);
      rerender(<ProgressBar value={90} animated />);
      
      // 等待动画完成
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });
      
      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
    });
  });
});