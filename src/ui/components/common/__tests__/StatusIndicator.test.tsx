/**
 * StatusIndicator组件测试
 * 验证状态指示器功能、样式隔离和用户交互
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { StatusIndicator, StatusType } from '../StatusIndicator';

// 模拟CSS Modules
jest.mock('../StatusIndicator.module.css', () => ({
  statusIndicator: 'ptm-status-indicator',
  indicator: 'ptm-indicator',
  text: 'ptm-text',
  small: 'ptm-small',
  medium: 'ptm-medium',
  large: 'ptm-large',
  'text-left': 'ptm-text-left',
  'text-right': 'ptm-text-right',
  'text-top': 'ptm-text-top',
  'text-bottom': 'ptm-text-bottom',
  verticalLayout: 'ptm-vertical-layout',
  clickable: 'ptm-clickable',
  pulse: 'ptm-pulse',
  success: 'ptm-success',
  warning: 'ptm-warning',
  danger: 'ptm-danger',
  info: 'ptm-info',
  pending: 'ptm-pending',
  inactive: 'ptm-inactive',
  processing: 'ptm-processing',
  iconContainer: 'ptm-icon-container',
}));

describe('StatusIndicator组件测试', () => {
  beforeEach(() => {
    // 创建PTM应用容器
    const container = document.createElement('div');
    container.className = 'ptm-app';
    document.body.appendChild(container);
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('基础功能测试', () => {
    test('应该正确渲染状态指示器', () => {
      render(<StatusIndicator status="success" />);
      
      const indicator = screen.getByLabelText('状态: 成功');
      expect(indicator).toBeInTheDocument();
      expect(indicator).toHaveClass('ptm-status-indicator');
    });

    test('应该显示正确的状态颜色', () => {
      const { container } = render(<StatusIndicator status="success" />);
      
      const indicatorDot = container.querySelector('.ptm-indicator');
      expect(indicatorDot).toHaveClass('ptm-success');
    });

    test('应该支持自定义文本', () => {
      render(<StatusIndicator status="warning" text="自定义警告" showText />);
      
      expect(screen.getByText('自定义警告')).toBeInTheDocument();
      expect(screen.getByLabelText('状态: 自定义警告')).toBeInTheDocument();
    });

    test('应该显示默认状态文本', () => {
      render(<StatusIndicator status="danger" showText />);
      
      expect(screen.getByText('错误')).toBeInTheDocument();
    });
  });

  describe('状态类型测试', () => {
    const statusTypes: StatusType[] = [
      'success', 'warning', 'danger', 'info', 
      'pending', 'inactive', 'processing'
    ];

    const expectedLabels = {
      success: '成功',
      warning: '警告', 
      danger: '错误',
      info: '信息',
      pending: '等待中',
      inactive: '未激活',
      processing: '处理中',
    };

    statusTypes.forEach(status => {
      test(`应该正确渲染${status}状态`, () => {
        const { container } = render(
          <StatusIndicator status={status} showText />
        );
        
        expect(screen.getByText(expectedLabels[status])).toBeInTheDocument();
        
        const indicatorDot = container.querySelector('.ptm-indicator');
        expect(indicatorDot).toHaveClass(`ptm-${status}`);
      });
    });
  });

  describe('尺寸变体测试', () => {
    test('应该应用small尺寸样式', () => {
      const { container } = render(<StatusIndicator status="info" size="small" />);
      
      expect(container.firstChild).toHaveClass('ptm-small');
    });

    test('应该应用medium尺寸样式', () => {
      const { container } = render(<StatusIndicator status="info" size="medium" />);
      
      expect(container.firstChild).toHaveClass('ptm-medium');
    });

    test('应该应用large尺寸样式', () => {
      const { container } = render(<StatusIndicator status="info" size="large" />);
      
      expect(container.firstChild).toHaveClass('ptm-large');
    });
  });

  describe('文本位置测试', () => {
    test('应该支持右侧文本位置', () => {
      const { container } = render(
        <StatusIndicator 
          status="success" 
          showText 
          textPosition="right" 
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-text-right');
    });

    test('应该支持左侧文本位置', () => {
      const { container } = render(
        <StatusIndicator 
          status="success" 
          showText 
          textPosition="left" 
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-text-left');
    });

    test('应该支持顶部文本位置', () => {
      const { container } = render(
        <StatusIndicator 
          status="success" 
          showText 
          textPosition="top" 
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-text-top');
    });

    test('应该支持底部文本位置', () => {
      const { container } = render(
        <StatusIndicator 
          status="success" 
          showText 
          textPosition="bottom" 
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-text-bottom');
    });
  });

  describe('脉冲动画测试', () => {
    test('应该应用脉冲动画样式', () => {
      const { container } = render(
        <StatusIndicator status="processing" pulse />
      );
      
      const indicatorDot = container.querySelector('.ptm-indicator');
      expect(indicatorDot).toHaveClass('ptm-pulse');
    });

    test('不启用脉冲时不应该有脉冲样式', () => {
      const { container } = render(
        <StatusIndicator status="processing" pulse={false} />
      );
      
      const indicatorDot = container.querySelector('.ptm-indicator');
      expect(indicatorDot).not.toHaveClass('ptm-pulse');
    });
  });

  describe('图标功能测试', () => {
    test('应该显示自定义图标', () => {
      const customIcon = <span data-testid="custom-icon">✓</span>;
      
      render(
        <StatusIndicator status="success" icon={customIcon} />
      );
      
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    test('图标应该在指示器内部', () => {
      const customIcon = <span data-testid="custom-icon">⚠</span>;
      const { container } = render(
        <StatusIndicator status="warning" icon={customIcon} />
      );
      
      const iconContainer = container.querySelector('.ptm-icon-container');
      expect(iconContainer).toBeInTheDocument();
      expect(iconContainer).toContainElement(screen.getByTestId('custom-icon'));
    });
  });

  describe('点击功能测试', () => {
    test('可点击状态应该响应点击事件', () => {
      const handleClick = jest.fn();
      
      render(
        <StatusIndicator 
          status="info" 
          clickable 
          onClick={handleClick} 
        />
      );
      
      const indicator = screen.getByLabelText('状态: 信息');
      expect(indicator).toHaveClass('ptm-clickable');
      expect(indicator).toHaveAttribute('role', 'button');
      expect(indicator).toHaveAttribute('tabIndex', '0');
      
      fireEvent.click(indicator);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    test('可点击状态应该支持键盘操作', () => {
      const handleClick = jest.fn();
      
      render(
        <StatusIndicator 
          status="info" 
          clickable 
          onClick={handleClick} 
        />
      );
      
      const indicator = screen.getByLabelText('状态: 信息');
      
      // 测试Enter键
      fireEvent.keyDown(indicator, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      // 测试空格键
      fireEvent.keyDown(indicator, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
      
      // 测试其他键不应该触发
      fireEvent.keyDown(indicator, { key: 'a' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    test('不可点击状态不应该响应点击事件', () => {
      const handleClick = jest.fn();
      
      render(
        <StatusIndicator 
          status="info" 
          clickable={false} 
          onClick={handleClick} 
        />
      );
      
      const indicator = screen.getByLabelText('状态: 信息');
      expect(indicator).not.toHaveClass('ptm-clickable');
      expect(indicator).not.toHaveAttribute('role', 'button');
      
      fireEvent.click(indicator);
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('样式隔离测试', () => {
    test('应该正确应用样式隔离', () => {
      const { container } = render(
        <div className="ptm-app">
          <StatusIndicator status="success" />
        </div>
      );
      
      const statusIndicator = container.querySelector('.ptm-status-indicator');
      expect(statusIndicator).not.toBeNull();
      
      // 验证状态指示器在PTM应用容器中
      const ptmContainer = statusIndicator?.closest('.ptm-app');
      expect(ptmContainer).not.toBeNull();
    });

    test('应该支持自定义类名', () => {
      const { container } = render(
        <StatusIndicator 
          status="warning" 
          className="custom-status" 
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-status-indicator');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect((container.firstChild as HTMLElement).className).toContain('custom-status');
    });

    test('组合多个属性应该正确工作', () => {
      const { container } = render(
        <StatusIndicator 
          status="processing"
          size="large"
          pulse
          clickable
          showText
          textPosition="right"
          className="custom-class"
        />
      );
      
      const indicator = container.firstChild;
      expect(indicator).toHaveClass('ptm-status-indicator');
      expect(indicator).toHaveClass('ptm-large');
      expect(indicator).toHaveClass('ptm-text-right');
      expect(indicator).toHaveClass('ptm-clickable');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect((indicator as HTMLElement).className).toContain('custom-class');
      
      const indicatorDot = container.querySelector('.ptm-indicator');
      expect(indicatorDot).toHaveClass('ptm-processing');
      expect(indicatorDot).toHaveClass('ptm-pulse');
      
      expect(screen.getByText('处理中')).toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    test('应该设置正确的ARIA标签', () => {
      render(<StatusIndicator status="success" text="操作成功" />);
      
      const indicator = screen.getByLabelText('状态: 操作成功');
      expect(indicator).toBeInTheDocument();
    });

    test('应该设置正确的title属性', () => {
      render(<StatusIndicator status="danger" />);
      
      const indicator = screen.getByLabelText('状态: 错误');
      expect(indicator).toHaveAttribute('title', '发生错误或失败');
    });

    test('可点击状态应该有正确的角色和tabIndex', () => {
      render(<StatusIndicator status="info" clickable />);
      
      const indicator = screen.getByLabelText('状态: 信息');
      expect(indicator).toHaveAttribute('role', 'button');
      expect(indicator).toHaveAttribute('tabIndex', '0');
    });

    test('不可点击状态不应该有button角色', () => {
      render(<StatusIndicator status="info" />);
      
      const indicator = screen.getByLabelText('状态: 信息');
      expect(indicator).not.toHaveAttribute('role');
      expect(indicator).not.toHaveAttribute('tabIndex');
    });

    test('应该支持data-testid属性', () => {
      render(
        <StatusIndicator 
          status="success" 
          data-testid="test-status" 
        />
      );
      
      expect(screen.getByTestId('test-status')).toBeInTheDocument();
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空文本', () => {
      render(<StatusIndicator status="info" text="" showText />);
      
      // 应该显示默认标签
      expect(screen.getByText('信息')).toBeInTheDocument();
    });

    test('应该处理长文本', () => {
      const longText = '这是一个非常长的状态描述文本，用于测试组件如何处理长文本内容';
      
      render(<StatusIndicator status="warning" text={longText} showText />);
      
      expect(screen.getByText(longText)).toBeInTheDocument();
    });

    test('应该正确处理组件卸载', () => {
      const { unmount } = render(<StatusIndicator status="success" pulse />);
      
      // 应该能够正常卸载而不报错
      expect(() => unmount()).not.toThrow();
    });

    test('应该处理所有状态类型的组合', () => {
      const statusTypes: StatusType[] = [
        'success', 'warning', 'danger', 'info', 
        'pending', 'inactive', 'processing'
      ];
      
      statusTypes.forEach(status => {
        const { unmount } = render(
          <StatusIndicator 
            status={status}
            size="large"
            pulse
            showText
            clickable
          />
        );
        
        expect(screen.getByRole('button')).toBeInTheDocument();
        unmount();
      });
    });
  });
});