/**
 * Avatar组件测试
 * 验证头像功能、样式隔离和用户交互
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Avatar } from '../Avatar';

// 模拟CSS Modules
jest.mock('../Avatar.module.css', () => ({
  avatar: 'ptm-avatar',
  small: 'ptm-small',
  medium: 'ptm-medium',
  large: 'ptm-large',
  xlarge: 'ptm-xlarge',
  circle: 'ptm-circle',
  square: 'ptm-square',
  clickable: 'ptm-clickable',
  loading: 'ptm-loading',
  image: 'ptm-image',
  text: 'ptm-text',
  statusIndicator: 'ptm-status-indicator',
  online: 'ptm-online',
  offline: 'ptm-offline',
  away: 'ptm-away',
  busy: 'ptm-busy',
  loadingOverlay: 'ptm-loading-overlay',
  spinner: 'ptm-spinner',
}));

describe('Avatar组件测试', () => {
  beforeEach(() => {
    // 创建PTM应用容器
    const container = document.createElement('div');
    container.className = 'ptm-app';
    document.body.appendChild(container);
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('基础功能测试', () => {
    test('应该正确渲染默认头像', () => {
      render(<Avatar />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveClass('ptm-avatar');
      expect(avatar).toHaveClass('ptm-medium');
      expect(avatar).toHaveClass('ptm-circle');
    });

    test('应该显示用户名称的首字母', () => {
      render(<Avatar name="张三" />);
      
      expect(screen.getByText('张三')).toBeInTheDocument();
      expect(screen.getByLabelText('张三的头像')).toBeInTheDocument();
    });

    test('应该处理英文名称的首字母', () => {
      render(<Avatar name="John Doe" />);
      
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    test('应该处理单个词的名称', () => {
      render(<Avatar name="Admin" />);
      
      expect(screen.getByText('AD')).toBeInTheDocument();
    });

    test('应该处理空名称', () => {
      render(<Avatar name="" />);
      
      expect(screen.getByText('?')).toBeInTheDocument();
    });
  });

  describe('图片头像测试', () => {
    test('应该显示图片头像', () => {
      render(<Avatar name="张三" src="https://example.com/avatar.jpg" />);
      
      const image = screen.getByAltText('张三');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', 'https://example.com/avatar.jpg');
      expect(image).toHaveClass('ptm-image');
    });

    test('图片加载失败时应该显示文字头像', async () => {
      render(<Avatar name="张三" src="invalid-url" />);
      
      const image = screen.getByAltText('张三');
      
      // 模拟图片加载失败
      fireEvent.error(image);
      
      await waitFor(() => {
        expect(screen.getByText('张三')).toBeInTheDocument();
      });
    });

    test('应该设置图片懒加载', () => {
      render(<Avatar src="https://example.com/avatar.jpg" />);
      
      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('loading', 'lazy');
    });
  });

  describe('尺寸变体测试', () => {
    test('应该应用small尺寸样式', () => {
      render(<Avatar size="small" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-small');
    });

    test('应该应用medium尺寸样式', () => {
      render(<Avatar size="medium" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-medium');
    });

    test('应该应用large尺寸样式', () => {
      render(<Avatar size="large" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-large');
    });

    test('应该应用xlarge尺寸样式', () => {
      render(<Avatar size="xlarge" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-xlarge');
    });
  });

  describe('形状变体测试', () => {
    test('应该应用circle形状样式', () => {
      render(<Avatar shape="circle" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-circle');
    });

    test('应该应用square形状样式', () => {
      render(<Avatar shape="square" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-square');
    });
  });

  describe('点击功能测试', () => {
    test('可点击头像应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Avatar clickable onClick={handleClick} />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-clickable');
      expect(avatar).toHaveAttribute('role', 'button');
      expect(avatar).toHaveAttribute('tabIndex', '0');
      
      fireEvent.click(avatar);
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    test('可点击头像应该支持键盘操作', () => {
      const handleClick = jest.fn();
      render(<Avatar clickable onClick={handleClick} />);
      
      const avatar = screen.getByLabelText('用户头像');
      
      // 测试Enter键
      fireEvent.keyDown(avatar, { key: 'Enter' });
      expect(handleClick).toHaveBeenCalledTimes(1);
      
      // 测试空格键
      fireEvent.keyDown(avatar, { key: ' ' });
      expect(handleClick).toHaveBeenCalledTimes(2);
      
      // 测试其他键不应该触发
      fireEvent.keyDown(avatar, { key: 'a' });
      expect(handleClick).toHaveBeenCalledTimes(2);
    });

    test('不可点击头像不应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Avatar clickable={false} onClick={handleClick} />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).not.toHaveClass('ptm-clickable');
      expect(avatar).not.toHaveAttribute('role', 'button');
      
      fireEvent.click(avatar);
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('状态指示器测试', () => {
    test('应该显示在线状态', () => {
      render(<Avatar showStatus status="online" />);
      
      const statusIndicator = screen.getByLabelText('状态: online');
      expect(statusIndicator).toBeInTheDocument();
      expect(statusIndicator).toHaveClass('ptm-status-indicator');
      expect(statusIndicator).toHaveClass('ptm-online');
    });

    test('应该显示离线状态', () => {
      render(<Avatar showStatus status="offline" />);
      
      const statusIndicator = screen.getByLabelText('状态: offline');
      expect(statusIndicator).toHaveClass('ptm-offline');
    });

    test('应该显示离开状态', () => {
      render(<Avatar showStatus status="away" />);
      
      const statusIndicator = screen.getByLabelText('状态: away');
      expect(statusIndicator).toHaveClass('ptm-away');
    });

    test('应该显示忙碌状态', () => {
      render(<Avatar showStatus status="busy" />);
      
      const statusIndicator = screen.getByLabelText('状态: busy');
      expect(statusIndicator).toHaveClass('ptm-busy');
    });

    test('不显示状态时不应该有状态指示器', () => {
      render(<Avatar showStatus={false} />);
      
      expect(screen.queryByLabelText(/状态:/)).not.toBeInTheDocument();
    });
  });

  describe('自定义样式测试', () => {
    test('应该支持自定义背景颜色', () => {
      render(<Avatar backgroundColor="#ff0000" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveStyle({ backgroundColor: '#ff0000' });
    });

    test('应该支持自定义文字颜色', () => {
      render(<Avatar name="Test" textColor="#00ff00" />);
      
      const text = screen.getByText('TE');
      expect(text).toHaveStyle({ color: '#00ff00' });
    });

    test('应该根据名称生成不同的背景颜色', () => {
      const { rerender } = render(<Avatar name="Alice" />);
      const avatar1 = screen.getByLabelText('Alice的头像');
      const style1 = window.getComputedStyle(avatar1);
      
      rerender(<Avatar name="Bob" />);
      const avatar2 = screen.getByLabelText('Bob的头像');
      const style2 = window.getComputedStyle(avatar2);
      
      // 不同名称应该生成不同的背景颜色
      expect(style1.backgroundColor).not.toBe(style2.backgroundColor);
    });
  });

  describe('样式隔离测试', () => {
    test('应该正确应用样式隔离', () => {
      const { container } = render(
        <div className="ptm-app">
          <Avatar name="Test" />
        </div>
      );
      
      const avatar = container.querySelector('.ptm-avatar');
      expect(avatar).not.toBeNull();
      
      // 验证头像在PTM应用容器中
      const ptmContainer = avatar?.closest('.ptm-app');
      expect(ptmContainer).not.toBeNull();
    });

    test('应该支持自定义类名', () => {
      render(<Avatar className="custom-avatar" />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveClass('ptm-avatar');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect(avatar.className).toContain('custom-avatar');
    });

    test('组合多个属性应该正确工作', () => {
      render(
        <Avatar 
          name="Test User"
          size="large"
          shape="square"
          clickable
          showStatus
          status="online"
          className="custom-class"
        />
      );
      
      const avatar = screen.getByLabelText('Test User的头像');
      expect(avatar).toHaveClass('ptm-avatar');
      expect(avatar).toHaveClass('ptm-large');
      expect(avatar).toHaveClass('ptm-square');
      expect(avatar).toHaveClass('ptm-clickable');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect(avatar.className).toContain('custom-class');
      
      expect(screen.getByLabelText('状态: online')).toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    test('应该设置正确的ARIA标签', () => {
      render(<Avatar name="张三" />);
      
      const avatar = screen.getByLabelText('张三的头像');
      expect(avatar).toBeInTheDocument();
    });

    test('应该支持data-testid属性', () => {
      render(<Avatar data-testid="test-avatar" />);
      
      expect(screen.getByTestId('test-avatar')).toBeInTheDocument();
    });

    test('可点击头像应该有正确的角色和tabIndex', () => {
      render(<Avatar clickable />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).toHaveAttribute('role', 'button');
      expect(avatar).toHaveAttribute('tabIndex', '0');
    });

    test('不可点击头像不应该有button角色', () => {
      render(<Avatar />);
      
      const avatar = screen.getByLabelText('用户头像');
      expect(avatar).not.toHaveAttribute('role');
      expect(avatar).not.toHaveAttribute('tabIndex');
    });
  });

  describe('边界情况测试', () => {
    test('应该处理非常长的名称', () => {
      render(<Avatar name="这是一个非常非常长的用户名称测试" />);
      
      // 应该只显示前两个字符
      expect(screen.getByText('这是')).toBeInTheDocument();
    });

    test('应该处理特殊字符的名称', () => {
      render(<Avatar name="@#$%^&*()" />);
      
      expect(screen.getByText('@#')).toBeInTheDocument();
    });

    test('应该处理数字名称', () => {
      render(<Avatar name="123456" />);
      
      expect(screen.getByText('12')).toBeInTheDocument();
    });

    test('应该正确处理组件卸载', () => {
      const { unmount } = render(<Avatar src="https://example.com/avatar.jpg" />);
      
      // 应该能够正常卸载而不报错
      expect(() => unmount()).not.toThrow();
    });

    test('应该处理图片加载成功', async () => {
      render(<Avatar src="https://example.com/avatar.jpg" />);
      
      const image = screen.getByAltText('用户头像');
      
      // 模拟图片加载成功
      fireEvent.load(image);
      
      // 不应该有错误
      expect(image).toBeInTheDocument();
    });
  });
});