/**
 * SearchBar组件测试
 * 验证搜索功能、样式隔离和用户交互
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchBar, SearchSuggestion } from '../SearchBar';

// 模拟CSS Modules
jest.mock('../SearchBar.module.css', () => ({
  container: 'ptm-search-container',
  inputWrapper: 'ptm-input-wrapper',
  focused: 'ptm-focused',
  disabled: 'ptm-disabled',
  searchIcon: 'ptm-search-icon',
  input: 'ptm-input',
  loading: 'ptm-loading',
  loadingIcon: 'ptm-loading-icon',
  spinner: 'ptm-spinner',
  clearButton: 'ptm-clear-button',
  suggestionList: 'ptm-suggestion-list',
  suggestionItem: 'ptm-suggestion-item',
  active: 'ptm-active',
  suggestionIcon: 'ptm-suggestion-icon',
  suggestionContent: 'ptm-suggestion-content',
  suggestionText: 'ptm-suggestion-text',
  suggestionCategory: 'ptm-suggestion-category',
  highlight: 'ptm-highlight',
}));

// 模拟定时器
jest.useFakeTimers();

describe('SearchBar组件测试', () => {
  const mockSuggestions: SearchSuggestion[] = [
    { id: '1', text: '项目管理', category: '项目' },
    { id: '2', text: '任务列表', category: '任务', icon: <span>📋</span> },
    { id: '3', text: '看板视图', category: '视图' },
  ];

  beforeEach(() => {
    // 创建PTM应用容器
    const container = document.createElement('div');
    container.className = 'ptm-app';
    document.body.appendChild(container);
  });

  afterEach(() => {
    document.body.innerHTML = '';
    jest.clearAllTimers();
  });

  describe('基础功能测试', () => {
    test('应该正确渲染搜索框', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="" onChange={handleChange} />);
      
      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByLabelText('搜索')).toBeInTheDocument();
    });

    test('应该显示占位符文本', () => {
      const handleChange = jest.fn();
      render(
        <SearchBar 
          value="" 
          onChange={handleChange} 
          placeholder="请输入搜索关键词" 
        />
      );
      
      expect(screen.getByPlaceholderText('请输入搜索关键词')).toBeInTheDocument();
    });

    test('应该响应输入变化', async () => {
      const handleChange = jest.fn();
      
      render(<SearchBar value="" onChange={handleChange} />);
      
      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '测试' } });
      
      expect(handleChange).toHaveBeenCalledWith('测试');
    });

    test('应该支持受控组件模式', () => {
      const handleChange = jest.fn();
      const { rerender } = render(
        <SearchBar value="初始值" onChange={handleChange} />
      );
      
      expect(screen.getByDisplayValue('初始值')).toBeInTheDocument();
      
      rerender(<SearchBar value="新值" onChange={handleChange} />);
      expect(screen.getByDisplayValue('新值')).toBeInTheDocument();
    });
  });

  describe('搜索功能测试', () => {
    test('应该在回车时触发搜索', () => {
      const handleSearch = jest.fn();
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="搜索内容" 
          onChange={handleChange} 
          onSearch={handleSearch}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Enter' });
      
      expect(handleSearch).toHaveBeenCalledWith('搜索内容');
    });

    test('应该支持防抖搜索', async () => {
      const handleSearch = jest.fn();
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="" 
          onChange={handleChange} 
          onSearch={handleSearch}
          debounceMs={300}
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // 快速输入多个字符
      fireEvent.change(input, { target: { value: '测' } });
      fireEvent.change(input, { target: { value: '测试' } });
      fireEvent.change(input, { target: { value: '测试内容' } });
      
      // 在防抖时间内不应该触发搜索
      expect(handleSearch).not.toHaveBeenCalled();
      
      // 等待防抖时间
      act(() => {
        jest.advanceTimersByTime(300);
      });
      
      // 应该只触发一次搜索，使用最后的值
      expect(handleSearch).toHaveBeenCalledTimes(1);
      expect(handleSearch).toHaveBeenCalledWith('测试内容');
    });
  });

  describe('清除功能测试', () => {
    test('有内容时应该显示清除按钮', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="有内容" onChange={handleChange} />);
      
      expect(screen.getByLabelText('清除搜索')).toBeInTheDocument();
    });

    test('无内容时不应该显示清除按钮', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="" onChange={handleChange} />);
      
      expect(screen.queryByLabelText('清除搜索')).not.toBeInTheDocument();
    });

    test('点击清除按钮应该清空内容', () => {
      const handleChange = jest.fn();
      const handleClear = jest.fn();
      
      render(
        <SearchBar 
          value="要清除的内容" 
          onChange={handleChange} 
          onClear={handleClear}
        />
      );
      
      const clearButton = screen.getByLabelText('清除搜索');
      fireEvent.click(clearButton);
      
      expect(handleChange).toHaveBeenCalledWith('');
      expect(handleClear).toHaveBeenCalled();
    });

    test('加载状态时不应该显示清除按钮', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="内容" onChange={handleChange} loading />);
      
      expect(screen.queryByLabelText('清除搜索')).not.toBeInTheDocument();
    });
  });

  describe('加载状态测试', () => {
    test('加载状态应该显示加载指示器', () => {
      const handleChange = jest.fn();
      const { container } = render(<SearchBar value="" onChange={handleChange} loading />);
      
      expect(container.querySelector('.ptm-spinner')).toBeInTheDocument();
    });

    test('加载状态应该隐藏清除按钮', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="内容" onChange={handleChange} loading />);
      
      expect(screen.queryByLabelText('清除搜索')).not.toBeInTheDocument();
    });
  });

  describe('禁用状态测试', () => {
    test('禁用状态应该禁用输入框', () => {
      const handleChange = jest.fn();
      render(<SearchBar value="" onChange={handleChange} disabled />);
      
      expect(screen.getByRole('textbox')).toBeDisabled();
    });

    test('禁用状态应该应用禁用样式', () => {
      const handleChange = jest.fn();
      const { container } = render(
        <SearchBar value="" onChange={handleChange} disabled />
      );
      
      expect(container.firstChild).toHaveClass('ptm-disabled');
    });
  });

  describe('建议功能测试', () => {
    test('有建议时应该显示建议列表', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      expect(screen.getByRole('listbox')).toBeInTheDocument();
      expect(screen.getByText('管理')).toBeInTheDocument(); // 高亮后的部分文本
      expect(screen.getByText('任务列表')).toBeInTheDocument();
    });

    test('应该支持键盘导航建议', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      // 向下箭头选择第一个建议
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      expect(screen.getByText('管理').closest('li')).toHaveClass('ptm-active');
      
      // 向下箭头选择第二个建议
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      expect(screen.getByText('任务列表').closest('li')).toHaveClass('ptm-active');
      
      // 向上箭头回到第一个建议
      fireEvent.keyDown(input, { key: 'ArrowUp' });
      expect(screen.getByText('管理').closest('li')).toHaveClass('ptm-active');
    });

    test('应该支持回车选择建议', () => {
      const handleChange = jest.fn();
      const handleSuggestionSelect = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
          onSuggestionSelect={handleSuggestionSelect}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      fireEvent.keyDown(input, { key: 'Enter' });
      
      expect(handleChange).toHaveBeenCalledWith('项目管理');
      expect(handleSuggestionSelect).toHaveBeenCalledWith(mockSuggestions[0]);
    });

    test('应该支持点击选择建议', () => {
      const handleChange = jest.fn();
      const handleSuggestionSelect = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
          onSuggestionSelect={handleSuggestionSelect}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      const suggestion = screen.getByText('任务列表');
      fireEvent.click(suggestion);
      
      expect(handleChange).toHaveBeenCalledWith('任务列表');
      expect(handleSuggestionSelect).toHaveBeenCalledWith(mockSuggestions[1]);
    });

    test('应该支持ESC键关闭建议列表', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      expect(screen.getByRole('listbox')).toBeInTheDocument();
      
      fireEvent.keyDown(input, { key: 'Escape' });
      
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });

  describe('高亮功能测试', () => {
    test('应该高亮匹配的文本', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
          highlightQuery={true}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      // 查找高亮的mark元素
      const highlightedElements = screen.getAllByText('项目');
      const hasHighlight = highlightedElements.some(el => el.tagName === 'MARK');
      expect(hasHighlight).toBe(true);
    });

    test('应该支持禁用高亮功能', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
          highlightQuery={false}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      // 不应该有mark元素
      const highlightedElements = screen.getAllByText('项目');
      const hasHighlight = highlightedElements.some(el => el.tagName === 'MARK');
      expect(hasHighlight).toBe(false);
    });
  });

  describe('样式隔离测试', () => {
    test('应该正确应用样式隔离', () => {
      const handleChange = jest.fn();
      const { container } = render(
        <div className="ptm-app">
          <SearchBar value="" onChange={handleChange} />
        </div>
      );
      
      const searchContainer = container.querySelector('.ptm-search-container');
      expect(searchContainer).not.toBeNull();
      
      // 验证搜索框在PTM应用容器中
      const ptmContainer = searchContainer?.closest('.ptm-app');
      expect(ptmContainer).not.toBeNull();
    });

    test('应该支持自定义类名', () => {
      const handleChange = jest.fn();
      const { container } = render(
        <SearchBar 
          value="" 
          onChange={handleChange} 
          className="custom-search"
        />
      );
      
      expect(container.firstChild).toHaveClass('ptm-search-container');
      // 由于classNames函数会处理自定义类名，检查类名是否包含
      expect((container.firstChild as HTMLElement)?.className).toContain('custom-search');
    });

    test('焦点状态应该应用正确样式', () => {
      const handleChange = jest.fn();
      const { container } = render(
        <SearchBar value="" onChange={handleChange} />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      expect(container.firstChild).toHaveClass('ptm-focused');
    });
  });

  describe('可访问性测试', () => {
    test('应该设置正确的ARIA属性', () => {
      const handleChange = jest.fn();
      render(
        <SearchBar 
          value="" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
        />
      );
      
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-label', '搜索');
      expect(input).toHaveAttribute('aria-expanded', 'false');
      expect(input).toHaveAttribute('aria-haspopup', 'listbox');
    });

    test('应该支持data-testid属性', () => {
      const handleChange = jest.fn();
      render(
        <SearchBar 
          value="" 
          onChange={handleChange} 
          data-testid="test-search"
        />
      );
      
      expect(screen.getByTestId('test-search')).toBeInTheDocument();
    });

    test('建议列表应该有正确的角色和标签', () => {
      const handleChange = jest.fn();
      
      render(
        <SearchBar 
          value="项目" 
          onChange={handleChange} 
          suggestions={mockSuggestions}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.focus(input);
      
      const listbox = screen.getByRole('listbox');
      expect(listbox).toHaveAttribute('aria-label', '搜索建议');
      
      const options = screen.getAllByRole('option');
      expect(options).toHaveLength(mockSuggestions.length);
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空的建议列表', () => {
      const handleChange = jest.fn();
      render(
        <SearchBar 
          value="搜索" 
          onChange={handleChange} 
          suggestions={[]}
        />
      );
      
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });

    test('应该处理特殊字符的搜索', () => {
      const handleChange = jest.fn();
      const handleSearch = jest.fn();
      
      render(
        <SearchBar 
          value=".*+?^$" 
          onChange={handleChange} 
          onSearch={handleSearch}
        />
      );
      
      const input = screen.getByRole('textbox');
      fireEvent.keyDown(input, { key: 'Enter' });
      
      expect(handleSearch).toHaveBeenCalledWith('.*+?^$');
    });

    test('应该正确处理组件卸载', () => {
      const handleChange = jest.fn();
      const { unmount } = render(
        <SearchBar value="" onChange={handleChange} />
      );
      
      // 应该能够正常卸载而不报错
      expect(() => unmount()).not.toThrow();
    });
  });
});