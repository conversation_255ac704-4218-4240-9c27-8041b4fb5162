/**
 * Button组件测试
 * 验证样式隔离和组件功能
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';
import { checkStyleIsolation } from '../../../utils/styles';

// 模拟CSS Modules
jest.mock('../Button.module.css', () => ({
  button: 'ptm-button',
  primary: 'ptm-primary',
  secondary: 'ptm-secondary',
  ghost: 'ptm-ghost',
  danger: 'ptm-danger',
  small: 'ptm-small',
  large: 'ptm-large',
  iconOnly: 'ptm-icon-only',
  loading: 'ptm-loading',
  icon: 'ptm-icon',
}));

describe('Button组件测试', () => {
  beforeEach(() => {
    // 创建PTM应用容器
    const container = document.createElement('div');
    container.className = 'ptm-app';
    document.body.appendChild(container);
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('基础功能测试', () => {
    test('应该正确渲染按钮文本', () => {
      render(<Button>点击我</Button>);
      expect(screen.getByRole('button')).toHaveTextContent('点击我');
    });

    test('应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>点击我</Button>);
      
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    test('禁用状态下不应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Button disabled onClick={handleClick}>点击我</Button>);
      
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).not.toHaveBeenCalled();
    });

    test('加载状态下不应该响应点击事件', () => {
      const handleClick = jest.fn();
      render(<Button loading onClick={handleClick}>点击我</Button>);
      
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe('样式变体测试', () => {
    test('应该应用primary变体样式', () => {
      render(<Button variant="primary">Primary</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-primary');
    });

    test('应该应用secondary变体样式', () => {
      render(<Button variant="secondary">Secondary</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-secondary');
    });

    test('应该应用ghost变体样式', () => {
      render(<Button variant="ghost">Ghost</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-ghost');
    });

    test('应该应用danger变体样式', () => {
      render(<Button variant="danger">Danger</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-danger');
    });
  });

  describe('尺寸变体测试', () => {
    test('应该应用small尺寸样式', () => {
      render(<Button size="small">Small</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-small');
    });

    test('应该应用large尺寸样式', () => {
      render(<Button size="large">Large</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-large');
    });

    test('medium尺寸不应该添加额外类名', () => {
      render(<Button size="medium">Medium</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).not.toHaveClass('ptm-medium');
    });
  });

  describe('特殊状态测试', () => {
    test('应该应用iconOnly样式', () => {
      render(<Button iconOnly>🔍</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-icon-only');
    });

    test('应该应用loading样式', () => {
      render(<Button loading>Loading</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-loading');
      expect(button).toHaveAttribute('aria-busy', 'true');
    });

    test('应该正确设置disabled属性', () => {
      render(<Button disabled>Disabled</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('图标功能测试', () => {
    test('应该在左侧显示图标', () => {
      render(<Button icon={<span>🔍</span>}>搜索</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toContainHTML('<span class="ptm-icon"><span>🔍</span></span>');
      expect(button).toHaveTextContent('搜索');
    });

    test('应该在右侧显示图标', () => {
      render(<Button icon={<span>→</span>} iconPosition="right">下一步</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toContainHTML('<span class="ptm-icon"><span>→</span></span>');
      expect(button).toHaveTextContent('下一步');
    });

    test('iconOnly模式应该只显示图标', () => {
      render(<Button iconOnly icon={<span>🔍</span>}>搜索</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toContainHTML('<span>🔍</span>');
      expect(button).toHaveClass('ptm-icon-only');
    });

    test('iconOnly模式没有icon时应该显示children', () => {
      render(<Button iconOnly>🔍</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveTextContent('🔍');
      expect(button).toHaveClass('ptm-icon-only');
    });
  });

  describe('可访问性测试', () => {
    test('应该设置正确的button类型', () => {
      render(<Button type="submit">Submit</Button>);
      expect(screen.getByRole('button')).toHaveAttribute('type', 'submit');
    });

    test('应该支持data-testid属性', () => {
      render(<Button data-testid="test-button">Test</Button>);
      expect(screen.getByTestId('test-button')).toBeInTheDocument();
    });

    test('应该支持焦点事件', () => {
      const handleFocus = jest.fn();
      const handleBlur = jest.fn();
      
      render(
        <Button onFocus={handleFocus} onBlur={handleBlur}>
          Focus Test
        </Button>
      );
      
      const button = screen.getByRole('button');
      fireEvent.focus(button);
      expect(handleFocus).toHaveBeenCalledTimes(1);
      
      fireEvent.blur(button);
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('样式隔离测试', () => {
    test('应该正确应用样式隔离', () => {
      const { container } = render(
        <div className="ptm-app">
          <Button>Isolated Button</Button>
        </div>
      );
      const button = container.querySelector('button');
      
      expect(button).not.toBeNull();
      if (button) {
        // 验证按钮具有正确的PTM类名
        expect(button.className).toContain('ptm-');
        
        // 验证按钮在PTM应用容器中
        const ptmContainer = button.closest('.ptm-app');
        expect(ptmContainer).not.toBeNull();
      }
    });

    test('应该支持自定义类名', () => {
      render(<Button className="custom-class">Custom</Button>);
      const button = screen.getByRole('button');
      
      expect(button).toHaveClass('ptm-button');
      // 由于classNames函数会为自定义类名添加前缀，所以检查带前缀的类名
      expect(button.className).toContain('custom-class');
    });

    test('组合多个样式类应该正确工作', () => {
      render(
        <Button 
          variant="primary" 
          size="large" 
          iconOnly 
          className="custom-class"
        >
          Combined
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('ptm-button');
      expect(button).toHaveClass('ptm-primary');
      expect(button).toHaveClass('ptm-large');
      expect(button).toHaveClass('ptm-icon-only');
      // 检查自定义类名是否存在（可能带前缀）
      expect(button.className).toContain('custom-class');
    });
  });

  describe('边界情况测试', () => {
    test('应该处理空的children', () => {
      render(<Button>{null}</Button>);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    test('应该处理React元素作为children', () => {
      render(
        <Button>
          <span>Icon</span>
          <span>Text</span>
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toContainHTML('<span>Icon</span><span>Text</span>');
    });

    test('应该正确处理事件对象', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Event Test</Button>);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'click',
          target: button
        })
      );
    });
  });
});