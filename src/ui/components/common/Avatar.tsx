/**
 * Avatar组件
 * 用户头像显示组件，支持文字头像和图片头像，实现不同尺寸的头像变体
 * 使用CSS Modules确保样式隔离
 */

import React, { useState, useCallback } from 'react';
import { classNames } from '../../utils/styles';
import styles from './Avatar.module.css';

export interface AvatarProps {
  /** 用户名称，用于生成文字头像 */
  name?: string;
  /** 头像图片URL */
  src?: string;
  /** 头像尺寸 */
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  /** 头像形状 */
  shape?: 'circle' | 'square';
  /** 自定义背景颜色 */
  backgroundColor?: string;
  /** 自定义文字颜色 */
  textColor?: string;
  /** 点击事件处理 */
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  /** 是否可点击 */
  clickable?: boolean;
  /** 是否显示在线状态 */
  showStatus?: boolean;
  /** 在线状态 */
  status?: 'online' | 'offline' | 'away' | 'busy';
  /** 自定义类名 */
  className?: string;
  /** 测试ID */
  'data-testid'?: string;
}

/**
 * 生成用户名的首字母
 */
const getInitials = (name: string): string => {
  if (!name) return '';
  
  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    // 单个词，取前两个字符
    return words[0].substring(0, 2).toUpperCase();
  } else {
    // 多个词，取每个词的首字母，最多两个
    return words
      .slice(0, 2)
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase();
  }
};

/**
 * 根据名称生成背景颜色
 */
const generateBackgroundColor = (name: string): string => {
  if (!name) return '#6b7280'; // 默认灰色
  
  // 预定义的颜色数组
  const colors = [
    '#ef4444', // red
    '#f97316', // orange
    '#f59e0b', // amber
    '#eab308', // yellow
    '#84cc16', // lime
    '#22c55e', // green
    '#10b981', // emerald
    '#14b8a6', // teal
    '#06b6d4', // cyan
    '#0ea5e9', // sky
    '#3b82f6', // blue
    '#6366f1', // indigo
    '#8b5cf6', // violet
    '#a855f7', // purple
    '#d946ef', // fuchsia
    '#ec4899', // pink
    '#f43f5e', // rose
  ];
  
  // 根据名称计算哈希值
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // 使用哈希值选择颜色
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * Avatar组件 - 确保样式隔离的头像组件
 */
export const Avatar: React.FC<AvatarProps> = ({
  name = '',
  src,
  size = 'medium',
  shape = 'circle',
  backgroundColor,
  textColor = '#ffffff',
  onClick,
  clickable = false,
  showStatus = false,
  status = 'offline',
  className,
  'data-testid': testId,
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(!!src);

  // 处理图片加载错误
  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoading(false);
  }, []);

  // 处理图片加载成功
  const handleImageLoad = useCallback(() => {
    setImageLoading(false);
  }, []);

  // 处理点击事件
  const handleClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (clickable && onClick) {
      onClick(event);
    }
  }, [clickable, onClick]);

  // 生成头像内容
  const renderAvatarContent = () => {
    // 如果有图片且没有错误，显示图片
    if (src && !imageError) {
      return (
        <img
          src={src}
          alt={name || '用户头像'}
          className={styles.image}
          onError={handleImageError}
          onLoad={handleImageLoad}
          loading="lazy"
        />
      );
    }

    // 否则显示文字头像
    const initials = getInitials(name);
    return (
      <span className={styles.text} style={{ color: textColor }}>
        {initials || '?'}
      </span>
    );
  };

  // 生成容器类名
  const containerClassName = classNames(
    styles.avatar,
    styles[size],
    styles[shape],
    clickable ? styles.clickable : undefined,
    imageLoading ? styles.loading : undefined,
    className
  );

  // 生成背景颜色
  const finalBackgroundColor = backgroundColor || generateBackgroundColor(name);

  // 生成状态指示器
  const renderStatusIndicator = () => {
    if (!showStatus) return null;

    return (
      <div
        className={classNames(styles.statusIndicator, styles[status])}
        aria-label={`状态: ${status}`}
      />
    );
  };

  return (
    <div
      className={containerClassName}
      style={{ backgroundColor: finalBackgroundColor }}
      onClick={handleClick}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      aria-label={name ? `${name}的头像` : '用户头像'}
      data-testid={testId}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick(e as any);
        }
      } : undefined}
    >
      {renderAvatarContent()}
      {renderStatusIndicator()}
      
      {/* 加载指示器 */}
      {imageLoading && (
        <div className={styles.loadingOverlay}>
          <div className={styles.spinner} />
        </div>
      )}
    </div>
  );
};

// 导出默认组件
export default Avatar;