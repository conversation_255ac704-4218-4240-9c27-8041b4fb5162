/**
 * SearchBar组件
 * 搜索输入框组件，支持搜索图标、清除功能、搜索建议和高亮功能
 * 使用CSS Modules确保样式隔离
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { classNames } from '../../utils/styles';
import styles from './SearchBar.module.css';

export interface SearchSuggestion {
  id: string;
  text: string;
  category?: string;
  icon?: React.ReactNode;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  suggestions?: SearchSuggestion[];
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  showSuggestions?: boolean;
  highlightQuery?: boolean;
  debounceMs?: number;
  className?: string;
  'data-testid'?: string;
}

/**
 * SearchBar组件 - 确保样式隔离的搜索栏组件
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onSearch,
  onClear,
  placeholder = '搜索...',
  disabled = false,
  loading = false,
  suggestions = [],
  onSuggestionSelect,
  showSuggestions = true,
  highlightQuery = true,
  debounceMs = 300,
  className,
  'data-testid': testId,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestionList, setShowSuggestionList] = useState(false);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionListRef = useRef<HTMLUListElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // 使用清理Hook
  useSearchBarCleanup(debounceTimerRef);

  // 防抖处理搜索
  const debouncedSearch = useCallback((searchValue: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(() => {
      onSearch?.(searchValue);
    }, debounceMs);
  }, [onSearch, debounceMs]);

  // 处理输入变化
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    onChange(newValue);
    
    // 显示建议列表
    if (newValue.trim() && suggestions.length > 0 && showSuggestions) {
      setShowSuggestionList(true);
      setActiveSuggestionIndex(-1);
    } else {
      setShowSuggestionList(false);
    }
    
    // 防抖搜索
    debouncedSearch(newValue);
  };

  // 处理清除
  const handleClear = () => {
    onChange('');
    setShowSuggestionList(false);
    setActiveSuggestionIndex(-1);
    onClear?.();
    inputRef.current?.focus();
  };

  // 处理键盘事件
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestionList || suggestions.length === 0) {
      if (event.key === 'Enter') {
        onSearch?.(value);
      }
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setActiveSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setActiveSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        event.preventDefault();
        if (activeSuggestionIndex >= 0) {
          handleSuggestionSelect(suggestions[activeSuggestionIndex]);
        } else {
          onSearch?.(value);
        }
        break;
      case 'Escape':
        setShowSuggestionList(false);
        setActiveSuggestionIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // 处理建议选择
  const handleSuggestionSelect = (suggestion: SearchSuggestion) => {
    onChange(suggestion.text);
    setShowSuggestionList(false);
    setActiveSuggestionIndex(-1);
    onSuggestionSelect?.(suggestion);
    inputRef.current?.focus();
  };

  // 处理焦点
  const handleFocus = () => {
    setIsFocused(true);
    if (value.trim() && suggestions.length > 0 && showSuggestions) {
      setShowSuggestionList(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    // 延迟隐藏建议列表，允许点击建议项
    setTimeout(() => {
      setShowSuggestionList(false);
      setActiveSuggestionIndex(-1);
    }, 200);
  };

  // 高亮搜索关键词
  const highlightText = (text: string, query: string) => {
    if (!highlightQuery || !query.trim()) {
      return text;
    }

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className={styles.highlight}>
          {part}
        </mark>
      ) : part
    );
  };

  // 生成容器类名
  const containerClassName = classNames(
    styles.container,
    isFocused ? styles.focused : undefined,
    disabled ? styles.disabled : undefined,
    className
  );

  // 生成输入框类名
  const inputClassName = classNames(
    styles.input,
    loading ? styles.loading : undefined
  );

  return (
    <div className={containerClassName} data-testid={testId}>
      <div className={styles.inputWrapper}>
        {/* 搜索图标 */}
        <div className={styles.searchIcon}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="11" cy="11" r="8" />
            <path d="m21 21-4.35-4.35" />
          </svg>
        </div>

        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClassName}
          aria-label="搜索"
          aria-expanded={showSuggestionList}
          aria-haspopup="listbox"
          aria-activedescendant={
            activeSuggestionIndex >= 0 
              ? `suggestion-${activeSuggestionIndex}` 
              : undefined
          }
          autoComplete="off"
        />

        {/* 加载指示器 */}
        {loading && (
          <div className={styles.loadingIcon}>
            <div className={styles.spinner} />
          </div>
        )}

        {/* 清除按钮 */}
        {value && !loading && (
          <button
            type="button"
            className={styles.clearButton}
            onClick={handleClear}
            aria-label="清除搜索"
            tabIndex={-1}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18" />
              <line x1="6" y1="6" x2="18" y2="18" />
            </svg>
          </button>
        )}
      </div>

      {/* 建议列表 */}
      {showSuggestionList && suggestions.length > 0 && (
        <ul
          ref={suggestionListRef}
          className={styles.suggestionList}
          role="listbox"
          aria-label="搜索建议"
        >
          {suggestions.map((suggestion, index) => (
            <li
              key={suggestion.id}
              id={`suggestion-${index}`}
              className={classNames(
                styles.suggestionItem,
                index === activeSuggestionIndex ? styles.active : undefined
              )}
              role="option"
              aria-selected={index === activeSuggestionIndex}
              onClick={() => handleSuggestionSelect(suggestion)}
              onMouseEnter={() => setActiveSuggestionIndex(index)}
            >
              {suggestion.icon && (
                <span className={styles.suggestionIcon}>
                  {suggestion.icon}
                </span>
              )}
              <div className={styles.suggestionContent}>
                <div className={styles.suggestionText}>
                  {highlightText(suggestion.text, value)}
                </div>
                {suggestion.category && (
                  <div className={styles.suggestionCategory}>
                    {suggestion.category}
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

// 清理定时器的自定义Hook
export const useSearchBarCleanup = (debounceTimerRef: React.MutableRefObject<NodeJS.Timeout | undefined>) => {
  useEffect(() => {
    return () => {
      // 组件卸载时清理定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [debounceTimerRef]);
};

// 导出默认组件
export default SearchBar;