/**
 * SearchBar组件样式 - 使用CSS Modules确保样式隔离
 * 所有类名都会自动添加哈希后缀，避免与Obsidian主题冲突
 */

.container {
  position: relative;
  width: 100%;
  font-family: var(--ptm-font-family);
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--ptm-bg-primary);
  border: 1px solid var(--ptm-border-normal);
  border-radius: var(--ptm-radius-base);
  transition: all var(--ptm-duration-normal) var(--ptm-easing-smooth);
  overflow: hidden;
}

.inputWrapper:hover {
  border-color: var(--ptm-border-dark);
}

.focused .inputWrapper {
  border-color: var(--ptm-primary) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

.disabled .inputWrapper {
  background: var(--ptm-bg-tertiary) !important;
  border-color: var(--ptm-border-light) !important;
  cursor: not-allowed;
}

/* 搜索图标 */
.searchIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--ptm-spacing-3);
  color: var(--ptm-text-muted);
  flex-shrink: 0;
}

.disabled .searchIcon {
  color: var(--ptm-gray-400) !important;
}

/* 输入框 */
.input {
  flex: 1;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-2);
  border: none !important;
  background: transparent !important;
  color: var(--ptm-text-primary) !important;
  font-family: var(--ptm-font-family) !important;
  font-size: var(--ptm-text-sm) !important;
  line-height: var(--ptm-leading-normal) !important;
  outline: none !important;
}

.input::placeholder {
  color: var(--ptm-text-muted) !important;
}

.input:disabled {
  color: var(--ptm-gray-400) !important;
  cursor: not-allowed;
}

.input:disabled::placeholder {
  color: var(--ptm-gray-300) !important;
}

/* 加载指示器 */
.loadingIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--ptm-spacing-3);
  flex-shrink: 0;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--ptm-gray-200);
  border-top-color: var(--ptm-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 清除按钮 */
.clearButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--ptm-spacing-2);
  margin-right: var(--ptm-spacing-1);
  border: none !important;
  background: transparent !important;
  color: var(--ptm-text-muted) !important;
  border-radius: var(--ptm-radius-sm);
  cursor: pointer;
  transition: all var(--ptm-duration-fast) var(--ptm-easing-smooth);
  flex-shrink: 0;
}

.clearButton:hover {
  background: var(--ptm-bg-secondary) !important;
  color: var(--ptm-text-primary) !important;
}

.clearButton:focus {
  outline: 2px solid var(--ptm-primary) !important;
  outline-offset: 2px !important;
}

/* 建议列表 */
.suggestionList {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: var(--ptm-z-dropdown);
  background: var(--ptm-bg-primary) !important;
  border: 1px solid var(--ptm-border-normal) !important;
  border-top: none !important;
  border-radius: 0 0 var(--ptm-radius-base) var(--ptm-radius-base);
  box-shadow: var(--ptm-shadow-md);
  max-height: 300px;
  overflow-y: auto;
  list-style: none !important;
  margin: 0 !important;
  padding: var(--ptm-spacing-1) 0 !important;
}

/* 建议项 */
.suggestionItem {
  display: flex;
  align-items: center;
  padding: var(--ptm-spacing-3) var(--ptm-spacing-4);
  cursor: pointer;
  transition: background-color var(--ptm-duration-fast) var(--ptm-easing-smooth);
  border: none !important;
  margin: 0 !important;
}

.suggestionItem:hover,
.suggestionItem.active {
  background: var(--ptm-bg-secondary) !important;
}

.suggestionIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--ptm-spacing-3);
  color: var(--ptm-text-muted);
  flex-shrink: 0;
}

.suggestionContent {
  flex: 1;
  min-width: 0;
}

.suggestionText {
  color: var(--ptm-text-primary) !important;
  font-size: var(--ptm-text-sm) !important;
  font-family: var(--ptm-font-family) !important;
  line-height: var(--ptm-leading-normal) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.suggestionCategory {
  color: var(--ptm-text-muted) !important;
  font-size: var(--ptm-text-xs) !important;
  font-family: var(--ptm-font-family) !important;
  margin-top: var(--ptm-spacing-1) !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
}

/* 高亮文本 */
.highlight {
  background: rgba(102, 126, 234, 0.2) !important;
  color: var(--ptm-primary) !important;
  font-weight: var(--ptm-font-medium) !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input {
    font-size: var(--ptm-text-xs) !important;
  }
  
  .suggestionList {
    max-height: 200px;
  }
  
  .suggestionItem {
    padding: var(--ptm-spacing-2) var(--ptm-spacing-3);
  }
}

/* 滚动条样式 */
.suggestionList::-webkit-scrollbar {
  width: 4px;
}

.suggestionList::-webkit-scrollbar-track {
  background: var(--ptm-gray-100);
}

.suggestionList::-webkit-scrollbar-thumb {
  background: var(--ptm-gray-300);
  border-radius: var(--ptm-radius-sm);
}

.suggestionList::-webkit-scrollbar-thumb:hover {
  background: var(--ptm-gray-400);
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .inputWrapper,
  .clearButton,
  .suggestionItem,
  .spinner {
    transition: none !important;
    animation: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .inputWrapper {
    border-width: 2px !important;
  }
  
  .focused .inputWrapper {
    border-width: 3px !important;
  }
  
  .highlight {
    background: #ffff00 !important;
    color: #000000 !important;
  }
}