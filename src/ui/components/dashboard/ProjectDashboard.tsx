// Main project dashboard component

import React, { useState, useEffect } from 'react';
import { Container, Grid, Stack, Flex } from '../layout/Layout';
import { Card, CardHeader, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { ProjectOverviewCard } from './ProjectOverviewCard';
import { TaskList } from './TaskList';
import { Project, Task, TaskStatus, Priority } from '../../../models';
import { PTMManager } from '../../../services/PTMManager';

export interface ProjectDashboardProps {
	ptmManager: PTMManager;
	selectedProjectId?: string;
	onProjectSelect?: (projectId: string) => void;
	onTaskSelect?: (taskId: string) => void;
	onCreateProject?: () => void;
	onCreateTask?: () => void;
	app?: any; // Obsidian App instance
}

export const ProjectDashboard: React.FC<ProjectDashboardProps> = ({
	ptmManager,
	selectedProjectId,
	onProjectSelect,
	onTaskSelect,
	onCreateProject,
	onCreateTask,
	app
}) => {
	const [projects, setProjects] = useState<Project[]>([]);
	const [tasks, setTasks] = useState<Task[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Load data
	useEffect(() => {
		loadDashboardData();
	}, [ptmManager]);

	const loadDashboardData = async () => {
		try {
			setLoading(true);
			setError(null);

			const projectManager = ptmManager.getProjectManager();
			const taskManager = ptmManager.getTaskManager();

			// Load all projects
			const allProjects = await projectManager.getAllProjects();
			setProjects(allProjects);

			// Load all tasks
			const taskRepository = ptmManager.getTaskRepository();
			const allTasks = await taskRepository.findAll();
			setTasks(allTasks);

		} catch (err) {
			console.error('Error loading dashboard data:', err);
			setError('加载仪表板数据失败');
		} finally {
			setLoading(false);
		}
	};

	// Filter tasks for selected project or all tasks
	const getProjectTasks = (projectId?: string) => {
		if (!projectId) return tasks;
		return tasks.filter(task => task.projectId === projectId);
	};

	// Get critical tasks (high/critical priority, in progress or blocked)
	const getCriticalTasks = () => {
		return tasks.filter(task => 
			(task.priority === Priority.HIGH || task.priority === Priority.CRITICAL) &&
			(task.status === TaskStatus.IN_PROGRESS || task.status === TaskStatus.BLOCKED)
		).sort((a, b) => {
			// Sort by priority (critical first) then by due date
			const priorityOrder = { [Priority.CRITICAL]: 0, [Priority.HIGH]: 1 };
			const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] ?? 2;
			const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] ?? 2;
			
			if (aPriority !== bPriority) return aPriority - bPriority;
			
			if (a.dueDate && b.dueDate) {
				return a.dueDate.getTime() - b.dueDate.getTime();
			}
			return a.dueDate ? -1 : b.dueDate ? 1 : 0;
		});
	};

	// Get overdue and upcoming tasks
	const getUpcomingTasks = () => {
		const now = new Date();
		const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

		return tasks.filter(task => {
			if (!task.dueDate || task.status === TaskStatus.COMPLETED) return false;
			
			// Include overdue tasks and tasks due within next week
			return task.dueDate <= nextWeek;
		}).sort((a, b) => {
			// Sort by due date (overdue first)
			if (a.dueDate && b.dueDate) {
				return a.dueDate.getTime() - b.dueDate.getTime();
			}
			return a.dueDate ? -1 : b.dueDate ? 1 : 0;
		});
	};

	// Get recent tasks (recently created or updated)
	const getRecentTasks = () => {
		const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
		
		return tasks.filter(task => 
			task.createdAt >= threeDaysAgo || task.updatedAt >= threeDaysAgo
		).sort((a, b) => {
			const aTime = Math.max(a.createdAt.getTime(), a.updatedAt.getTime());
			const bTime = Math.max(b.createdAt.getTime(), b.updatedAt.getTime());
			return bTime - aTime; // Most recent first
		});
	};

	// Handle create project
	const handleCreateProject = async () => {
		if (onCreateProject) {
			onCreateProject();
		} else if (app) {
			// 直接创建项目创建模态框
			try {
				const { ProjectCreateModal } = await import('../common/ProjectCreateModal');
				const modal = new ProjectCreateModal(app, ptmManager);
				modal.open();
			} catch (error) {
				console.error('Error opening project creation modal:', error);
			}
		}
	};

	// Handle create task
	const handleCreateTask = async () => {
		if (onCreateTask) {
			onCreateTask();
		} else if (app) {
			// 直接创建任务创建模态框
			try {
				const { TaskCreateModal } = await import('../common/TaskCreateModal');
				const modal = new TaskCreateModal(app, ptmManager);
				modal.open();
			} catch (error) {
				console.error('Error opening task creation modal:', error);
			}
		}
	};

	// Get dashboard statistics
	const getDashboardStats = () => {
		const activeProjects = projects.filter(p => p.status === 'active').length;
		const totalTasks = tasks.length;
		const completedTasks = tasks.filter(t => t.status === TaskStatus.COMPLETED).length;
		const overdueTasks = tasks.filter(t => 
			t.dueDate && new Date() > t.dueDate && t.status !== TaskStatus.COMPLETED
		).length;

		return {
			activeProjects,
			totalTasks,
			completedTasks,
			overdueTasks,
			completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
		};
	};

	if (loading) {
		return (
			<Container padding>
				<div style={{ textAlign: 'center', padding: '2rem' }}>
					<div className="ptm-spinner">
						<svg viewBox="0 0 24 24" style={{ width: '2rem', height: '2rem' }}>
							<circle
								className="ptm-spinner__circle"
								cx="12"
								cy="12"
								r="10"
								fill="none"
								strokeWidth="2"
							/>
						</svg>
					</div>
					<p style={{ marginTop: '1rem', color: 'var(--text-muted)' }}>
						加载仪表板数据...
					</p>
				</div>
			</Container>
		);
	}

	if (error) {
		return (
			<Container padding>
				<Card variant="outlined">
					<CardContent>
						<div style={{ textAlign: 'center', padding: '2rem' }}>
							<p style={{ color: 'var(--text-error)', marginBottom: '1rem' }}>
								{error}
							</p>
							<Button onClick={loadDashboardData}>
								重新加载
							</Button>
						</div>
					</CardContent>
				</Card>
			</Container>
		);
	}

	const stats = getDashboardStats();
	const criticalTasks = getCriticalTasks();
	const upcomingTasks = getUpcomingTasks();
	const recentTasks = getRecentTasks();

	return (
		<Container padding>
			<Stack spacing="lg">
				{/* Dashboard Header */}
				<Flex justify="between" align="center">
					<div>
						<h1 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 600 }}>
							项目仪表板
						</h1>
						<p style={{ margin: '0.25rem 0 0 0', color: 'var(--text-muted)' }}>
							项目和任务概览
						</p>
					</div>
					<Flex gap="sm">
						<Button variant="secondary" onClick={handleCreateTask}>
							新建任务
						</Button>
						<Button variant="primary" onClick={handleCreateProject}>
							新建项目
						</Button>
					</Flex>
				</Flex>

				{/* Dashboard Statistics */}
				<Grid cols={4} gap="md" responsive>
					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '2rem', 
									fontWeight: 'bold', 
									color: 'var(--interactive-accent)' 
								}}>
									{stats.activeProjects}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									活跃项目
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '2rem', 
									fontWeight: 'bold', 
									color: 'var(--text-normal)' 
								}}>
									{stats.totalTasks}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									总任务数
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '2rem', 
									fontWeight: 'bold', 
									color: 'var(--text-success)' 
								}}>
									{Math.round(stats.completionRate)}%
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									完成率
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '2rem', 
									fontWeight: 'bold', 
									color: stats.overdueTasks > 0 ? 'var(--text-error)' : 'var(--text-success)' 
								}}>
									{stats.overdueTasks}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									逾期任务
								</div>
							</div>
						</CardContent>
					</Card>
				</Grid>

				{/* Project Overview Cards */}
				{projects.length > 0 && (
					<div>
						<h2 style={{ 
							fontSize: '1.25rem', 
							fontWeight: 600, 
							marginBottom: '1rem' 
						}}>
							项目概览
						</h2>
						<div style={{ 
							display: 'flex', 
							gap: 'var(--ptm-spacing-md)', 
							overflowX: 'auto',
							paddingBottom: '0.5rem'
						}}>
							{projects.slice(0, 6).map(project => (
								<ProjectOverviewCard
									key={project.id}
									project={project}
									tasks={getProjectTasks(project.id)}
									onViewProject={onProjectSelect}
									onEditProject={onProjectSelect}
								/>
							))}
						</div>
					</div>
				)}

				{/* Task Lists */}
				<Grid cols={3} gap="md" responsive>
					{/* Critical Tasks */}
					<TaskList
						title="关键任务"
						tasks={criticalTasks}
						maxItems={5}
						emptyMessage="暂无关键任务"
						onTaskClick={onTaskSelect}
						onViewAll={() => {/* Handle view all critical tasks */}}
					/>

					{/* Upcoming & Overdue Tasks */}
					<TaskList
						title="即将到期"
						tasks={upcomingTasks}
						maxItems={5}
						emptyMessage="暂无即将到期的任务"
						onTaskClick={onTaskSelect}
						onViewAll={() => {/* Handle view all upcoming tasks */}}
					/>

					{/* Recent Tasks */}
					<TaskList
						title="最近更新"
						tasks={recentTasks}
						maxItems={5}
						showDueDate={false}
						emptyMessage="暂无最近更新的任务"
						onTaskClick={onTaskSelect}
						onViewAll={() => {/* Handle view all recent tasks */}}
					/>
				</Grid>

				{/* Empty State */}
				{projects.length === 0 && (
					<Card variant="outlined">
						<CardContent>
							<div style={{ 
								textAlign: 'center', 
								padding: '3rem 1rem',
								color: 'var(--text-muted)'
							}}>
								<div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
								<h3 style={{ margin: '0 0 0.5rem 0' }}>欢迎使用项目任务管理器</h3>
								<p style={{ margin: '0 0 1.5rem 0' }}>
									开始创建您的第一个项目来管理任务
								</p>
								<Button variant="primary" onClick={handleCreateProject}>
									创建项目
								</Button>
							</div>
						</CardContent>
					</Card>
				)}
			</Stack>
		</Container>
	);
};