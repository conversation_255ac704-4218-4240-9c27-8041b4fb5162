// Task list component for dashboard

import React from 'react';
import { <PERSON>, <PERSON>Header, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { Stack, Flex } from '../layout/Layout';
import { Task, TaskStatus, Priority } from '../../../models';

export interface TaskListProps {
	title: string;
	tasks: Task[];
	maxItems?: number;
	showPriority?: boolean;
	showDueDate?: boolean;
	showStatus?: boolean;
	emptyMessage?: string;
	onTaskClick?: (taskId: string) => void;
	onViewAll?: () => void;
}

export const TaskList: React.FC<TaskListProps> = ({
	title,
	tasks,
	maxItems = 5,
	showPriority = true,
	showDueDate = true,
	showStatus = true,
	emptyMessage = '暂无任务',
	onTaskClick,
	onViewAll
}) => {
	const displayTasks = tasks.slice(0, maxItems);
	const hasMoreTasks = tasks.length > maxItems;

	const getStatusInfo = (status: TaskStatus) => {
		const statusMap = {
			[TaskStatus.TODO]: { color: 'var(--text-muted)', label: '待办', emoji: '📝' },
			[TaskStatus.IN_PROGRESS]: { color: 'var(--interactive-accent)', label: '进行中', emoji: '⏳' },
			[TaskStatus.BLOCKED]: { color: 'var(--text-warning)', label: '阻塞', emoji: '⏸️' },
			[TaskStatus.REVIEW]: { color: 'var(--text-accent)', label: '审核', emoji: '👁️' },
			[TaskStatus.COMPLETED]: { color: 'var(--text-success)', label: '完成', emoji: '✅' },
			[TaskStatus.CANCELLED]: { color: 'var(--text-error)', label: '取消', emoji: '❌' }
		};
		return statusMap[status] || { color: 'var(--text-muted)', label: '未知', emoji: '❓' };
	};

	const getPriorityInfo = (priority: Priority) => {
		const priorityMap = {
			[Priority.CRITICAL]: { color: 'var(--text-error)', label: '紧急', emoji: '🔥' },
			[Priority.HIGH]: { color: 'var(--text-warning)', label: '高', emoji: '🔼' },
			[Priority.MEDIUM]: { color: 'var(--text-normal)', label: '中', emoji: '' },
			[Priority.LOW]: { color: 'var(--text-muted)', label: '低', emoji: '🔽' }
		};
		return priorityMap[priority] || { color: 'var(--text-normal)', label: '中', emoji: '' };
	};

	const formatDueDate = (dueDate: Date) => {
		const now = new Date();
		const diffTime = dueDate.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays < 0) {
			return { text: `逾期 ${Math.abs(diffDays)} 天`, color: 'var(--text-error)' };
		} else if (diffDays === 0) {
			return { text: '今天到期', color: 'var(--text-warning)' };
		} else if (diffDays === 1) {
			return { text: '明天到期', color: 'var(--text-warning)' };
		} else if (diffDays <= 7) {
			return { text: `${diffDays} 天后到期`, color: 'var(--text-warning)' };
		} else {
			return { text: dueDate.toLocaleDateString(), color: 'var(--text-muted)' };
		}
	};

	return (
		<Card variant="default">
			<CardHeader>
				<Flex justify="between" align="center">
					<h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: 600 }}>
						{title}
					</h3>
					{hasMoreTasks && onViewAll && (
						<Button variant="ghost" size="small" onClick={onViewAll}>
							查看全部 ({tasks.length})
						</Button>
					)}
				</Flex>
			</CardHeader>

			<CardContent>
				{displayTasks.length === 0 ? (
					<div style={{ 
						padding: 'var(--ptm-spacing-lg)', 
						textAlign: 'center', 
						color: 'var(--text-muted)' 
					}}>
						{emptyMessage}
					</div>
				) : (
					<Stack spacing="none">
						{displayTasks.map((task, index) => {
							const statusInfo = getStatusInfo(task.status);
							const priorityInfo = getPriorityInfo(task.priority);
							const dueDateInfo = task.dueDate ? formatDueDate(task.dueDate) : null;

							return (
								<div
									key={task.id}
									className="ptm-task-list-item"
									onClick={() => onTaskClick?.(task.id)}
									style={{
										padding: 'var(--ptm-spacing-sm) var(--ptm-spacing-md)',
										borderBottom: index < displayTasks.length - 1 ? 
											'1px solid var(--background-modifier-border)' : 'none',
										cursor: onTaskClick ? 'pointer' : 'default'
									}}
								>
									<Stack spacing="sm">
										{/* Task title and status */}
										<Flex align="center" gap="sm">
											{showStatus && (
												<span 
													style={{ 
														fontSize: '1rem',
														lineHeight: 1
													}}
													title={statusInfo.label}
												>
													{statusInfo.emoji}
												</span>
											)}
											<span 
												style={{ 
													flex: 1,
													fontSize: '0.875rem',
													fontWeight: 500,
													color: 'var(--text-normal)',
													textDecoration: task.status === TaskStatus.COMPLETED ? 
														'line-through' : 'none',
													opacity: task.status === TaskStatus.COMPLETED ? 0.7 : 1
												}}
											>
												{task.title}
											</span>
											{showPriority && priorityInfo.emoji && (
												<span 
													style={{ 
														fontSize: '0.875rem',
														color: priorityInfo.color
													}}
													title={`优先级: ${priorityInfo.label}`}
												>
													{priorityInfo.emoji}
												</span>
											)}
										</Flex>

										{/* Task metadata */}
										<Flex align="center" gap="md" style={{ fontSize: '0.75rem' }}>
											{showStatus && (
												<span style={{ color: statusInfo.color }}>
													{statusInfo.label}
												</span>
											)}
											{showPriority && (
												<span style={{ color: priorityInfo.color }}>
													优先级: {priorityInfo.label}
												</span>
											)}
											{showDueDate && dueDateInfo && (
												<span style={{ color: dueDateInfo.color }}>
													📅 {dueDateInfo.text}
												</span>
											)}
										</Flex>

										{/* Task description (if exists and short) */}
										{task.description && task.description.length <= 100 && (
											<p style={{ 
												margin: 0,
												fontSize: '0.75rem',
												color: 'var(--text-muted)',
												lineHeight: 1.4
											}}>
												{task.description}
											</p>
										)}
									</Stack>
								</div>
							);
						})}
					</Stack>
				)}
			</CardContent>
		</Card>
	);
};