// Project overview card component for dashboard

import React from 'react';
import { <PERSON>, CardHeader, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { ProgressRing } from '../common/ProgressRing';
import { Flex, Stack } from '../layout/Layout';
import { Project, ProjectStatus, Task, TaskStatus } from '../../../models';

export interface ProjectOverviewCardProps {
	project: Project;
	tasks: Task[];
	onViewProject?: (projectId: string) => void;
	onEditProject?: (projectId: string) => void;
}

export const ProjectOverviewCard: React.FC<ProjectOverviewCardProps> = ({
	project,
	tasks,
	onViewProject,
	onEditProject
}) => {
	// Calculate project statistics
	const totalTasks = tasks.length;
	const completedTasks = tasks.filter(task => task.status === TaskStatus.COMPLETED).length;
	const inProgressTasks = tasks.filter(task => task.status === TaskStatus.IN_PROGRESS).length;
	const blockedTasks = tasks.filter(task => task.status === TaskStatus.BLOCKED).length;
	const overdueTasks = tasks.filter(task => 
		task.dueDate && 
		new Date() > task.dueDate && 
		task.status !== TaskStatus.COMPLETED
	).length;

	const completionPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

	// Get status color and label
	const getStatusInfo = (status: ProjectStatus) => {
		const statusMap = {
			[ProjectStatus.PLANNING]: { color: 'var(--text-muted)', label: '规划中' },
			[ProjectStatus.ACTIVE]: { color: 'var(--text-success)', label: '进行中' },
			[ProjectStatus.ON_HOLD]: { color: 'var(--text-warning)', label: '暂停' },
			[ProjectStatus.COMPLETED]: { color: 'var(--text-success)', label: '已完成' },
			[ProjectStatus.CANCELLED]: { color: 'var(--text-error)', label: '已取消' }
		};
		return statusMap[status] || { color: 'var(--text-muted)', label: '未知' };
	};

	const statusInfo = getStatusInfo(project.status);

	// Check if project is overdue
	const isOverdue = project.endDate && new Date() > project.endDate && 
		project.status !== ProjectStatus.COMPLETED;

	// Calculate days remaining
	const getDaysRemaining = () => {
		if (!project.endDate) return null;
		const now = new Date();
		const endDate = new Date(project.endDate);
		const diffTime = endDate.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		return diffDays;
	};

	const daysRemaining = getDaysRemaining();

	return (
		<Card variant="elevated" className="ptm-project-overview-card">
			<CardHeader>
				<Flex justify="between" align="start">
					<div>
						<h3 className="ptm-project-title">{project.name}</h3>
						{project.description && (
							<p className="ptm-project-description">{project.description}</p>
						)}
					</div>
					<div 
						className="ptm-project-status"
						style={{ color: statusInfo.color }}
					>
						{statusInfo.label}
					</div>
				</Flex>
			</CardHeader>

			<CardContent>
				<Stack spacing="md">
					{/* Progress Section */}
					<Flex align="center" gap="lg">
						<ProgressRing 
							progress={completionPercentage}
							size="md"
							label="完成"
						/>
						
						<div className="ptm-project-stats">
							<Stack spacing="sm">
								<div>
									<span className="ptm-stat-label">总任务:</span>
									<span className="ptm-stat-value">{totalTasks}</span>
								</div>
								<div>
									<span className="ptm-stat-label">已完成:</span>
									<span className="ptm-stat-value" style={{ color: 'var(--text-success)' }}>
										{completedTasks}
									</span>
								</div>
								{inProgressTasks > 0 && (
									<div>
										<span className="ptm-stat-label">进行中:</span>
										<span className="ptm-stat-value" style={{ color: 'var(--interactive-accent)' }}>
											{inProgressTasks}
										</span>
									</div>
								)}
								{blockedTasks > 0 && (
									<div>
										<span className="ptm-stat-label">阻塞:</span>
										<span className="ptm-stat-value" style={{ color: 'var(--text-warning)' }}>
											{blockedTasks}
										</span>
									</div>
								)}
							</Stack>
						</div>
					</Flex>

					{/* Timeline Section */}
					{(project.endDate || daysRemaining !== null) && (
						<div className="ptm-project-timeline">
							<Stack spacing="sm">
								{project.endDate && (
									<div>
										<span className="ptm-timeline-label">截止日期:</span>
										<span className="ptm-timeline-value">
											{project.endDate.toLocaleDateString()}
										</span>
									</div>
								)}
								{daysRemaining !== null && (
									<div>
										<span className="ptm-timeline-label">剩余天数:</span>
										<span 
											className="ptm-timeline-value"
											style={{ 
												color: isOverdue ? 'var(--text-error)' : 
													daysRemaining <= 7 ? 'var(--text-warning)' : 
													'var(--text-normal)'
											}}
										>
											{isOverdue ? `逾期 ${Math.abs(daysRemaining)} 天` : 
											 daysRemaining === 0 ? '今天到期' :
											 `${daysRemaining} 天`}
										</span>
									</div>
								)}
							</Stack>
						</div>
					)}

					{/* Alerts Section */}
					{(overdueTasks > 0 || isOverdue) && (
						<div className="ptm-project-alerts">
							{isOverdue && (
								<div className="ptm-alert ptm-alert--error">
									⚠️ 项目已逾期
								</div>
							)}
							{overdueTasks > 0 && (
								<div className="ptm-alert ptm-alert--warning">
									📅 {overdueTasks} 个任务已逾期
								</div>
							)}
						</div>
					)}

					{/* Tags */}
					{project.tags.length > 0 && (
						<div className="ptm-project-tags">
							{project.tags.map((tag: string) => (
								<span key={tag} className="ptm-tag">
									#{tag}
								</span>
							))}
						</div>
					)}

					{/* Actions */}
					<Flex gap="sm" justify="end">
						{onEditProject && (
							<Button 
								variant="ghost" 
								size="small"
								onClick={() => onEditProject(project.id)}
							>
								编辑
							</Button>
						)}
						{onViewProject && (
							<Button 
								variant="primary" 
								size="small"
								onClick={() => onViewProject(project.id)}
							>
								查看详情
							</Button>
						)}
					</Flex>
				</Stack>
			</CardContent>
		</Card>
	);
};