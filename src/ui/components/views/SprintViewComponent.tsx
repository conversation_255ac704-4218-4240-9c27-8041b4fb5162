import React, { useState, useEffect } from 'react';
import { Sprint } from '../../../models/Sprint';
import { Task } from '../../../models/Task';
import { Project } from '../../../models/Project';
import { SprintBoard } from '../sprint/SprintBoard';
import { SprintRetrospective } from '../sprint/SprintRetrospective';

interface SprintViewComponentProps {
	ptmManager?: any; // PTMManager instance
}

/**
 * Sprint 视图主组件
 */
export const SprintViewComponent: React.FC<SprintViewComponentProps> = ({ ptmManager }) => {
	const [projects, setProjects] = useState<Project[]>([]);
	const [selectedProjectId, setSelectedProjectId] = useState<string>('');
	const [sprints, setSprints] = useState<Sprint[]>([]);
	const [tasks, setTasks] = useState<Task[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string>('');
	const [showRetrospective, setShowRetrospective] = useState<Sprint | null>(null);

	// 从 PTMManager 加载数据
	useEffect(() => {
		loadData();
	}, [ptmManager, selectedProjectId]);

	const loadData = async () => {
		setIsLoading(true);
		try {
			if (!ptmManager) {
				setError('PTM 管理器未初始化');
				return;
			}

			// 从 PTMManager 获取真实数据
			const projectRepository = ptmManager.getProjectRepository();
			const sprintManager = ptmManager.getSprintManager();
			const taskRepository = ptmManager.getTaskRepository();
			
			const projectsData = await projectRepository.findAll();
			setProjects(projectsData);
			
			if (projectsData.length > 0 && !selectedProjectId) {
				setSelectedProjectId(projectsData[0].id);
			}
			
			if (selectedProjectId) {
				// 获取项目的 Sprint 数据
				const sprintsData = sprintManager.getProjectSprints(selectedProjectId);
				setSprints(sprintsData);
				
				// 获取项目的任务数据
				const tasksData = await taskRepository.findByProject(selectedProjectId);
				setTasks(tasksData);
			}
			
		} catch (err) {
			setError(err instanceof Error ? err.message : '加载数据失败');
		} finally {
			setIsLoading(false);
		}
	};

	const handleCreateSprint = async (name: string, projectId: string, startDate: Date, endDate: Date, goal?: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const newSprint = await sprintManager.createSprint(name, projectId, startDate, endDate, goal);
			
			setSprints(prev => [newSprint, ...prev]);
		} catch (error) {
			console.error('创建 Sprint 失败:', error);
			throw error;
		}
	};

	const handleUpdateSprint = async (sprintId: string, updates: Partial<Sprint>) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const updatedSprint = await sprintManager.updateSprint(sprintId, updates);
			
			setSprints(prev => prev.map(sprint => 
				sprint.id === sprintId ? updatedSprint : sprint
			));
		} catch (error) {
			console.error('更新 Sprint 失败:', error);
			throw error;
		}
	};

	const handleDeleteSprint = async (sprintId: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			await sprintManager.deleteSprint(sprintId);
			
			setSprints(prev => prev.filter(sprint => sprint.id !== sprintId));
		} catch (error) {
			console.error('删除 Sprint 失败:', error);
			throw error;
		}
	};

	const handleStartSprint = async (sprintId: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const updatedSprint = await sprintManager.startSprint(sprintId);
			
			setSprints(prev => prev.map(sprint => 
				sprint.id === sprintId ? updatedSprint : sprint
			));
		} catch (error) {
			console.error('开始 Sprint 失败:', error);
			throw error;
		}
	};

	const handleCompleteSprint = async (sprintId: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const taskRepository = ptmManager.getTaskRepository();
			
			// 获取 Sprint 相关的任务
			const sprint = sprints.find(s => s.id === sprintId);
			if (!sprint) {
				throw new Error('Sprint 不存在');
			}
			
			const sprintTasks = await taskRepository.findByIds(sprint.taskIds);
			const updatedSprint = await sprintManager.completeSprint(sprintId, sprintTasks);
			
			setSprints(prev => prev.map(s => 
				s.id === sprintId ? updatedSprint : s
			));
			
			// 显示回顾界面
			setShowRetrospective(updatedSprint);
		} catch (error) {
			console.error('完成 Sprint 失败:', error);
			throw error;
		}
	};

	const handleAddTaskToSprint = async (sprintId: string, taskId: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const updatedSprint = await sprintManager.addTaskToSprint(sprintId, taskId);
			
			setSprints(prev => prev.map(sprint => 
				sprint.id === sprintId ? updatedSprint : sprint
			));
		} catch (error) {
			console.error('添加任务到 Sprint 失败:', error);
			throw error;
		}
	};

	const handleRemoveTaskFromSprint = async (sprintId: string, taskId: string) => {
		try {
			if (!ptmManager) {
				throw new Error('PTM 管理器未初始化');
			}

			const sprintManager = ptmManager.getSprintManager();
			const updatedSprint = await sprintManager.removeTaskFromSprint(sprintId, taskId);
			
			setSprints(prev => prev.map(sprint => 
				sprint.id === sprintId ? updatedSprint : sprint
			));
		} catch (error) {
			console.error('从 Sprint 移除任务失败:', error);
			throw error;
		}
	};

	const handleSaveRetrospective = async (sprintId: string, retrospective: any) => {
		try {
			// 这里应该保存回顾数据
			console.log('保存 Sprint 回顾:', sprintId, retrospective);
		} catch (error) {
			console.error('保存 Sprint 回顾失败:', error);
			throw error;
		}
	};

	if (isLoading) {
		return (
			<div className="ptm-loading">
				<div className="loading-spinner"></div>
				<p>加载中...</p>
			</div>
		);
	}

	if (error) {
		return (
			<div className="ptm-error">
				<h3>加载失败</h3>
				<p>{error}</p>
				<button onClick={loadData} className="mod-cta">
					重试
				</button>
			</div>
		);
	}

	if (projects.length === 0) {
		return (
			<div className="empty-state">
				<div className="empty-state-icon">📁</div>
				<h3>暂无项目</h3>
				<p>请先创建一个项目，然后再管理 Sprint</p>
			</div>
		);
	}

	const selectedProject = projects.find(p => p.id === selectedProjectId);
	const projectSprints = sprints.filter(s => s.projectId === selectedProjectId);
	const projectTasks = tasks.filter(t => t.projectId === selectedProjectId);

	return (
		<div className="sprint-view-container">
			<div className="sprint-view-header">
				<div className="project-selector">
					<label>选择项目:</label>
					<select
						value={selectedProjectId}
						onChange={(e) => setSelectedProjectId(e.target.value)}
						className="dropdown"
					>
						{projects.map(project => (
							<option key={project.id} value={project.id}>
								{project.name}
							</option>
						))}
					</select>
				</div>
			</div>

			{selectedProject && (
				<SprintBoard
					projectId={selectedProject.id}
					sprints={projectSprints}
					tasks={projectTasks}
					onCreateSprint={handleCreateSprint}
					onUpdateSprint={handleUpdateSprint}
					onDeleteSprint={handleDeleteSprint}
					onStartSprint={handleStartSprint}
					onCompleteSprint={handleCompleteSprint}
					onAddTaskToSprint={handleAddTaskToSprint}
					onRemoveTaskFromSprint={handleRemoveTaskFromSprint}
				/>
			)}

			{showRetrospective && (
				<SprintRetrospective
					sprint={showRetrospective}
					tasks={tasks.filter(task => showRetrospective.taskIds.includes(task.id))}
					onClose={() => setShowRetrospective(null)}
					onSaveRetrospective={handleSaveRetrospective}
				/>
			)}
		</div>
	);
};