// 独立任务列表视图的React组件

import React, { useState, useEffect, useMemo } from 'react';
import { Container, Grid, Stack, Flex } from '../layout/Layout';
import { Card, CardContent } from '../common/Card';
import { Button } from '../common/Button';

import { Task, TaskStatus, Priority, Project } from '../../../models';
import { PTMManager } from '../../../services/PTMManager';

export interface TaskListViewComponentProps {
	ptmManager: PTMManager;
	onTaskSelect?: (taskId: string) => void;
	onTaskCreate?: () => void;
	onTaskEdit?: (taskId: string) => void;
	onTaskDelete?: (taskId: string) => void;
}

interface FilterOptions {
	projectId?: string;
	status?: TaskStatus;
	priority?: Priority;
	searchText?: string;
	showCompleted: boolean;
	sortBy: 'title' | 'priority' | 'dueDate' | 'createdAt' | 'updatedAt';
	sortOrder: 'asc' | 'desc';
	showHierarchy: boolean;
}

interface TaskWithLevel extends Task {
	level: number;
	hasChildren: boolean;
	isExpanded?: boolean;
	children?: TaskWithLevel[];
}

// 安全的日期转换函数
const safeParseDate = (dateValue: any): Date => {
	if (dateValue instanceof Date) {
		return dateValue;
	}
	if (typeof dateValue === 'string' || typeof dateValue === 'number') {
		const parsed = new Date(dateValue);
		if (isNaN(parsed.getTime())) {
			// 如果日期无效，返回当前时间
			console.warn('Invalid date value:', dateValue, 'using current time instead');
			return new Date();
		}
		return parsed;
	}
	// 如果是其他类型，返回当前时间
	console.warn('Unexpected date type:', typeof dateValue, dateValue, 'using current time instead');
	return new Date();
};

export const TaskListViewComponent: React.FC<TaskListViewComponentProps> = ({
	ptmManager,
	onTaskSelect,
	onTaskCreate,
	onTaskEdit,
	onTaskDelete
}) => {
	const [tasks, setTasks] = useState<Task[]>([]);
	const [projects, setProjects] = useState<Project[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
	const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
	const [filters, setFilters] = useState<FilterOptions>({
		showCompleted: false,
		sortBy: 'updatedAt',
		sortOrder: 'desc',
		showHierarchy: false
	});

	// 错误边界处理
	React.useEffect(() => {
		const handleError = (event: ErrorEvent) => {
			console.error('TaskListViewComponent: Unhandled error:', event.error);
			setError(`组件运行时错误: ${event.error?.message || '未知错误'}`);
		};

		const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
			console.error('TaskListViewComponent: Unhandled promise rejection:', event.reason);
			setError(`异步操作失败: ${event.reason?.message || '未知错误'}`);
		};

		window.addEventListener('error', handleError);
		window.addEventListener('unhandledrejection', handleUnhandledRejection);

		return () => {
			window.removeEventListener('error', handleError);
			window.removeEventListener('unhandledrejection', handleUnhandledRejection);
		};
	}, []);

	// 加载数据
	useEffect(() => {
		loadData();
	}, [ptmManager]);

	const loadData = async () => {
		try {
			console.log('TaskListViewComponent: Starting to load data...');
			setLoading(true);
			setError(null);

			// 检查 PTMManager 是否存在
			if (!ptmManager) {
				throw new Error('PTMManager 未提供');
			}

			console.log('TaskListViewComponent: Getting managers...');
			const projectManager = ptmManager.getProjectManager();
			const taskRepository = ptmManager.getTaskRepository();

			if (!projectManager) {
				throw new Error('ProjectManager 未初始化');
			}

			if (!taskRepository) {
				throw new Error('TaskRepository 未初始化');
			}

			console.log('TaskListViewComponent: Loading projects and tasks...');
			// 加载所有项目和任务
			const [allProjects, allTasks] = await Promise.all([
				projectManager.getAllProjects(),
				taskRepository.findAll()
			]);

			console.log('TaskListViewComponent: Loaded', allProjects.length, 'projects and', allTasks.length, 'tasks');

			// 使用安全的日期转换函数
			const normalizedTasks = allTasks.map(task => ({
				...task,
				createdAt: safeParseDate(task.createdAt),
				updatedAt: safeParseDate(task.updatedAt),
				startDate: task.startDate ? safeParseDate(task.startDate) : undefined,
				dueDate: task.dueDate ? safeParseDate(task.dueDate) : undefined,
				completedDate: task.completedDate ? safeParseDate(task.completedDate) : undefined
			}));

			const normalizedProjects = allProjects.map(project => ({
				...project,
				createdAt: safeParseDate(project.createdAt),
				updatedAt: safeParseDate(project.updatedAt),
				startDate: safeParseDate(project.startDate),
				endDate: project.endDate ? safeParseDate(project.endDate) : undefined
			}));

			setProjects(normalizedProjects);
			setTasks(normalizedTasks);
		} catch (err) {
			console.error('Error loading task list data:', err);
			setError('加载任务数据失败');
		} finally {
			setLoading(false);
		}
	};

	// 构建层级树状结构
	const buildTaskHierarchy = (tasks: Task[]): TaskWithLevel[] => {
		const taskMap = new Map<string, Task>();
		const rootTasks: Task[] = [];
		
		// 创建任务映射
		tasks.forEach(task => {
			taskMap.set(task.id, task);
		});

		// 分离根任务和子任务
		tasks.forEach(task => {
			if (!task.parentTaskId || !taskMap.has(task.parentTaskId)) {
				rootTasks.push(task);
			}
		});

		// 递归构建层级结构
		const buildLevel = (parentTasks: Task[], level: number = 0): TaskWithLevel[] => {
			return parentTasks.map(task => {
				const children = tasks.filter(t => t.parentTaskId === task.id);
				const hasChildren = children.length > 0;
				const isExpanded = expandedTasks.has(task.id);

				const taskWithLevel: TaskWithLevel = {
					...task,
					level,
					hasChildren,
					isExpanded,
					children: hasChildren ? buildLevel(children, level + 1) : undefined
				};

				return taskWithLevel;
			});
		};

		return buildLevel(rootTasks);
	};

	// 扁平化层级结构（用于显示）
	const flattenHierarchy = (hierarchyTasks: TaskWithLevel[]): TaskWithLevel[] => {
		const result: TaskWithLevel[] = [];

		const flatten = (tasks: TaskWithLevel[]) => {
			tasks.forEach(task => {
				result.push(task);
				// 只有当任务展开且有子任务时才显示子任务
				if (task.hasChildren && task.isExpanded && task.children) {
					flatten(task.children);
				}
			});
		};

		flatten(hierarchyTasks);
		return result;
	};

	// 过滤和排序任务
	const filteredAndSortedTasks = useMemo(() => {
		let filtered = tasks.filter(task => {
			// 项目筛选
			if (filters.projectId && task.projectId !== filters.projectId) {
				return false;
			}

			// 状态筛选
			if (filters.status && task.status !== filters.status) {
				return false;
			}

			// 优先级筛选
			if (filters.priority && task.priority !== filters.priority) {
				return false;
			}

			// 是否显示已完成任务
			if (!filters.showCompleted && task.status === TaskStatus.COMPLETED) {
				return false;
			}

			// 搜索文本筛选
			if (filters.searchText) {
				const searchLower = filters.searchText.toLowerCase();
				return task.title.toLowerCase().includes(searchLower) ||
					   (task.description && task.description.toLowerCase().includes(searchLower));
			}

			return true;
		});

		// 如果启用层级显示，构建层级结构
		if (filters.showHierarchy) {
			const hierarchy = buildTaskHierarchy(filtered);
			return flattenHierarchy(hierarchy);
		}

		// 普通排序
		filtered.sort((a, b) => {
			let comparison = 0;

			// 辅助函数：安全地获取日期时间戳
			const getDateTimestamp = (date: Date | string | undefined): number => {
				if (!date) return 0;
				if (typeof date === 'string') {
					return new Date(date).getTime();
				}
				return date.getTime();
			};

			switch (filters.sortBy) {
				case 'title':
					comparison = a.title.localeCompare(b.title);
					break;
				case 'priority':
					const priorityOrder = { [Priority.CRITICAL]: 0, [Priority.HIGH]: 1, [Priority.MEDIUM]: 2, [Priority.LOW]: 3 };
					comparison = (priorityOrder[a.priority] || 2) - (priorityOrder[b.priority] || 2);
					break;
				case 'dueDate':
					if (a.dueDate && b.dueDate) {
						comparison = getDateTimestamp(a.dueDate) - getDateTimestamp(b.dueDate);
					} else if (a.dueDate) {
						comparison = -1;
					} else if (b.dueDate) {
						comparison = 1;
					}
					break;
				case 'createdAt':
					comparison = getDateTimestamp(a.createdAt) - getDateTimestamp(b.createdAt);
					break;
				case 'updatedAt':
					comparison = getDateTimestamp(a.updatedAt) - getDateTimestamp(b.updatedAt);
					break;
			}

			return filters.sortOrder === 'desc' ? -comparison : comparison;
		});

		return filtered;
	}, [tasks, filters, expandedTasks]);

	// 获取统计信息
	const getStatistics = () => {
		const total = filteredAndSortedTasks.length;
		const completed = filteredAndSortedTasks.filter(t => t.status === TaskStatus.COMPLETED).length;
		const inProgress = filteredAndSortedTasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length;
		const overdue = filteredAndSortedTasks.filter(t => {
			if (!t.dueDate || t.status === TaskStatus.COMPLETED) return false;
			
			// 安全地处理日期比较
			const dueDate = typeof t.dueDate === 'string' ? new Date(t.dueDate) : t.dueDate;
			return new Date() > dueDate;
		}).length;

		return { total, completed, inProgress, overdue };
	};

	const stats = getStatistics();

	// 事件处理函数
	const handleTaskToggleSelect = (taskId: string) => {
		setSelectedTasks(prev => {
			const newSet = new Set(prev);
			if (newSet.has(taskId)) {
				newSet.delete(taskId);
			} else {
				newSet.add(taskId);
			}
			return newSet;
		});
	};

	const handleTaskToggleExpand = (taskId: string) => {
		setExpandedTasks(prev => {
			const newSet = new Set(prev);
			if (newSet.has(taskId)) {
				newSet.delete(taskId);
			} else {
				newSet.add(taskId);
			}
			return newSet;
		});
	};

	const handleTaskStatusChange = async (taskId: string, status: TaskStatus) => {
		try {
			const taskManager = ptmManager.getTaskManager();
			await taskManager.updateTask(taskId, { status });
			// 重新加载数据以反映更改
			await loadData();
		} catch (err) {
			console.error('Error updating task status:', err);
			// TODO: 显示错误提示
		}
	};

	const handleBatchOperation = async (operation: string, taskIds: string[]) => {
		try {
			const taskManager = ptmManager.getTaskManager();
			
			switch (operation) {
				case 'complete':
					// 批量标记完成
					for (const taskId of taskIds) {
						const task = tasks.find(t => t.id === taskId);
						if (task && task.status !== TaskStatus.COMPLETED) {
							await taskManager.updateTask(taskId, { status: TaskStatus.COMPLETED });
						}
					}
					break;
				
				case 'delete':
					// 批量删除
					if (confirm(`确定要删除选中的 ${taskIds.length} 个任务吗？`)) {
						for (const taskId of taskIds) {
							await taskManager.deleteTask(taskId);
						}
					} else {
						return; // 用户取消操作
					}
					break;
				
				default:
					console.warn('Unknown batch operation:', operation);
					return;
			}
			
			// 清空选择并重新加载数据
			setSelectedTasks(new Set());
			await loadData();
		} catch (err) {
			console.error('Error performing batch operation:', err);
			// TODO: 显示错误提示
		}
	};

	const handleTaskUpdate = async (taskId: string, updates: Partial<Task>) => {
		try {
			const taskManager = ptmManager.getTaskManager();
			await taskManager.updateTask(taskId, updates);
			// 重新加载数据以反映更改
			await loadData();
		} catch (err) {
			console.error('Error updating task:', err);
			// TODO: 显示错误提示
		}
	};

	const handleCreateSubTask = async (parentTaskId: string) => {
		try {
			const taskManager = ptmManager.getTaskManager();
			const parentTask = tasks.find(t => t.id === parentTaskId);
			
			if (parentTask) {
				const createOptions = {
					title: '新子任务',
					description: '',
					projectId: parentTask.projectId,
					parentTaskId: parentTaskId,
					priority: Priority.MEDIUM,
					assignee: parentTask.assignee,
					tags: [],
					estimatedHours: 0
				};

				await taskManager.createTask(createOptions);
				// 自动展开父任务以显示新创建的子任务
				setExpandedTasks(prev => new Set([...prev, parentTaskId]));
				// 重新加载数据
				await loadData();
			}
		} catch (err) {
			console.error('Error creating sub task:', err);
			// TODO: 显示错误提示
		}
	};

	const handleTaskReorder = async (draggedTaskId: string, targetTaskId: string) => {
		try {
			// 简单的拖拽排序实现
			console.log('Task reorder:', draggedTaskId, 'to', targetTaskId);
			
			// 在实际应用中，这里可以实现更复杂的排序逻辑
			// 比如调整任务的排序字段或者在层级结构中重新排列
			
			const draggedTask = tasks.find(t => t.id === draggedTaskId);
			const targetTask = tasks.find(t => t.id === targetTaskId);
			
			if (draggedTask && targetTask) {
				// 如果目标任务有父任务，将拖拽的任务设为同一个父任务的子任务
				if (targetTask.parentTaskId) {
					await handleTaskUpdate(draggedTaskId, { parentTaskId: targetTask.parentTaskId });
				} else {
					// 如果目标任务是根任务，将拖拽的任务也设为根任务
					await handleTaskUpdate(draggedTaskId, { parentTaskId: undefined });
				}
			}
		} catch (err) {
			console.error('Error reordering tasks:', err);
			// TODO: 显示错误提示
		}
	};

	if (loading) {
		return (
			<Container padding>
				<div style={{ textAlign: 'center', padding: '2rem' }}>
					<div className="ptm-spinner">
						<svg viewBox="0 0 24 24" style={{ width: '2rem', height: '2rem' }}>
							<circle
								className="ptm-spinner__circle"
								cx="12"
								cy="12"
								r="10"
								fill="none"
								strokeWidth="2"
							/>
						</svg>
					</div>
					<p style={{ marginTop: '1rem', color: 'var(--text-muted)' }}>
						加载任务数据...
					</p>
				</div>
			</Container>
		);
	}

	if (error) {
		return (
			<Container padding>
				<Card variant="outlined">
					<CardContent>
						<div style={{ textAlign: 'center', padding: '2rem' }}>
							<p style={{ color: 'var(--text-error)', marginBottom: '1rem' }}>
								{error}
							</p>
							<Button onClick={loadData}>
								重新加载
							</Button>
						</div>
					</CardContent>
				</Card>
			</Container>
		);
	}

	return (
		<Container padding>
			<Stack spacing="lg">
				{/* 标题和操作栏 */}
				<Flex justify="between" align="center">
					<div>
						<h1 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 600 }}>
							任务列表
						</h1>
						<p style={{ margin: '0.25rem 0 0 0', color: 'var(--text-muted)' }}>
							管理所有项目的任务
						</p>
					</div>
					{onTaskCreate && (
						<Button variant="primary" onClick={onTaskCreate}>
							新建任务
						</Button>
					)}
				</Flex>

				{/* 统计信息 */}
				<Grid cols={4} gap="md" responsive>
					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '1.5rem', 
									fontWeight: 'bold', 
									color: 'var(--text-normal)' 
								}}>
									{stats.total}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									总任务数
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '1.5rem', 
									fontWeight: 'bold', 
									color: 'var(--interactive-accent)' 
								}}>
									{stats.inProgress}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									进行中
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '1.5rem', 
									fontWeight: 'bold', 
									color: 'var(--text-success)' 
								}}>
									{stats.completed}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									已完成
								</div>
							</div>
						</CardContent>
					</Card>

					<Card variant="outlined">
						<CardContent>
							<div style={{ textAlign: 'center' }}>
								<div style={{ 
									fontSize: '1.5rem', 
									fontWeight: 'bold', 
									color: stats.overdue > 0 ? 'var(--text-error)' : 'var(--text-success)' 
								}}>
									{stats.overdue}
								</div>
								<div style={{ 
									fontSize: '0.875rem', 
									color: 'var(--text-muted)',
									marginTop: '0.25rem'
								}}>
									逾期任务
								</div>
							</div>
						</CardContent>
					</Card>
				</Grid>

				{/* 筛选和搜索栏 */}
				<Card variant="outlined">
					<CardContent>
						<Grid cols={4} gap="md" responsive>
							{/* 搜索框 */}
							<div>
								<label style={{ 
									display: 'block', 
									fontSize: '0.875rem', 
									fontWeight: 500, 
									marginBottom: '0.5rem',
									color: 'var(--text-normal)'
								}}>
									搜索任务
								</label>
								<input
									type="text"
									placeholder="搜索任务标题或描述..."
									value={filters.searchText || ''}
									onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
									style={{
										width: '100%',
										padding: '0.5rem',
										border: '1px solid var(--background-modifier-border)',
										borderRadius: 'var(--ptm-radius-md)',
										backgroundColor: 'var(--background-primary)',
										color: 'var(--text-normal)',
										fontSize: '0.875rem'
									}}
								/>
							</div>

							{/* 项目筛选 */}
							<div>
								<label style={{ 
									display: 'block', 
									fontSize: '0.875rem', 
									fontWeight: 500, 
									marginBottom: '0.5rem',
									color: 'var(--text-normal)'
								}}>
									项目筛选
								</label>
								<select
									value={filters.projectId || ''}
									onChange={(e) => setFilters(prev => ({ ...prev, projectId: e.target.value || undefined }))}
									style={{
										width: '100%',
										padding: '0.5rem',
										border: '1px solid var(--background-modifier-border)',
										borderRadius: 'var(--ptm-radius-md)',
										backgroundColor: 'var(--background-primary)',
										color: 'var(--text-normal)',
										fontSize: '0.875rem'
									}}
								>
									<option value="">所有项目</option>
									{projects.map(project => (
										<option key={project.id} value={project.id}>
											{project.name}
										</option>
									))}
								</select>
							</div>

							{/* 状态筛选 */}
							<div>
								<label style={{ 
									display: 'block', 
									fontSize: '0.875rem', 
									fontWeight: 500, 
									marginBottom: '0.5rem',
									color: 'var(--text-normal)'
								}}>
									状态筛选
								</label>
								<select
									value={filters.status || ''}
									onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as TaskStatus || undefined }))}
									style={{
										width: '100%',
										padding: '0.5rem',
										border: '1px solid var(--background-modifier-border)',
										borderRadius: 'var(--ptm-radius-md)',
										backgroundColor: 'var(--background-primary)',
										color: 'var(--text-normal)',
										fontSize: '0.875rem'
									}}
								>
									<option value="">所有状态</option>
									<option value={TaskStatus.TODO}>待办</option>
									<option value={TaskStatus.IN_PROGRESS}>进行中</option>
									<option value={TaskStatus.BLOCKED}>阻塞</option>
									<option value={TaskStatus.REVIEW}>审核</option>
									<option value={TaskStatus.COMPLETED}>已完成</option>
									<option value={TaskStatus.CANCELLED}>已取消</option>
								</select>
							</div>

							{/* 优先级筛选 */}
							<div>
								<label style={{ 
									display: 'block', 
									fontSize: '0.875rem', 
									fontWeight: 500, 
									marginBottom: '0.5rem',
									color: 'var(--text-normal)'
								}}>
									优先级筛选
								</label>
								<select
									value={filters.priority || ''}
									onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value as Priority || undefined }))}
									style={{
										width: '100%',
										padding: '0.5rem',
										border: '1px solid var(--background-modifier-border)',
										borderRadius: 'var(--ptm-radius-md)',
										backgroundColor: 'var(--background-primary)',
										color: 'var(--text-normal)',
										fontSize: '0.875rem'
									}}
								>
									<option value="">所有优先级</option>
									<option value={Priority.CRITICAL}>🔴 紧急</option>
									<option value={Priority.HIGH}>🟠 高</option>
									<option value={Priority.MEDIUM}>🟡 中</option>
									<option value={Priority.LOW}>🟢 低</option>
								</select>
							</div>
						</Grid>

						{/* 排序和选项 */}
						<div style={{ marginTop: '1rem' }}>
							<Flex gap="md" align="center" wrap>
								<div>
									<label style={{ 
										fontSize: '0.875rem', 
										fontWeight: 500, 
										marginRight: '0.5rem',
										color: 'var(--text-normal)'
									}}>
										排序:
									</label>
									<select
										value={filters.sortBy}
										onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
										style={{
											padding: '0.25rem 0.5rem',
											border: '1px solid var(--background-modifier-border)',
											borderRadius: 'var(--ptm-radius-sm)',
											backgroundColor: 'var(--background-primary)',
											color: 'var(--text-normal)',
											fontSize: '0.875rem',
											marginRight: '0.5rem'
										}}
									>
										<option value="updatedAt">更新时间</option>
										<option value="createdAt">创建时间</option>
										<option value="title">标题</option>
										<option value="priority">优先级</option>
										<option value="dueDate">到期时间</option>
									</select>
									<select
										value={filters.sortOrder}
										onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as 'asc' | 'desc' }))}
										style={{
											padding: '0.25rem 0.5rem',
											border: '1px solid var(--background-modifier-border)',
											borderRadius: 'var(--ptm-radius-sm)',
											backgroundColor: 'var(--background-primary)',
											color: 'var(--text-normal)',
											fontSize: '0.875rem'
										}}
									>
										<option value="desc">降序</option>
										<option value="asc">升序</option>
									</select>
								</div>

								<label style={{ 
									display: 'flex', 
									alignItems: 'center', 
									fontSize: '0.875rem',
									color: 'var(--text-normal)',
									cursor: 'pointer'
								}}>
									<input
										type="checkbox"
										checked={filters.showCompleted}
										onChange={(e) => setFilters(prev => ({ ...prev, showCompleted: e.target.checked }))}
										style={{ marginRight: '0.5rem' }}
									/>
									显示已完成任务
								</label>

								<label style={{ 
									display: 'flex', 
									alignItems: 'center', 
									fontSize: '0.875rem',
									color: 'var(--text-normal)',
									cursor: 'pointer'
								}}>
									<input
										type="checkbox"
										checked={filters.showHierarchy}
										onChange={(e) => setFilters(prev => ({ ...prev, showHierarchy: e.target.checked }))}
										style={{ marginRight: '0.5rem' }}
									/>
									层级树状显示
								</label>
							</Flex>
						</div>
					</CardContent>
				</Card>

				{/* 简化的任务列表 */}
				<Card variant="outlined">
					<CardContent style={{ padding: 0 }}>
						{/* 标题栏 */}
						<div style={{ 
							padding: '1rem',
							borderBottom: '1px solid var(--background-modifier-border)',
							backgroundColor: 'var(--background-secondary)'
						}}>
							<h3 style={{ margin: 0, fontSize: '1rem', fontWeight: 600 }}>
								任务列表 ({filteredAndSortedTasks.length})
							</h3>
						</div>

						{/* 任务列表 */}
						<div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
							{filteredAndSortedTasks.length === 0 ? (
								<div style={{ 
									textAlign: 'center', 
									padding: '2rem',
									color: 'var(--text-muted)'
								}}>
									<div style={{ fontSize: '2rem', marginBottom: '1rem' }}>📝</div>
									<p>没有找到匹配的任务</p>
								</div>
							) : (
								filteredAndSortedTasks.slice(0, 50).map(task => (
									<div 
										key={task.id}
										style={{
											padding: '0.75rem',
											borderBottom: '1px solid var(--background-modifier-border)',
											cursor: 'pointer',
											transition: 'background-color 0.2s ease'
										}}
										onClick={() => onTaskSelect?.(task.id)}
										onMouseEnter={(e) => {
											e.currentTarget.style.backgroundColor = 'var(--background-modifier-hover-weak)';
										}}
										onMouseLeave={(e) => {
											e.currentTarget.style.backgroundColor = 'transparent';
										}}
									>
										<div style={{ 
											fontSize: '0.875rem', 
											fontWeight: 500, 
											color: task.status === 'completed' ? 'var(--text-muted)' : 'var(--text-normal)',
											textDecoration: task.status === 'completed' ? 'line-through' : 'none',
											marginBottom: '0.25rem'
										}}>
											{task.title}
										</div>
										<div style={{ 
											fontSize: '0.75rem', 
											color: 'var(--text-muted)' 
										}}>
											状态: {task.status} | 优先级: {task.priority}
											{task.dueDate && ` | 到期: ${new Date(task.dueDate).toLocaleDateString()}`}
										</div>
									</div>
								))
							)}
							{filteredAndSortedTasks.length > 50 && (
								<div style={{ 
									padding: '1rem', 
									textAlign: 'center', 
									color: 'var(--text-muted)',
									fontSize: '0.875rem'
								}}>
									显示前 50 个任务，共 {filteredAndSortedTasks.length} 个任务
								</div>
							)}
						</div>
					</CardContent>
				</Card>

				{/* 空状态 */}
				{filteredAndSortedTasks.length === 0 && !loading && (
					<Card variant="outlined">
						<CardContent>
							<div style={{ 
								textAlign: 'center', 
								padding: '3rem 1rem',
								color: 'var(--text-muted)'
							}}>
								<div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📝</div>
								<h3 style={{ margin: '0 0 0.5rem 0' }}>没有找到任务</h3>
								<p style={{ margin: '0 0 1.5rem 0' }}>
									{tasks.length === 0 ? '还没有创建任何任务' : '当前筛选条件下没有匹配的任务'}
								</p>
								{onTaskCreate && tasks.length === 0 && (
									<Button variant="primary" onClick={onTaskCreate}>
										创建第一个任务
									</Button>
								)}
							</div>
						</CardContent>
					</Card>
				)}
			</Stack>
		</Container>
	);
};