// 甘特图视图组件

import React, { useState, useEffect } from 'react';
import { GanttChart } from '../gantt/GanttChart';
import { SimpleGanttTest } from '../gantt/SimpleGanttTest';
import { GanttManager } from '../../../services/GanttManager';
import { PTMManager } from '../../../services/PTMManager';
import { Project } from '../../../models/Project';
import { InputModal } from '../common/InputModal';
import { ChevronDown, Plus, Settings, TestTube } from 'lucide-react';

interface GanttViewComponentProps {
	ptmManager: PTMManager;
}

export const GanttViewComponent: React.FC<GanttViewComponentProps> = ({
	ptmManager
}) => {
	const [ganttManager, setGanttManager] = useState<GanttManager | null>(null);
	const [projects, setProjects] = useState<Project[]>([]);
	const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [testMode, setTestMode] = useState(false);

	useEffect(() => {
		initializeGanttView();
	}, [ptmManager]);

	const initializeGanttView = async () => {
		try {
			setIsLoading(true);
			setError(null);

			// 创建甘特图管理器
			const manager = new GanttManager(
				ptmManager.getTaskManager(),
				ptmManager.getProjectManager()
			);
			setGanttManager(manager);

			// 加载所有项目
			const allProjects = await ptmManager.getProjectRepository().findAll();
			setProjects(allProjects);

			// 选择第一个项目作为默认项目
			if (allProjects.length > 0) {
				setSelectedProjectId(allProjects[0].id);
			}

		} catch (err) {
			console.error('初始化甘特图视图失败:', err);
			setError(err instanceof Error ? err.message : '初始化失败');
		} finally {
			setIsLoading(false);
		}
	};

	const handleProjectChange = (projectId: string) => {
		setSelectedProjectId(projectId);
	};

	const handleCreateProject = () => {
		setShowCreateModal(true);
	};

	const handleConfirmCreateProject = async (projectName: string) => {
		try {
			setShowCreateModal(false);
			
			const newProject = await ptmManager.getProjectManager().createProject({
				name: projectName,
				description: '通过甘特图视图创建的项目',
				priority: 'medium' as any,
				tags: ['gantt']
			});

			// 更新项目列表
			const updatedProjects = await ptmManager.getProjectRepository().findAll();
			setProjects(updatedProjects);
			setSelectedProjectId(newProject.id);

		} catch (err) {
			console.error('创建项目失败:', err);
			// 使用Notice而不是alert
			console.error('创建项目失败: ' + (err instanceof Error ? err.message : String(err)));
		}
	};

	const handleCancelCreateProject = () => {
		setShowCreateModal(false);
	};

	// 渲染加载状态
	if (isLoading) {
		return (
			<div className="gantt-view-loading">
				<div className="loading-spinner"></div>
				<p>正在加载甘特图视图...</p>
			</div>
		);
	}

	// 渲染错误状态
	if (error) {
		return (
			<div className="gantt-view-error">
				<h3>加载甘特图视图失败</h3>
				<p>{error}</p>
				<button onClick={initializeGanttView}>重试</button>
			</div>
		);
	}

	// 渲染无项目状态
	if (projects.length === 0) {
		return (
			<div className="gantt-view-empty">
				<div className="empty-state">
					<div className="empty-icon">📊</div>
					<h3>暂无项目</h3>
					<p>创建第一个项目以开始使用甘特图功能</p>
					<button 
						className="create-project-btn"
						onClick={handleCreateProject}
					>
						<Plus size={16} />
						创建项目
					</button>
				</div>
			</div>
		);
	}

	// 渲染甘特图视图
	return (
		<div className="gantt-view-container">
			{/* 项目选择器 */}
			<div className="gantt-view-header">
				<div className="project-selector">
					<label htmlFor="project-select">选择项目:</label>
					<div className="select-wrapper">
						<select
							id="project-select"
							value={selectedProjectId || ''}
							onChange={(e) => handleProjectChange(e.target.value)}
						>
							{projects.map(project => (
								<option key={project.id} value={project.id}>
									{project.name}
								</option>
							))}
						</select>
						<ChevronDown size={16} className="select-icon" />
					</div>
				</div>

				<div className="header-actions">
					<button 
						className={`action-btn ${testMode ? 'active' : ''}`}
						onClick={() => setTestMode(!testMode)}
						title={testMode ? '切换到正常模式' : '切换到测试模式'}
					>
						<TestTube size={16} />
						{testMode ? '正常模式' : '测试模式'}
					</button>
					<button 
						className="action-btn"
						onClick={handleCreateProject}
						title="创建新项目"
					>
						<Plus size={16} />
						新建项目
					</button>
					<button 
						className="action-btn"
						onClick={initializeGanttView}
						title="刷新视图"
					>
						<Settings size={16} />
						刷新
					</button>
				</div>
			</div>

			{/* 甘特图内容 */}
			{testMode ? (
				<div className="gantt-view-content">
					<SimpleGanttTest />
				</div>
			) : selectedProjectId && ganttManager ? (
				<div className="gantt-view-content">
					<GanttChart
						projectId={selectedProjectId}
						ganttManager={ganttManager}
						onTaskSelect={(taskId) => {
							console.log('甘特图中选择任务:', taskId);
						}}
						onTaskUpdate={(taskId) => {
							console.log('甘特图中任务已更新:', taskId);
						}}
					/>
				</div>
			) : null}

			<InputModal
				isOpen={showCreateModal}
				title="创建新项目"
				placeholder="请输入项目名称"
				onConfirm={handleConfirmCreateProject}
				onCancel={handleCancelCreateProject}
			/>
		</div>
	);
};