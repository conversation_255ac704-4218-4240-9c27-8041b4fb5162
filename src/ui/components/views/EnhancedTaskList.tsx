// 增强版任务列表组件，支持层级显示、批量操作、快速编辑等高级功能

import React, { useState, useCallback } from 'react';
import { Card, CardContent } from '../common/Card';
import { Button } from '../common/Button';
import { Flex } from '../layout/Layout';
import { Task, TaskStatus, Priority } from '../../../models';

interface TaskWithLevel extends Task {
	level: number;
	hasChildren: boolean;
	isExpanded?: boolean;
	children?: TaskWithLevel[];
}

interface EnhancedTaskListProps {
	title: string;
	tasks: TaskWithLevel[];
	selectedTasks: Set<string>;
	expandedTasks: Set<string>;
	showHierarchy: boolean;
	onTaskSelect?: (taskId: string) => void;
	onTaskToggleSelect?: (taskId: string) => void;
	onTaskToggleExpand?: (taskId: string) => void;
	onTaskEdit?: (taskId: string) => void;
	onTaskDelete?: (taskId: string) => void;
	onTaskStatusChange?: (taskId: string, status: TaskStatus) => void;
	onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void;
	onCreateSubTask?: (parentTaskId: string) => void;
	onBatchOperation?: (operation: string, taskIds: string[]) => void;
	onTaskReorder?: (draggedTaskId: string, targetTaskId: string) => void;
	emptyMessage?: string;
}

// 优先级颜色映射
const getPriorityColor = (priority: Priority): string => {
	switch (priority) {
		case Priority.CRITICAL: return 'var(--text-error)';
		case Priority.HIGH: return 'var(--text-warning)';
		case Priority.MEDIUM: return 'var(--text-accent)';
		case Priority.LOW: return 'var(--text-success)';
		default: return 'var(--text-muted)';
	}
};

// 优先级图标映射
const getPriorityIcon = (priority: Priority): string => {
	switch (priority) {
		case Priority.CRITICAL: return '🔴';
		case Priority.HIGH: return '🟠';
		case Priority.MEDIUM: return '🟡';
		case Priority.LOW: return '🟢';
		default: return '⚪';
	}
};

// 状态图标映射
const getStatusIcon = (status: TaskStatus): string => {
	switch (status) {
		case TaskStatus.TODO: return '⭕';
		case TaskStatus.IN_PROGRESS: return '🔄';
		case TaskStatus.BLOCKED: return '🚫';
		case TaskStatus.REVIEW: return '👀';
		case TaskStatus.COMPLETED: return '✅';
		case TaskStatus.CANCELLED: return '❌';
		default: return '⭕';
	}
};

// 格式化日期
const formatDate = (date: Date | string | undefined): string => {
	if (!date) return '';
	
	const d = typeof date === 'string' ? new Date(date) : date;
	if (isNaN(d.getTime())) return '';
	
	const now = new Date();
	const diffTime = d.getTime() - now.getTime();
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	
	if (diffDays === 0) return '今天';
	if (diffDays === 1) return '明天';
	if (diffDays === -1) return '昨天';
	if (diffDays > 0 && diffDays <= 7) return `${diffDays}天后`;
	if (diffDays < 0 && diffDays >= -7) return `${Math.abs(diffDays)}天前`;
	
	return d.toLocaleDateString('zh-CN', { 
		month: 'short', 
		day: 'numeric',
		year: d.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
	});
};

// 单个任务项组件
const TaskItem: React.FC<{
	task: TaskWithLevel;
	isSelected: boolean;
	showHierarchy: boolean;
	onSelect?: (taskId: string) => void;
	onToggleSelect?: (taskId: string) => void;
	onToggleExpand?: (taskId: string) => void;
	onEdit?: (taskId: string) => void;
	onDelete?: (taskId: string) => void;
	onStatusChange?: (taskId: string, status: TaskStatus) => void;
	onTaskUpdate?: (taskId: string, updates: Partial<Task>) => void;
	onCreateSubTask?: (parentTaskId: string) => void;
	onDragStart?: (taskId: string) => void;
	onDragOver?: (taskId: string) => void;
	onDrop?: (draggedTaskId: string, targetTaskId: string) => void;
}> = ({
	task,
	isSelected,
	showHierarchy,
	onSelect,
	onToggleSelect,
	onToggleExpand,
	onEdit,
	onDelete,
	onStatusChange,
	onTaskUpdate,
	onCreateSubTask,
	onDragStart,
	onDragOver,
	onDrop
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editTitle, setEditTitle] = useState(task.title);
	const [isDragOver, setIsDragOver] = useState(false);

	const handleTitleSave = useCallback(() => {
		if (editTitle.trim() && editTitle !== task.title) {
			onTaskUpdate?.(task.id, { title: editTitle.trim() });
		}
		setIsEditing(false);
	}, [editTitle, task.id, task.title, onTaskUpdate]);

	const handleTitleCancel = useCallback(() => {
		setEditTitle(task.title);
		setIsEditing(false);
	}, [task.title]);

	const isOverdue = task.dueDate && task.status !== TaskStatus.COMPLETED && 
		new Date() > (typeof task.dueDate === 'string' ? new Date(task.dueDate) : task.dueDate);

	return (
		<div
			draggable={true}
			onDragStart={(e) => {
				e.dataTransfer.setData('text/plain', task.id);
				onDragStart?.(task.id);
			}}
			onDragOver={(e) => {
				e.preventDefault();
				setIsDragOver(true);
				onDragOver?.(task.id);
			}}
			onDragLeave={(e) => {
				e.preventDefault();
				setIsDragOver(false);
			}}
			onDrop={(e) => {
				e.preventDefault();
				setIsDragOver(false);
				const draggedTaskId = e.dataTransfer.getData('text/plain');
				if (draggedTaskId !== task.id) {
					onDrop?.(draggedTaskId, task.id);
				}
			}}
			style={{
				padding: '0.75rem',
				borderBottom: '1px solid var(--background-modifier-border)',
				backgroundColor: isSelected 
					? 'var(--background-modifier-hover)' 
					: isDragOver 
						? 'var(--background-modifier-hover-weak)' 
						: 'transparent',
				cursor: 'pointer',
				transition: 'background-color 0.2s ease',
				border: isDragOver ? '2px dashed var(--interactive-accent)' : '2px solid transparent'
			}}
			onMouseEnter={(e) => {
				if (!isSelected && !isDragOver) {
					e.currentTarget.style.backgroundColor = 'var(--background-modifier-hover-weak)';
				}
			}}
			onMouseLeave={(e) => {
				if (!isSelected && !isDragOver) {
					e.currentTarget.style.backgroundColor = 'transparent';
				}
			}}
		>
			<Flex align="center" gap="sm">
				{/* 批量选择复选框 */}
				<input
					type="checkbox"
					checked={isSelected}
					onChange={(e) => {
						e.stopPropagation();
						onToggleSelect?.(task.id);
					}}
					style={{ marginRight: '0.5rem' }}
				/>

				{/* 层级缩进和展开/折叠按钮 */}
				{showHierarchy && (
					<div style={{ 
						width: `${task.level * 1.5}rem`,
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'flex-end'
					}}>
						{task.hasChildren && (
							<button
								onClick={(e) => {
									e.stopPropagation();
									onToggleExpand?.(task.id);
								}}
								style={{
									background: 'none',
									border: 'none',
									cursor: 'pointer',
									padding: '0.25rem',
									fontSize: '0.75rem',
									color: 'var(--text-muted)'
								}}
							>
								{task.isExpanded ? '▼' : '▶'}
							</button>
						)}
					</div>
				)}

				{/* 状态图标 */}
				<div
					style={{
						fontSize: '1rem',
						cursor: 'pointer',
						padding: '0.25rem'
					}}
					onClick={(e) => {
						e.stopPropagation();
						// 循环切换状态
						const statusOrder = [TaskStatus.TODO, TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED];
						const currentIndex = statusOrder.indexOf(task.status);
						const nextStatus = statusOrder[(currentIndex + 1) % statusOrder.length];
						onStatusChange?.(task.id, nextStatus);
					}}
					title="点击切换状态"
				>
					{getStatusIcon(task.status)}
				</div>

				{/* 任务标题 */}
				<div style={{ flex: 1, minWidth: 0 }}>
					{isEditing ? (
						<input
							type="text"
							value={editTitle}
							onChange={(e) => setEditTitle(e.target.value)}
							onBlur={handleTitleSave}
							onKeyDown={(e) => {
								if (e.key === 'Enter') {
									handleTitleSave();
								} else if (e.key === 'Escape') {
									handleTitleCancel();
								}
							}}
							autoFocus
							style={{
								width: '100%',
								padding: '0.25rem 0.5rem',
								border: '1px solid var(--interactive-accent)',
								borderRadius: 'var(--ptm-radius-sm)',
								backgroundColor: 'var(--background-primary)',
								color: 'var(--text-normal)',
								fontSize: '0.875rem'
							}}
						/>
					) : (
						<div
							onClick={() => onSelect?.(task.id)}
							onDoubleClick={() => setIsEditing(true)}
							style={{
								fontSize: '0.875rem',
								fontWeight: 500,
								color: task.status === TaskStatus.COMPLETED ? 'var(--text-muted)' : 'var(--text-normal)',
								textDecoration: task.status === TaskStatus.COMPLETED ? 'line-through' : 'none',
								overflow: 'hidden',
								textOverflow: 'ellipsis',
								whiteSpace: 'nowrap'
							}}
							title={task.title}
						>
							{task.title}
						</div>
					)}
				</div>

				{/* 优先级标识 */}
				<div
					style={{
						fontSize: '0.875rem',
						color: getPriorityColor(task.priority),
						padding: '0.25rem'
					}}
					title={`优先级: ${task.priority}`}
				>
					{getPriorityIcon(task.priority)}
				</div>

				{/* 到期日期 */}
				{task.dueDate && (
					<div
						style={{
							fontSize: '0.75rem',
							color: isOverdue ? 'var(--text-error)' : 'var(--text-muted)',
							fontWeight: isOverdue ? 600 : 400,
							padding: '0.25rem 0.5rem',
							borderRadius: 'var(--ptm-radius-sm)',
							backgroundColor: isOverdue ? 'var(--background-modifier-error-weak)' : 'transparent',
							whiteSpace: 'nowrap'
						}}
						title={typeof task.dueDate === 'string' ? task.dueDate : task.dueDate.toLocaleString('zh-CN')}
					>
						{formatDate(task.dueDate)}
					</div>
				)}

				{/* 操作按钮 */}
				<div style={{ display: 'flex', gap: '0.25rem' }}>
					{showHierarchy && onCreateSubTask && (
						<button
							onClick={(e) => {
								e.stopPropagation();
								onCreateSubTask(task.id);
							}}
							style={{
								background: 'none',
								border: 'none',
								cursor: 'pointer',
								padding: '0.25rem',
								fontSize: '0.75rem',
								color: 'var(--text-accent)',
								borderRadius: 'var(--ptm-radius-sm)'
							}}
							title="创建子任务"
						>
							➕
						</button>
					)}
					{onEdit && (
						<button
							onClick={(e) => {
								e.stopPropagation();
								onEdit(task.id);
							}}
							style={{
								background: 'none',
								border: 'none',
								cursor: 'pointer',
								padding: '0.25rem',
								fontSize: '0.75rem',
								color: 'var(--text-muted)',
								borderRadius: 'var(--ptm-radius-sm)'
							}}
							title="编辑任务"
						>
							✏️
						</button>
					)}
					{onDelete && (
						<button
							onClick={(e) => {
								e.stopPropagation();
								if (confirm('确定要删除这个任务吗？')) {
									onDelete(task.id);
								}
							}}
							style={{
								background: 'none',
								border: 'none',
								cursor: 'pointer',
								padding: '0.25rem',
								fontSize: '0.75rem',
								color: 'var(--text-error)',
								borderRadius: 'var(--ptm-radius-sm)'
							}}
							title="删除任务"
						>
							🗑️
						</button>
					)}
				</div>
			</Flex>
		</div>
	);
};

export const EnhancedTaskList: React.FC<EnhancedTaskListProps> = ({
	title,
	tasks,
	selectedTasks,
	expandedTasks,
	showHierarchy,
	onTaskSelect,
	onTaskToggleSelect,
	onTaskToggleExpand,
	onTaskEdit,
	onTaskDelete,
	onTaskStatusChange,
	onTaskUpdate,
	onCreateSubTask,
	onBatchOperation,
	onTaskReorder,
	emptyMessage = '没有任务'
}) => {
	const selectedTaskIds = Array.from(selectedTasks);
	const hasSelection = selectedTaskIds.length > 0;

	const handleSelectAll = useCallback(() => {
		if (hasSelection) {
			// 取消全选
			tasks.forEach(task => onTaskToggleSelect?.(task.id));
		} else {
			// 全选
			tasks.forEach(task => {
				if (!selectedTasks.has(task.id)) {
					onTaskToggleSelect?.(task.id);
				}
			});
		}
	}, [tasks, selectedTasks, hasSelection, onTaskToggleSelect]);

	if (tasks.length === 0) {
		return (
			<Card variant="outlined">
				<CardContent>
					<div style={{ 
						textAlign: 'center', 
						padding: '2rem',
						color: 'var(--text-muted)'
					}}>
						<div style={{ fontSize: '2rem', marginBottom: '1rem' }}>📝</div>
						<p>{emptyMessage}</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card variant="outlined">
			<CardContent style={{ padding: 0 }}>
				{/* 标题和批量操作栏 */}
				<div style={{ 
					padding: '1rem',
					borderBottom: '1px solid var(--background-modifier-border)',
					backgroundColor: 'var(--background-secondary)'
				}}>
					<Flex justify="between" align="center">
						<div>
							<h3 style={{ margin: 0, fontSize: '1rem', fontWeight: 600 }}>
								{title}
							</h3>
							{hasSelection && (
								<p style={{ 
									margin: '0.25rem 0 0 0', 
									fontSize: '0.75rem',
									color: 'var(--text-muted)'
								}}>
									已选择 {selectedTaskIds.length} 个任务
								</p>
							)}
						</div>

						<Flex gap="sm" align="center">
							{/* 全选/取消全选 */}
							<Button
								variant="ghost"
								size="small"
								onClick={handleSelectAll}
							>
								{hasSelection ? '取消全选' : '全选'}
							</Button>

							{/* 批量操作 */}
							{hasSelection && onBatchOperation && (
								<>
									<Button
										variant="ghost"
										size="small"
										onClick={() => onBatchOperation('complete', selectedTaskIds)}
									>
										标记完成
									</Button>
									<Button
										variant="ghost"
										size="small"
										onClick={() => onBatchOperation('delete', selectedTaskIds)}
									>
										批量删除
									</Button>
								</>
							)}
						</Flex>
					</Flex>
				</div>

				{/* 任务列表 */}
				<div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
					{tasks.map(task => (
						<TaskItem
							key={task.id}
							task={task}
							isSelected={selectedTasks.has(task.id)}
							showHierarchy={showHierarchy}
							onSelect={onTaskSelect}
							onToggleSelect={onTaskToggleSelect}
							onToggleExpand={onTaskToggleExpand}
							onEdit={onTaskEdit}
							onDelete={onTaskDelete}
							onStatusChange={onTaskStatusChange}
							onTaskUpdate={onTaskUpdate}
							onDragStart={() => {}}
							onDragOver={() => {}}
							onDrop={(draggedTaskId, targetTaskId) => {
								onTaskReorder?.(draggedTaskId, targetTaskId);
							}}
						/>
					))}
				</div>
			</CardContent>
		</Card>
	);
};