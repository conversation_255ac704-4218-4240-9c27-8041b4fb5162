// 独立任务视图组件 - 无项目环境下的任务管理界面

import React, { useState, useEffect, useMemo } from 'react';
import { Task, TaskStatus, Priority } from '../../../models';
import { IndependentTaskModeController, TaskModeStats } from '../../../services/IndependentTaskModeController';
import { Card } from '../common/Card';

interface IndependentTaskViewProps {
	controller: IndependentTaskModeController;
}

interface TaskFilters {
	status?: TaskStatus;
	priority?: Priority;
	assignee?: string;
	searchText?: string;
	tags?: string[];
	dateRange?: {
		start?: Date;
		end?: Date;
	};
}

interface TaskFormData {
	title: string;
	description: string;
	priority: Priority;
	assignee: string;
	estimatedHours: number;
	startDate: string;
	dueDate: string;
	tags: string;
}

/**
 * 独立任务视图组件
 * 提供无项目环境下的任务管理功能
 */
export const IndependentTaskViewComponent: React.FC<IndependentTaskViewProps> = ({ controller }) => {
	const [tasks, setTasks] = useState<Task[]>([]);
	const [stats, setStats] = useState<TaskModeStats | null>(null);
	const [filters, setFilters] = useState<TaskFilters>({});
	const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [editingTask, setEditingTask] = useState<Task | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 表单数据状态
	const [formData, setFormData] = useState<TaskFormData>({
		title: '',
		description: '',
		priority: Priority.MEDIUM,
		assignee: '',
		estimatedHours: 0,
		startDate: new Date().toISOString().split('T')[0],
		dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
		tags: ''
	});

	// 加载任务数据
	const loadTasks = async () => {
		try {
			setLoading(true);
			setError(null);
			const [tasksData, statsData] = await Promise.all([
				controller.getIndependentTasks(),
				controller.getIndependentTaskStats()
			]);
			setTasks(tasksData);
			setStats(statsData);
		} catch (err) {
			setError(err instanceof Error ? err.message : '加载任务失败');
		} finally {
			setLoading(false);
		}
	};

	// 初始化加载
	useEffect(() => {
		if (controller.isIndependentMode()) {
			loadTasks();
		}
	}, [controller]);

	// 过滤后的任务列表
	const filteredTasks = useMemo(() => {
		return tasks.filter(task => {
			// 状态过滤
			if (filters.status && task.status !== filters.status) {
				return false;
			}

			// 优先级过滤
			if (filters.priority && task.priority !== filters.priority) {
				return false;
			}

			// 负责人过滤
			if (filters.assignee && task.assignee !== filters.assignee) {
				return false;
			}

			// 文本搜索
			if (filters.searchText) {
				const searchLower = filters.searchText.toLowerCase();
				const titleMatch = task.title.toLowerCase().includes(searchLower);
				const descMatch = task.description?.toLowerCase().includes(searchLower) || false;
				if (!titleMatch && !descMatch) {
					return false;
				}
			}

			// 标签过滤
			if (filters.tags && filters.tags.length > 0) {
				const hasMatchingTag = filters.tags.some(tag => task.tags.includes(tag));
				if (!hasMatchingTag) {
					return false;
				}
			}

			// 日期范围过滤
			if (filters.dateRange) {
				if (filters.dateRange.start && task.dueDate) {
					const taskDueDate = new Date(task.dueDate);
					if (taskDueDate < filters.dateRange.start) {
						return false;
					}
				}
				if (filters.dateRange.end && task.dueDate) {
					const taskDueDate = new Date(task.dueDate);
					if (taskDueDate > filters.dateRange.end) {
						return false;
					}
				}
			}

			return true;
		});
	}, [tasks, filters]);

	// 创建任务
	const handleCreateTask = async (e: React.FormEvent) => {
		e.preventDefault();
		try {
			setLoading(true);
			await controller.createIndependentTask({
				title: formData.title,
				description: formData.description || undefined,
				priority: formData.priority,
				assignee: formData.assignee || undefined,
				estimatedHours: formData.estimatedHours || undefined,
				startDate: formData.startDate ? new Date(formData.startDate) : undefined,
				dueDate: formData.dueDate ? new Date(formData.dueDate) : undefined,
				tags: formData.tags ? formData.tags.split(',').map(t => t.trim()).filter(t => t) : []
			});
			
			// 重置表单
			setFormData({
				title: '',
				description: '',
				priority: Priority.MEDIUM,
				assignee: '',
				estimatedHours: 0,
				startDate: new Date().toISOString().split('T')[0],
				dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
				tags: ''
			});
			setShowCreateForm(false);
			await loadTasks();
		} catch (err) {
			setError(err instanceof Error ? err.message : '创建任务失败');
		} finally {
			setLoading(false);
		}
	};

	// 更新任务
	const handleUpdateTask = async (taskId: string, updates: Partial<Task>) => {
		try {
			setLoading(true);
			await controller.updateIndependentTask(taskId, updates);
			await loadTasks();
		} catch (err) {
			setError(err instanceof Error ? err.message : '更新任务失败');
		} finally {
			setLoading(false);
		}
	};

	// 删除任务
	const handleDeleteTask = async (taskId: string) => {
		if (!confirm('确定要删除这个任务吗？')) {
			return;
		}

		try {
			setLoading(true);
			await controller.deleteIndependentTask(taskId);
			await loadTasks();
		} catch (err) {
			setError(err instanceof Error ? err.message : '删除任务失败');
		} finally {
			setLoading(false);
		}
	};

	// 批量操作
	const handleBatchStatusUpdate = async (status: TaskStatus) => {
		if (selectedTasks.size === 0) {
			return;
		}

		try {
			setLoading(true);
			await controller.batchUpdateTaskStatus(Array.from(selectedTasks), status);
			setSelectedTasks(new Set());
			await loadTasks();
		} catch (err) {
			setError(err instanceof Error ? err.message : '批量更新失败');
		} finally {
			setLoading(false);
		}
	};

	const handleBatchDelete = async () => {
		if (selectedTasks.size === 0) {
			return;
		}

		if (!confirm(`确定要删除选中的 ${selectedTasks.size} 个任务吗？`)) {
			return;
		}

		try {
			setLoading(true);
			await controller.batchDeleteTasks(Array.from(selectedTasks));
			setSelectedTasks(new Set());
			await loadTasks();
		} catch (err) {
			setError(err instanceof Error ? err.message : '批量删除失败');
		} finally {
			setLoading(false);
		}
	};

	// 任务选择处理
	const handleTaskSelect = (taskId: string, selected: boolean) => {
		const newSelected = new Set(selectedTasks);
		if (selected) {
			newSelected.add(taskId);
		} else {
			newSelected.delete(taskId);
		}
		setSelectedTasks(newSelected);
	};

	const handleSelectAll = (selected: boolean) => {
		if (selected) {
			setSelectedTasks(new Set(filteredTasks.map(t => t.id)));
		} else {
			setSelectedTasks(new Set());
		}
	};

	// 获取状态显示文本
	const getStatusText = (status: TaskStatus): string => {
		const statusMap = {
			[TaskStatus.TODO]: '待办',
			[TaskStatus.IN_PROGRESS]: '进行中',
			[TaskStatus.BLOCKED]: '阻塞',
			[TaskStatus.REVIEW]: '审核',
			[TaskStatus.COMPLETED]: '已完成',
			[TaskStatus.CANCELLED]: '已取消'
		};
		return statusMap[status];
	};

	// 获取优先级显示文本
	const getPriorityText = (priority: Priority): string => {
		const priorityMap = {
			[Priority.LOW]: '低',
			[Priority.MEDIUM]: '中',
			[Priority.HIGH]: '高',
			[Priority.CRITICAL]: '紧急'
		};
		return priorityMap[priority];
	};

	// 获取优先级颜色
	const getPriorityColor = (priority: Priority): string => {
		const colorMap = {
			[Priority.LOW]: '#10b981',
			[Priority.MEDIUM]: '#f59e0b',
			[Priority.HIGH]: '#ef4444',
			[Priority.CRITICAL]: '#dc2626'
		};
		return colorMap[priority];
	};

	if (!controller.isIndependentMode()) {
		return (
			<div className="independent-task-view">
				<Card>
					<div className="text-center py-8">
						<h3>当前不在独立任务模式</h3>
						<p>请切换到独立任务模式以使用此功能</p>
					</div>
				</Card>
			</div>
		);
	}

	return (
		<div className="independent-task-view">
			{/* 错误提示 */}
			{error && (
				<div className="error-banner" style={{ 
					background: '#fee2e2', 
					color: '#dc2626', 
					padding: '12px', 
					borderRadius: '6px', 
					marginBottom: '16px' 
				}}>
					{error}
					<button 
						onClick={() => setError(null)}
						style={{ float: 'right', background: 'none', border: 'none', cursor: 'pointer' }}
					>
						×
					</button>
				</div>
			)}

			{/* 统计信息 */}
			{stats && (
				<div className="stats-section" style={{ marginBottom: '24px' }}>
					<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
						<Card>
							<h4>总任务数</h4>
							<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#3b82f6' }}>
								{stats.totalTasks}
							</div>
						</Card>
						<Card>
							<h4>已完成</h4>
							<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
								{stats.completedTasks}
							</div>
						</Card>
						<Card>
							<h4>进行中</h4>
							<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f59e0b' }}>
								{stats.inProgressTasks}
							</div>
						</Card>
						<Card>
							<h4>逾期任务</h4>
							<div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ef4444' }}>
								{stats.overdueTasks}
							</div>
						</Card>
					</div>
				</div>
			)}

			{/* 工具栏 */}
			<div className="toolbar" style={{ 
				display: 'flex', 
				justifyContent: 'space-between', 
				alignItems: 'center', 
				marginBottom: '24px',
				flexWrap: 'wrap',
				gap: '12px'
			}}>
				<div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
					<button 
						onClick={() => setShowCreateForm(true)}
						style={{
							background: '#3b82f6',
							color: 'white',
							border: 'none',
							padding: '8px 16px',
							borderRadius: '6px',
							cursor: 'pointer'
						}}
					>
						创建任务
					</button>
					
					{selectedTasks.size > 0 && (
						<>
							<span style={{ color: '#6b7280' }}>
								已选择 {selectedTasks.size} 个任务
							</span>
							<button 
								onClick={() => handleBatchStatusUpdate(TaskStatus.COMPLETED)}
								style={{
									background: '#10b981',
									color: 'white',
									border: 'none',
									padding: '6px 12px',
									borderRadius: '4px',
									cursor: 'pointer',
									fontSize: '14px'
								}}
							>
								标记完成
							</button>
							<button 
								onClick={handleBatchDelete}
								style={{
									background: '#ef4444',
									color: 'white',
									border: 'none',
									padding: '6px 12px',
									borderRadius: '4px',
									cursor: 'pointer',
									fontSize: '14px'
								}}
							>
								批量删除
							</button>
						</>
					)}
				</div>

				<button 
					onClick={loadTasks}
					disabled={loading}
					style={{
						background: '#f3f4f6',
						border: '1px solid #d1d5db',
						padding: '8px 16px',
						borderRadius: '6px',
						cursor: loading ? 'not-allowed' : 'pointer'
					}}
				>
					{loading ? '加载中...' : '刷新'}
				</button>
			</div>

			{/* 过滤器 */}
			<Card style={{ marginBottom: '24px' }}>
				<h4 style={{ marginBottom: '16px' }}>筛选和搜索</h4>
				<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
					<div>
						<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
							搜索
						</label>
						<input
							type="text"
							placeholder="搜索任务标题或描述..."
							value={filters.searchText || ''}
							onChange={(e) => setFilters({ ...filters, searchText: e.target.value || undefined })}
							style={{
								width: '100%',
								padding: '8px 12px',
								border: '1px solid #d1d5db',
								borderRadius: '6px'
							}}
						/>
					</div>

					<div>
						<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
							状态
						</label>
						<select
							value={filters.status || ''}
							onChange={(e) => setFilters({ ...filters, status: e.target.value as TaskStatus || undefined })}
							style={{
								width: '100%',
								padding: '8px 12px',
								border: '1px solid #d1d5db',
								borderRadius: '6px'
							}}
						>
							<option value="">全部状态</option>
							<option value={TaskStatus.TODO}>待办</option>
							<option value={TaskStatus.IN_PROGRESS}>进行中</option>
							<option value={TaskStatus.BLOCKED}>阻塞</option>
							<option value={TaskStatus.REVIEW}>审核</option>
							<option value={TaskStatus.COMPLETED}>已完成</option>
							<option value={TaskStatus.CANCELLED}>已取消</option>
						</select>
					</div>

					<div>
						<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
							优先级
						</label>
						<select
							value={filters.priority || ''}
							onChange={(e) => setFilters({ ...filters, priority: e.target.value as Priority || undefined })}
							style={{
								width: '100%',
								padding: '8px 12px',
								border: '1px solid #d1d5db',
								borderRadius: '6px'
							}}
						>
							<option value="">全部优先级</option>
							<option value={Priority.LOW}>低</option>
							<option value={Priority.MEDIUM}>中</option>
							<option value={Priority.HIGH}>高</option>
							<option value={Priority.CRITICAL}>紧急</option>
						</select>
					</div>

					<div>
						<label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', fontWeight: '500' }}>
							负责人
						</label>
						<input
							type="text"
							placeholder="负责人..."
							value={filters.assignee || ''}
							onChange={(e) => setFilters({ ...filters, assignee: e.target.value || undefined })}
							style={{
								width: '100%',
								padding: '8px 12px',
								border: '1px solid #d1d5db',
								borderRadius: '6px'
							}}
						/>
					</div>
				</div>

				{/* 清除过滤器 */}
				<div style={{ marginTop: '16px' }}>
					<button
						onClick={() => setFilters({})}
						style={{
							background: '#f3f4f6',
							border: '1px solid #d1d5db',
							padding: '6px 12px',
							borderRadius: '4px',
							cursor: 'pointer',
							fontSize: '14px'
						}}
					>
						清除筛选
					</button>
				</div>
			</Card>

			{/* 任务列表 */}
			<Card>
				<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
					<h4>任务列表 ({filteredTasks.length})</h4>
					<div>
						<label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '14px' }}>
							<input
								type="checkbox"
								checked={filteredTasks.length > 0 && selectedTasks.size === filteredTasks.length}
								onChange={(e) => handleSelectAll(e.target.checked)}
							/>
							全选
						</label>
					</div>
				</div>

				{filteredTasks.length === 0 ? (
					<div style={{ textAlign: 'center', padding: '32px', color: '#6b7280' }}>
						{tasks.length === 0 ? '暂无任务，点击"创建任务"开始' : '没有符合条件的任务'}
					</div>
				) : (
					<div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
						{filteredTasks.map(task => (
							<div
								key={task.id}
								style={{
									border: '1px solid #e5e7eb',
									borderRadius: '8px',
									padding: '16px',
									background: selectedTasks.has(task.id) ? '#f0f9ff' : 'white'
								}}
							>
								<div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
									<input
										type="checkbox"
										checked={selectedTasks.has(task.id)}
										onChange={(e) => handleTaskSelect(task.id, e.target.checked)}
										style={{ marginTop: '4px' }}
									/>
									
									<div style={{ flex: 1 }}>
										<div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
											<h5 style={{ margin: 0, fontSize: '16px' }}>{task.title}</h5>
											<span
												style={{
													background: getPriorityColor(task.priority),
													color: 'white',
													padding: '2px 8px',
													borderRadius: '12px',
													fontSize: '12px'
												}}
											>
												{getPriorityText(task.priority)}
											</span>
											<span
												style={{
													background: task.status === TaskStatus.COMPLETED ? '#10b981' : 
													         task.status === TaskStatus.IN_PROGRESS ? '#f59e0b' : '#6b7280',
													color: 'white',
													padding: '2px 8px',
													borderRadius: '12px',
													fontSize: '12px'
												}}
											>
												{getStatusText(task.status)}
											</span>
										</div>

										{task.description && (
											<p style={{ margin: '0 0 8px 0', color: '#6b7280', fontSize: '14px' }}>
												{task.description}
											</p>
										)}

										<div style={{ display: 'flex', gap: '16px', fontSize: '14px', color: '#6b7280' }}>
											{task.assignee && <span>负责人: {task.assignee}</span>}
											{task.dueDate && (
												<span>
													截止: {new Date(task.dueDate).toLocaleDateString()}
												</span>
											)}
											{task.estimatedHours && <span>预估: {task.estimatedHours}h</span>}
										</div>

										{task.tags.length > 0 && (
											<div style={{ marginTop: '8px' }}>
												{task.tags.map(tag => (
													<span
														key={tag}
														style={{
															background: '#f3f4f6',
															color: '#374151',
															padding: '2px 6px',
															borderRadius: '4px',
															fontSize: '12px',
															marginRight: '4px'
														}}
													>
														#{tag}
													</span>
												))}
											</div>
										)}
									</div>

									<div style={{ display: 'flex', gap: '8px' }}>
										<select
											value={task.status}
											onChange={(e) => handleUpdateTask(task.id, { status: e.target.value as TaskStatus })}
											style={{
												padding: '4px 8px',
												border: '1px solid #d1d5db',
												borderRadius: '4px',
												fontSize: '12px'
											}}
										>
											<option value={TaskStatus.TODO}>待办</option>
											<option value={TaskStatus.IN_PROGRESS}>进行中</option>
											<option value={TaskStatus.BLOCKED}>阻塞</option>
											<option value={TaskStatus.REVIEW}>审核</option>
											<option value={TaskStatus.COMPLETED}>已完成</option>
											<option value={TaskStatus.CANCELLED}>已取消</option>
										</select>
										
										<button
											onClick={() => handleDeleteTask(task.id)}
											style={{
												background: '#fee2e2',
												color: '#dc2626',
												border: 'none',
												padding: '4px 8px',
												borderRadius: '4px',
												cursor: 'pointer',
												fontSize: '12px'
											}}
										>
											删除
										</button>
									</div>
								</div>
							</div>
						))}
					</div>
				)}
			</Card>

			{/* 创建任务表单模态框 */}
			{showCreateForm && (
				<div style={{
					position: 'fixed',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					background: 'rgba(0, 0, 0, 0.5)',
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					zIndex: 1000
				}}>
					<div style={{
						background: 'white',
						borderRadius: '8px',
						padding: '24px',
						width: '90%',
						maxWidth: '600px',
						maxHeight: '90vh',
						overflow: 'auto'
					}}>
						<h3 style={{ marginTop: 0 }}>创建新任务</h3>
						
						<form onSubmit={handleCreateTask}>
							<div style={{ display: 'grid', gap: '16px' }}>
								<div>
									<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
										任务标题 *
									</label>
									<input
										type="text"
										required
										value={formData.title}
										onChange={(e) => setFormData({ ...formData, title: e.target.value })}
										style={{
											width: '100%',
											padding: '8px 12px',
											border: '1px solid #d1d5db',
											borderRadius: '6px'
										}}
									/>
								</div>

								<div>
									<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
										描述
									</label>
									<textarea
										value={formData.description}
										onChange={(e) => setFormData({ ...formData, description: e.target.value })}
										rows={3}
										style={{
											width: '100%',
											padding: '8px 12px',
											border: '1px solid #d1d5db',
											borderRadius: '6px',
											resize: 'vertical'
										}}
									/>
								</div>

								<div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
									<div>
										<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
											优先级
										</label>
										<select
											value={formData.priority}
											onChange={(e) => setFormData({ ...formData, priority: e.target.value as Priority })}
											style={{
												width: '100%',
												padding: '8px 12px',
												border: '1px solid #d1d5db',
												borderRadius: '6px'
											}}
										>
											<option value={Priority.LOW}>低</option>
											<option value={Priority.MEDIUM}>中</option>
											<option value={Priority.HIGH}>高</option>
											<option value={Priority.CRITICAL}>紧急</option>
										</select>
									</div>

									<div>
										<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
											负责人
										</label>
										<input
											type="text"
											value={formData.assignee}
											onChange={(e) => setFormData({ ...formData, assignee: e.target.value })}
											style={{
												width: '100%',
												padding: '8px 12px',
												border: '1px solid #d1d5db',
												borderRadius: '6px'
											}}
										/>
									</div>
								</div>

								<div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
									<div>
										<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
											预估工时
										</label>
										<input
											type="number"
											min="0"
											step="0.5"
											value={formData.estimatedHours}
											onChange={(e) => setFormData({ ...formData, estimatedHours: parseFloat(e.target.value) || 0 })}
											style={{
												width: '100%',
												padding: '8px 12px',
												border: '1px solid #d1d5db',
												borderRadius: '6px'
											}}
										/>
									</div>

									<div>
										<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
											开始日期
										</label>
										<input
											type="date"
											value={formData.startDate}
											onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
											style={{
												width: '100%',
												padding: '8px 12px',
												border: '1px solid #d1d5db',
												borderRadius: '6px'
											}}
										/>
									</div>

									<div>
										<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
											截止日期
										</label>
										<input
											type="date"
											value={formData.dueDate}
											onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
											style={{
												width: '100%',
												padding: '8px 12px',
												border: '1px solid #d1d5db',
												borderRadius: '6px'
											}}
										/>
									</div>
								</div>

								<div>
									<label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>
										标签 (用逗号分隔)
									</label>
									<input
										type="text"
										placeholder="例如: 重要, 紧急, 开发"
										value={formData.tags}
										onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
										style={{
											width: '100%',
											padding: '8px 12px',
											border: '1px solid #d1d5db',
											borderRadius: '6px'
										}}
									/>
								</div>
							</div>

							<div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px', marginTop: '24px' }}>
								<button
									type="button"
									onClick={() => setShowCreateForm(false)}
									style={{
										background: '#f3f4f6',
										border: '1px solid #d1d5db',
										padding: '8px 16px',
										borderRadius: '6px',
										cursor: 'pointer'
									}}
								>
									取消
								</button>
								<button
									type="submit"
									disabled={loading}
									style={{
										background: '#3b82f6',
										color: 'white',
										border: 'none',
										padding: '8px 16px',
										borderRadius: '6px',
										cursor: loading ? 'not-allowed' : 'pointer'
									}}
								>
									{loading ? '创建中...' : '创建任务'}
								</button>
							</div>
						</form>
					</div>
				</div>
			)}
		</div>
	);
};