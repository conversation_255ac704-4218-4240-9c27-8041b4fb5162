// 简化版任务列表视图组件，用于诊断问题

import React, { useState, useEffect } from 'react';
import { Task, Project } from '../../../models';
import { PTMManager } from '../../../services/PTMManager';

export interface SimpleTaskListViewComponentProps {
	ptmManager: PTMManager;
	onTaskSelect?: (taskId: string) => void;
	onTaskCreate?: () => void;
	onTaskEdit?: (taskId: string) => void;
	onTaskDelete?: (taskId: string) => void;
}

export const SimpleTaskListViewComponent: React.FC<SimpleTaskListViewComponentProps> = ({
	ptmManager,
	onTaskSelect,
	onTaskCreate,
	onTaskEdit,
	onTaskDelete
}) => {
	const [tasks, setTasks] = useState<Task[]>([]);
	const [projects, setProjects] = useState<Project[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 加载数据
	useEffect(() => {
		loadData();
	}, [ptmManager]);

	const loadData = async () => {
		try {
			console.log('SimpleTaskListViewComponent: Starting to load data...');
			setLoading(true);
			setError(null);

			if (!ptmManager) {
				throw new Error('PTMManager 未提供');
			}

			const projectManager = ptmManager.getProjectManager();
			const taskRepository = ptmManager.getTaskRepository();

			if (!projectManager || !taskRepository) {
				throw new Error('管理器未初始化');
			}

			const [allProjects, allTasks] = await Promise.all([
				projectManager.getAllProjects(),
				taskRepository.findAll()
			]);

			console.log('SimpleTaskListViewComponent: Loaded', allProjects.length, 'projects and', allTasks.length, 'tasks');

			setProjects(allProjects);
			setTasks(allTasks);
		} catch (err) {
			console.error('Error loading task list data:', err);
			setError('加载任务数据失败');
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div style={{ padding: '2rem', textAlign: 'center' }}>
				<p style={{ color: 'var(--text-muted)' }}>
					加载任务数据...
				</p>
			</div>
		);
	}

	if (error) {
		return (
			<div style={{ padding: '2rem', textAlign: 'center' }}>
				<p style={{ color: 'var(--text-error)', marginBottom: '1rem' }}>
					{error}
				</p>
				<button 
					onClick={loadData}
					style={{
						padding: '0.5rem 1rem',
						backgroundColor: 'var(--interactive-accent)',
						color: 'var(--text-on-accent)',
						border: 'none',
						borderRadius: '0.25rem',
						cursor: 'pointer'
					}}
				>
					重新加载
				</button>
			</div>
		);
	}

	return (
		<div style={{ padding: '1rem' }}>
			<h1 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 600, color: 'var(--text-normal)' }}>
				任务列表 (简化版)
			</h1>
			<p style={{ margin: '0.25rem 0 1rem 0', color: 'var(--text-muted)' }}>
				管理所有项目的任务
			</p>

			{/* 统计信息 */}
			<div style={{ 
				display: 'grid', 
				gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
				gap: '1rem', 
				marginBottom: '2rem' 
			}}>
				<div style={{ 
					padding: '1rem', 
					backgroundColor: 'var(--background-primary)', 
					border: '1px solid var(--background-modifier-border)', 
					borderRadius: '0.5rem',
					textAlign: 'center'
				}}>
					<div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--text-normal)' }}>
						{projects.length}
					</div>
					<div style={{ fontSize: '0.875rem', color: 'var(--text-muted)', marginTop: '0.25rem' }}>
						项目数量
					</div>
				</div>

				<div style={{ 
					padding: '1rem', 
					backgroundColor: 'var(--background-primary)', 
					border: '1px solid var(--background-modifier-border)', 
					borderRadius: '0.5rem',
					textAlign: 'center'
				}}>
					<div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--text-normal)' }}>
						{tasks.length}
					</div>
					<div style={{ fontSize: '0.875rem', color: 'var(--text-muted)', marginTop: '0.25rem' }}>
						任务总数
					</div>
				</div>
			</div>

			{/* 简单的任务列表 */}
			<div style={{ 
				backgroundColor: 'var(--background-primary)', 
				border: '1px solid var(--background-modifier-border)', 
				borderRadius: '0.5rem',
				overflow: 'hidden'
			}}>
				<div style={{ 
					padding: '1rem', 
					borderBottom: '1px solid var(--background-modifier-border)', 
					backgroundColor: 'var(--background-secondary)'
				}}>
					<h3 style={{ margin: 0, fontSize: '1rem', fontWeight: 600 }}>
						任务列表 ({tasks.length})
					</h3>
				</div>

				<div style={{ maxHeight: '400px', overflowY: 'auto' }}>
					{tasks.length === 0 ? (
						<div style={{ 
							padding: '2rem', 
							textAlign: 'center', 
							color: 'var(--text-muted)' 
						}}>
							<p>没有找到任务</p>
						</div>
					) : (
						tasks.slice(0, 10).map(task => (
							<div 
								key={task.id}
								style={{ 
									padding: '0.75rem 1rem', 
									borderBottom: '1px solid var(--background-modifier-border)',
									cursor: 'pointer'
								}}
								onClick={() => onTaskSelect?.(task.id)}
								onMouseEnter={(e) => {
									e.currentTarget.style.backgroundColor = 'var(--background-modifier-hover)';
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.backgroundColor = 'transparent';
								}}
							>
								<div style={{ 
									fontSize: '0.875rem', 
									fontWeight: 500, 
									color: 'var(--text-normal)',
									marginBottom: '0.25rem'
								}}>
									{task.title}
								</div>
								<div style={{ 
									fontSize: '0.75rem', 
									color: 'var(--text-muted)' 
								}}>
									状态: {task.status} | 优先级: {task.priority}
								</div>
							</div>
						))
					)}
					{tasks.length > 10 && (
						<div style={{ 
							padding: '1rem', 
							textAlign: 'center', 
							color: 'var(--text-muted)',
							fontSize: '0.875rem'
						}}>
							显示前 10 个任务，共 {tasks.length} 个任务
						</div>
					)}
				</div>
			</div>

			{/* 操作按钮 */}
			{onTaskCreate && (
				<div style={{ marginTop: '1rem' }}>
					<button 
						onClick={onTaskCreate}
						style={{
							padding: '0.5rem 1rem',
							backgroundColor: 'var(--interactive-accent)',
							color: 'var(--text-on-accent)',
							border: 'none',
							borderRadius: '0.25rem',
							cursor: 'pointer',
							fontWeight: 500
						}}
					>
						新建任务
					</button>
				</div>
			)}
		</div>
	);
};