// 交互式数据探索组件

import React, { useState, useCallback, useMemo } from 'react';
import { 
	ChartSeries, 
	ChartDataPoint, 
	ChartType, 
	ChartFilter, 
	ChartUtils,
	CHART_COLORS 
} from './ChartTypes';
import { EnhancedChart } from './EnhancedChart';
import { Button } from '../common/Button';
import { Card } from '../common/Card';

interface DataExplorerProps {
	data: ChartSeries[];
	title?: string;
	enableDrilldown?: boolean;
	enableCrossfilter?: boolean;
	availableChartTypes?: ChartType[];
	onDataChange?: (data: ChartSeries[]) => void;
}

interface DrilldownState {
	level: number;
	path: string[];
	data: ChartSeries[];
}

export const InteractiveDataExplorer: React.FC<DataExplorerProps> = ({
	data,
	title = '数据探索器',
	enableDrilldown = true,
	enableCrossfilter = true,
	availableChartTypes = [
		ChartType.BAR,
		ChartType.LINE,
		ChartType.PIE,
		ChartType.SCATTER,
		ChartType.HEATMAP
	],
	onDataChange
}) => {
	const [currentChartType, setCurrentChartType] = useState<ChartType>(availableChartTypes[0]);
	const [activeFilters, setActiveFilters] = useState<ChartFilter[]>([]);
	const [selectedPoints, setSelectedPoints] = useState<ChartDataPoint[]>([]);
	const [drilldownState, setDrilldownState] = useState<DrilldownState>({
		level: 0,
		path: [],
		data: data
	});
	const [groupByField, setGroupByField] = useState<string>('');
	const [sortBy, setSortBy] = useState<'label' | 'value'>('value');
	const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
	const [showStatistics, setShowStatistics] = useState(true);
	const [showTrendline, setShowTrendline] = useState(false);

	// 获取所有可用的字段用于分组和筛选
	const availableFields = useMemo(() => {
		const fields = new Set<string>();
		data.forEach(series => {
			series.data.forEach(point => {
				if (point.metadata) {
					Object.keys(point.metadata).forEach(key => fields.add(key));
				}
			});
		});
		return Array.from(fields);
	}, [data]);

	// 处理过滤和分组后的数据
	const processedData = useMemo(() => {
		let result = drilldownState.data;

		// 应用过滤器
		if (activeFilters.length > 0) {
			result = result.map(series => ({
				...series,
				data: ChartUtils.applyFilters(series.data, activeFilters)
			}));
		}

		// 应用分组
		if (groupByField) {
			result = result.map(series => {
				const grouped = ChartUtils.groupByField(series.data, groupByField);
				const newData = Object.entries(grouped).map(([key, points]) => ({
					label: key,
					value: points.reduce((sum, point) => sum + point.value, 0),
					metadata: { groupedBy: groupByField, count: points.length }
				}));
				
				return {
					...series,
					data: ChartUtils.sortData(newData, sortBy, sortOrder)
				};
			});
		} else {
			// 应用排序
			result = result.map(series => ({
				...series,
				data: ChartUtils.sortData(series.data, sortBy, sortOrder)
			}));
		}

		return result;
	}, [drilldownState.data, activeFilters, groupByField, sortBy, sortOrder]);

	// 计算统计信息
	const statistics = useMemo(() => {
		const allValues = processedData.flatMap(series => series.data.map(point => point.value));
		if (allValues.length === 0) return null;

		const sum = allValues.reduce((acc, val) => acc + val, 0);
		const mean = sum / allValues.length;
		const sortedValues = [...allValues].sort((a, b) => a - b);
		const median = sortedValues.length % 2 === 0
			? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
			: sortedValues[Math.floor(sortedValues.length / 2)];
		const min = Math.min(...allValues);
		const max = Math.max(...allValues);
		const variance = allValues.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / allValues.length;
		const stdDev = Math.sqrt(variance);

		return {
			count: allValues.length,
			sum,
			mean,
			median,
			min,
			max,
			stdDev,
			variance
		};
	}, [processedData]);

	// 趋势线数据
	const trendlineData = useMemo(() => {
		if (!showTrendline || processedData.length === 0) return null;
		
		const firstSeries = processedData[0];
		if (firstSeries.data.length < 2) return null;

		const trend = ChartUtils.calculateTrendline(firstSeries.data);
		const trendPoints = firstSeries.data.map((point, index) => ({
			label: point.label,
			value: trend.slope * index + trend.intercept,
			metadata: { isTrendline: true }
		}));

		return {
			name: '趋势线',
			data: trendPoints,
			color: '#ff6b6b',
			type: ChartType.LINE
		};
	}, [processedData, showTrendline]);

	// 图表配置
	const chartConfig = useMemo(() => ({
		type: currentChartType,
		title: title,
		responsive: true,
		interactive: true,
		showLegend: processedData.length > 1 || (trendlineData && showTrendline),
		showTooltip: true,
		showGrid: true,
		showDataLabels: currentChartType === ChartType.PIE || currentChartType === ChartType.DONUT,
		enableZoom: currentChartType === ChartType.LINE || currentChartType === ChartType.SCATTER,
		multiSelect: true,
		exportEnabled: true,
		drilldown: enableDrilldown,
		crossfilter: enableCrossfilter,
		filters: activeFilters
	}), [currentChartType, title, processedData.length, trendlineData, showTrendline, enableDrilldown, enableCrossfilter, activeFilters]);

	// 最终的图表数据（包含趋势线）
	const finalChartData = useMemo(() => {
		const result = [...processedData];
		if (trendlineData && showTrendline) {
			result.push(trendlineData);
		}
		return result;
	}, [processedData, trendlineData, showTrendline]);

	// 事件处理
	const handleChartTypeChange = useCallback((type: ChartType) => {
		setCurrentChartType(type);
	}, []);

	const handleFilterAdd = useCallback((filter: ChartFilter) => {
		setActiveFilters(prev => [...prev, filter]);
	}, []);

	const handleFilterRemove = useCallback((index: number) => {
		setActiveFilters(prev => prev.filter((_, i) => i !== index));
	}, []);

	const handleFilterClear = useCallback(() => {
		setActiveFilters([]);
	}, []);

	const handleDrillDown = useCallback((point: ChartDataPoint) => {
		if (!enableDrilldown) return;

		// 简化的钻取实现
		const newPath = [...drilldownState.path, point.label];
		const filteredData = data.map(series => ({
			...series,
			data: series.data.filter(p => 
				p.metadata && 
				Object.values(p.metadata).some(value => 
					String(value).toLowerCase().includes(point.label.toLowerCase())
				)
			)
		}));

		setDrilldownState({
			level: drilldownState.level + 1,
			path: newPath,
			data: filteredData
		});
	}, [enableDrilldown, drilldownState, data]);

	const handleDrillUp = useCallback(() => {
		if (drilldownState.level === 0) return;

		const newPath = drilldownState.path.slice(0, -1);
		setDrilldownState({
			level: Math.max(0, drilldownState.level - 1),
			path: newPath,
			data: newPath.length === 0 ? data : drilldownState.data // 简化实现
		});
	}, [drilldownState, data]);

	const handleSelectionChange = useCallback((selection: ChartDataPoint[]) => {
		setSelectedPoints(selection);
	}, []);

	const handleExport = useCallback((format: 'png' | 'svg' | 'pdf' | 'csv') => {
		console.log(`导出格式: ${format}`);
		// 实际的导出逻辑会在这里实现
	}, []);

	return (
		<div className="ptm-data-explorer">
			{/* 控制面板 */}
			<Card className="ptm-explorer-controls">
				<div className="ptm-controls-row">
					{/* 图表类型选择 */}
					<div className="ptm-control-group">
						<label>图表类型:</label>
						<select 
							value={currentChartType} 
							onChange={(e) => handleChartTypeChange(e.target.value as ChartType)}
							className="ptm-select"
						>
							{availableChartTypes.map(type => (
								<option key={type} value={type}>
									{getChartTypeLabel(type)}
								</option>
							))}
						</select>
					</div>

					{/* 分组字段 */}
					{availableFields.length > 0 && (
						<div className="ptm-control-group">
							<label>分组字段:</label>
							<select 
								value={groupByField} 
								onChange={(e) => setGroupByField(e.target.value)}
								className="ptm-select"
							>
								<option value="">无分组</option>
								{availableFields.map(field => (
									<option key={field} value={field}>{field}</option>
								))}
							</select>
						</div>
					)}

					{/* 排序选项 */}
					<div className="ptm-control-group">
						<label>排序:</label>
						<select 
							value={sortBy} 
							onChange={(e) => setSortBy(e.target.value as 'label' | 'value')}
							className="ptm-select"
						>
							<option value="value">按值</option>
							<option value="label">按标签</option>
						</select>
						<select 
							value={sortOrder} 
							onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
							className="ptm-select"
						>
							<option value="desc">降序</option>
							<option value="asc">升序</option>
						</select>
					</div>

					{/* 显示选项 */}
					<div className="ptm-control-group">
						<label>
							<input 
								type="checkbox" 
								checked={showStatistics}
								onChange={(e) => setShowStatistics(e.target.checked)}
							/>
							显示统计
						</label>
						<label>
							<input 
								type="checkbox" 
								checked={showTrendline}
								onChange={(e) => setShowTrendline(e.target.checked)}
							/>
							显示趋势线
						</label>
					</div>
				</div>

				{/* 过滤器 */}
				<div className="ptm-filters-section">
					<div className="ptm-filters-header">
						<h4>过滤器</h4>
						{activeFilters.length > 0 && (
							<Button onClick={handleFilterClear} size="small" variant="secondary">
								清除所有
							</Button>
						)}
					</div>
					
					{activeFilters.length > 0 && (
						<div className="ptm-active-filters">
							{activeFilters.map((filter, index) => (
								<div key={index} className="ptm-filter-tag">
									<span>{filter.field} {filter.operator} {String(filter.value)}</span>
									<button onClick={() => handleFilterRemove(index)}>×</button>
								</div>
							))}
						</div>
					)}

					{/* 简化的过滤器添加界面 */}
					{availableFields.length > 0 && (
						<div className="ptm-add-filter">
							<select className="ptm-select">
								<option value="">选择字段添加过滤器</option>
								{availableFields.map(field => (
									<option key={field} value={field}>{field}</option>
								))}
							</select>
						</div>
					)}
				</div>

				{/* 钻取路径 */}
				{drilldownState.path.length > 0 && (
					<div className="ptm-breadcrumb">
						<span>钻取路径: </span>
						{drilldownState.path.map((step, index) => (
							<span key={index} className="ptm-breadcrumb-item">
								{index > 0 && ' > '}
								{step}
							</span>
						))}
					</div>
				)}
			</Card>

			{/* 主图表 */}
			<Card className="ptm-main-chart">
				<EnhancedChart
					data={finalChartData}
					config={chartConfig}
					onDrillDown={handleDrillDown}
					onDrillUp={drilldownState.level > 0 ? handleDrillUp : undefined}
					onSelectionChange={handleSelectionChange}
					onExport={handleExport}
				/>
			</Card>

			{/* 侧边信息面板 */}
			<div className="ptm-info-panels">
				{/* 统计信息 */}
				{showStatistics && statistics && (
					<Card className="ptm-statistics-panel">
						<h4>统计信息</h4>
						<div className="ptm-stats-grid">
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">数据点数:</span>
								<span className="ptm-stat-value">{statistics.count}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">总和:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.sum)}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">平均值:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.mean)}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">中位数:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.median)}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">最小值:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.min)}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">最大值:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.max)}</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-label">标准差:</span>
								<span className="ptm-stat-value">{ChartUtils.formatValue(statistics.stdDev)}</span>
							</div>
						</div>
					</Card>
				)}

				{/* 选中数据详情 */}
				{selectedPoints.length > 0 && (
					<Card className="ptm-selection-panel">
						<h4>选中数据 ({selectedPoints.length})</h4>
						<div className="ptm-selected-items">
							{selectedPoints.map((point, index) => (
								<div key={index} className="ptm-selected-item">
									<div className="ptm-item-label">{point.label}</div>
									<div className="ptm-item-value">{ChartUtils.formatValue(point.value)}</div>
									{point.metadata && (
										<div className="ptm-item-metadata">
											{Object.entries(point.metadata).map(([key, value]) => (
												<div key={key} className="ptm-metadata-item">
													{key}: {String(value)}
												</div>
											))}
										</div>
									)}
								</div>
							))}
						</div>
						<div className="ptm-selection-summary">
							<strong>
								选中总值: {ChartUtils.formatValue(
									selectedPoints.reduce((sum, point) => sum + point.value, 0)
								)}
							</strong>
						</div>
					</Card>
				)}
			</div>

			{/* jsx样式已移除 */}
			{/*
				.ptm-data-explorer {
					display: flex;
					flex-direction: column;
					gap: 16px;
					height: 100%;
				}

				.ptm-explorer-controls {
					flex-shrink: 0;
				}

				.ptm-controls-row {
					display: flex;
					gap: 16px;
					align-items: end;
					flex-wrap: wrap;
					margin-bottom: 16px;
				}

				.ptm-control-group {
					display: flex;
					flex-direction: column;
					gap: 4px;
					min-width: 120px;
				}

				.ptm-control-group label {
					font-size: 12px;
					font-weight: 500;
					color: var(--text-normal);
				}

				.ptm-select {
					padding: 4px 8px;
					border: 1px solid var(--background-modifier-border);
					border-radius: 4px;
					background: var(--background-primary);
					color: var(--text-normal);
					font-size: 12px;
				}

				.ptm-filters-section {
					border-top: 1px solid var(--background-modifier-border);
					padding-top: 12px;
				}

				.ptm-filters-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8px;
				}

				.ptm-filters-header h4 {
					margin: 0;
					font-size: 14px;
					font-weight: 500;
				}

				.ptm-active-filters {
					display: flex;
					gap: 8px;
					flex-wrap: wrap;
					margin-bottom: 8px;
				}

				.ptm-filter-tag {
					display: flex;
					align-items: center;
					gap: 4px;
					background: var(--background-modifier-hover);
					border: 1px solid var(--background-modifier-border);
					border-radius: 12px;
					padding: 2px 8px;
					font-size: 11px;
				}

				.ptm-filter-tag button {
					background: none;
					border: none;
					color: var(--text-muted);
					cursor: pointer;
					font-size: 14px;
					line-height: 1;
				}

				.ptm-breadcrumb {
					font-size: 12px;
					color: var(--text-muted);
					margin-top: 8px;
				}

				.ptm-breadcrumb-item {
					color: var(--text-normal);
				}

				.ptm-main-chart {
					flex: 1;
					min-height: 400px;
				}

				.ptm-info-panels {
					display: flex;
					gap: 16px;
				}

				.ptm-statistics-panel,
				.ptm-selection-panel {
					flex: 1;
					min-width: 200px;
				}

				.ptm-statistics-panel h4,
				.ptm-selection-panel h4 {
					margin: 0 0 12px 0;
					font-size: 14px;
					font-weight: 500;
				}

				.ptm-stats-grid {
					display: grid;
					grid-template-columns: 1fr 1fr;
					gap: 8px;
				}

				.ptm-stat-item {
					display: flex;
					justify-content: space-between;
					font-size: 12px;
				}

				.ptm-stat-label {
					color: var(--text-muted);
				}

				.ptm-stat-value {
					font-weight: 500;
					color: var(--text-normal);
				}

				.ptm-selected-items {
					max-height: 200px;
					overflow-y: auto;
				}

				.ptm-selected-item {
					padding: 8px;
					border: 1px solid var(--background-modifier-border);
					border-radius: 4px;
					margin-bottom: 8px;
					font-size: 12px;
				}

				.ptm-item-label {
					font-weight: 500;
					margin-bottom: 2px;
				}

				.ptm-item-value {
					color: var(--text-accent);
					margin-bottom: 4px;
				}

				.ptm-item-metadata {
					font-size: 11px;
					color: var(--text-muted);
				}

				.ptm-metadata-item {
					margin: 1px 0;
				}

				.ptm-selection-summary {
					margin-top: 8px;
					padding-top: 8px;
					border-top: 1px solid var(--background-modifier-border);
					font-size: 12px;
				}

				@media (max-width: 768px) {
					.ptm-controls-row {
						flex-direction: column;
						align-items: stretch;
					}

					.ptm-info-panels {
						flex-direction: column;
					}
				}
			*/}
		</div>
	);
};

// 辅助函数
function getChartTypeLabel(type: ChartType): string {
	const labels: Record<ChartType, string> = {
		[ChartType.BAR]: '柱状图',
		[ChartType.COLUMN]: '柱状图',
		[ChartType.HORIZONTAL_BAR]: '水平柱状图',
		[ChartType.STACKED_BAR]: '堆叠柱状图',
		[ChartType.STACKED_COLUMN]: '堆叠柱状图',
		[ChartType.LINE]: '折线图',
		[ChartType.MULTI_LINE]: '多线图',
		[ChartType.AREA]: '面积图',
		[ChartType.PIE]: '饼图',
		[ChartType.DONUT]: '环形图',
		[ChartType.SCATTER]: '散点图',
		[ChartType.HEATMAP]: '热力图',
		[ChartType.RADAR]: '雷达图',
		[ChartType.GAUGE]: '仪表盘',
		[ChartType.FUNNEL]: '漏斗图',
		[ChartType.TREEMAP]: '树状图',
		[ChartType.WATERFALL]: '瀑布图',
		[ChartType.COMBO]: '组合图',
		[ChartType.GANTT]: '甘特图',
		[ChartType.BURNDOWN]: '燃尽图'
	};
	
	return labels[type] || type;
}