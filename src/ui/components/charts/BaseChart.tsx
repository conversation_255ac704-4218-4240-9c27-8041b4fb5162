// 基础图表组件

import React, { useRef, useEffect, useState } from 'react';
import { ChartProps, ChartDataPoint, ChartSeries, ChartUtils } from './ChartTypes';

export const BaseChart: React.FC<ChartProps> = ({
	data,
	config,
	onDataPointClick,
	onDrillDown,
	onDrillUp,
	className
}) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const [dimensions, setDimensions] = useState({ width: 400, height: 300 });
	const [hoveredPoint, setHoveredPoint] = useState<{ point: ChartDataPoint; series: ChartSeries; x: number; y: number } | null>(null);

	// 响应式尺寸处理
	useEffect(() => {
		const updateDimensions = () => {
			if (containerRef.current) {
				const { width, height } = containerRef.current.getBoundingClientRect();
				setDimensions({
					width: config.width || width || 400,
					height: config.height || height || 300
				});
			}
		};

		updateDimensions();
		if (config.responsive) {
			window.addEventListener('resize', updateDimensions);
			return () => window.removeEventListener('resize', updateDimensions);
		}
	}, [config.width, config.height, config.responsive]);

	// 绘制图表
	useEffect(() => {
		const canvas = canvasRef.current;
		if (!canvas || !data.length) return;

		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		// 设置画布尺寸
		canvas.width = dimensions.width;
		canvas.height = dimensions.height;

		// 清空画布
		ctx.clearRect(0, 0, dimensions.width, dimensions.height);

		// 根据图表类型绘制
		switch (config.type) {
			case 'pie':
			case 'donut':
				drawPieChart(ctx, data[0], dimensions, config);
				break;
			case 'bar':
				drawBarChart(ctx, data, dimensions, config);
				break;
			case 'line':
			case 'area':
				drawLineChart(ctx, data, dimensions, config);
				break;
			default:
				drawBarChart(ctx, data, dimensions, config);
		}
	}, [data, dimensions, config]);

	// 鼠标事件处理
	const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
		if (!config.interactive) return;

		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const x = event.clientX - rect.left;
		const y = event.clientY - rect.top;

		// 检测鼠标悬停的数据点
		const hoveredData = detectHoveredPoint(x, y, data, dimensions, config);
		setHoveredPoint(hoveredData);
	};

	const handleMouseLeave = () => {
		setHoveredPoint(null);
	};

	const handleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
		if (!config.interactive) return;

		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const x = event.clientX - rect.left;
		const y = event.clientY - rect.top;

		const clickedData = detectHoveredPoint(x, y, data, dimensions, config);
		if (clickedData && onDataPointClick) {
			onDataPointClick(clickedData.point, clickedData.series);
		}

		if (clickedData && config.drilldown && onDrillDown) {
			onDrillDown(clickedData.point);
		}
	};

	return (
		<div 
			ref={containerRef}
			className={`ptm-chart-container ${className || ''}`}
			style={{ position: 'relative', width: '100%', height: '100%' }}
		>
			{/* 图表标题 */}
			{config.title && (
				<div className="ptm-chart-title">
					<h3>{config.title}</h3>
					{config.subtitle && <p>{config.subtitle}</p>}
				</div>
			)}

			{/* 钻取导航 */}
			{config.drilldown && onDrillUp && (
				<div className="ptm-chart-breadcrumb">
					<button onClick={onDrillUp} className="ptm-chart-back-btn">
						← 返回上级
					</button>
				</div>
			)}

			{/* 画布 */}
			<canvas
				ref={canvasRef}
				onMouseMove={handleMouseMove}
				onMouseLeave={handleMouseLeave}
				onClick={handleClick}
				style={{ 
					cursor: config.interactive ? 'pointer' : 'default',
					display: 'block',
					maxWidth: '100%'
				}}
			/>

			{/* 工具提示 */}
			{config.showTooltip && hoveredPoint && (
				<div 
					className="ptm-chart-tooltip"
					style={{
						position: 'absolute',
						left: hoveredPoint.x + 10,
						top: hoveredPoint.y - 10,
						background: 'var(--background-primary)',
						border: '1px solid var(--background-modifier-border)',
						borderRadius: '4px',
						padding: '8px',
						fontSize: '12px',
						pointerEvents: 'none',
						zIndex: 1000,
						boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
					}}
				>
					<div><strong>{hoveredPoint.point.label}</strong></div>
					<div>值: {ChartUtils.formatValue(hoveredPoint.point.value)}</div>
					{hoveredPoint.series.name && (
						<div>系列: {hoveredPoint.series.name}</div>
					)}
				</div>
			)}

			{/* 图例 */}
			{config.showLegend && data.length > 1 && (
				<div className="ptm-chart-legend">
					{data.map((series, index) => (
						<div key={series.name || index} className="ptm-legend-item">
							<div 
								className="ptm-legend-color"
								style={{ 
									backgroundColor: series.color || ChartUtils.getColorByIndex(index)
								}}
							/>
							<span>{series.name || `系列 ${index + 1}`}</span>
						</div>
					))}
				</div>
			)}

			{/* 样式已移至CSS模块或内联样式 */}
		</div>
	);
};

// 绘制饼图
function drawPieChart(
	ctx: CanvasRenderingContext2D, 
	series: ChartSeries, 
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const centerX = width / 2;
	const centerY = height / 2;
	const radius = Math.min(width, height) / 2 - 20;
	const innerRadius = config.type === 'donut' ? radius * 0.5 : 0;

	const total = series.data.reduce((sum, point) => sum + point.value, 0);
	let currentAngle = -Math.PI / 2; // 从顶部开始

	series.data.forEach((point, index) => {
		const sliceAngle = (point.value / total) * 2 * Math.PI;
		const color = point.color || ChartUtils.getColorByIndex(index);

		// 绘制扇形
		ctx.beginPath();
		ctx.moveTo(centerX, centerY);
		ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
		if (innerRadius > 0) {
			ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true);
		}
		ctx.closePath();
		ctx.fillStyle = color;
		ctx.fill();

		// 绘制边框
		ctx.strokeStyle = 'var(--background-primary)';
		ctx.lineWidth = 2;
		ctx.stroke();

		// 绘制标签
		const labelAngle = currentAngle + sliceAngle / 2;
		const labelRadius = radius + 15;
		const labelX = centerX + Math.cos(labelAngle) * labelRadius;
		const labelY = centerY + Math.sin(labelAngle) * labelRadius;

		ctx.fillStyle = 'var(--text-normal)';
		ctx.font = '12px var(--font-interface)';
		ctx.textAlign = 'center';
		ctx.fillText(point.label, labelX, labelY);

		// 绘制百分比
		const percentage = ((point.value / total) * 100).toFixed(1);
		ctx.fillText(`${percentage}%`, labelX, labelY + 14);

		currentAngle += sliceAngle;
	});
}

// 绘制柱状图
function drawBarChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	// 计算最大值
	const maxValue = Math.max(...data.flatMap(series => series.data.map(point => point.value)));
	const scale = chartHeight / maxValue;

	// 绘制网格
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		// 水平网格线
		for (let i = 0; i <= 5; i++) {
			const y = padding + (chartHeight / 5) * i;
			ctx.beginPath();
			ctx.moveTo(padding, y);
			ctx.lineTo(width - padding, y);
			ctx.stroke();
		}
	}

	// 绘制柱子
	const barWidth = chartWidth / (data[0]?.data.length || 1) / data.length * 0.8;
	const groupWidth = chartWidth / (data[0]?.data.length || 1);

	data.forEach((series, seriesIndex) => {
		series.data.forEach((point, pointIndex) => {
			const x = padding + pointIndex * groupWidth + seriesIndex * barWidth;
			const barHeight = point.value * scale;
			const y = height - padding - barHeight;

			const color = point.color || series.color || ChartUtils.getColorByIndex(seriesIndex);

			// 绘制柱子
			ctx.fillStyle = color;
			ctx.fillRect(x, y, barWidth, barHeight);

			// 绘制边框
			ctx.strokeStyle = 'var(--background-modifier-border)';
			ctx.lineWidth = 1;
			ctx.strokeRect(x, y, barWidth, barHeight);

			// 绘制数值标签
			ctx.fillStyle = 'var(--text-normal)';
			ctx.font = '10px var(--font-interface)';
			ctx.textAlign = 'center';
			ctx.fillText(
				ChartUtils.formatValue(point.value),
				x + barWidth / 2,
				y - 5
			);
		});
	});

	// 绘制X轴标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	ctx.textAlign = 'center';
	data[0]?.data.forEach((point, index) => {
		const x = padding + index * groupWidth + groupWidth / 2;
		ctx.fillText(point.label, x, height - 10);
	});

	// 绘制Y轴标签
	ctx.textAlign = 'right';
	for (let i = 0; i <= 5; i++) {
		const value = (maxValue / 5) * (5 - i);
		const y = padding + (chartHeight / 5) * i + 4;
		ctx.fillText(ChartUtils.formatValue(value), padding - 10, y);
	}
}

// 绘制折线图
function drawLineChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	// 计算最大值和最小值
	const allValues = data.flatMap(series => series.data.map(point => point.value));
	const maxValue = Math.max(...allValues);
	const minValue = Math.min(...allValues);
	const valueRange = maxValue - minValue;
	const scale = chartHeight / valueRange;

	// 绘制网格
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		// 水平网格线
		for (let i = 0; i <= 5; i++) {
			const y = padding + (chartHeight / 5) * i;
			ctx.beginPath();
			ctx.moveTo(padding, y);
			ctx.lineTo(width - padding, y);
			ctx.stroke();
		}

		// 垂直网格线
		const pointCount = data[0]?.data.length || 0;
		for (let i = 0; i <= pointCount - 1; i++) {
			const x = padding + (chartWidth / (pointCount - 1)) * i;
			ctx.beginPath();
			ctx.moveTo(x, padding);
			ctx.lineTo(x, height - padding);
			ctx.stroke();
		}
	}

	// 绘制线条和区域
	data.forEach((series, seriesIndex) => {
		const color = series.color || ChartUtils.getColorByIndex(seriesIndex);
		const pointCount = series.data.length;

		if (pointCount === 0) return;

		// 创建路径
		ctx.beginPath();
		series.data.forEach((point, index) => {
			const x = padding + (chartWidth / (pointCount - 1)) * index;
			const y = height - padding - (point.value - minValue) * scale;

			if (index === 0) {
				ctx.moveTo(x, y);
			} else {
				ctx.lineTo(x, y);
			}
		});

		// 绘制区域填充（如果是面积图）
		if (config.type === 'area') {
			ctx.lineTo(padding + chartWidth, height - padding);
			ctx.lineTo(padding, height - padding);
			ctx.closePath();
			ctx.fillStyle = color + '20'; // 添加透明度
			ctx.fill();
		}

		// 绘制线条
		ctx.strokeStyle = color;
		ctx.lineWidth = 2;
		ctx.stroke();

		// 绘制数据点
		series.data.forEach((point, index) => {
			const x = padding + (chartWidth / (pointCount - 1)) * index;
			const y = height - padding - (point.value - minValue) * scale;

			ctx.beginPath();
			ctx.arc(x, y, 4, 0, 2 * Math.PI);
			ctx.fillStyle = color;
			ctx.fill();
			ctx.strokeStyle = 'var(--background-primary)';
			ctx.lineWidth = 2;
			ctx.stroke();
		});
	});

	// 绘制X轴标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	ctx.textAlign = 'center';
	data[0]?.data.forEach((point, index) => {
		const x = padding + (chartWidth / (data[0].data.length - 1)) * index;
		ctx.fillText(point.label, x, height - 10);
	});

	// 绘制Y轴标签
	ctx.textAlign = 'right';
	for (let i = 0; i <= 5; i++) {
		const value = minValue + (valueRange / 5) * (5 - i);
		const y = padding + (chartHeight / 5) * i + 4;
		ctx.fillText(ChartUtils.formatValue(value), padding - 10, y);
	}
}

// 检测鼠标悬停的数据点
function detectHoveredPoint(
	x: number, 
	y: number, 
	data: ChartSeries[], 
	dimensions: { width: number; height: number },
	config: any
): { point: ChartDataPoint; series: ChartSeries; x: number; y: number } | null {
	// 简化实现：返回第一个系列的第一个点作为示例
	if (data.length > 0 && data[0].data.length > 0) {
		return {
			point: data[0].data[0],
			series: data[0],
			x,
			y
		};
	}
	return null;
}