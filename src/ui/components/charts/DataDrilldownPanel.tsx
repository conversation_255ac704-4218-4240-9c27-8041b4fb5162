// 数据钻取面板组件

import React, { useState, useCallback, useMemo } from 'react';
import { 
	ChartSeries, 
	ChartDataPoint, 
	ChartType, 
	ChartFilter, 
	ChartUtils,
	CHART_COLORS 
} from './ChartTypes';
import { EnhancedChart } from './EnhancedChart';
import { Button } from '../common/Button';
import { Card } from '../common/Card';

interface DrilldownLevel {
	id: string;
	title: string;
	data: ChartSeries[];
	filters: ChartFilter[];
	parentPoint?: ChartDataPoint;
}

interface DataDrilldownPanelProps {
	initialData: ChartSeries[];
	title?: string;
	maxLevels?: number;
	onDataChange?: (data: ChartSeries[], level: number) => void;
	onFilterChange?: (filters: ChartFilter[]) => void;
}

export const DataDrilldownPanel: React.FC<DataDrilldownPanelProps> = ({
	initialData,
	title = '数据钻取分析',
	maxLevels = 5,
	onDataChange,
	onFilterChange
}) => {
	const [drilldownStack, setDrilldownStack] = useState<DrilldownLevel[]>([
		{
			id: 'root',
			title: '总览',
			data: initialData,
			filters: []
		}
	]);
	const [currentChartType, setCurrentChartType] = useState<ChartType>(ChartType.BAR);
	const [showComparison, setShowComparison] = useState(false);
	const [selectedPoints, setSelectedPoints] = useState<ChartDataPoint[]>([]);

	const currentLevel = drilldownStack[drilldownStack.length - 1];
	const canDrillDown = drilldownStack.length < maxLevels;
	const canDrillUp = drilldownStack.length > 1;

	// 处理钻取下探
	const handleDrillDown = useCallback((point: ChartDataPoint) => {
		if (!canDrillDown) return;

		// 生成下一级数据
		const nextLevelData = generateDrilldownData(point, currentLevel.data);
		if (!nextLevelData || nextLevelData.length === 0) return;

		const newLevel: DrilldownLevel = {
			id: `level_${drilldownStack.length}`,
			title: `${point.label} - 详细分析`,
			data: nextLevelData,
			filters: [...currentLevel.filters],
			parentPoint: point
		};

		setDrilldownStack(prev => [...prev, newLevel]);
		onDataChange?.(nextLevelData, drilldownStack.length);
	}, [canDrillDown, currentLevel, drilldownStack.length, onDataChange]);

	// 处理钻取上升
	const handleDrillUp = useCallback(() => {
		if (!canDrillUp) return;

		setDrilldownStack(prev => {
			const newStack = prev.slice(0, -1);
			const newCurrentLevel = newStack[newStack.length - 1];
			onDataChange?.(newCurrentLevel.data, newStack.length - 1);
			return newStack;
		});
	}, [canDrillUp, onDataChange]);

	// 跳转到指定层级
	const jumpToLevel = useCallback((levelIndex: number) => {
		if (levelIndex < 0 || levelIndex >= drilldownStack.length) return;

		setDrilldownStack(prev => {
			const newStack = prev.slice(0, levelIndex + 1);
			const newCurrentLevel = newStack[newStack.length - 1];
			onDataChange?.(newCurrentLevel.data, levelIndex);
			return newStack;
		});
	}, [drilldownStack.length, onDataChange]);

	// 重置到根级别
	const resetToRoot = useCallback(() => {
		setDrilldownStack([drilldownStack[0]]);
		onDataChange?.(initialData, 0);
	}, [drilldownStack, initialData, onDataChange]);

	// 添加过滤器
	const addFilter = useCallback((filter: ChartFilter) => {
		setDrilldownStack(prev => {
			const newStack = [...prev];
			const currentIndex = newStack.length - 1;
			newStack[currentIndex] = {
				...newStack[currentIndex],
				filters: [...newStack[currentIndex].filters, filter]
			};
			return newStack;
		});
		onFilterChange?.(currentLevel.filters);
	}, [currentLevel.filters, onFilterChange]);

	// 移除过滤器
	const removeFilter = useCallback((filterIndex: number) => {
		setDrilldownStack(prev => {
			const newStack = [...prev];
			const currentIndex = newStack.length - 1;
			newStack[currentIndex] = {
				...newStack[currentIndex],
				filters: newStack[currentIndex].filters.filter((_, i) => i !== filterIndex)
			};
			return newStack;
		});
	}, []);

	// 应用过滤器后的数据
	const filteredData = useMemo(() => {
		if (currentLevel.filters.length === 0) return currentLevel.data;

		return currentLevel.data.map(series => ({
			...series,
			data: ChartUtils.applyFilters(series.data, currentLevel.filters)
		}));
	}, [currentLevel.data, currentLevel.filters]);

	// 比较数据（当前级别与上一级别）
	const comparisonData = useMemo(() => {
		if (!showComparison || drilldownStack.length < 2) return null;

		const previousLevel = drilldownStack[drilldownStack.length - 2];
		const parentPoint = currentLevel.parentPoint;
		
		if (!parentPoint) return null;

		// 创建比较数据
		return [
			{
				name: '上级数据',
				data: [{ 
					label: parentPoint.label, 
					value: parentPoint.value,
					color: CHART_COLORS.primary[0]
				}]
			},
			{
				name: '当前级别总计',
				data: [{
					label: '总计',
					value: filteredData.reduce((sum, series) => 
						sum + series.data.reduce((seriesSum, point) => seriesSum + point.value, 0), 0
					),
					color: CHART_COLORS.primary[1]
				}]
			}
		];
	}, [showComparison, drilldownStack, currentLevel.parentPoint, filteredData]);

	// 图表配置
	const chartConfig = useMemo(() => ({
		type: currentChartType,
		title: currentLevel.title,
		responsive: true,
		interactive: true,
		showLegend: filteredData.length > 1,
		showTooltip: true,
		showGrid: true,
		drilldown: canDrillDown,
		multiSelect: true,
		exportEnabled: true
	}), [currentChartType, currentLevel.title, filteredData.length, canDrillDown]);

	// 统计信息
	const statistics = useMemo(() => {
		const allValues = filteredData.flatMap(series => series.data.map(point => point.value));
		if (allValues.length === 0) return null;

		const sum = allValues.reduce((acc, val) => acc + val, 0);
		const mean = sum / allValues.length;
		const max = Math.max(...allValues);
		const min = Math.min(...allValues);

		return { count: allValues.length, sum, mean, max, min };
	}, [filteredData]);

	return (
		<div className="ptm-drilldown-panel">
			{/* 面包屑导航 */}
			<Card className="ptm-breadcrumb-card">
				<div className="ptm-breadcrumb-nav">
					<div className="ptm-breadcrumb-path">
						{drilldownStack.map((level, index) => (
							<React.Fragment key={level.id}>
								{index > 0 && <span className="ptm-breadcrumb-separator">›</span>}
								<button
									className={`ptm-breadcrumb-item ${index === drilldownStack.length - 1 ? 'active' : ''}`}
									onClick={() => jumpToLevel(index)}
									disabled={index === drilldownStack.length - 1}
								>
									{level.title}
								</button>
							</React.Fragment>
						))}
					</div>
					
					<div className="ptm-breadcrumb-actions">
						{canDrillUp && (
							<Button onClick={handleDrillUp} size="small" variant="secondary">
								← 返回上级
							</Button>
						)}
						{drilldownStack.length > 1 && (
							<Button onClick={resetToRoot} size="small" variant="secondary">
								回到根级别
							</Button>
						)}
					</div>
				</div>

				{/* 当前级别信息 */}
				<div className="ptm-level-info">
					<div className="ptm-level-stats">
						<span>级别: {drilldownStack.length}</span>
						{statistics && (
							<>
								<span>数据点: {statistics.count}</span>
								<span>总计: {ChartUtils.formatValue(statistics.sum)}</span>
								<span>平均: {ChartUtils.formatValue(statistics.mean)}</span>
							</>
						)}
					</div>
					
					{currentLevel.parentPoint && (
						<div className="ptm-parent-info">
							来源: {currentLevel.parentPoint.label} ({ChartUtils.formatValue(currentLevel.parentPoint.value)})
						</div>
					)}
				</div>
			</Card>

			{/* 控制面板 */}
			<Card className="ptm-controls-card">
				<div className="ptm-controls-row">
					{/* 图表类型选择 */}
					<div className="ptm-control-group">
						<label>图表类型:</label>
						<select 
							value={currentChartType} 
							onChange={(e) => setCurrentChartType(e.target.value as ChartType)}
							className="ptm-select"
						>
							<option value={ChartType.BAR}>柱状图</option>
							<option value={ChartType.PIE}>饼图</option>
							<option value={ChartType.LINE}>折线图</option>
							<option value={ChartType.DONUT}>环形图</option>
							<option value={ChartType.HORIZONTAL_BAR}>水平柱状图</option>
						</select>
					</div>

					{/* 显示选项 */}
					<div className="ptm-control-group">
						<label>
							<input 
								type="checkbox" 
								checked={showComparison}
								onChange={(e) => setShowComparison(e.target.checked)}
							/>
							显示级别对比
						</label>
					</div>
				</div>

				{/* 过滤器 */}
				{currentLevel.filters.length > 0 && (
					<div className="ptm-filters-section">
						<h4>当前过滤器</h4>
						<div className="ptm-active-filters">
							{currentLevel.filters.map((filter, index) => (
								<div key={index} className="ptm-filter-tag">
									<span>{filter.field} {filter.operator} {String(filter.value)}</span>
									<button onClick={() => removeFilter(index)}>×</button>
								</div>
							))}
						</div>
					</div>
				)}
			</Card>

			{/* 主图表 */}
			<Card className="ptm-main-chart-card">
				<EnhancedChart
					data={filteredData}
					config={chartConfig}
					onDrillDown={handleDrillDown}
					onDrillUp={canDrillUp ? handleDrillUp : undefined}
					onSelectionChange={setSelectedPoints}
				/>
			</Card>

			{/* 对比图表 */}
			{showComparison && comparisonData && (
				<Card className="ptm-comparison-card">
					<h4>级别对比</h4>
					<EnhancedChart
						data={comparisonData}
						config={{
							type: ChartType.BAR,
							title: '当前级别与上级对比',
							responsive: true,
							interactive: false,
							showLegend: true,
							showTooltip: true,
							showGrid: true
						}}
					/>
				</Card>
			)}

			{/* 选中数据详情 */}
			{selectedPoints.length > 0 && (
				<Card className="ptm-selection-card">
					<h4>选中数据详情</h4>
					<div className="ptm-selected-items">
						{selectedPoints.map((point, index) => (
							<div key={index} className="ptm-selected-item">
								<div className="ptm-item-header">
									<span className="ptm-item-label">{point.label}</span>
									<span className="ptm-item-value">{ChartUtils.formatValue(point.value)}</span>
								</div>
								
								{point.metadata && (
									<div className="ptm-item-metadata">
										{Object.entries(point.metadata).map(([key, value]) => (
											<div key={key} className="ptm-metadata-row">
												<span className="ptm-metadata-key">{key}:</span>
												<span className="ptm-metadata-value">{String(value)}</span>
											</div>
										))}
									</div>
								)}

								{canDrillDown && (
									<Button 
										onClick={() => handleDrillDown(point)}
										size="small"
										variant="primary"
										className="ptm-drill-button"
									>
										钻取到 {point.label}
									</Button>
								)}
							</div>
						))}
					</div>
				</Card>
			)}

			{/* jsx样式已移除 */}
			{/*
				.ptm-drilldown-panel {
					display: flex;
					flex-direction: column;
					gap: 16px;
				}

				.ptm-breadcrumb-card {
					flex-shrink: 0;
				}

				.ptm-breadcrumb-nav {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12px;
				}

				.ptm-breadcrumb-path {
					display: flex;
					align-items: center;
					gap: 8px;
				}

				.ptm-breadcrumb-item {
					background: none;
					border: none;
					color: var(--text-muted);
					cursor: pointer;
					padding: 4px 8px;
					border-radius: 4px;
					font-size: 14px;
					transition: all 0.2s;
				}

				.ptm-breadcrumb-item:hover:not(:disabled) {
					background: var(--background-modifier-hover);
					color: var(--text-normal);
				}

				.ptm-breadcrumb-item.active {
					color: var(--text-normal);
					font-weight: 500;
					cursor: default;
				}

				.ptm-breadcrumb-item:disabled {
					cursor: default;
				}

				.ptm-breadcrumb-separator {
					color: var(--text-muted);
					font-size: 16px;
				}

				.ptm-breadcrumb-actions {
					display: flex;
					gap: 8px;
				}

				.ptm-level-info {
					padding-top: 12px;
					border-top: 1px solid var(--background-modifier-border);
				}

				.ptm-level-stats {
					display: flex;
					gap: 16px;
					font-size: 12px;
					color: var(--text-muted);
					margin-bottom: 4px;
				}

				.ptm-parent-info {
					font-size: 12px;
					color: var(--text-accent);
				}

				.ptm-controls-card {
					flex-shrink: 0;
				}

				.ptm-controls-row {
					display: flex;
					gap: 16px;
					align-items: end;
					flex-wrap: wrap;
				}

				.ptm-control-group {
					display: flex;
					flex-direction: column;
					gap: 4px;
				}

				.ptm-control-group label {
					font-size: 12px;
					font-weight: 500;
					color: var(--text-normal);
				}

				.ptm-select {
					padding: 4px 8px;
					border: 1px solid var(--background-modifier-border);
					border-radius: 4px;
					background: var(--background-primary);
					color: var(--text-normal);
					font-size: 12px;
				}

				.ptm-filters-section {
					margin-top: 16px;
					padding-top: 16px;
					border-top: 1px solid var(--background-modifier-border);
				}

				.ptm-filters-section h4 {
					margin: 0 0 8px 0;
					font-size: 14px;
					font-weight: 500;
				}

				.ptm-active-filters {
					display: flex;
					gap: 8px;
					flex-wrap: wrap;
				}

				.ptm-filter-tag {
					display: flex;
					align-items: center;
					gap: 4px;
					background: var(--background-modifier-hover);
					border: 1px solid var(--background-modifier-border);
					border-radius: 12px;
					padding: 2px 8px;
					font-size: 11px;
				}

				.ptm-filter-tag button {
					background: none;
					border: none;
					color: var(--text-muted);
					cursor: pointer;
					font-size: 14px;
					line-height: 1;
				}

				.ptm-main-chart-card {
					flex: 1;
					min-height: 400px;
				}

				.ptm-comparison-card h4,
				.ptm-selection-card h4 {
					margin: 0 0 16px 0;
					font-size: 16px;
					font-weight: 600;
				}

				.ptm-selected-items {
					display: flex;
					flex-direction: column;
					gap: 12px;
				}

				.ptm-selected-item {
					padding: 12px;
					border: 1px solid var(--background-modifier-border);
					border-radius: 6px;
					background: var(--background-secondary);
				}

				.ptm-item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8px;
				}

				.ptm-item-label {
					font-weight: 500;
					color: var(--text-normal);
				}

				.ptm-item-value {
					color: var(--text-accent);
					font-weight: 600;
				}

				.ptm-item-metadata {
					margin-bottom: 8px;
				}

				.ptm-metadata-row {
					display: flex;
					justify-content: space-between;
					font-size: 12px;
					margin: 2px 0;
				}

				.ptm-metadata-key {
					color: var(--text-muted);
				}

				.ptm-metadata-value {
					color: var(--text-normal);
				}

				.ptm-drill-button {
					margin-top: 8px;
				}

				@media (max-width: 768px) {
					.ptm-breadcrumb-nav {
						flex-direction: column;
						align-items: stretch;
						gap: 12px;
					}

					.ptm-breadcrumb-actions {
						justify-content: center;
					}

					.ptm-controls-row {
						flex-direction: column;
						align-items: stretch;
					}
				}
			*/}
		</div>
	);
};

// 辅助函数：生成钻取数据
function generateDrilldownData(point: ChartDataPoint, currentData: ChartSeries[]): ChartSeries[] {
	// 简化的钻取数据生成逻辑
	// 实际应用中，这里应该根据业务逻辑生成相应的下级数据
	
	if (!point.metadata) return [];

	// 示例：根据元数据生成子级数据
	const subData: ChartDataPoint[] = [];
	
	// 如果有子项数据
	if (point.metadata.children && Array.isArray(point.metadata.children)) {
		point.metadata.children.forEach((child: any, index: number) => {
			subData.push({
				label: child.name || `子项 ${index + 1}`,
				value: child.value || Math.random() * point.value,
				metadata: child.metadata || {}
			});
		});
	} else {
		// 生成模拟的子级数据
		const subItemCount = Math.min(5, Math.max(2, Math.floor(Math.random() * 6)));
		let remainingValue = point.value;
		
		for (let i = 0; i < subItemCount; i++) {
			const isLast = i === subItemCount - 1;
			const value = isLast ? remainingValue : Math.random() * (remainingValue / (subItemCount - i));
			
			subData.push({
				label: `${point.label} - 子项 ${i + 1}`,
				value: Math.max(0, value),
				metadata: {
					parent: point.label,
					level: 'sub',
					index: i
				}
			});
			
			remainingValue -= value;
		}
	}

	return [{
		name: `${point.label} 详细分析`,
		data: subData
	}];
}