// 增强的图表组件 - 支持更多图表类型和交互功能

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { 
	ChartProps, 
	ChartDataPoint, 
	ChartSeries, 
	ChartUtils, 
	ChartType,
	ChartConfig,
	ChartFilter,
	ChartInteractionState,
	CHART_COLORS
} from './ChartTypes';
import { Button } from '../common/Button';

export const EnhancedChart: React.FC<ChartProps> = ({
	data,
	config,
	onDataPointClick,
	onDrillDown,
	onDrillUp,
	onFilterChange,
	onSelectionChange,
	onZoom,
	onExport,
	className
}) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const [dimensions, setDimensions] = useState({ width: 400, height: 300 });
	const [interactionState, setInteractionState] = useState<ChartInteractionState>({
		selectedPoints: [],
		hoveredPoint: null,
		zoomRange: null,
		activeFilters: config.filters || [],
		drilldownPath: []
	});
	const [isExporting, setIsExporting] = useState(false);

	// 响应式尺寸处理
	useEffect(() => {
		const updateDimensions = () => {
			if (containerRef.current) {
				const { width, height } = containerRef.current.getBoundingClientRect();
				setDimensions({
					width: config.width || width || 400,
					height: config.height || height || 300
				});
			}
		};

		updateDimensions();
		if (config.responsive) {
			window.addEventListener('resize', updateDimensions);
			return () => window.removeEventListener('resize', updateDimensions);
		}
	}, [config.width, config.height, config.responsive]);

	// 处理过滤后的数据
	const filteredData = React.useMemo(() => {
		if (!interactionState.activeFilters.length) return data;
		
		return data.map(series => ({
			...series,
			data: ChartUtils.applyFilters(series.data, interactionState.activeFilters)
		}));
	}, [data, interactionState.activeFilters]);

	// 绘制图表
	useEffect(() => {
		const canvas = canvasRef.current;
		if (!canvas || !filteredData.length) return;

		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		// 设置画布尺寸
		canvas.width = dimensions.width;
		canvas.height = dimensions.height;

		// 清空画布
		ctx.clearRect(0, 0, dimensions.width, dimensions.height);

		// 根据图表类型绘制
		switch (config.type) {
			case ChartType.PIE:
			case ChartType.DONUT:
				drawPieChart(ctx, filteredData[0], dimensions, config);
				break;
			case ChartType.BAR:
			case ChartType.COLUMN:
				drawBarChart(ctx, filteredData, dimensions, config);
				break;
			case ChartType.HORIZONTAL_BAR:
				drawHorizontalBarChart(ctx, filteredData, dimensions, config);
				break;
			case ChartType.STACKED_BAR:
			case ChartType.STACKED_COLUMN:
				drawStackedBarChart(ctx, filteredData, dimensions, config);
				break;
			case ChartType.LINE:
			case ChartType.AREA:
			case ChartType.MULTI_LINE:
				drawLineChart(ctx, filteredData, dimensions, config);
				break;
			case ChartType.SCATTER:
				drawScatterChart(ctx, filteredData, dimensions, config);
				break;
			case ChartType.HEATMAP:
				drawHeatmap(ctx, filteredData, dimensions, config);
				break;
			case ChartType.RADAR:
				drawRadarChart(ctx, filteredData[0], dimensions, config);
				break;
			case ChartType.GAUGE:
				drawGaugeChart(ctx, filteredData[0], dimensions, config);
				break;
			case ChartType.FUNNEL:
				drawFunnelChart(ctx, filteredData[0], dimensions, config);
				break;
			default:
				drawBarChart(ctx, filteredData, dimensions, config);
		}

		// 绘制阈值线
		if (config.thresholds) {
			drawThresholds(ctx, config.thresholds, dimensions, config);
		}

		// 绘制选中状态
		if (interactionState.selectedPoints.length > 0) {
			drawSelection(ctx, interactionState.selectedPoints, dimensions, config);
		}

	}, [filteredData, dimensions, config, interactionState.selectedPoints]);

	// 鼠标事件处理
	const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
		if (!config.interactive) return;

		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const x = event.clientX - rect.left;
		const y = event.clientY - rect.top;

		// 检测鼠标悬停的数据点
		const hoveredData = detectHoveredPoint(x, y, filteredData, dimensions, config);
		
		setInteractionState(prev => ({
			...prev,
			hoveredPoint: hoveredData?.point || null
		}));
	}, [config.interactive, filteredData, dimensions, config]);

	const handleMouseLeave = useCallback(() => {
		setInteractionState(prev => ({
			...prev,
			hoveredPoint: null
		}));
	}, []);

	const handleClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
		if (!config.interactive) return;

		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const x = event.clientX - rect.left;
		const y = event.clientY - rect.top;

		const clickedData = detectHoveredPoint(x, y, filteredData, dimensions, config);
		if (!clickedData) return;

		// 处理多选
		if (config.multiSelect && event.ctrlKey) {
			setInteractionState(prev => {
				const isSelected = prev.selectedPoints.some(p => p.label === clickedData.point.label);
				const newSelection = isSelected
					? prev.selectedPoints.filter(p => p.label !== clickedData.point.label)
					: [...prev.selectedPoints, clickedData.point];
				
				onSelectionChange?.(newSelection);
				return { ...prev, selectedPoints: newSelection };
			});
		} else {
			setInteractionState(prev => ({
				...prev,
				selectedPoints: [clickedData.point]
			}));
			onSelectionChange?.([clickedData.point]);
		}

		// 触发点击事件
		onDataPointClick?.(clickedData.point, clickedData.series);

		// 处理钻取
		if (config.drilldown && onDrillDown) {
			onDrillDown(clickedData.point);
		}
	}, [config.interactive, config.multiSelect, config.drilldown, filteredData, dimensions, config, onDataPointClick, onDrillDown, onSelectionChange]);

	// 导出功能
	const handleExport = useCallback(async (format: 'png' | 'svg' | 'pdf' | 'csv') => {
		setIsExporting(true);
		
		try {
			if (format === 'csv') {
				const csvData = ChartUtils.exportChartData(filteredData, 'csv');
				const blob = new Blob([csvData], { type: 'text/csv' });
				const url = URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `chart_data_${Date.now()}.csv`;
				a.click();
				URL.revokeObjectURL(url);
			} else if (format === 'png') {
				const canvas = canvasRef.current;
				if (canvas) {
					const url = canvas.toDataURL('image/png');
					const a = document.createElement('a');
					a.href = url;
					a.download = `chart_${Date.now()}.png`;
					a.click();
				}
			}
			
			onExport?.(format);
		} catch (error) {
			console.error('导出失败:', error);
		} finally {
			setIsExporting(false);
		}
	}, [filteredData, onExport]);

	// 过滤器处理
	const handleFilterChange = useCallback((filters: ChartFilter[]) => {
		setInteractionState(prev => ({
			...prev,
			activeFilters: filters
		}));
		onFilterChange?.(filters);
	}, [onFilterChange]);

	// 清除选择
	const clearSelection = useCallback(() => {
		setInteractionState(prev => ({
			...prev,
			selectedPoints: []
		}));
		onSelectionChange?.([]);
	}, [onSelectionChange]);

	return (
		<div 
			ref={containerRef}
			className={`ptm-enhanced-chart-container ${className || ''}`}
			style={{ position: 'relative', width: '100%', height: '100%' }}
		>
			{/* 图表标题 */}
			{config.title && (
				<div className="ptm-chart-title">
					<h3>{config.title}</h3>
					{config.subtitle && <p>{config.subtitle}</p>}
				</div>
			)}

			{/* 工具栏 */}
			<div className="ptm-chart-toolbar">
				{/* 钻取导航 */}
				{config.drilldown && onDrillUp && interactionState.drilldownPath.length > 0 && (
					<Button onClick={onDrillUp} size="small" variant="secondary">
						← 返回上级
					</Button>
				)}

				{/* 选择信息 */}
				{interactionState.selectedPoints.length > 0 && (
					<div className="ptm-selection-info">
						<span>已选择 {interactionState.selectedPoints.length} 个数据点</span>
						<Button onClick={clearSelection} size="small" variant="secondary">
							清除选择
						</Button>
					</div>
				)}

				{/* 导出按钮 */}
				{config.exportEnabled && (
					<div className="ptm-export-buttons">
						<Button 
							onClick={() => handleExport('png')} 
							size="small" 
							disabled={isExporting}
						>
							导出PNG
						</Button>
						<Button 
							onClick={() => handleExport('csv')} 
							size="small" 
							disabled={isExporting}
						>
							导出CSV
						</Button>
					</div>
				)}
			</div>

			{/* 画布 */}
			<canvas
				ref={canvasRef}
				onMouseMove={handleMouseMove}
				onMouseLeave={handleMouseLeave}
				onClick={handleClick}
				style={{ 
					cursor: config.interactive ? 'pointer' : 'default',
					display: 'block',
					maxWidth: '100%'
				}}
			/>

			{/* 工具提示 */}
			{config.showTooltip && interactionState.hoveredPoint && (
				<div 
					className="ptm-chart-tooltip"
					style={{
						position: 'absolute',
						left: '50%',
						top: '20px',
						transform: 'translateX(-50%)',
						background: 'var(--background-primary)',
						border: '1px solid var(--background-modifier-border)',
						borderRadius: '4px',
						padding: '8px',
						fontSize: '12px',
						pointerEvents: 'none',
						zIndex: 1000,
						boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
					}}
				>
					<div><strong>{interactionState.hoveredPoint.label}</strong></div>
					<div>值: {ChartUtils.formatValue(interactionState.hoveredPoint.value)}</div>
					{interactionState.hoveredPoint.metadata && (
						<div>
							{Object.entries(interactionState.hoveredPoint.metadata).map(([key, value]) => (
								<div key={key}>{key}: {String(value)}</div>
							))}
						</div>
					)}
				</div>
			)}

			{/* 图例 */}
			{config.showLegend && filteredData.length > 1 && (
				<div className="ptm-chart-legend">
					{filteredData.map((series, index) => (
						<div key={series.name || index} className="ptm-legend-item">
							<div 
								className="ptm-legend-color"
								style={{ 
									backgroundColor: series.color || ChartUtils.getColorByIndex(index)
								}}
							/>
							<span>{series.name || `系列 ${index + 1}`}</span>
						</div>
					))}
				</div>
			)}

			{/* 统计信息 */}
			{config.interactive && (
				<div className="ptm-chart-stats">
					<div>数据点总数: {filteredData.reduce((sum, series) => sum + series.data.length, 0)}</div>
					{interactionState.selectedPoints.length > 0 && (
						<div>
							选中数据总值: {ChartUtils.formatValue(
								interactionState.selectedPoints.reduce((sum, point) => sum + point.value, 0)
							)}
						</div>
					)}
				</div>
			)}

			{/* jsx样式已移除 */}
			{/*
				.ptm-enhanced-chart-container {
					font-family: var(--font-interface);
				}

				.ptm-chart-title {
					text-align: center;
					margin-bottom: 16px;
				}

				.ptm-chart-title h3 {
					margin: 0 0 4px 0;
					font-size: 16px;
					font-weight: 600;
					color: var(--text-normal);
				}

				.ptm-chart-title p {
					margin: 0;
					font-size: 14px;
					color: var(--text-muted);
				}

				.ptm-chart-toolbar {
					display: flex;
					align-items: center;
					gap: 12px;
					margin-bottom: 12px;
					flex-wrap: wrap;
				}

				.ptm-selection-info {
					display: flex;
					align-items: center;
					gap: 8px;
					font-size: 12px;
					color: var(--text-muted);
				}

				.ptm-export-buttons {
					display: flex;
					gap: 4px;
				}

				.ptm-chart-legend {
					display: flex;
					flex-wrap: wrap;
					gap: 12px;
					margin-top: 12px;
					justify-content: center;
				}

				.ptm-legend-item {
					display: flex;
					align-items: center;
					gap: 6px;
					font-size: 12px;
					color: var(--text-normal);
				}

				.ptm-legend-color {
					width: 12px;
					height: 12px;
					border-radius: 2px;
				}

				.ptm-chart-stats {
					margin-top: 8px;
					padding-top: 8px;
					border-top: 1px solid var(--background-modifier-border);
					font-size: 11px;
					color: var(--text-muted);
					display: flex;
					gap: 16px;
				}
			*/}
		</div>
	);
};

// 绘制函数实现

function drawPieChart(
	ctx: CanvasRenderingContext2D,
	series: ChartSeries,
	dimensions: { width: number; height: number },
	config: ChartConfig
) {
	const { width, height } = dimensions;
	const centerX = width / 2;
	const centerY = height / 2;
	const radius = Math.min(width, height) / 2 - 20;
	
	const total = series.data.reduce((sum, point) => sum + point.value, 0);
	let currentAngle = -Math.PI / 2; // 从顶部开始
	
	series.data.forEach((point, index) => {
		const sliceAngle = (point.value / total) * 2 * Math.PI;
		
		// 绘制扇形
		ctx.beginPath();
		ctx.moveTo(centerX, centerY);
		ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
		ctx.closePath();
		
		// 设置颜色
		ctx.fillStyle = config.colors?.[index % (config.colors?.length || 1)] || `hsl(${index * 60}, 70%, 50%)`;
		ctx.fill();
		
		// 绘制边框
		ctx.strokeStyle = '#fff';
		ctx.lineWidth = 2;
		ctx.stroke();
		
		currentAngle += sliceAngle;
	});
}

function drawBarChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: ChartConfig
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;
	
	if (data.length === 0) return;
	
	const maxValue = Math.max(...data.flatMap(series => series.data.map(point => point.value)));
	const barWidth = chartWidth / (data[0].data.length * data.length + data[0].data.length - 1);
	
	data.forEach((series, seriesIndex) => {
		series.data.forEach((point, pointIndex) => {
			const barHeight = (point.value / maxValue) * chartHeight;
			const x = padding + pointIndex * (barWidth * data.length + barWidth) + seriesIndex * barWidth;
			const y = height - padding - barHeight;
			
			// 绘制柱子
			ctx.fillStyle = config.colors?.[seriesIndex % (config.colors?.length || 1)] || `hsl(${seriesIndex * 60}, 70%, 50%)`;
			ctx.fillRect(x, y, barWidth, barHeight);
			
			// 绘制边框
			ctx.strokeStyle = '#333';
			ctx.lineWidth = 1;
			ctx.strokeRect(x, y, barWidth, barHeight);
		});
	});
}

function drawLineChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: ChartConfig
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;
	
	if (data.length === 0) return;
	
	const maxValue = Math.max(...data.flatMap(series => series.data.map(point => point.value)));
	const minValue = Math.min(...data.flatMap(series => series.data.map(point => point.value)));
	const valueRange = maxValue - minValue || 1;
	
	data.forEach((series, seriesIndex) => {
		if (series.data.length === 0) return;
		
		ctx.beginPath();
		ctx.strokeStyle = config.colors?.[seriesIndex % (config.colors?.length || 1)] || `hsl(${seriesIndex * 60}, 70%, 50%)`;
		ctx.lineWidth = 2;
		
		series.data.forEach((point, pointIndex) => {
			const x = padding + (pointIndex / (series.data.length - 1)) * chartWidth;
			const y = height - padding - ((point.value - minValue) / valueRange) * chartHeight;
			
			if (pointIndex === 0) {
				ctx.moveTo(x, y);
			} else {
				ctx.lineTo(x, y);
			}
		});
		
		ctx.stroke();
		
		// 绘制数据点
		series.data.forEach((point, pointIndex) => {
			const x = padding + (pointIndex / (series.data.length - 1)) * chartWidth;
			const y = height - padding - ((point.value - minValue) / valueRange) * chartHeight;
			
			ctx.beginPath();
			ctx.arc(x, y, 4, 0, 2 * Math.PI);
			ctx.fillStyle = config.colors?.[seriesIndex % (config.colors?.length || 1)] || `hsl(${seriesIndex * 60}, 70%, 50%)`;
			ctx.fill();
			ctx.strokeStyle = '#fff';
			ctx.lineWidth = 2;
			ctx.stroke();
		});
	});
}

function drawHorizontalBarChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 60;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	// 计算最大值
	const maxValue = Math.max(...data.flatMap(series => series.data.map(point => point.value)));
	const scale = chartWidth / maxValue;

	// 绘制网格
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		// 垂直网格线
		for (let i = 0; i <= 5; i++) {
			const x = padding + (chartWidth / 5) * i;
			ctx.beginPath();
			ctx.moveTo(x, padding);
			ctx.lineTo(x, height - padding);
			ctx.stroke();
		}
	}

	// 绘制柱子
	const barHeight = chartHeight / (data[0]?.data.length || 1) / data.length * 0.8;
	const groupHeight = chartHeight / (data[0]?.data.length || 1);

	data.forEach((series, seriesIndex) => {
		series.data.forEach((point, pointIndex) => {
			const y = padding + pointIndex * groupHeight + seriesIndex * barHeight;
			const barWidth = point.value * scale;
			const x = padding;

			const color = point.color || series.color || ChartUtils.getColorByIndex(seriesIndex);

			// 绘制柱子
			ctx.fillStyle = color;
			ctx.fillRect(x, y, barWidth, barHeight);

			// 绘制边框
			ctx.strokeStyle = 'var(--background-modifier-border)';
			ctx.lineWidth = 1;
			ctx.strokeRect(x, y, barWidth, barHeight);

			// 绘制数值标签
			if (config.showDataLabels) {
				ctx.fillStyle = 'var(--text-normal)';
				ctx.font = '10px var(--font-interface)';
				ctx.textAlign = 'left';
				ctx.fillText(
					ChartUtils.formatValue(point.value),
					x + barWidth + 5,
					y + barHeight / 2 + 3
				);
			}
		});
	});

	// 绘制Y轴标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	ctx.textAlign = 'right';
	data[0]?.data.forEach((point, index) => {
		const y = padding + index * groupHeight + groupHeight / 2 + 4;
		ctx.fillText(point.label, padding - 10, y);
	});

	// 绘制X轴标签
	ctx.textAlign = 'center';
	for (let i = 0; i <= 5; i++) {
		const value = (maxValue / 5) * i;
		const x = padding + (chartWidth / 5) * i;
		ctx.fillText(ChartUtils.formatValue(value), x, height - 10);
	}
}

function drawStackedBarChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	// 计算每个数据点的堆叠总值
	const stackedData: { [key: string]: { total: number; values: number[] } } = {};
	
	if (data[0]) {
		data[0].data.forEach((point, pointIndex) => {
			const total = data.reduce((sum, series) => sum + (series.data[pointIndex]?.value || 0), 0);
			const values = data.map(series => series.data[pointIndex]?.value || 0);
			stackedData[point.label] = { total, values };
		});
	}

	const maxValue = Math.max(...Object.values(stackedData).map(item => item.total));
	const scale = chartHeight / maxValue;

	// 绘制网格
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		for (let i = 0; i <= 5; i++) {
			const y = padding + (chartHeight / 5) * i;
			ctx.beginPath();
			ctx.moveTo(padding, y);
			ctx.lineTo(width - padding, y);
			ctx.stroke();
		}
	}

	// 绘制堆叠柱子
	const barWidth = chartWidth / Object.keys(stackedData).length * 0.8;
	const barSpacing = chartWidth / Object.keys(stackedData).length;

	Object.entries(stackedData).forEach(([label, stackData], index) => {
		const x = padding + index * barSpacing + (barSpacing - barWidth) / 2;
		let currentY = height - padding;

		// 从下往上绘制每个堆叠段
		stackData.values.forEach((value, seriesIndex) => {
			if (value <= 0) return;

			const segmentHeight = value * scale;
			const y = currentY - segmentHeight;
			const color = data[seriesIndex]?.color || ChartUtils.getColorByIndex(seriesIndex);

			// 绘制段
			ctx.fillStyle = color;
			ctx.fillRect(x, y, barWidth, segmentHeight);

			// 绘制边框
			ctx.strokeStyle = 'var(--background-modifier-border)';
			ctx.lineWidth = 1;
			ctx.strokeRect(x, y, barWidth, segmentHeight);

			// 绘制数值标签
			if (config.showDataLabels && segmentHeight > 15) {
				ctx.fillStyle = 'white';
				ctx.font = '10px var(--font-interface)';
				ctx.textAlign = 'center';
				ctx.fillText(
					ChartUtils.formatValue(value),
					x + barWidth / 2,
					y + segmentHeight / 2 + 3
				);
			}

			currentY = y;
		});
	});

	// 绘制X轴标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	ctx.textAlign = 'center';
	Object.keys(stackedData).forEach((label, index) => {
		const x = padding + index * barSpacing + barSpacing / 2;
		ctx.fillText(label, x, height - 10);
	});

	// 绘制Y轴标签
	ctx.textAlign = 'right';
	for (let i = 0; i <= 5; i++) {
		const value = (maxValue / 5) * (5 - i);
		const y = padding + (chartHeight / 5) * i + 4;
		ctx.fillText(ChartUtils.formatValue(value), padding - 10, y);
	}
}

function drawScatterChart(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	// 计算X和Y的范围
	const allXValues = data.flatMap(series => series.data.map(point => point.metadata?.x || 0));
	const allYValues = data.flatMap(series => series.data.map(point => point.value));
	
	const minX = Math.min(...allXValues);
	const maxX = Math.max(...allXValues);
	const minY = Math.min(...allYValues);
	const maxY = Math.max(...allYValues);
	
	const xRange = maxX - minX;
	const yRange = maxY - minY;
	const xScale = chartWidth / xRange;
	const yScale = chartHeight / yRange;

	// 绘制网格
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		// 垂直网格线
		for (let i = 0; i <= 5; i++) {
			const x = padding + (chartWidth / 5) * i;
			ctx.beginPath();
			ctx.moveTo(x, padding);
			ctx.lineTo(x, height - padding);
			ctx.stroke();
		}
		
		// 水平网格线
		for (let i = 0; i <= 5; i++) {
			const y = padding + (chartHeight / 5) * i;
			ctx.beginPath();
			ctx.moveTo(padding, y);
			ctx.lineTo(width - padding, y);
			ctx.stroke();
		}
	}

	// 绘制散点
	data.forEach((series, seriesIndex) => {
		const color = series.color || ChartUtils.getColorByIndex(seriesIndex);
		
		series.data.forEach(point => {
			const x = padding + (point.metadata?.x - minX) * xScale;
			const y = height - padding - (point.value - minY) * yScale;
			const size = point.metadata?.size || 4;

			// 绘制点
			ctx.beginPath();
			ctx.arc(x, y, size, 0, 2 * Math.PI);
			ctx.fillStyle = color;
			ctx.fill();
			ctx.strokeStyle = 'var(--background-primary)';
			ctx.lineWidth = 1;
			ctx.stroke();
		});
	});

	// 绘制轴标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	
	// X轴
	ctx.textAlign = 'center';
	for (let i = 0; i <= 5; i++) {
		const value = minX + (xRange / 5) * i;
		const x = padding + (chartWidth / 5) * i;
		ctx.fillText(ChartUtils.formatValue(value), x, height - 10);
	}
	
	// Y轴
	ctx.textAlign = 'right';
	for (let i = 0; i <= 5; i++) {
		const value = minY + (yRange / 5) * (5 - i);
		const y = padding + (chartHeight / 5) * i + 4;
		ctx.fillText(ChartUtils.formatValue(value), padding - 10, y);
	}
}

function drawHeatmap(
	ctx: CanvasRenderingContext2D,
	data: ChartSeries[],
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;

	if (!data[0] || !data[0].data.length) return;

	// 假设数据是二维网格格式
	const gridData = data[0].data;
	const rows = Math.sqrt(gridData.length);
	const cols = rows;
	
	const cellWidth = chartWidth / cols;
	const cellHeight = chartHeight / rows;

	// 计算值的范围用于颜色映射
	const values = gridData.map(point => point.value);
	const minValue = Math.min(...values);
	const maxValue = Math.max(...values);
	const valueRange = maxValue - minValue;

	// 绘制热力图单元格
	gridData.forEach((point, index) => {
		const row = Math.floor(index / cols);
		const col = index % cols;
		
		const x = padding + col * cellWidth;
		const y = padding + row * cellHeight;
		
		// 计算颜色强度
		const intensity = valueRange > 0 ? (point.value - minValue) / valueRange : 0;
		const color = interpolateColor(CHART_COLORS.heatmap.low, CHART_COLORS.heatmap.high, intensity);
		
		// 绘制单元格
		ctx.fillStyle = color;
		ctx.fillRect(x, y, cellWidth, cellHeight);
		
		// 绘制边框
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 0.5;
		ctx.strokeRect(x, y, cellWidth, cellHeight);
		
		// 绘制数值
		if (config.showDataLabels && cellWidth > 30 && cellHeight > 20) {
			ctx.fillStyle = intensity > 0.5 ? 'white' : 'var(--text-normal)';
			ctx.font = '10px var(--font-interface)';
			ctx.textAlign = 'center';
			ctx.fillText(
				ChartUtils.formatValue(point.value),
				x + cellWidth / 2,
				y + cellHeight / 2 + 3
			);
		}
	});
}

function drawRadarChart(
	ctx: CanvasRenderingContext2D,
	series: ChartSeries,
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const centerX = width / 2;
	const centerY = height / 2;
	const radius = Math.min(width, height) / 2 - 40;
	
	const data = series.data;
	const angleStep = (2 * Math.PI) / data.length;
	const maxValue = Math.max(...data.map(point => point.value));

	// 绘制网格圆圈
	if (config.showGrid) {
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		
		for (let i = 1; i <= 5; i++) {
			const r = (radius / 5) * i;
			ctx.beginPath();
			ctx.arc(centerX, centerY, r, 0, 2 * Math.PI);
			ctx.stroke();
		}
		
		// 绘制轴线
		data.forEach((_, index) => {
			const angle = index * angleStep - Math.PI / 2;
			const x = centerX + Math.cos(angle) * radius;
			const y = centerY + Math.sin(angle) * radius;
			
			ctx.beginPath();
			ctx.moveTo(centerX, centerY);
			ctx.lineTo(x, y);
			ctx.stroke();
		});
	}

	// 绘制数据区域
	ctx.beginPath();
	data.forEach((point, index) => {
		const angle = index * angleStep - Math.PI / 2;
		const value = point.value / maxValue;
		const x = centerX + Math.cos(angle) * radius * value;
		const y = centerY + Math.sin(angle) * radius * value;
		
		if (index === 0) {
			ctx.moveTo(x, y);
		} else {
			ctx.lineTo(x, y);
		}
	});
	ctx.closePath();
	
	// 填充区域
	ctx.fillStyle = (series.color || CHART_COLORS.primary[0]) + '40';
	ctx.fill();
	
	// 绘制边框
	ctx.strokeStyle = series.color || CHART_COLORS.primary[0];
	ctx.lineWidth = 2;
	ctx.stroke();

	// 绘制数据点
	data.forEach((point, index) => {
		const angle = index * angleStep - Math.PI / 2;
		const value = point.value / maxValue;
		const x = centerX + Math.cos(angle) * radius * value;
		const y = centerY + Math.sin(angle) * radius * value;
		
		ctx.beginPath();
		ctx.arc(x, y, 4, 0, 2 * Math.PI);
		ctx.fillStyle = series.color || CHART_COLORS.primary[0];
		ctx.fill();
		ctx.strokeStyle = 'var(--background-primary)';
		ctx.lineWidth = 2;
		ctx.stroke();
	});

	// 绘制标签
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = '12px var(--font-interface)';
	ctx.textAlign = 'center';
	
	data.forEach((point, index) => {
		const angle = index * angleStep - Math.PI / 2;
		const labelRadius = radius + 20;
		const x = centerX + Math.cos(angle) * labelRadius;
		const y = centerY + Math.sin(angle) * labelRadius + 4;
		
		ctx.fillText(point.label, x, y);
	});
}

function drawGaugeChart(
	ctx: CanvasRenderingContext2D,
	series: ChartSeries,
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const centerX = width / 2;
	const centerY = height / 2;
	const radius = Math.min(width, height) / 2 - 20;
	
	const value = series.data[0]?.value || 0;
	const maxValue = config.maxValue || 100;
	const percentage = Math.min(value / maxValue, 1);
	
	const startAngle = Math.PI;
	const endAngle = 2 * Math.PI;
	const valueAngle = startAngle + (endAngle - startAngle) * percentage;

	// 绘制背景弧
	ctx.beginPath();
	ctx.arc(centerX, centerY, radius, startAngle, endAngle);
	ctx.strokeStyle = 'var(--background-modifier-border)';
	ctx.lineWidth = 20;
	ctx.stroke();

	// 绘制值弧
	ctx.beginPath();
	ctx.arc(centerX, centerY, radius, startAngle, valueAngle);
	ctx.strokeStyle = series.color || CHART_COLORS.primary[0];
	ctx.lineWidth = 20;
	ctx.stroke();

	// 绘制中心文本
	ctx.fillStyle = 'var(--text-normal)';
	ctx.font = 'bold 24px var(--font-interface)';
	ctx.textAlign = 'center';
	ctx.fillText(ChartUtils.formatValue(value), centerX, centerY);
	
	ctx.font = '14px var(--font-interface)';
	ctx.fillText(series.data[0]?.label || '', centerX, centerY + 25);
}

function drawFunnelChart(
	ctx: CanvasRenderingContext2D,
	series: ChartSeries,
	dimensions: { width: number; height: number },
	config: any
) {
	const { width, height } = dimensions;
	const padding = 40;
	const chartWidth = width - padding * 2;
	const chartHeight = height - padding * 2;
	
	const data = series.data;
	const maxValue = Math.max(...data.map(point => point.value));
	const segmentHeight = chartHeight / data.length;
	const centerX = width / 2;

	data.forEach((point, index) => {
		const y = padding + index * segmentHeight;
		const widthRatio = point.value / maxValue;
		const segmentWidth = chartWidth * widthRatio;
		const x = padding + (chartWidth - segmentWidth) / 2;
		
		const color = point.color || ChartUtils.getColorByIndex(index);

		// 绘制梯形段
		ctx.fillStyle = color;
		ctx.beginPath();
		
		if (index === 0) {
			// 第一段是矩形
			ctx.rect(x, y, segmentWidth, segmentHeight);
		} else {
			// 其他段是梯形
			const prevWidthRatio = data[index - 1].value / maxValue;
			const prevSegmentWidth = chartWidth * prevWidthRatio;
			const prevX = padding + (chartWidth - prevSegmentWidth) / 2;
			
			ctx.moveTo(prevX, y);
			ctx.lineTo(prevX + prevSegmentWidth, y);
			ctx.lineTo(x + segmentWidth, y + segmentHeight);
			ctx.lineTo(x, y + segmentHeight);
		}
		
		ctx.closePath();
		ctx.fill();
		
		// 绘制边框
		ctx.strokeStyle = 'var(--background-modifier-border)';
		ctx.lineWidth = 1;
		ctx.stroke();

		// 绘制标签
		ctx.fillStyle = 'var(--text-normal)';
		ctx.font = '12px var(--font-interface)';
		ctx.textAlign = 'center';
		ctx.fillText(
			`${point.label}: ${ChartUtils.formatValue(point.value)}`,
			centerX,
			y + segmentHeight / 2 + 4
		);
	});
}

// 辅助函数
function drawThresholds(
	ctx: CanvasRenderingContext2D,
	thresholds: any[],
	dimensions: { width: number; height: number },
	config: any
) {
	// 简化实现：绘制水平阈值线
	const { width, height } = dimensions;
	const padding = 40;
	
	thresholds.forEach(threshold => {
		// 这里需要根据具体的图表类型和数据范围来计算Y位置
		// 简化实现
		const y = height / 2;
		
		ctx.strokeStyle = threshold.color;
		ctx.lineWidth = 2;
		ctx.setLineDash(threshold.style === 'dashed' ? [5, 5] : []);
		
		ctx.beginPath();
		ctx.moveTo(padding, y);
		ctx.lineTo(width - padding, y);
		ctx.stroke();
		
		ctx.setLineDash([]);
	});
}

function drawSelection(
	ctx: CanvasRenderingContext2D,
	selectedPoints: ChartDataPoint[],
	dimensions: { width: number; height: number },
	config: any
) {
	// 简化实现：在选中的数据点周围绘制高亮
	ctx.strokeStyle = '#ff6b6b';
	ctx.lineWidth = 3;
	ctx.setLineDash([3, 3]);
	
	// 这里需要根据具体的图表类型来绘制选中状态
	// 简化实现
	
	ctx.setLineDash([]);
}

function detectHoveredPoint(
	x: number, 
	y: number, 
	data: ChartSeries[], 
	dimensions: { width: number; height: number },
	config: any
): { point: ChartDataPoint; series: ChartSeries } | null {
	// 简化实现：返回第一个系列的第一个点作为示例
	if (data.length > 0 && data[0].data.length > 0) {
		return {
			point: data[0].data[0],
			series: data[0]
		};
	}
	return null;
}

function interpolateColor(color1: string, color2: string, factor: number): string {
	// 简化的颜色插值实现
	// 实际应该解析颜色值并进行插值计算
	return factor < 0.5 ? color1 : color2;
}