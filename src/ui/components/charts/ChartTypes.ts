// 图表类型定义

export enum ChartType {
	LINE = 'line',
	BAR = 'bar',
	PIE = 'pie',
	DONUT = 'donut',
	AREA = 'area',
	SCATTER = 'scatter',
	GANTT = 'gantt',
	BURNDOWN = 'burndown',
	// 新增图表类型
	COLUMN = 'column',
	STACKED_BAR = 'stacked_bar',
	STACKED_COLUMN = 'stacked_column',
	HORIZONTAL_BAR = 'horizontal_bar',
	MULTI_LINE = 'multi_line',
	COMBO = 'combo',
	RADAR = 'radar',
	HEATMAP = 'heatmap',
	TREEMAP = 'treemap',
	FUNNEL = 'funnel',
	GAUGE = 'gauge',
	WATERFALL = 'waterfall'
}

export interface ChartDataPoint {
	label: string;
	value: number;
	color?: string;
	metadata?: any;
}

export interface ChartSeries {
	name: string;
	data: ChartDataPoint[];
	color?: string;
	type?: ChartType;
}

export interface ChartConfig {
	type: ChartType;
	title?: string;
	subtitle?: string;
	width?: number;
	height?: number;
	responsive?: boolean;
	interactive?: boolean;
	showLegend?: boolean;
	showTooltip?: boolean;
	showGrid?: boolean;
	colors?: string[];
	animation?: boolean;
	drilldown?: boolean;
	// 新增配置选项
	stacked?: boolean;
	showDataLabels?: boolean;
	enableZoom?: boolean;
	enablePan?: boolean;
	crossfilter?: boolean;
	brushSelection?: boolean;
	multiSelect?: boolean;
	exportEnabled?: boolean;
	realTimeUpdate?: boolean;
	aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
	groupBy?: string;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
	filters?: ChartFilter[];
	thresholds?: ChartThreshold[];
}

export interface ChartProps {
	data: ChartSeries[];
	config: ChartConfig;
	onDataPointClick?: (point: ChartDataPoint, series: ChartSeries) => void;
	onDrillDown?: (point: ChartDataPoint) => void;
	onDrillUp?: () => void;
	onFilterChange?: (filters: ChartFilter[]) => void;
	onSelectionChange?: (selection: ChartDataPoint[]) => void;
	onZoom?: (range: { start: number; end: number }) => void;
	onExport?: (format: 'png' | 'svg' | 'pdf' | 'csv') => void;
	className?: string;
}

// 新增接口定义
export interface ChartFilter {
	field: string;
	operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains';
	value: any;
	label?: string;
}

export interface ChartThreshold {
	value: number;
	color: string;
	label?: string;
	style?: 'solid' | 'dashed' | 'dotted';
}

export interface DrilldownLevel {
	field: string;
	label: string;
	aggregation?: 'sum' | 'avg' | 'count';
}

export interface ChartInteractionState {
	selectedPoints: ChartDataPoint[];
	hoveredPoint: ChartDataPoint | null;
	zoomRange: { start: number; end: number } | null;
	activeFilters: ChartFilter[];
	drilldownPath: DrilldownLevel[];
}

// 预定义颜色主题
export const CHART_COLORS = {
	primary: [
		'#3b82f6', // blue
		'#10b981', // emerald
		'#f59e0b', // amber
		'#ef4444', // red
		'#8b5cf6', // violet
		'#06b6d4', // cyan
		'#84cc16', // lime
		'#f97316', // orange
	],
	status: {
		todo: '#6b7280',
		in_progress: '#3b82f6',
		blocked: '#ef4444',
		review: '#f59e0b',
		completed: '#10b981',
		cancelled: '#9ca3af'
	},
	priority: {
		low: '#10b981',
		medium: '#f59e0b',
		high: '#ef4444',
		critical: '#dc2626'
	},
	// 新增颜色主题
	gradient: [
		'#667eea',
		'#764ba2',
		'#f093fb',
		'#f5576c',
		'#4facfe',
		'#00f2fe',
		'#43e97b',
		'#38f9d7'
	],
	pastel: [
		'#ffeaa7',
		'#fab1a0',
		'#ff7675',
		'#fd79a8',
		'#fdcb6e',
		'#e17055',
		'#81ecec',
		'#74b9ff'
	],
	dark: [
		'#2d3436',
		'#636e72',
		'#b2bec3',
		'#ddd',
		'#74b9ff',
		'#0984e3',
		'#00b894',
		'#00cec9'
	],
	heatmap: {
		low: '#f7fbff',
		medium: '#6baed6',
		high: '#08519c'
	}
};

// 图表工具函数
export class ChartUtils {
	static formatValue(value: number, type: 'number' | 'percentage' | 'currency' | 'time' = 'number'): string {
		switch (type) {
			case 'percentage':
				return `${value.toFixed(1)}%`;
			case 'currency':
				return `¥${Math.round(value).toLocaleString()}`;
			case 'time':
				return `${value}h`;
			default:
				return value.toLocaleString();
		}
	}

	static getColorByIndex(index: number, colors: string[] = CHART_COLORS.primary): string {
		return colors[index % colors.length];
	}

	static getStatusColor(status: string): string {
		return CHART_COLORS.status[status as keyof typeof CHART_COLORS.status] || CHART_COLORS.primary[0];
	}

	static getPriorityColor(priority: string): string {
		return CHART_COLORS.priority[priority as keyof typeof CHART_COLORS.priority] || CHART_COLORS.primary[0];
	}

	static aggregateData(data: ChartDataPoint[], groupBy: string): ChartDataPoint[] {
		const grouped = data.reduce((acc, point) => {
			const key = point.metadata?.[groupBy] || point.label;
			if (!acc[key]) {
				acc[key] = { label: key, value: 0, metadata: point.metadata };
			}
			acc[key].value += point.value;
			return acc;
		}, {} as Record<string, ChartDataPoint>);

		return Object.values(grouped);
	}

	static sortData(data: ChartDataPoint[], sortBy: 'label' | 'value' = 'value', order: 'asc' | 'desc' = 'desc'): ChartDataPoint[] {
		return [...data].sort((a, b) => {
			const aVal = sortBy === 'label' ? a.label : a.value;
			const bVal = sortBy === 'label' ? b.label : b.value;
			
			if (typeof aVal === 'string' && typeof bVal === 'string') {
				return order === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
			}
			
			return order === 'asc' ? (aVal as number) - (bVal as number) : (bVal as number) - (aVal as number);
		});
	}

	static filterData(data: ChartDataPoint[], filters: Record<string, any>): ChartDataPoint[] {
		return data.filter(point => {
			return Object.entries(filters).every(([key, value]) => {
				if (value === null || value === undefined) return true;
				return point.metadata?.[key] === value;
			});
		});
	}

	static calculatePercentages(data: ChartDataPoint[]): ChartDataPoint[] {
		const total = data.reduce((sum, point) => sum + point.value, 0);
		if (total === 0) return data;

		return data.map(point => ({
			...point,
			value: (point.value / total) * 100
		}));
	}

	// 新增工具方法
	static applyFilters(data: ChartDataPoint[], filters: ChartFilter[]): ChartDataPoint[] {
		return data.filter(point => {
			return filters.every(filter => {
				const fieldValue = point.metadata?.[filter.field] ?? point.value;
				
				switch (filter.operator) {
					case 'eq':
						return fieldValue === filter.value;
					case 'ne':
						return fieldValue !== filter.value;
					case 'gt':
						return fieldValue > filter.value;
					case 'gte':
						return fieldValue >= filter.value;
					case 'lt':
						return fieldValue < filter.value;
					case 'lte':
						return fieldValue <= filter.value;
					case 'in':
						return Array.isArray(filter.value) && filter.value.includes(fieldValue);
					case 'nin':
						return Array.isArray(filter.value) && !filter.value.includes(fieldValue);
					case 'contains':
						return String(fieldValue).toLowerCase().includes(String(filter.value).toLowerCase());
					default:
						return true;
				}
			});
		});
	}

	static groupByField(data: ChartDataPoint[], field: string): Record<string, ChartDataPoint[]> {
		return data.reduce((groups, point) => {
			const key = point.metadata?.[field] || 'unknown';
			if (!groups[key]) {
				groups[key] = [];
			}
			groups[key].push(point);
			return groups;
		}, {} as Record<string, ChartDataPoint[]>);
	}

	static calculateMovingAverage(data: ChartDataPoint[], windowSize: number): ChartDataPoint[] {
		if (windowSize <= 1) return data;
		
		return data.map((point, index) => {
			// 计算窗口范围，以当前点为中心
			const halfWindow = Math.floor(windowSize / 2);
			const start = Math.max(0, index - halfWindow);
			const end = Math.min(data.length, index + halfWindow + 1);
			const window = data.slice(start, end);
			const average = window.reduce((sum, p) => sum + p.value, 0) / window.length;
			
			return {
				...point,
				value: average,
				metadata: {
					...point.metadata,
					originalValue: point.value,
					windowSize
				}
			};
		});
	}

	static detectOutliers(data: ChartDataPoint[], threshold: number = 2): ChartDataPoint[] {
		if (data.length < 3) return []; // 需要至少3个数据点才能检测异常值
		
		const values = data.map(p => p.value);
		const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
		const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1); // 使用样本方差
		const stdDev = Math.sqrt(variance);
		
		return data.filter(point => Math.abs(point.value - mean) > threshold * stdDev);
	}

	static interpolateData(data: ChartDataPoint[], targetLength: number): ChartDataPoint[] {
		if (data.length >= targetLength) return data;
		
		const result: ChartDataPoint[] = [];
		const step = (data.length - 1) / (targetLength - 1);
		
		for (let i = 0; i < targetLength; i++) {
			const index = i * step;
			const lowerIndex = Math.floor(index);
			const upperIndex = Math.ceil(index);
			
			if (lowerIndex === upperIndex) {
				result.push({ ...data[lowerIndex] });
			} else {
				const ratio = index - lowerIndex;
				const lowerPoint = data[lowerIndex];
				const upperPoint = data[upperIndex];
				
				result.push({
					label: `${lowerPoint.label}-${upperPoint.label}`,
					value: lowerPoint.value + (upperPoint.value - lowerPoint.value) * ratio,
					metadata: {
						interpolated: true,
						originalIndices: [lowerIndex, upperIndex],
						ratio
					}
				});
			}
		}
		
		return result;
	}

	static generateColorGradient(startColor: string, endColor: string, steps: number): string[] {
		const colors: string[] = [];
		
		// 简化的颜色插值实现
		for (let i = 0; i < steps; i++) {
			const ratio = i / (steps - 1);
			// 这里应该实现真正的颜色插值，暂时使用简化版本
			colors.push(ratio < 0.5 ? startColor : endColor);
		}
		
		return colors;
	}

	static exportChartData(data: ChartSeries[], format: 'csv' | 'json' | 'xlsx'): string {
		switch (format) {
			case 'csv':
				let csv = 'Series,Label,Value\n';
				data.forEach(series => {
					series.data.forEach(point => {
						csv += `"${series.name}","${point.label}",${point.value}\n`;
					});
				});
				return csv;
				
			case 'json':
				return JSON.stringify(data, null, 2);
				
			case 'xlsx':
				// 简化实现，实际应该使用专门的库
				return JSON.stringify(data, null, 2);
				
			default:
				return JSON.stringify(data, null, 2);
		}
	}

	static calculateTrendline(data: ChartDataPoint[]): { slope: number; intercept: number; r2: number } {
		const n = data.length;
		if (n < 2) return { slope: 0, intercept: 0, r2: 0 };
		
		const xValues = data.map((_, index) => index);
		const yValues = data.map(point => point.value);
		
		const sumX = xValues.reduce((sum, x) => sum + x, 0);
		const sumY = yValues.reduce((sum, y) => sum + y, 0);
		const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
		const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);
		const sumYY = yValues.reduce((sum, y) => sum + y * y, 0);
		
		const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
		const intercept = (sumY - slope * sumX) / n;
		
		// 计算R²
		const yMean = sumY / n;
		const ssRes = yValues.reduce((sum, y, i) => {
			const predicted = slope * xValues[i] + intercept;
			return sum + Math.pow(y - predicted, 2);
		}, 0);
		const ssTot = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
		const r2 = 1 - (ssRes / ssTot);
		
		return { slope, intercept, r2 };
	}
}