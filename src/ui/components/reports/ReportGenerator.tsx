// 报告生成器UI组件

import React, { useState, useEffect } from 'react';
import { 
	ReportType, 
	ReportFormat, 
	ReportGenerationConfig,
	ReportGenerationResult,
	ReportTemplate
} from '../../../models/Report';
import { ProjectReportGenerator } from '../../../services/ProjectReportGenerator';
import { Button } from '../common/Button';
import { Card } from '../common/Card';
import { InteractiveDataExplorer } from '../charts/InteractiveDataExplorer';
import { EnhancedChart } from '../charts/EnhancedChart';
import { ChartType, ChartSeries, ChartUtils } from '../charts/ChartTypes';

interface ReportGeneratorProps {
	projectId: string;
	projectName: string;
	reportGenerator: ProjectReportGenerator;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
	projectId,
	projectName,
	reportGenerator
}) => {
	const [reportType, setReportType] = useState<ReportType>(ReportType.PROJECT_PROGRESS);
	const [reportFormat, setReportFormat] = useState<ReportFormat>(ReportFormat.JSON);
	const [timeRange, setTimeRange] = useState({
		startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
		endDate: new Date()
	});
	const [outputPath, setOutputPath] = useState('reports');
	const [isGenerating, setIsGenerating] = useState(false);
	const [lastResult, setLastResult] = useState<ReportGenerationResult | null>(null);
	const [availableTemplates, setAvailableTemplates] = useState<ReportTemplate[]>([]);
	const [selectedTemplate, setSelectedTemplate] = useState<string>('');
	const [useTemplate, setUseTemplate] = useState(false);
	const [showVisualization, setShowVisualization] = useState(false);
	const [visualizationData, setVisualizationData] = useState<ChartSeries[]>([]);

	// 加载可用模板
	useEffect(() => {
		const templates = reportGenerator.getAvailableTemplates();
		setAvailableTemplates(templates);
	}, [reportGenerator]);

	// 当报告类型改变时，更新可用模板
	useEffect(() => {
		const templates = reportGenerator.getTemplatesByType(reportType);
		setAvailableTemplates(templates);
		setSelectedTemplate(''); // 重置选择的模板
	}, [reportType, reportGenerator]);

	const handleGenerateReport = async () => {
		setIsGenerating(true);
		setLastResult(null);

		try {
			const config: ReportGenerationConfig = {
				projectId,
				reportType,
				timeRange,
				format: reportFormat,
				outputPath: outputPath || undefined,
				template: useTemplate && selectedTemplate ? selectedTemplate : undefined,
				includeCharts: true,
				includeTrends: true,
				includeRecommendations: true
			};

			// 根据是否使用模板选择不同的生成方法
			const result = useTemplate && selectedTemplate 
				? await reportGenerator.generateReportWithTemplate(config)
				: await reportGenerator.generateAndExportReport(config);
			
			setLastResult(result);

			// 生成可视化数据
			if (result.success && result.data) {
				const chartData = generateVisualizationData(result.data, reportType);
				setVisualizationData(chartData);
				setShowVisualization(true);
			}
		} catch (error) {
			console.error('报告生成失败:', error);
			setLastResult({
				success: false,
				reportId: 'error',
				data: null,
				generatedAt: new Date(),
				error: error instanceof Error ? error.message : '未知错误'
			});
		} finally {
			setIsGenerating(false);
		}
	};

	const formatDate = (date: Date) => {
		return date.toISOString().split('T')[0];
	};

	const parseDate = (dateString: string) => {
		return new Date(dateString);
	};

	// 生成可视化数据
	const generateVisualizationData = (reportData: any, type: ReportType): ChartSeries[] => {
		switch (type) {
			case ReportType.PROJECT_PROGRESS:
				return [
					{
						name: '任务状态分布',
						data: [
							{ label: '待办', value: reportData.totalTasks - reportData.completedTasks - reportData.inProgressTasks - reportData.blockedTasks },
							{ label: '进行中', value: reportData.inProgressTasks },
							{ label: '已完成', value: reportData.completedTasks },
							{ label: '已阻塞', value: reportData.blockedTasks }
						]
					},
					{
						name: '每日进度',
						data: reportData.dailyProgress?.map((point: any) => ({
							label: point.date.toLocaleDateString(),
							value: point.completionPercentage,
							metadata: { 
								completedTasks: point.completedTasks,
								totalTasks: point.totalTasks
							}
						})) || []
					}
				];

			case ReportType.TIME_TRACKING:
				return [
					{
						name: '时间对比',
						data: [
							{ label: '预估时间', value: reportData.totalEstimatedHours },
							{ label: '实际时间', value: reportData.totalActualHours }
						]
					},
					{
						name: '团队时间分析',
						data: reportData.teamTimeAnalysis?.map((member: any) => ({
							label: member.assignee,
							value: member.totalActualHours,
							metadata: {
								estimatedHours: member.totalEstimatedHours,
								efficiency: member.efficiency,
								tasksCompleted: member.tasksCompleted
							}
						})) || []
					}
				];

			case ReportType.TEAM_PERFORMANCE:
				return [
					{
						name: '团队绩效',
						data: reportData.memberPerformance?.map((member: any) => ({
							label: member.assignee,
							value: member.completionRate * 100,
							metadata: {
								tasksAssigned: member.tasksAssigned,
								tasksCompleted: member.tasksCompleted,
								averageTaskTime: member.averageTaskTime,
								onTimeDelivery: member.onTimeDelivery
							}
						})) || []
					}
				];

			default:
				return [];
		}
	};

	return (
		<Card>
			<div className="ptm-report-generator">
				<h3>项目报告生成器</h3>
				<p>项目: {projectName}</p>

				<div className="ptm-form-group">
					<label htmlFor="report-type">报告类型:</label>
					<select
						id="report-type"
						value={reportType}
						onChange={(e) => setReportType(e.target.value as ReportType)}
						className="ptm-select"
					>
						<option value={ReportType.PROJECT_PROGRESS}>项目进度报告</option>
						<option value={ReportType.TIME_TRACKING}>时间跟踪报告</option>
						<option value={ReportType.TEAM_PERFORMANCE}>团队绩效报告</option>
					</select>
				</div>

				<div className="ptm-form-group">
					<label htmlFor="report-format">导出格式:</label>
					<select
						id="report-format"
						value={reportFormat}
						onChange={(e) => setReportFormat(e.target.value as ReportFormat)}
						className="ptm-select"
					>
						<option value={ReportFormat.JSON}>JSON</option>
						<option value={ReportFormat.MARKDOWN}>Markdown</option>
						<option value={ReportFormat.CSV}>CSV</option>
						<option value={ReportFormat.HTML}>HTML</option>
					</select>
				</div>

				<div className="ptm-form-group">
					<label>时间范围:</label>
					<div className="ptm-date-range">
						<input
							type="date"
							value={formatDate(timeRange.startDate)}
							onChange={(e) => setTimeRange(prev => ({
								...prev,
								startDate: parseDate(e.target.value)
							}))}
							className="ptm-input"
						/>
						<span>至</span>
						<input
							type="date"
							value={formatDate(timeRange.endDate)}
							onChange={(e) => setTimeRange(prev => ({
								...prev,
								endDate: parseDate(e.target.value)
							}))}
							className="ptm-input"
						/>
					</div>
				</div>

				<div className="ptm-form-group">
					<label>
						<input
							type="checkbox"
							checked={useTemplate}
							onChange={(e) => setUseTemplate(e.target.checked)}
							style={{ marginRight: '8px' }}
						/>
						使用报告模板
					</label>
				</div>

				{useTemplate && (
					<div className="ptm-form-group">
						<label htmlFor="report-template">选择模板:</label>
						<select
							id="report-template"
							value={selectedTemplate}
							onChange={(e) => setSelectedTemplate(e.target.value)}
							className="ptm-select"
							disabled={availableTemplates.length === 0}
						>
							<option value="">请选择模板</option>
							{availableTemplates.map(template => (
								<option key={template.id} value={template.id}>
									{template.name}
									{template.description && ` - ${template.description}`}
								</option>
							))}
						</select>
						{availableTemplates.length === 0 && (
							<p className="ptm-help-text">当前报告类型没有可用模板</p>
						)}
					</div>
				)}

				<div className="ptm-form-group">
					<label htmlFor="output-path">输出路径 (可选):</label>
					<input
						id="output-path"
						type="text"
						value={outputPath}
						onChange={(e) => setOutputPath(e.target.value)}
						placeholder="留空则仅生成数据不保存文件"
						className="ptm-input"
					/>
				</div>

				<div className="ptm-form-actions">
					<Button
						onClick={handleGenerateReport}
						disabled={isGenerating}
						variant="primary"
					>
						{isGenerating ? '生成中...' : '生成报告'}
					</Button>
				</div>

				{lastResult && (
					<div className="ptm-report-result">
						{lastResult.success ? (
							<div className="ptm-success">
								<h4>报告生成成功</h4>
								<p>报告ID: {lastResult.reportId}</p>
								<p>生成时间: {lastResult.generatedAt.toLocaleString()}</p>
								{lastResult.filePath && (
									<p>文件路径: {lastResult.filePath}</p>
								)}
								
								{/* 显示报告摘要 */}
								{reportType === ReportType.PROJECT_PROGRESS && lastResult.data && (
									<div className="ptm-report-summary">
										<h5>报告摘要</h5>
										<ul>
											<li>总任务数: {lastResult.data.totalTasks}</li>
											<li>已完成: {lastResult.data.completedTasks}</li>
											<li>进行中: {lastResult.data.inProgressTasks}</li>
											<li>已阻塞: {lastResult.data.blockedTasks}</li>
											<li>完成率: {lastResult.data.completionPercentage.toFixed(1)}%</li>
											<li>逾期任务: {lastResult.data.overdueTasksCount}</li>
										</ul>
									</div>
								)}

								{reportType === ReportType.TIME_TRACKING && lastResult.data && (
									<div className="ptm-report-summary">
										<h5>报告摘要</h5>
										<ul>
											<li>预估总时间: {lastResult.data.totalEstimatedHours}小时</li>
											<li>实际总时间: {lastResult.data.totalActualHours}小时</li>
											<li>时间偏差: {lastResult.data.timeVariance}小时</li>
											<li>偏差百分比: {lastResult.data.timeVariancePercentage.toFixed(1)}%</li>
										</ul>
									</div>
								)}

								{reportType === ReportType.TEAM_PERFORMANCE && lastResult.data && (
									<div className="ptm-report-summary">
										<h5>报告摘要</h5>
										<ul>
											<li>团队规模: {lastResult.data.teamSize}人</li>
											<li>活跃成员: {lastResult.data.activeMembers}人</li>
											<li>分配任务: {lastResult.data.totalTasksAssigned}个</li>
											<li>完成任务: {lastResult.data.totalTasksCompleted}个</li>
											<li>团队速度: {lastResult.data.teamVelocity.toFixed(2)}任务/天</li>
										</ul>
									</div>
								)}

								{/* 可视化控制按钮 */}
								<div className="ptm-visualization-controls">
									<Button
										onClick={() => setShowVisualization(!showVisualization)}
										variant="secondary"
										size="small"
									>
										{showVisualization ? '隐藏可视化' : '显示可视化'}
									</Button>
								</div>
							</div>
						) : (
							<div className="ptm-error">
								<h4>报告生成失败</h4>
								<p>错误信息: {lastResult.error}</p>
							</div>
						)}
					</div>
				)}

				{/* 数据可视化面板 */}
				{showVisualization && visualizationData.length > 0 && (
					<div className="ptm-visualization-panel">
						<Card>
							<h4>数据可视化</h4>
							<InteractiveDataExplorer
								data={visualizationData}
								title={`${getReportTypeLabel(reportType)} - 数据探索`}
								enableDrilldown={true}
								enableCrossfilter={true}
								availableChartTypes={getAvailableChartTypes(reportType)}
							/>
						</Card>
					</div>
				)}
			</div>

			<style>{`
				.ptm-report-generator {
					padding: 16px;
				}

				.ptm-form-group {
					margin-bottom: 16px;
				}

				.ptm-form-group label {
					display: block;
					margin-bottom: 4px;
					font-weight: 500;
					color: var(--text-normal);
				}

				.ptm-select,
				.ptm-input {
					width: 100%;
					padding: 8px 12px;
					border: 1px solid var(--background-modifier-border);
					border-radius: 4px;
					background: var(--background-primary);
					color: var(--text-normal);
					font-size: 14px;
				}

				.ptm-select:focus,
				.ptm-input:focus {
					outline: none;
					border-color: var(--interactive-accent);
				}

				.ptm-date-range {
					display: flex;
					align-items: center;
					gap: 8px;
				}

				.ptm-date-range input {
					flex: 1;
				}

				.ptm-date-range span {
					color: var(--text-muted);
					font-size: 14px;
				}

				.ptm-form-actions {
					margin-top: 24px;
				}

				.ptm-report-result {
					margin-top: 24px;
					padding: 16px;
					border-radius: 4px;
				}

				.ptm-success {
					background: var(--background-modifier-success);
					border: 1px solid var(--background-modifier-success);
				}

				.ptm-error {
					background: var(--background-modifier-error);
					border: 1px solid var(--background-modifier-error);
				}

				.ptm-success h4 {
					color: var(--text-success);
					margin: 0 0 8px 0;
				}

				.ptm-error h4 {
					color: var(--text-error);
					margin: 0 0 8px 0;
				}

				.ptm-success p,
				.ptm-error p {
					margin: 4px 0;
					font-size: 14px;
				}

				.ptm-report-summary {
					margin-top: 12px;
					padding-top: 12px;
					border-top: 1px solid var(--background-modifier-border);
				}

				.ptm-report-summary h5 {
					margin: 0 0 8px 0;
					font-size: 14px;
					font-weight: 500;
				}

				.ptm-report-summary ul {
					margin: 0;
					padding-left: 20px;
				}

				.ptm-report-summary li {
					margin: 2px 0;
					font-size: 13px;
				}

				.ptm-help-text {
					margin: 4px 0;
					font-size: 12px;
					color: var(--text-muted);
					font-style: italic;
				}

				.ptm-visualization-controls {
					margin-top: 16px;
					padding-top: 16px;
					border-top: 1px solid var(--background-modifier-border);
				}

				.ptm-visualization-panel {
					margin-top: 16px;
				}

				.ptm-visualization-panel h4 {
					margin: 0 0 16px 0;
					font-size: 16px;
					font-weight: 600;
					color: var(--text-normal);
				}
			`}</style>
		</Card>
	);
};

// 辅助函数
function getReportTypeLabel(type: ReportType): string {
	switch (type) {
		case ReportType.PROJECT_PROGRESS:
			return '项目进度报告';
		case ReportType.TIME_TRACKING:
			return '时间跟踪报告';
		case ReportType.TEAM_PERFORMANCE:
			return '团队绩效报告';
		default:
			return '项目报告';
	}
}

function getAvailableChartTypes(reportType: ReportType): ChartType[] {
	switch (reportType) {
		case ReportType.PROJECT_PROGRESS:
			return [ChartType.PIE, ChartType.BAR, ChartType.LINE, ChartType.DONUT];
		case ReportType.TIME_TRACKING:
			return [ChartType.BAR, ChartType.LINE, ChartType.SCATTER, ChartType.HEATMAP];
		case ReportType.TEAM_PERFORMANCE:
			return [ChartType.BAR, ChartType.RADAR, ChartType.SCATTER, ChartType.HORIZONTAL_BAR];
		default:
			return [ChartType.BAR, ChartType.LINE, ChartType.PIE];
	}
}