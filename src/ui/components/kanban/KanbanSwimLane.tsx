// 看板泳道组件

import React from 'react';
import { KanbanSwimLane as KanbanSwimLaneType, KanbanColumn, KanbanTaskCard, DragDropData } from '../../../models/Kanban';
import { KanbanColumn as KanbanColumnComponent } from './KanbanColumn';
import { Card } from '../common/Card';
import { Flex } from '../layout/Layout';

export interface KanbanSwimLaneProps {
	swimLane: KanbanSwimLaneType;
	columns: KanbanColumn[];
	getTasksForColumn: (columnId: string) => KanbanTaskCard[];
	draggedTask: string | null;
	onDragStart: (taskId: string) => void;
	onDragEnd: () => void;
	onTaskDrop: (dragData: DragDropData) => void;
	onTaskClick: (taskId: string) => void;
	onTaskEdit: (taskId: string) => void;
	onTaskCreate: (columnId: string) => void;
}

export const KanbanSwimLane: React.FC<KanbanSwimLaneProps> = ({
	swimLane,
	columns,
	getTasksForColumn,
	draggedTask,
	onDragStart,
	onDragEnd,
	onTaskDrop,
	onTaskClick,
	onTaskEdit,
	onTaskCreate
}) => {
	// 计算泳道中的总任务数
	const totalTasks = columns.reduce((total, column) => {
		return total + getTasksForColumn(column.id).length;
	}, 0);

	return (
		<div className="ptm-kanban-swimlane" style={{ marginBottom: '2rem' }}>
			{/* 泳道头部 */}
			<Card variant="outlined" style={{ marginBottom: '1rem' }}>
				<div 
					className="ptm-swimlane-header" 
					style={{ 
						padding: '0.75rem 1rem',
						borderLeft: `4px solid ${swimLane.color || '#6b7280'}`,
						backgroundColor: 'var(--background-secondary)'
					}}
				>
					<Flex justify="between" align="center">
						<div>
							<h3 style={{ 
								margin: 0, 
								fontSize: '1rem', 
								fontWeight: 600,
								color: 'var(--text-normal)'
							}}>
								{swimLane.title}
							</h3>
							<p style={{ 
								margin: '0.25rem 0 0 0', 
								fontSize: '0.75rem', 
								color: 'var(--text-muted)' 
							}}>
								{totalTasks} 个任务
							</p>
						</div>
						
						{/* 泳道类型标识 */}
						<div 
							className="ptm-swimlane-type"
							style={{
								padding: '0.25rem 0.5rem',
								backgroundColor: 'var(--interactive-normal)',
								color: 'var(--text-muted)',
								borderRadius: '0.25rem',
								fontSize: '0.75rem',
								fontWeight: 500
							}}
						>
							{swimLane.type === 'priority' ? '优先级' :
							 swimLane.type === 'assignee' ? '负责人' :
							 swimLane.type === 'tag' ? '标签' : '自定义'}
						</div>
					</Flex>
				</div>
			</Card>

			{/* 泳道列 */}
			<div className="ptm-swimlane-columns" style={{ 
				display: 'flex', 
				gap: '1rem',
				overflowX: 'auto',
				paddingBottom: '0.5rem'
			}}>
				{columns
					.filter(col => !col.isCollapsed)
					.sort((a, b) => a.position - b.position)
					.map(column => (
						<KanbanColumnComponent
							key={`${swimLane.id}-${column.id}`}
							column={column}
							tasks={getTasksForColumn(column.id)}
							stats={null} // 在泳道模式下不显示统计信息
							draggedTask={draggedTask}
							onDragStart={onDragStart}
							onDragEnd={onDragEnd}
							onTaskDrop={onTaskDrop}
							onTaskClick={onTaskClick}
							onTaskEdit={onTaskEdit}
							onTaskCreate={() => onTaskCreate(column.id)}
						/>
					))}
			</div>
		</div>
	);
};