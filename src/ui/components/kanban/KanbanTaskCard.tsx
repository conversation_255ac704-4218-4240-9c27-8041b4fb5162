// 看板任务卡片组件

import React, { useCallback } from 'react';
import { KanbanTaskCard as KanbanTaskCardType } from '../../../models/Kanban';
import { TaskUtils } from '../../../models/Task';
import { Card } from '../common/Card';
import { Button } from '../common/Button';

export interface KanbanTaskCardProps {
	task: KanbanTaskCardType;
	isDragging: boolean;
	onDragStart: () => void;
	onDragEnd: () => void;
	onClick: () => void;
	onEdit: () => void;
}

export const KanbanTaskCard: React.FC<KanbanTaskCardProps> = ({
	task,
	isDragging,
	onDragStart,
	onDragEnd,
	onClick,
	onEdit
}) => {
	// 处理拖拽开始
	const handleDragStart = useCallback((e: React.DragEvent) => {
		e.dataTransfer.setData('text/plain', task.taskId);
		e.dataTransfer.effectAllowed = 'move';
		onDragStart();
	}, [task.taskId, onDragStart]);

	// 处理拖拽结束
	const handleDragEnd = useCallback(() => {
		onDragEnd();
	}, [onDragEnd]);

	// 处理卡片点击
	const handleClick = useCallback((e: React.MouseEvent) => {
		e.preventDefault();
		onClick();
	}, [onClick]);

	// 处理编辑按钮点击
	const handleEditClick = useCallback((e: React.MouseEvent) => {
		e.stopPropagation();
		onEdit();
	}, [onEdit]);

	// 获取优先级颜色
	const getPriorityColor = (priority: string): string => {
		switch (priority) {
			case 'critical': return '#ef4444';
			case 'high': return '#f97316';
			case 'medium': return '#eab308';
			case 'low': return '#6b7280';
			default: return '#6b7280';
		}
	};

	// 获取状态emoji
	const getStatusEmoji = () => {
		return TaskUtils.getEmojiForStatus(task.status);
	};

	// 格式化日期 - 安全处理可能的字符串或Date对象
	const formatDate = (date: Date | string | undefined): string => {
		if (!date) return '';
		
		try {
			const dateObj = typeof date === 'string' ? new Date(date) : date;
			if (isNaN(dateObj.getTime())) return '无效日期';
			
			return new Intl.DateTimeFormat('zh-CN', {
				month: 'short',
				day: 'numeric'
			}).format(dateObj);
		} catch (error) {
			console.warn('日期格式化失败:', date, error);
			return '日期错误';
		}
	};

	// 安全获取日期字符串用于title
	const getDateString = (date: Date | string | undefined): string => {
		if (!date) return '';
		
		try {
			const dateObj = typeof date === 'string' ? new Date(date) : date;
			if (isNaN(dateObj.getTime())) return '无效日期';
			
			return dateObj.toLocaleDateString('zh-CN');
		} catch (error) {
			console.warn('日期字符串获取失败:', date, error);
			return '日期错误';
		}
	};

	// 卡片样式
	const getCardStyle = (): React.CSSProperties => {
		const baseStyle: React.CSSProperties = {
			cursor: 'pointer',
			transition: 'all 0.2s ease',
			borderLeft: `4px solid ${getPriorityColor(task.priority)}`
		};

		if (isDragging) {
			baseStyle.opacity = 0.5;
			baseStyle.transform = 'rotate(5deg)';
		}

		if (task.isOverdue) {
			baseStyle.backgroundColor = 'rgba(239, 68, 68, 0.1)';
		}

		return baseStyle;
	};

	return (
		<div
			className={`ptm-task-card ${isDragging ? 'dragging' : ''} ${task.isOverdue ? 'overdue' : ''}`}
			style={getCardStyle()}
			draggable
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
			onClick={handleClick}
		>
			<Card variant="outlined">
				{/* 卡片头部 */}
				<div className="ptm-task-card-header">
					<div className="ptm-task-status">
						<span className="ptm-status-emoji">{getStatusEmoji()}</span>
					</div>
					<div className="ptm-task-actions">
						<Button
							variant="ghost"
							size="small"
							onClick={handleEditClick}
							title="编辑任务"
						>
							✏️
						</Button>
					</div>
				</div>

				{/* 任务标题 */}
				<div className="ptm-task-title">
					<h4>{task.title}</h4>
				</div>

				{/* 任务描述 */}
				{task.description && (
					<div className="ptm-task-description">
						<p>{task.description.length > 100 ? 
							`${task.description.substring(0, 100)}...` : 
							task.description
						}</p>
					</div>
				)}

				{/* 任务标签 */}
				{task.tags.length > 0 && (
					<div className="ptm-task-tags">
						{task.tags.slice(0, 3).map((tag, index) => (
							<span key={`${task.taskId}-tag-${index}`} className="ptm-tag">
								{tag}
							</span>
						))}
						{task.tags.length > 3 && (
							<span key={`${task.taskId}-tag-more`} className="ptm-tag-more">
								+{task.tags.length - 3}
							</span>
						)}
					</div>
				)}

				{/* 任务元信息 */}
				<div className="ptm-task-meta">
					{/* 负责人 */}
					{task.assignee && (
						<div className="ptm-task-assignee" title={`负责人: ${task.assignee}`}>
							<span className="ptm-assignee-avatar">
								{task.assignee.charAt(0).toUpperCase()}
							</span>
						</div>
					)}

					{/* 截止日期 */}
					{task.dueDate && (
						<div 
							className={`ptm-task-due-date ${task.isOverdue ? 'overdue' : ''}`}
							title={`截止日期: ${getDateString(task.dueDate)}`}
						>
							<span>📅</span>
							<span>{formatDate(task.dueDate)}</span>
						</div>
					)}

					{/* 工时信息 */}
					{(task.estimatedHours || task.actualHours) && (
						<div className="ptm-task-hours" title="预估/实际工时">
							<span>⏱️</span>
							<span>
								{task.estimatedHours || 0}h
								{task.actualHours ? `/${task.actualHours}h` : ''}
							</span>
						</div>
					)}
				</div>

				{/* 任务指示器 */}
				<div className="ptm-task-indicators">
					{/* 子任务指示器 */}
					{task.hasChildren && (
						<div className="ptm-task-indicator" title={`${task.childrenCount} 个子任务`}>
							<span>👥</span>
							<span>{task.childrenCount}</span>
						</div>
					)}

					{/* 依赖关系指示器 */}
					{task.dependenciesCount > 0 && (
						<div className="ptm-task-indicator" title={`${task.dependenciesCount} 个依赖`}>
							<span>🔗</span>
							<span>{task.dependenciesCount}</span>
						</div>
					)}

					{/* 关联笔记指示器 */}
					{task.linkedNotesCount > 0 && (
						<div className="ptm-task-indicator" title={`${task.linkedNotesCount} 个关联笔记`}>
							<span>📝</span>
							<span>{task.linkedNotesCount}</span>
						</div>
					)}
				</div>
			</Card>
		</div>
	);
};