// 看板统计信息组件

import React from 'react';
import { KanbanConfig, KanbanStats as KanbanStatsType } from '../../../models/Kanban';
import { Card } from '../common/Card';
import { Flex } from '../layout/Layout';

export interface KanbanStatsProps {
	stats: KanbanStatsType;
	config: KanbanConfig;
}

export const KanbanStats: React.FC<KanbanStatsProps> = ({
	stats,
	config
}) => {
	return (
		<Card variant="outlined" style={{ margin: '1rem 0' }}>
			<div className="ptm-kanban-stats" style={{ padding: '1rem' }}>
				<Flex gap="lg" wrap>
					{/* 总体统计 */}
					<div className="ptm-stat-group">
						<h4 style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem', color: 'var(--text-muted)' }}>
							总体统计
						</h4>
						<Flex gap="md">
							<div className="ptm-stat-item">
								<span className="ptm-stat-value" style={{ 
									fontSize: '1.25rem', 
									fontWeight: 600, 
									color: 'var(--text-normal)' 
								}}>
									{stats.totalTasks}
								</span>
								<span className="ptm-stat-label" style={{ 
									fontSize: '0.75rem', 
									color: 'var(--text-muted)',
									display: 'block'
								}}>
									总任务
								</span>
							</div>
							<div className="ptm-stat-item">
								<span className="ptm-stat-value" style={{ 
									fontSize: '1.25rem', 
									fontWeight: 600, 
									color: 'var(--text-success)' 
								}}>
									{stats.completionRate}%
								</span>
								<span className="ptm-stat-label" style={{ 
									fontSize: '0.75rem', 
									color: 'var(--text-muted)',
									display: 'block'
								}}>
									完成率
								</span>
							</div>
							{stats.overdueTasksCount > 0 && (
								<div className="ptm-stat-item">
									<span className="ptm-stat-value" style={{ 
										fontSize: '1.25rem', 
										fontWeight: 600, 
										color: 'var(--text-error)' 
									}}>
										{stats.overdueTasksCount}
									</span>
									<span className="ptm-stat-label" style={{ 
										fontSize: '0.75rem', 
										color: 'var(--text-muted)',
										display: 'block'
									}}>
										逾期任务
									</span>
								</div>
							)}
						</Flex>
					</div>

					{/* 按列统计 */}
					<div className="ptm-stat-group">
						<h4 style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem', color: 'var(--text-muted)' }}>
							各列任务数
						</h4>
						<Flex gap="md" wrap>
							{config.columns.map(column => {
								const count = stats.tasksByColumn[column.id] || 0;
								const isWipViolation = stats.wipViolations.includes(column.id);
								
								return (
									<div key={column.id} className="ptm-stat-item">
										<span 
											className="ptm-stat-value" 
											style={{ 
												fontSize: '1rem', 
												fontWeight: 600, 
												color: isWipViolation ? 'var(--text-error)' : 'var(--text-normal)'
											}}
										>
											{count}
											{column.wipLimit && config.showWipLimits && (
												<span style={{ fontSize: '0.75rem', color: 'var(--text-muted)' }}>
													/{column.wipLimit}
												</span>
											)}
										</span>
										<span 
											className="ptm-stat-label" 
											style={{ 
												fontSize: '0.75rem', 
												color: 'var(--text-muted)',
												display: 'block'
											}}
										>
											{column.title}
										</span>
									</div>
								);
							})}
						</Flex>
					</div>

					{/* 泳道统计 */}
					{config.enableSwimLanes && stats.tasksBySwimLane && (
						<div className="ptm-stat-group">
							<h4 style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem', color: 'var(--text-muted)' }}>
								泳道分布
							</h4>
							<Flex gap="md" wrap>
								{config.swimLanes?.map(swimLane => {
									const count = stats.tasksBySwimLane![swimLane.id] || 0;
									
									return (
										<div key={swimLane.id} className="ptm-stat-item">
											<span className="ptm-stat-value" style={{ 
												fontSize: '1rem', 
												fontWeight: 600, 
												color: 'var(--text-normal)' 
											}}>
												{count}
											</span>
											<span className="ptm-stat-label" style={{ 
												fontSize: '0.75rem', 
												color: 'var(--text-muted)',
												display: 'block'
											}}>
												{swimLane.title}
											</span>
										</div>
									);
								})}
							</Flex>
						</div>
					)}

					{/* WIP 限制警告 */}
					{stats.wipViolations.length > 0 && (
						<div className="ptm-stat-group">
							<h4 style={{ 
								margin: '0 0 0.5rem 0', 
								fontSize: '0.875rem', 
								color: 'var(--text-error)' 
							}}>
								WIP 限制警告
							</h4>
							<div style={{ 
								padding: '0.5rem', 
								backgroundColor: 'rgba(239, 68, 68, 0.1)', 
								border: '1px solid var(--text-error)',
								borderRadius: '0.25rem',
								fontSize: '0.8125rem'
							}}>
								以下列超出了 WIP 限制：
								{stats.wipViolations.map(columnId => {
									const column = config.columns.find(col => col.id === columnId);
									return column ? ` ${column.title}` : '';
								}).join(', ')}
							</div>
						</div>
					)}
				</Flex>
			</div>
		</Card>
	);
};