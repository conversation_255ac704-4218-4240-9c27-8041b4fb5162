// 任务卡片测试组件

import React, { useState } from 'react';
import { KanbanTaskCard } from './KanbanTaskCard';
import { KanbanTaskCard as KanbanTaskCardType } from '../../../models/Kanban';
import { TaskStatus } from '../../../models/Task';
import { Container, Stack } from '../layout/Layout';
import { Card, CardHeader, CardContent } from '../common/Card';
import { Button } from '../common/Button';

// 创建测试数据
const createTestTask = (id: string, overrides: Partial<KanbanTaskCardType> = {}): KanbanTaskCardType => ({
	taskId: id,
	title: `测试任务 ${id}`,
	description: '这是一个测试任务的描述，用于验证任务卡片组件的显示效果。',
	status: TaskStatus.TODO,
	priority: 'medium',
	assignee: '张三',
	tags: ['前端', '开发', 'React'],
	dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
	estimatedHours: 8,
	actualHours: 4,
	isOverdue: false,
	hasChildren: true,
	childrenCount: 3,
	dependenciesCount: 1,
	linkedNotesCount: 2,
	...overrides
});

const testTasks: KanbanTaskCardType[] = [
	createTestTask('1', { 
		title: '普通任务',
		priority: 'medium',
		tags: ['前端', '开发', 'React']
	}),
	createTestTask('2', { 
		title: '高优先级任务',
		priority: 'high',
		status: TaskStatus.IN_PROGRESS,
		tags: ['后端', 'API', 'Node.js']
	}),
	createTestTask('3', { 
		title: '紧急任务',
		priority: 'critical',
		isOverdue: true,
		dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
		tags: ['Bug修复', '生产环境', '紧急']
	}),
	createTestTask('4', { 
		title: '已完成任务',
		status: TaskStatus.COMPLETED,
		priority: 'low',
		actualHours: 6,
		tags: ['文档', '测试']
	}),
	createTestTask('5', { 
		title: '无负责人任务',
		assignee: undefined,
		tags: [],
		hasChildren: false,
		childrenCount: 0,
		dependenciesCount: 0,
		linkedNotesCount: 0
	})
];

export const KanbanTaskCardTest: React.FC = () => {
	const [draggedTask, setDraggedTask] = useState<string | null>(null);
	const [selectedTask, setSelectedTask] = useState<string | null>(null);
	const [editingTask, setEditingTask] = useState<string | null>(null);

	const handleDragStart = (taskId: string) => {
		console.log('拖拽开始:', taskId);
		setDraggedTask(taskId);
	};

	const handleDragEnd = () => {
		console.log('拖拽结束');
		setDraggedTask(null);
	};

	const handleTaskClick = (taskId: string) => {
		console.log('任务点击:', taskId);
		setSelectedTask(taskId);
	};

	const handleTaskEdit = (taskId: string) => {
		console.log('任务编辑:', taskId);
		setEditingTask(taskId);
	};

	return (
		<Container padding>
			<Stack spacing="lg">
				<Card variant="elevated">
					<CardHeader>
						<h2>看板任务卡片测试</h2>
						<p>测试不同状态和属性的任务卡片显示效果</p>
					</CardHeader>
					<CardContent>
						<Stack spacing="md">
							{/* 状态显示 */}
							<div>
								<h3>当前状态</h3>
								<ul>
									<li>拖拽中的任务: {draggedTask || '无'}</li>
									<li>选中的任务: {selectedTask || '无'}</li>
									<li>编辑中的任务: {editingTask || '无'}</li>
								</ul>
							</div>

							{/* 重置按钮 */}
							<div>
								<Button 
									variant="secondary" 
									onClick={() => {
										setDraggedTask(null);
										setSelectedTask(null);
										setEditingTask(null);
									}}
								>
									重置状态
								</Button>
							</div>
						</Stack>
					</CardContent>
				</Card>

				{/* 任务卡片展示 */}
				<Card variant="elevated">
					<CardHeader>
						<h3>任务卡片展示</h3>
					</CardHeader>
					<CardContent>
						<div style={{ 
							display: 'grid', 
							gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
							gap: '1rem' 
						}}>
							{testTasks.map(task => (
								<div key={task.taskId} style={{ maxWidth: '350px' }}>
									<h4 style={{ marginBottom: '0.5rem', fontSize: '0.9rem', color: 'var(--text-muted)' }}>
										{task.title} ({task.status})
									</h4>
									<KanbanTaskCard
										task={task}
										isDragging={draggedTask === task.taskId}
										onDragStart={() => handleDragStart(task.taskId)}
										onDragEnd={handleDragEnd}
										onClick={() => handleTaskClick(task.taskId)}
										onEdit={() => handleTaskEdit(task.taskId)}
									/>
								</div>
							))}
						</div>
					</CardContent>
				</Card>

				{/* 拖拽测试区域 */}
				<Card variant="elevated">
					<CardHeader>
						<h3>拖拽测试区域</h3>
						<p>将任务卡片拖拽到下面的区域进行测试</p>
					</CardHeader>
					<CardContent>
						<div 
							style={{
								minHeight: '200px',
								border: '2px dashed var(--background-modifier-border)',
								borderRadius: '8px',
								display: 'flex',
								alignItems: 'center',
								justifyContent: 'center',
								backgroundColor: 'var(--background-secondary)',
								color: 'var(--text-muted)'
							}}
							onDragOver={(e) => {
								e.preventDefault();
								e.currentTarget.style.backgroundColor = 'var(--background-modifier-hover)';
							}}
							onDragLeave={(e) => {
								e.currentTarget.style.backgroundColor = 'var(--background-secondary)';
							}}
							onDrop={(e) => {
								e.preventDefault();
								e.currentTarget.style.backgroundColor = 'var(--background-secondary)';
								const taskId = e.dataTransfer.getData('text/plain');
								console.log('任务放置:', taskId);
								alert(`任务 ${taskId} 已放置到测试区域`);
							}}
						>
							<p>拖拽任务卡片到这里</p>
						</div>
					</CardContent>
				</Card>
			</Stack>
		</Container>
	);
};