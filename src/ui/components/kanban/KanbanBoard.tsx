// 看板主组件

import React, { useState, useEffect, useCallback } from 'react';
import { KanbanConfig, KanbanTaskCard, KanbanStats, DragDropData } from '../../../models/Kanban';
import { KanbanManager } from '../../../services/KanbanManager';
import { KanbanColumn } from './KanbanColumn';
import { KanbanSwimLane } from './KanbanSwimLane';
import { KanbanHeader } from './KanbanHeader';
import { KanbanStats as KanbanStatsComponent } from './KanbanStats';
import { Card } from '../common/Card';
import { Button } from '../common/Button';
import { Container, Stack, Flex } from '../layout/Layout';

export interface KanbanBoardProps {
	kanbanManager: KanbanManager;
	kanbanId: string;
	onTaskSelect?: (taskId: string) => void;
	onTaskEdit?: (taskId: string) => void;
	onTaskCreate?: (columnId: string) => void;
	onConfigEdit?: () => void;
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
	kanbanManager,
	kanbanId,
	onTaskSelect,
	onTaskEdit,
	onTaskCreate,
	onConfigEdit
}) => {
	const [config, setConfig] = useState<KanbanConfig | null>(null);
	const [tasks, setTasks] = useState<KanbanTaskCard[]>([]);
	const [stats, setStats] = useState<KanbanStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [draggedTask, setDraggedTask] = useState<string | null>(null);

	// 加载看板数据
	const loadKanbanData = useCallback(async () => {
		try {
			setLoading(true);
			setError(null);

			const kanbanConfig = kanbanManager.getKanban(kanbanId);
			if (!kanbanConfig) {
				throw new Error(`看板不存在: ${kanbanId}`);
			}

			const [kanbanTasks, kanbanStats] = await Promise.all([
				kanbanManager.getKanbanTasks(kanbanId),
				kanbanManager.getKanbanStats(kanbanId)
			]);

			setConfig(kanbanConfig);
			setTasks(kanbanTasks);
			setStats(kanbanStats);
		} catch (err) {
			console.error('KanbanBoard: 加载数据失败', err);
			setError(err instanceof Error ? err.message : '加载失败');
		} finally {
			setLoading(false);
		}
	}, [kanbanManager, kanbanId]);

	// 初始化和数据刷新
	useEffect(() => {
		loadKanbanData();
	}, [loadKanbanData]);

	// 自动刷新
	useEffect(() => {
		if (config?.autoRefresh && config.refreshInterval > 0) {
			const interval = setInterval(loadKanbanData, config.refreshInterval * 1000);
			return () => clearInterval(interval);
		}
	}, [config, loadKanbanData]);

	// 处理任务拖拽开始
	const handleDragStart = useCallback((taskId: string) => {
		setDraggedTask(taskId);
	}, []);

	// 处理任务拖拽结束
	const handleDragEnd = useCallback(() => {
		setDraggedTask(null);
	}, []);

	// 处理任务拖拽放置
	const handleTaskDrop = useCallback(async (dragData: DragDropData) => {
		try {
			await kanbanManager.handleTaskDrag(kanbanId, dragData);
			await loadKanbanData(); // 重新加载数据
		} catch (err) {
			console.error('KanbanBoard: 拖拽失败', err);
			setError(err instanceof Error ? err.message : '拖拽失败');
		}
	}, [kanbanManager, kanbanId, loadKanbanData]);

	// 处理任务点击
	const handleTaskClick = useCallback((taskId: string) => {
		onTaskSelect?.(taskId);
	}, [onTaskSelect]);

	// 处理任务编辑
	const handleTaskEdit = useCallback((taskId: string) => {
		onTaskEdit?.(taskId);
	}, [onTaskEdit]);

	// 处理创建任务
	const handleTaskCreate = useCallback((columnId: string) => {
		onTaskCreate?.(columnId);
	}, [onTaskCreate]);

	// 按列分组任务
	const getTasksByColumn = useCallback((columnId: string) => {
		if (!config) return [];
		
		const column = config.columns.find(col => col.id === columnId);
		if (!column) return [];
		
		return tasks.filter(task => task.status === column.status);
	}, [config, tasks]);

	// 按泳道分组任务
	const getTasksBySwimLane = useCallback((swimLaneId: string, columnId: string) => {
		if (!config) return [];
		
		const column = config.columns.find(col => col.id === columnId);
		const swimLane = config.swimLanes?.find(lane => lane.id === swimLaneId);
		
		if (!column || !swimLane) return [];
		
		return tasks.filter(task => {
			if (task.status !== column.status) return false;
			
			// 根据泳道类型匹配任务
			switch (swimLane.type) {
				case 'priority':
					return task.priority === swimLane.value;
				case 'assignee':
					return swimLane.value === '' ? !task.assignee : task.assignee === swimLane.value;
				case 'tag':
					return task.tags.includes(swimLane.value);
				default:
					return false;
			}
		});
	}, [config, tasks]);

	if (loading) {
		return (
			<Container padding>
				<div className="ptm-kanban-loading">
					<div className="ptm-spinner">
						<svg viewBox="0 0 24 24">
							<circle
								className="ptm-spinner__circle"
								cx="12"
								cy="12"
								r="10"
								fill="none"
								strokeWidth="2"
							/>
						</svg>
					</div>
					<p>加载看板数据...</p>
				</div>
			</Container>
		);
	}

	if (error) {
		return (
			<Container padding>
				<Card variant="elevated">
					<div className="ptm-error-state">
						<h3>加载失败</h3>
						<p>{error}</p>
						<Flex gap="sm">
							<Button variant="primary" onClick={loadKanbanData}>
								重试
							</Button>
							{onConfigEdit && (
								<Button variant="secondary" onClick={onConfigEdit}>
									编辑配置
								</Button>
							)}
						</Flex>
					</div>
				</Card>
			</Container>
		);
	}

	if (!config) {
		return (
			<Container padding>
				<Card variant="elevated">
					<div className="ptm-empty-state">
						<h3>看板不存在</h3>
						<p>请检查看板配置或创建新的看板。</p>
					</div>
				</Card>
			</Container>
		);
	}

	return (
		<div className="ptm-kanban-board">
			{/* 看板头部 */}
			<KanbanHeader
				config={config}
				stats={stats}
				onRefresh={loadKanbanData}
				onConfigEdit={onConfigEdit}
			/>

			{/* 统计信息 */}
			{stats && config.showTaskCount && (
				<KanbanStatsComponent stats={stats} config={config} />
			)}

			{/* 看板内容 */}
			<div className="ptm-kanban-content">
				{config.enableSwimLanes && config.swimLanes && config.swimLanes.length > 0 ? (
					// 泳道模式
					<div className="ptm-kanban-swimlanes">
						{config.swimLanes
							.filter(lane => !lane.isCollapsed)
							.sort((a, b) => a.position - b.position)
							.map(swimLane => (
								<KanbanSwimLane
									key={swimLane.id}
									swimLane={swimLane}
									columns={config.columns}
									getTasksForColumn={(columnId: string) => getTasksBySwimLane(swimLane.id, columnId)}
									draggedTask={draggedTask}
									onDragStart={handleDragStart}
									onDragEnd={handleDragEnd}
									onTaskDrop={handleTaskDrop}
									onTaskClick={handleTaskClick}
									onTaskEdit={handleTaskEdit}
									onTaskCreate={handleTaskCreate}
								/>
							))}
					</div>
				) : (
					// 普通列模式
					<div className="ptm-kanban-columns">
						{config.columns
							.filter(col => !col.isCollapsed)
							.sort((a, b) => a.position - b.position)
							.map(column => (
								<KanbanColumn
									key={column.id}
									column={column}
									tasks={getTasksByColumn(column.id)}
									stats={stats}
									draggedTask={draggedTask}
									onDragStart={handleDragStart}
									onDragEnd={handleDragEnd}
									onTaskDrop={handleTaskDrop}
									onTaskClick={handleTaskClick}
									onTaskEdit={handleTaskEdit}
									onTaskCreate={() => handleTaskCreate(column.id)}
								/>
							))}
					</div>
				)}
			</div>
		</div>
	);
};