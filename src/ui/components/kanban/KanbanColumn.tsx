// 看板列组件

import React, { useState, useCallback } from 'react';
import { KanbanColumn as KanbanColumnType, KanbanTaskCard, KanbanStats, DragDropData } from '../../../models/Kanban';
import { KanbanTaskCard as TaskCardComponent } from './KanbanTaskCard';
import { Button } from '../common/Button';
import { Card } from '../common/Card';

export interface KanbanColumnProps {
	column: KanbanColumnType;
	tasks: KanbanTaskCard[];
	stats: KanbanStats | null;
	draggedTask: string | null;
	onDragStart: (taskId: string) => void;
	onDragEnd: () => void;
	onTaskDrop: (dragData: DragDropData) => void;
	onTaskClick: (taskId: string) => void;
	onTaskEdit: (taskId: string) => void;
	onTaskCreate: () => void;
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
	column,
	tasks,
	stats,
	draggedTask,
	onDragStart,
	onDragEnd,
	onTaskDrop,
	onTaskClick,
	onTaskEdit,
	onTaskCreate
}) => {
	const [isDragOver, setIsDragOver] = useState(false);

	// 获取列中的任务数量
	const taskCount = tasks.length;
	
	// 检查是否违反WIP限制
	const isWipViolation = stats?.wipViolations.includes(column.id) || false;
	const isWipWarning = column.wipLimit && taskCount >= column.wipLimit * 0.8;

	// 处理拖拽进入
	const handleDragEnter = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(true);
	}, []);

	// 处理拖拽离开
	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		// 只有当拖拽真正离开列容器时才设置为false
		if (!e.currentTarget.contains(e.relatedTarget as Node)) {
			setIsDragOver(false);
		}
	}, []);

	// 处理拖拽悬停
	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
	}, []);

	// 处理拖拽放置
	const handleDrop = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(false);

		if (!draggedTask) return;

		// 计算放置位置
		const rect = e.currentTarget.getBoundingClientRect();
		const y = e.clientY - rect.top;
		const taskElements = e.currentTarget.querySelectorAll('.ptm-task-card');
		
		let targetIndex = tasks.length;
		
		for (let i = 0; i < taskElements.length; i++) {
			const taskRect = taskElements[i].getBoundingClientRect();
			const taskY = taskRect.top - rect.top + taskRect.height / 2;
			
			if (y < taskY) {
				targetIndex = i;
				break;
			}
		}

		// 找到源列ID
		const draggedTaskData = tasks.find(t => t.taskId === draggedTask);
		const sourceColumnId = draggedTaskData ? 
			getColumnIdByStatus(draggedTaskData.status) : column.id;

		const dragData: DragDropData = {
			taskId: draggedTask,
			sourceColumnId,
			targetColumnId: column.id,
			sourceIndex: 0, // 这里可以优化为实际的源索引
			targetIndex
		};

		onTaskDrop(dragData);
	}, [draggedTask, tasks, column.id, onTaskDrop]);

	// 根据状态获取列ID（简化实现）
	const getColumnIdByStatus = (status: string): string => {
		// 这里应该从配置中查找，简化实现直接返回列ID
		return column.id;
	};

	// 列标题样式
	const getColumnHeaderStyle = (): React.CSSProperties => {
		const baseStyle: React.CSSProperties = {
			borderTop: `3px solid ${column.color || '#6b7280'}`
		};

		if (isWipViolation) {
			baseStyle.borderTopColor = '#ef4444';
		} else if (isWipWarning) {
			baseStyle.borderTopColor = '#f59e0b';
		}

		return baseStyle;
	};

	// 列容器样式
	const getColumnStyle = (): React.CSSProperties => {
		const baseStyle: React.CSSProperties = {
			minHeight: '400px',
			transition: 'all 0.2s ease'
		};

		if (isDragOver) {
			baseStyle.backgroundColor = 'var(--background-secondary)';
			baseStyle.transform = 'scale(1.02)';
		}

		return baseStyle;
	};

	return (
		<div 
			className={`ptm-kanban-column ${isDragOver ? 'drag-over' : ''}`}
			style={getColumnStyle()}
			onDragEnter={handleDragEnter}
			onDragLeave={handleDragLeave}
			onDragOver={handleDragOver}
			onDrop={handleDrop}
		>
			<Card variant="outlined">
				{/* 列头部 */}
				<div 
					className="ptm-kanban-column-header"
					style={getColumnHeaderStyle()}
				>
					<div className="ptm-column-title">
						<h3>{column.title}</h3>
						<div className="ptm-column-badges">
							{/* 任务数量 */}
							<span className="ptm-task-count">
								{taskCount}
							</span>
							
							{/* WIP限制指示器 */}
							{column.wipLimit && (
								<span 
									className={`ptm-wip-indicator ${
										isWipViolation ? 'violation' : 
										isWipWarning ? 'warning' : 'normal'
									}`}
									title={`WIP限制: ${taskCount}/${column.wipLimit}`}
								>
									{taskCount}/{column.wipLimit}
								</span>
							)}
						</div>
					</div>

					{/* 添加任务按钮 */}
					<Button
						variant="ghost"
						size="small"
						onClick={onTaskCreate}
						className="ptm-add-task-btn"
						title="添加任务"
					>
						+
					</Button>
				</div>

				{/* 任务列表 */}
				<div className="ptm-kanban-column-content">
					{tasks.length === 0 ? (
						<div className="ptm-empty-column">
							<p>暂无任务</p>
							<Button
								variant="ghost"
								size="small"
								onClick={onTaskCreate}
							>
								添加第一个任务
							</Button>
						</div>
					) : (
						<div className="ptm-task-list">
							{tasks.map((task, index) => (
								<TaskCardComponent
									key={task.taskId}
									task={task}
									isDragging={draggedTask === task.taskId}
									onDragStart={() => onDragStart(task.taskId)}
									onDragEnd={onDragEnd}
									onClick={() => onTaskClick(task.taskId)}
									onEdit={() => onTaskEdit(task.taskId)}
								/>
							))}
						</div>
					)}
				</div>

				{/* 拖拽放置区域指示器 */}
				{isDragOver && (
					<div className="ptm-drop-indicator">
						<div className="ptm-drop-zone">
							<span>放置任务到这里</span>
						</div>
					</div>
				)}
			</Card>
		</div>
	);
};