// 看板头部组件

import React from 'react';
import { KanbanConfig, KanbanStats } from '../../../models/Kanban';
import { Button } from '../common/Button';
import { Card } from '../common/Card';
import { Flex } from '../layout/Layout';

export interface KanbanHeaderProps {
	config: KanbanConfig;
	stats: KanbanStats | null;
	onRefresh: () => void;
	onConfigEdit?: () => void;
}

export const KanbanHeader: React.FC<KanbanHeaderProps> = ({
	config,
	stats,
	onRefresh,
	onConfigEdit
}) => {
	return (
		<Card variant="outlined">
			<div className="ptm-kanban-header" style={{ padding: '1rem' }}>
				<Flex justify="between" align="center">
					<div className="ptm-kanban-title">
						<h2 style={{ margin: 0, fontSize: '1.25rem', fontWeight: 600 }}>
							{config.name}
						</h2>
						{config.projectId && (
							<p style={{ margin: '0.25rem 0 0 0', fontSize: '0.875rem', color: 'var(--text-muted)' }}>
								项目看板
							</p>
						)}
					</div>

					<Flex gap="sm" align="center">
						{/* 统计信息快速预览 */}
						{stats && (
							<div className="ptm-quick-stats" style={{ 
								display: 'flex', 
								gap: '1rem', 
								fontSize: '0.875rem',
								color: 'var(--text-muted)'
							}}>
								<span>总任务: {stats.totalTasks}</span>
								<span>完成率: {stats.completionRate}%</span>
								{stats.overdueTasksCount > 0 && (
									<span style={{ color: 'var(--text-error)' }}>
										逾期: {stats.overdueTasksCount}
									</span>
								)}
							</div>
						)}

						{/* 操作按钮 */}
						<Button
							variant="ghost"
							size="small"
							onClick={onRefresh}
							title="刷新看板"
						>
							🔄
						</Button>

						{onConfigEdit && (
							<Button
								variant="ghost"
								size="small"
								onClick={onConfigEdit}
								title="编辑看板配置"
							>
								⚙️
							</Button>
						)}
					</Flex>
				</Flex>
			</div>
		</Card>
	);
};