/**
 * SprintRetrospective组件测试
 * 测试样式隔离和基本功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SprintRetrospective } from '../SprintRetrospective';
import { Sprint, SprintStatus } from '../../../../types/core';
import { Task, TaskStatus, Priority } from '../../../../models/Task';

// Mock CSS Modules
jest.mock('../SprintRetrospective.module.css', () => ({
	ptmModalContainer: 'ptm-modal-container',
	ptmModalBg: 'ptm-modal-bg',
	ptmModal: 'ptm-modal',
	ptmModalHeader: 'ptm-modal-header',
	ptmModalTitle: 'ptm-modal-title',
	ptmModalCloseButton: 'ptm-modal-close-button',
	ptmModalContent: 'ptm-modal-content',
	ptmSprintRetrospective: 'ptm-sprint-retrospective',
	ptmRetrospectiveStats: 'ptm-retrospective-stats',
	ptmStatsTitle: 'ptm-stats-title',
	ptmStatsGrid: 'ptm-stats-grid',
	ptmStatItem: 'ptm-stat-item',
	ptmStatLabel: 'ptm-stat-label',
	ptmStatValue: 'ptm-stat-value',
	ptmRetrospectiveSection: 'ptm-retrospective-section',
	ptmSectionTitle: 'ptm-section-title',
	ptmRetrospectiveItem: 'ptm-retrospective-item',
	ptmRetrospectiveTextarea: 'ptm-retrospective-textarea',
	ptmRemoveItemButton: 'ptm-remove-item-button',
	ptmAddItemButton: 'ptm-add-item-button',
	ptmVelocityAdjustment: 'ptm-velocity-adjustment',
	ptmVelocityLabel: 'ptm-velocity-label',
	ptmVelocityInput: 'ptm-velocity-input',
	ptmVelocityNote: 'ptm-velocity-note',
	ptmModalButtonContainer: 'ptm-modal-button-container',
	ptmButton: 'ptm-button',
	ptmButtonPrimary: 'ptm-button-primary'
}));

describe('SprintRetrospective组件', () => {
	const mockSprint: Sprint = {
		id: 'sprint_1',
		name: '测试Sprint',
		description: '测试Sprint描述',
		status: SprintStatus.COMPLETED,
		startDate: new Date('2024-01-01'),
		endDate: new Date('2024-01-14'),
		projectId: 'project_1',
		taskIds: ['task_1', 'task_2'],
		velocity: 10,
		createdAt: new Date(),
		updatedAt: new Date()
	};

	const mockTasks: Task[] = [
		{
			id: 'task_1',
			title: '已完成任务',
			status: TaskStatus.COMPLETED,
			priority: Priority.MEDIUM,
			projectId: 'project_1',
			estimatedHours: 8,
			childTaskIds: [],
			dependencies: [],
			tags: [],
			linkedNotes: [],
			position: 0,
			createdAt: new Date(),
			updatedAt: new Date()
		},
		{
			id: 'task_2',
			title: '进行中任务',
			status: TaskStatus.IN_PROGRESS,
			priority: Priority.HIGH,
			projectId: 'project_1',
			estimatedHours: 4,
			childTaskIds: [],
			dependencies: [],
			tags: [],
			linkedNotes: [],
			position: 1,
			createdAt: new Date(),
			updatedAt: new Date()
		}
	];

	const mockOnClose = jest.fn();
	const mockOnSaveRetrospective = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe('样式隔离测试', () => {
		test('应该使用PTM前缀的CSS类名', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 检查主要容器使用了PTM前缀的类名
			const modalContainer = document.querySelector('.ptm-modal-container');
			expect(modalContainer).toBeInTheDocument();

			const modal = document.querySelector('.ptm-modal');
			expect(modal).toBeInTheDocument();

			const modalHeader = document.querySelector('.ptm-modal-header');
			expect(modalHeader).toBeInTheDocument();
		});

		test('应该避免使用通用的CSS类名', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 确保没有使用通用的类名
			expect(document.querySelector('.modal')).not.toBeInTheDocument();
			expect(document.querySelector('.modal-container')).not.toBeInTheDocument();
			expect(document.querySelector('.modal-header')).not.toBeInTheDocument();
			expect(document.querySelector('.button')).not.toBeInTheDocument();
		});

		test('标题元素应该有特定的类名', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 检查标题元素使用了特定类名
			const modalTitle = screen.getByText('Sprint 回顾 - 测试Sprint');
			expect(modalTitle).toHaveClass('ptm-modal-title');

			const statsTitle = screen.getByText('Sprint 统计');
			expect(statsTitle).toHaveClass('ptm-stats-title');
		});

		test('按钮应该使用PTM样式类', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			const cancelButton = screen.getByText('取消');
			expect(cancelButton).toHaveClass('ptm-button');

			const saveButton = screen.getByText('保存回顾');
			expect(saveButton).toHaveClass('ptm-button');
			expect(saveButton).toHaveClass('ptm-button-primary');
		});

		test('输入元素应该使用PTM样式类', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 检查textarea元素
			const textareas = document.querySelectorAll('textarea');
			textareas.forEach(textarea => {
				expect(textarea).toHaveClass('ptm-retrospective-textarea');
			});

			// 检查数字输入框
			const velocityInput = screen.getByDisplayValue('10');
			expect(velocityInput).toHaveClass('ptm-velocity-input');
		});
	});

	describe('基本功能测试', () => {
		test('应该正确显示Sprint统计信息', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 检查统计信息显示
			expect(screen.getByText('50%')).toBeInTheDocument(); // 完成率
			expect(screen.getByText('1/2')).toBeInTheDocument(); // 完成任务数
			expect(screen.getByText('8/12h')).toBeInTheDocument(); // 工时完成
		});

		test('应该支持添加和删除回顾项目', async () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 添加新项目
			const addButtons = screen.getAllByText('+ 添加项目');
			fireEvent.click(addButtons[0]); // 点击"做得好的地方"的添加按钮

			// 应该有新的textarea出现
			const textareas = document.querySelectorAll('textarea');
			expect(textareas.length).toBeGreaterThan(4); // 初始4个 + 新增的

			// 删除项目
			const removeButtons = document.querySelectorAll('.ptm-remove-item-button');
			if (removeButtons.length > 0) {
				fireEvent.click(removeButtons[0]);
			}
		});

		test('应该支持调整团队速度', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			const velocityInput = screen.getByDisplayValue('10');
			fireEvent.change(velocityInput, { target: { value: '15' } });

			expect(velocityInput).toHaveValue(15);
		});

		test('应该支持保存回顾数据', async () => {
			mockOnSaveRetrospective.mockResolvedValue(undefined);

			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			const saveButton = screen.getByText('保存回顾');
			fireEvent.click(saveButton);

			await waitFor(() => {
				expect(mockOnSaveRetrospective).toHaveBeenCalledWith(
					'sprint_1',
					expect.objectContaining({
						whatWentWell: expect.any(Array),
						whatCouldImprove: expect.any(Array),
						actionItems: expect.any(Array),
						teamVelocity: expect.any(Number),
						notes: expect.any(String)
					})
				);
			});
		});

		test('应该支持关闭模态框', () => {
			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 点击关闭按钮
			const closeButton = screen.getByText('×');
			fireEvent.click(closeButton);

			expect(mockOnClose).toHaveBeenCalled();

			// 点击背景关闭
			const modalBg = document.querySelector('.ptm-modal-bg');
			if (modalBg) {
				fireEvent.click(modalBg);
				expect(mockOnClose).toHaveBeenCalledTimes(2);
			}

			// 点击取消按钮
			const cancelButton = screen.getByText('取消');
			fireEvent.click(cancelButton);

			expect(mockOnClose).toHaveBeenCalledTimes(3);
		});
	});

	describe('错误处理测试', () => {
		test('应该处理保存失败的情况', async () => {
			const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
			mockOnSaveRetrospective.mockRejectedValue(new Error('保存失败'));

			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			const saveButton = screen.getByText('保存回顾');
			fireEvent.click(saveButton);

			await waitFor(() => {
				expect(consoleSpy).toHaveBeenCalledWith(
					'保存 Sprint 回顾失败:',
					expect.any(Error)
				);
			});

			consoleSpy.mockRestore();
		});

		test('应该在保存过程中显示加载状态', async () => {
			let resolvePromise: () => void;
			const savePromise = new Promise<void>((resolve) => {
				resolvePromise = resolve;
			});
			mockOnSaveRetrospective.mockReturnValue(savePromise);

			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			const saveButton = screen.getByText('保存回顾');
			fireEvent.click(saveButton);

			// 应该显示加载状态
			expect(screen.getByText('保存中...')).toBeInTheDocument();
			expect(saveButton).toBeDisabled();

			// 完成保存
			resolvePromise!();
			await waitFor(() => {
				expect(mockOnClose).toHaveBeenCalled();
			});
		});
	});

	describe('响应式设计测试', () => {
		test('应该在小屏幕上正确显示', () => {
			// 模拟小屏幕
			Object.defineProperty(window, 'innerWidth', {
				writable: true,
				configurable: true,
				value: 600,
			});

			render(
				<SprintRetrospective
					sprint={mockSprint}
					tasks={mockTasks}
					onClose={mockOnClose}
					onSaveRetrospective={mockOnSaveRetrospective}
				/>
			);

			// 组件应该正常渲染
			expect(screen.getByText('Sprint 回顾 - 测试Sprint')).toBeInTheDocument();
		});
	});
});