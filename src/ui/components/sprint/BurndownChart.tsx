import React, { useMemo } from 'react';
import { Sprint, BurndownPoint, SprintUtils } from '../../../models/Sprint';
import { Task } from '../../../models/Task';

interface BurndownChartProps {
	sprint: Sprint;
	tasks: Task[];
	onClose: () => void;
}

/**
 * 燃尽图组件
 */
export const BurndownChart: React.FC<BurndownChartProps> = ({
	sprint,
	tasks,
	onClose
}) => {
	// 生成燃尽图数据
	const burndownData = useMemo(() => {
		const sprintTasks = tasks.filter(task => sprint.taskIds.includes(task.id));
		const startDate = new Date(sprint.startDate);
		const endDate = new Date(sprint.endDate);
		const duration = SprintUtils.getDuration(sprint);
		
		const data: BurndownPoint[] = [];
		const totalHours = sprintTasks.reduce((sum, task) => sum + (task.estimatedHours || 1), 0);
		
		// 理想燃尽线数据
		const idealBurndown: { date: Date; remainingHours: number }[] = [];
		for (let i = 0; i <= duration; i++) {
			const currentDate = new Date(startDate);
			currentDate.setDate(startDate.getDate() + i);
			
			const idealRemaining = totalHours * (1 - i / duration);
			idealBurndown.push({
				date: currentDate,
				remainingHours: Math.max(0, idealRemaining)
			});
		}
		
		// 实际燃尽数据
		const actualBurndown: BurndownPoint[] = [];
		for (let i = 0; i <= duration; i++) {
			const currentDate = new Date(startDate);
			currentDate.setDate(startDate.getDate() + i);
			
			// 如果是未来日期，跳过
			if (currentDate > new Date()) break;
			
			// 计算到当前日期为止完成的任务
			const completedTasks = sprintTasks.filter(task => {
				return task.completedDate && new Date(task.completedDate) <= currentDate;
			});
			
			const completedHours = completedTasks.reduce((sum, task) => sum + (task.estimatedHours || 1), 0);
			const remainingHours = Math.max(0, totalHours - completedHours);
			
			actualBurndown.push({
				date: currentDate,
				remainingHours,
				completedTasks: completedTasks.length
			});
		}
		
		return { idealBurndown, actualBurndown, totalHours };
	}, [sprint, tasks]);

	// 计算图表尺寸和比例
	const chartWidth = 600;
	const chartHeight = 400;
	const padding = 60;
	const plotWidth = chartWidth - 2 * padding;
	const plotHeight = chartHeight - 2 * padding;
	
	const maxHours = burndownData.totalHours;
	const duration = SprintUtils.getDuration(sprint);
	
	// 坐标转换函数
	const getX = (dayIndex: number) => padding + (dayIndex / duration) * plotWidth;
	const getY = (hours: number) => padding + (1 - hours / maxHours) * plotHeight;

	// 生成理想燃尽线路径
	const idealPath = burndownData.idealBurndown
		.map((point, index) => {
			const x = getX(index);
			const y = getY(point.remainingHours);
			return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
		})
		.join(' ');

	// 生成实际燃尽线路径
	const actualPath = burndownData.actualBurndown
		.map((point, index) => {
			const x = getX(index);
			const y = getY(point.remainingHours);
			return index === 0 ? `M ${x} ${y}` : `L ${x} ${y}`;
		})
		.join(' ');

	// 格式化日期
	const formatDate = (date: Date) => {
		return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
	};

	// 计算统计信息
	const currentProgress = burndownData.actualBurndown.length > 0 
		? burndownData.actualBurndown[burndownData.actualBurndown.length - 1]
		: null;
	
	const expectedProgress = burndownData.idealBurndown.find(point => {
		const today = new Date();
		return point.date.toDateString() === today.toDateString();
	});

	return (
		<div className="modal-container">
			<div className="modal-bg" onClick={onClose}></div>
			<div className="modal modal-large">
				<div className="modal-header">
					<h3>燃尽图 - {sprint.name}</h3>
					<button className="modal-close-button" onClick={onClose}>×</button>
				</div>
				
				<div className="modal-content">
					<div className="burndown-chart-container">
						<div className="burndown-stats">
							<div className="burndown-stat">
								<span className="stat-label">总工时:</span>
								<span className="stat-value">{burndownData.totalHours}h</span>
							</div>
							{currentProgress && (
								<div className="burndown-stat">
									<span className="stat-label">剩余工时:</span>
									<span className="stat-value">{currentProgress.remainingHours}h</span>
								</div>
							)}
							{expectedProgress && currentProgress && (
								<div className="burndown-stat">
									<span className="stat-label">进度状态:</span>
									<span className={`stat-value ${
										currentProgress.remainingHours <= expectedProgress.remainingHours 
											? 'ahead' : 'behind'
									}`}>
										{currentProgress.remainingHours <= expectedProgress.remainingHours 
											? '超前' : '滞后'}
									</span>
								</div>
							)}
						</div>

						<div className="burndown-chart">
							<svg width={chartWidth} height={chartHeight}>
								{/* 网格线 */}
								<defs>
									<pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
										<path d="M 40 0 L 0 0 0 40" fill="none" stroke="var(--background-modifier-border)" strokeWidth="1"/>
									</pattern>
								</defs>
								<rect width={chartWidth} height={chartHeight} fill="url(#grid)" />
								
								{/* 坐标轴 */}
								<line 
									x1={padding} y1={padding} 
									x2={padding} y2={chartHeight - padding}
									stroke="var(--text-normal)" strokeWidth="2"
								/>
								<line 
									x1={padding} y1={chartHeight - padding} 
									x2={chartWidth - padding} y2={chartHeight - padding}
									stroke="var(--text-normal)" strokeWidth="2"
								/>
								
								{/* Y轴标签 */}
								{Array.from({ length: 6 }, (_, i) => {
									const hours = (maxHours / 5) * i;
									const y = getY(hours);
									return (
										<g key={i}>
											<line 
												x1={padding - 5} y1={y} 
												x2={padding} y2={y}
												stroke="var(--text-normal)"
											/>
											<text 
												x={padding - 10} y={y + 4} 
												textAnchor="end" 
												fontSize="12" 
												fill="var(--text-normal)"
											>
												{Math.round(hours)}h
											</text>
										</g>
									);
								})}
								
								{/* X轴标签 */}
								{Array.from({ length: Math.min(duration + 1, 8) }, (_, i) => {
									const dayIndex = Math.floor((duration / 7) * i);
									const x = getX(dayIndex);
									const date = new Date(sprint.startDate);
									date.setDate(date.getDate() + dayIndex);
									return (
										<g key={i}>
											<line 
												x1={x} y1={chartHeight - padding} 
												x2={x} y2={chartHeight - padding + 5}
												stroke="var(--text-normal)"
											/>
											<text 
												x={x} y={chartHeight - padding + 20} 
												textAnchor="middle" 
												fontSize="12" 
												fill="var(--text-normal)"
											>
												{formatDate(date)}
											</text>
										</g>
									);
								})}
								
								{/* 理想燃尽线 */}
								<path 
									d={idealPath}
									fill="none" 
									stroke="var(--text-muted)" 
									strokeWidth="2"
									strokeDasharray="5,5"
								/>
								
								{/* 实际燃尽线 */}
								{actualPath && (
									<path 
										d={actualPath}
										fill="none" 
										stroke="var(--color-accent)" 
										strokeWidth="3"
									/>
								)}
								
								{/* 数据点 */}
								{burndownData.actualBurndown.map((point, index) => (
									<circle
										key={index}
										cx={getX(index)}
										cy={getY(point.remainingHours)}
										r="4"
										fill="var(--color-accent)"
										stroke="white"
										strokeWidth="2"
									/>
								))}
							</svg>
						</div>

						<div className="burndown-legend">
							<div className="legend-item">
								<div className="legend-line ideal"></div>
								<span>理想燃尽线</span>
							</div>
							<div className="legend-item">
								<div className="legend-line actual"></div>
								<span>实际燃尽线</span>
							</div>
						</div>

						{burndownData.actualBurndown.length > 0 && (
							<div className="burndown-data-table">
								<h4>燃尽数据</h4>
								<table>
									<thead>
										<tr>
											<th>日期</th>
											<th>剩余工时</th>
											<th>完成任务</th>
										</tr>
									</thead>
									<tbody>
										{burndownData.actualBurndown.slice(-7).map((point, index) => (
											<tr key={index}>
												<td>{formatDate(point.date)}</td>
												<td>{point.remainingHours}h</td>
												<td>{point.completedTasks}</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
						)}
					</div>
				</div>
				
				<div className="modal-button-container">
					<button onClick={onClose} className="mod-cta mod-primary">
						关闭
					</button>
				</div>
			</div>
		</div>
	);
};