import React, { useState } from 'react';
import { Sprint, SprintStatus, SprintUtils } from '../../../models/Sprint';
import { Task, TaskStatus } from '../../../models/Task';
import styles from './SprintRetrospective.module.css';

interface SprintRetrospectiveProps {
	sprint: Sprint;
	tasks: Task[];
	onClose: () => void;
	onSaveRetrospective?: (sprintId: string, retrospective: SprintRetrospectiveData) => Promise<void>;
}

interface SprintRetrospectiveData {
	whatWentWell: string[];
	whatCouldImprove: string[];
	actionItems: string[];
	teamVelocity: number;
	notes: string;
}

/**
 * Sprint 回顾组件
 */
export const SprintRetrospective: React.FC<SprintRetrospectiveProps> = ({
	sprint,
	tasks,
	onClose,
	onSaveRetrospective
}) => {
	const [retrospective, setRetrospective] = useState<SprintRetrospectiveData>({
		whatWentWell: [''],
		whatCouldImprove: [''],
		actionItems: [''],
		teamVelocity: sprint.velocity || 0,
		notes: ''
	});

	const [isLoading, setIsLoading] = useState(false);

	// 计算 Sprint 统计信息
	const sprintTasks = tasks.filter(task => sprint.taskIds.includes(task.id));
	const completedTasks = sprintTasks.filter(task => task.status === TaskStatus.COMPLETED);
	const inProgressTasks = sprintTasks.filter(task => task.status === TaskStatus.IN_PROGRESS);
	const todoTasks = sprintTasks.filter(task => task.status === TaskStatus.TODO);
	
	const totalEstimatedHours = sprintTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
	const completedHours = completedTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
	const completionRate = sprintTasks.length > 0 ? (completedTasks.length / sprintTasks.length) * 100 : 0;
	
	const duration = SprintUtils.getDuration(sprint);
	const actualDuration = sprint.status === SprintStatus.COMPLETED 
		? Math.ceil((new Date().getTime() - sprint.startDate.getTime()) / (1000 * 60 * 60 * 24))
		: duration;

	// 计算团队速度（故事点或任务数）
	const calculatedVelocity = completedTasks.reduce((sum, task) => {
		return sum + ((task as any).storyPoints || 1);
	}, 0);

	const addItem = (field: keyof Pick<SprintRetrospectiveData, 'whatWentWell' | 'whatCouldImprove' | 'actionItems'>) => {
		setRetrospective(prev => ({
			...prev,
			[field]: [...prev[field], '']
		}));
	};

	const updateItem = (
		field: keyof Pick<SprintRetrospectiveData, 'whatWentWell' | 'whatCouldImprove' | 'actionItems'>, 
		index: number, 
		value: string
	) => {
		setRetrospective(prev => ({
			...prev,
			[field]: prev[field].map((item, i) => i === index ? value : item)
		}));
	};

	const removeItem = (
		field: keyof Pick<SprintRetrospectiveData, 'whatWentWell' | 'whatCouldImprove' | 'actionItems'>, 
		index: number
	) => {
		setRetrospective(prev => ({
			...prev,
			[field]: prev[field].filter((_, i) => i !== index)
		}));
	};

	const handleSave = async () => {
		if (!onSaveRetrospective) return;
		
		setIsLoading(true);
		try {
			// 过滤掉空的条目
			const cleanedRetrospective = {
				...retrospective,
				whatWentWell: retrospective.whatWentWell.filter(item => item.trim()),
				whatCouldImprove: retrospective.whatCouldImprove.filter(item => item.trim()),
				actionItems: retrospective.actionItems.filter(item => item.trim())
			};
			
			await onSaveRetrospective(sprint.id, cleanedRetrospective);
			onClose();
		} catch (error) {
			console.error('保存 Sprint 回顾失败:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className={styles.ptmModalContainer}>
			<div className={styles.ptmModalBg} onClick={onClose}></div>
			<div className={styles.ptmModal}>
				<div className={styles.ptmModalHeader}>
					<h3 className={styles.ptmModalTitle}>Sprint 回顾 - {sprint.name}</h3>
					<button className={styles.ptmModalCloseButton} onClick={onClose}>×</button>
				</div>
				
				<div className={styles.ptmModalContent}>
					<div className={styles.ptmSprintRetrospective}>
						{/* Sprint 统计概览 */}
						<div className={styles.ptmRetrospectiveStats}>
							<h4 className={styles.ptmStatsTitle}>Sprint 统计</h4>
							<div className={styles.ptmStatsGrid}>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>持续时间</span>
									<span className={styles.ptmStatValue}>{actualDuration} 天</span>
								</div>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>任务完成率</span>
									<span className={styles.ptmStatValue}>{Math.round(completionRate)}%</span>
								</div>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>完成任务</span>
									<span className={styles.ptmStatValue}>{completedTasks.length}/{sprintTasks.length}</span>
								</div>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>工时完成</span>
									<span className={styles.ptmStatValue}>{completedHours}/{totalEstimatedHours}h</span>
								</div>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>团队速度</span>
									<span className={styles.ptmStatValue}>{calculatedVelocity} 点</span>
								</div>
								<div className={styles.ptmStatItem}>
									<span className={styles.ptmStatLabel}>未完成任务</span>
									<span className={styles.ptmStatValue}>{inProgressTasks.length + todoTasks.length}</span>
								</div>
							</div>
						</div>

						{/* 做得好的地方 */}
						<div className={styles.ptmRetrospectiveSection}>
							<h4 className={styles.ptmSectionTitle}>做得好的地方 ✅</h4>
							{retrospective.whatWentWell.map((item, index) => (
								<div key={index} className={styles.ptmRetrospectiveItem}>
									<textarea
										className={styles.ptmRetrospectiveTextarea}
										value={item}
										onChange={(e) => updateItem('whatWentWell', index, e.target.value)}
										placeholder="描述这个 Sprint 中做得好的地方..."
										rows={2}
									/>
									<button
										onClick={() => removeItem('whatWentWell', index)}
										className={styles.ptmRemoveItemButton}
										title="删除"
									>
										×
									</button>
								</div>
							))}
							<button
								onClick={() => addItem('whatWentWell')}
								className={styles.ptmAddItemButton}
							>
								+ 添加项目
							</button>
						</div>

						{/* 需要改进的地方 */}
						<div className={styles.ptmRetrospectiveSection}>
							<h4 className={styles.ptmSectionTitle}>需要改进的地方 🔄</h4>
							{retrospective.whatCouldImprove.map((item, index) => (
								<div key={index} className={styles.ptmRetrospectiveItem}>
									<textarea
										className={styles.ptmRetrospectiveTextarea}
										value={item}
										onChange={(e) => updateItem('whatCouldImprove', index, e.target.value)}
										placeholder="描述需要改进的地方..."
										rows={2}
									/>
									<button
										onClick={() => removeItem('whatCouldImprove', index)}
										className={styles.ptmRemoveItemButton}
										title="删除"
									>
										×
									</button>
								</div>
							))}
							<button
								onClick={() => addItem('whatCouldImprove')}
								className={styles.ptmAddItemButton}
							>
								+ 添加项目
							</button>
						</div>

						{/* 行动项 */}
						<div className={styles.ptmRetrospectiveSection}>
							<h4 className={styles.ptmSectionTitle}>行动项 🎯</h4>
							{retrospective.actionItems.map((item, index) => (
								<div key={index} className={styles.ptmRetrospectiveItem}>
									<textarea
										className={styles.ptmRetrospectiveTextarea}
										value={item}
										onChange={(e) => updateItem('actionItems', index, e.target.value)}
										placeholder="下个 Sprint 要采取的具体行动..."
										rows={2}
									/>
									<button
										onClick={() => removeItem('actionItems', index)}
										className={styles.ptmRemoveItemButton}
										title="删除"
									>
										×
									</button>
								</div>
							))}
							<button
								onClick={() => addItem('actionItems')}
								className={styles.ptmAddItemButton}
							>
								+ 添加行动项
							</button>
						</div>

						{/* 团队速度调整 */}
						<div className={styles.ptmRetrospectiveSection}>
							<h4 className={styles.ptmSectionTitle}>团队速度</h4>
							<div className={styles.ptmVelocityAdjustment}>
								<label className={styles.ptmVelocityLabel}>
									调整后的团队速度（故事点）:
									<input
										className={styles.ptmVelocityInput}
										type="number"
										value={retrospective.teamVelocity}
										onChange={(e) => setRetrospective(prev => ({
											...prev,
											teamVelocity: parseInt(e.target.value) || 0
										}))}
										min="0"
									/>
								</label>
								<div className={styles.ptmVelocityNote}>
									计算得出的速度: {calculatedVelocity} 点
								</div>
							</div>
						</div>

						{/* 额外备注 */}
						<div className={styles.ptmRetrospectiveSection}>
							<h4 className={styles.ptmSectionTitle}>额外备注</h4>
							<textarea
								className={styles.ptmRetrospectiveTextarea}
								value={retrospective.notes}
								onChange={(e) => setRetrospective(prev => ({
									...prev,
									notes: e.target.value
								}))}
								placeholder="记录其他重要的观察和想法..."
								rows={4}
							/>
						</div>
					</div>
				</div>
				
				<div className={styles.ptmModalButtonContainer}>
					<button onClick={onClose} className={styles.ptmButton}>
						取消
					</button>
					{onSaveRetrospective && (
						<button 
							onClick={handleSave} 
							disabled={isLoading}
							className={`${styles.ptmButton} ${styles.ptmButtonPrimary}`}
						>
							{isLoading ? '保存中...' : '保存回顾'}
						</button>
					)}
				</div>
			</div>
		</div>
	);
};