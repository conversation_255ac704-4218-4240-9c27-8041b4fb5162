/* Sprint回顾组件样式 - 使用CSS Modules进行样式隔离 */

.ptmModalContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ptmModalBg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

.ptmModal {
  position: relative;
  background-color: var(--ptm-background-primary, var(--background-primary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 8px;
  box-shadow: var(--ptm-shadow-large, 0 8px 32px rgba(0, 0, 0, 0.2));
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ptmModalHeader {
  padding: 16px 20px;
  border-bottom: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--ptm-background-secondary, var(--background-secondary));
}

.ptmModalTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmModalCloseButton {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--ptm-text-muted, var(--text-muted));
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.ptmModalCloseButton:hover {
  background-color: var(--ptm-background-hover, var(--background-modifier-hover));
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmModalContent {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.ptmSprintRetrospective {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ptmRetrospectiveStats {
  background-color: var(--ptm-background-secondary, var(--background-secondary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 6px;
  padding: 16px;
}

.ptmStatsTitle {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmStatsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.ptmStatItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  border-radius: 4px;
  border: 1px solid var(--ptm-border-light, var(--background-modifier-border-hover));
}

.ptmStatLabel {
  font-size: 12px;
  color: var(--ptm-text-muted, var(--text-muted));
  margin-bottom: 4px;
}

.ptmStatValue {
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmRetrospectiveSection {
  background-color: var(--ptm-background-secondary, var(--background-secondary));
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 6px;
  padding: 16px;
}

.ptmSectionTitle {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmRetrospectiveItem {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: flex-start;
}

.ptmRetrospectiveTextarea {
  flex: 1;
  min-height: 60px;
  padding: 8px 12px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  font-family: var(--ptm-font-family, var(--font-interface));
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.ptmRetrospectiveTextarea:focus {
  outline: none;
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  box-shadow: 0 0 0 2px var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.2));
}

.ptmRetrospectiveTextarea::placeholder {
  color: var(--ptm-text-placeholder, var(--text-faint));
}

.ptmRemoveItemButton {
  background: none;
  border: none;
  color: var(--ptm-text-danger, var(--text-error));
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.ptmRemoveItemButton:hover {
  background-color: var(--ptm-background-danger-hover, rgba(var(--color-red-rgb), 0.1));
}

.ptmAddItemButton {
  background: none;
  border: 1px dashed var(--ptm-border-color, var(--background-modifier-border));
  color: var(--ptm-text-muted, var(--text-muted));
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  width: 100%;
}

.ptmAddItemButton:hover {
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  color: var(--ptm-accent-color, var(--interactive-accent));
  background-color: var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.05));
}

.ptmVelocityAdjustment {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ptmVelocityLabel {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: var(--ptm-text-primary, var(--text-normal));
}

.ptmVelocityInput {
  padding: 6px 10px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  font-size: 14px;
  width: 120px;
}

.ptmVelocityInput:focus {
  outline: none;
  border-color: var(--ptm-accent-color, var(--interactive-accent));
  box-shadow: 0 0 0 2px var(--ptm-accent-color-alpha, rgba(var(--interactive-accent-rgb), 0.2));
}

.ptmVelocityNote {
  font-size: 12px;
  color: var(--ptm-text-muted, var(--text-muted));
  font-style: italic;
}

.ptmModalButtonContainer {
  padding: 16px 20px;
  border-top: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background-color: var(--ptm-background-secondary, var(--background-secondary));
}

.ptmButton {
  padding: 8px 16px;
  border: 1px solid var(--ptm-border-color, var(--background-modifier-border));
  border-radius: 4px;
  background-color: var(--ptm-background-primary, var(--background-primary));
  color: var(--ptm-text-primary, var(--text-normal));
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ptmButton:hover {
  background-color: var(--ptm-background-hover, var(--background-modifier-hover));
}

.ptmButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ptmButtonPrimary {
  background-color: var(--ptm-accent-color, var(--interactive-accent));
  color: var(--ptm-accent-text, var(--text-on-accent));
  border-color: var(--ptm-accent-color, var(--interactive-accent));
}

.ptmButtonPrimary:hover:not(:disabled) {
  background-color: var(--ptm-accent-color-hover, var(--interactive-accent-hover));
  border-color: var(--ptm-accent-color-hover, var(--interactive-accent-hover));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ptmModal {
    width: 95vw;
    margin: 20px;
  }
  
  .ptmStatsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ptmModalButtonContainer {
    flex-direction: column;
  }
  
  .ptmButton {
    width: 100%;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .ptmModal {
    box-shadow: var(--ptm-shadow-large, 0 8px 32px rgba(0, 0, 0, 0.4));
  }
}