import React, { useState, useEffect } from 'react';
import { Sprint, SprintStatus } from '../../../models/Sprint';
import { Task } from '../../../models/Task';
import { SprintCard } from './SprintCard';
import { SprintCreateModal } from './SprintCreateModal';

interface SprintBoardProps {
	projectId: string;
	sprints: Sprint[];
	tasks: Task[];
	onCreateSprint: (name: string, projectId: string, startDate: Date, endDate: Date, goal?: string) => Promise<void>;
	onUpdateSprint: (sprintId: string, updates: Partial<Sprint>) => Promise<void>;
	onDeleteSprint: (sprintId: string) => Promise<void>;
	onStartSprint: (sprintId: string) => Promise<void>;
	onCompleteSprint: (sprintId: string) => Promise<void>;
	onAddTaskToSprint: (sprintId: string, taskId: string) => Promise<void>;
	onRemoveTaskFromSprint: (sprintId: string, taskId: string) => Promise<void>;
}

/**
 * Sprint 管理面板组件
 */
export const SprintBoard: React.FC<SprintBoardProps> = ({
	projectId,
	sprints,
	tasks,
	onCreateSprint,
	onUpdateSprint,
	onDeleteSprint,
	onStartSprint,
	onCompleteSprint,
	onAddTaskToSprint,
	onRemoveTaskFromSprint
}) => {
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [filter, setFilter] = useState<SprintStatus | 'all'>('all');

	// 按状态分组 Sprint
	const groupedSprints = {
		active: sprints.filter(s => s.status === SprintStatus.ACTIVE),
		planning: sprints.filter(s => s.status === SprintStatus.PLANNING),
		completed: sprints.filter(s => s.status === SprintStatus.COMPLETED),
		cancelled: sprints.filter(s => s.status === SprintStatus.CANCELLED)
	};

	// 根据筛选条件获取要显示的 Sprint
	const getFilteredSprints = () => {
		if (filter === 'all') {
			return sprints.sort((a, b) => {
				// 活跃的 Sprint 排在最前面
				if (a.status === SprintStatus.ACTIVE && b.status !== SprintStatus.ACTIVE) return -1;
				if (b.status === SprintStatus.ACTIVE && a.status !== SprintStatus.ACTIVE) return 1;
				// 然后是计划中的
				if (a.status === SprintStatus.PLANNING && b.status === SprintStatus.COMPLETED) return -1;
				if (b.status === SprintStatus.PLANNING && a.status === SprintStatus.COMPLETED) return 1;
				// 最后按创建时间排序
				return b.createdAt.getTime() - a.createdAt.getTime();
			});
		}
		return sprints.filter(s => s.status === filter);
	};

	const filteredSprints = getFilteredSprints();

	return (
		<div className="sprint-board">
			<div className="sprint-board-header">
				<div className="sprint-board-title">
					<h2>Sprint 管理</h2>
					<div className="sprint-stats">
						<span className="sprint-stat">
							活跃: {groupedSprints.active.length}
						</span>
						<span className="sprint-stat">
							计划中: {groupedSprints.planning.length}
						</span>
						<span className="sprint-stat">
							已完成: {groupedSprints.completed.length}
						</span>
					</div>
				</div>
				
				<div className="sprint-board-controls">
					<select
						value={filter}
						onChange={(e) => setFilter(e.target.value as SprintStatus | 'all')}
						className="dropdown"
					>
						<option value="all">所有 Sprint</option>
						<option value={SprintStatus.ACTIVE}>活跃</option>
						<option value={SprintStatus.PLANNING}>计划中</option>
						<option value={SprintStatus.COMPLETED}>已完成</option>
						<option value={SprintStatus.CANCELLED}>已取消</option>
					</select>
					
					<button
						onClick={() => setShowCreateModal(true)}
						className="mod-cta mod-primary"
					>
						创建 Sprint
					</button>
				</div>
			</div>

			<div className="sprint-board-content">
				{filteredSprints.length === 0 ? (
					<div className="empty-state">
						<div className="empty-state-icon">📋</div>
						<h3>暂无 Sprint</h3>
						<p>创建第一个 Sprint 来开始敏捷开发流程</p>
						<button
							onClick={() => setShowCreateModal(true)}
							className="mod-cta mod-primary"
						>
							创建 Sprint
						</button>
					</div>
				) : (
					<div className="sprint-grid">
						{filteredSprints.map(sprint => (
							<SprintCard
								key={sprint.id}
								sprint={sprint}
								tasks={tasks.filter(task => sprint.taskIds.includes(task.id))}
								allTasks={tasks.filter(task => task.projectId === projectId && !sprints.some(s => s.taskIds.includes(task.id)))}
								onUpdate={(updates) => onUpdateSprint(sprint.id, updates)}
								onDelete={() => onDeleteSprint(sprint.id)}
								onStart={() => onStartSprint(sprint.id)}
								onComplete={() => onCompleteSprint(sprint.id)}
								onAddTask={(taskId) => onAddTaskToSprint(sprint.id, taskId)}
								onRemoveTask={(taskId) => onRemoveTaskFromSprint(sprint.id, taskId)}
							/>
						))}
					</div>
				)}
			</div>

			{showCreateModal && (
				<SprintCreateModal
					projectId={projectId}
					onCreateSprint={onCreateSprint}
					onClose={() => setShowCreateModal(false)}
				/>
			)}
		</div>
	);
};