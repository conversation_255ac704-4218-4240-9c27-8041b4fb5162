import React, { useState } from 'react';
import { Sprint } from '../../../models/Sprint';
import { Task } from '../../../models/Task';

interface SprintTaskAssignmentProps {
	sprint: Sprint;
	sprintTasks: Task[];
	availableTasks: Task[];
	onAddTask: (taskId: string) => Promise<void>;
	onRemoveTask: (taskId: string) => Promise<void>;
	onClose: () => void;
}

/**
 * Sprint 任务分配组件
 */
export const SprintTaskAssignment: React.FC<SprintTaskAssignmentProps> = ({
	sprint,
	sprintTasks,
	availableTasks,
	onAddTask,
	onRemoveTask,
	onClose
}) => {
	const [searchTerm, setSearchTerm] = useState('');
	const [isLoading, setIsLoading] = useState(false);

	// 过滤可用任务
	const filteredAvailableTasks = availableTasks.filter(task =>
		task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
		task.description?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	// 过滤 Sprint 任务
	const filteredSprintTasks = sprintTasks.filter(task =>
		task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
		task.description?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const handleAddTask = async (taskId: string) => {
		setIsLoading(true);
		try {
			await onAddTask(taskId);
		} catch (error) {
			console.error('添加任务到 Sprint 失败:', error);
		} finally {
			setIsLoading(false);
		}
	};

	const handleRemoveTask = async (taskId: string) => {
		setIsLoading(true);
		try {
			await onRemoveTask(taskId);
		} catch (error) {
			console.error('从 Sprint 移除任务失败:', error);
		} finally {
			setIsLoading(false);
		}
	};

	const getTaskStatusColor = (status: string) => {
		switch (status) {
			case 'completed': return 'var(--color-green)';
			case 'in_progress': return 'var(--color-accent)';
			case 'todo': return 'var(--text-muted)';
			default: return 'var(--text-muted)';
		}
	};

	const getTaskStatusText = (status: string) => {
		switch (status) {
			case 'completed': return '已完成';
			case 'in_progress': return '进行中';
			case 'todo': return '待办';
			default: return status;
		}
	};

	return (
		<div className="modal-container">
			<div className="modal-bg" onClick={onClose}></div>
			<div className="modal modal-large">
				<div className="modal-header">
					<h3>管理 Sprint 任务 - {sprint.name}</h3>
					<button className="modal-close-button" onClick={onClose}>×</button>
				</div>
				
				<div className="modal-content">
					<div className="task-assignment-search">
						<input
							type="text"
							placeholder="搜索任务..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="task-search-input"
						/>
					</div>

					<div className="task-assignment-container">
						{/* Sprint 中的任务 */}
						<div className="task-assignment-section">
							<h4>Sprint 任务 ({filteredSprintTasks.length})</h4>
							<div className="task-assignment-list">
								{filteredSprintTasks.length === 0 ? (
									<div className="empty-task-list">
										{searchTerm ? '没有匹配的任务' : '暂无任务'}
									</div>
								) : (
									filteredSprintTasks.map(task => (
										<div key={task.id} className="task-assignment-item">
											<div className="task-info">
												<div className="task-title">{task.title}</div>
												{task.description && (
													<div className="task-description">{task.description}</div>
												)}
												<div className="task-meta">
													<span 
														className="task-status"
														style={{ color: getTaskStatusColor(task.status) }}
													>
														{getTaskStatusText(task.status)}
													</span>
													{task.estimatedHours && (
														<span className="task-hours">
															{task.estimatedHours}h
														</span>
													)}
													{task.priority && (
														<span className={`task-priority priority-${task.priority}`}>
															{task.priority}
														</span>
													)}
												</div>
											</div>
											<button
												onClick={() => handleRemoveTask(task.id)}
												disabled={isLoading}
												className="task-action-button remove-button"
												title="从 Sprint 中移除"
											>
												移除
											</button>
										</div>
									))
								)}
							</div>
						</div>

						{/* 可用任务 */}
						<div className="task-assignment-section">
							<h4>可用任务 ({filteredAvailableTasks.length})</h4>
							<div className="task-assignment-list">
								{filteredAvailableTasks.length === 0 ? (
									<div className="empty-task-list">
										{searchTerm ? '没有匹配的任务' : '暂无可用任务'}
									</div>
								) : (
									filteredAvailableTasks.map(task => (
										<div key={task.id} className="task-assignment-item">
											<div className="task-info">
												<div className="task-title">{task.title}</div>
												{task.description && (
													<div className="task-description">{task.description}</div>
												)}
												<div className="task-meta">
													<span 
														className="task-status"
														style={{ color: getTaskStatusColor(task.status) }}
													>
														{getTaskStatusText(task.status)}
													</span>
													{task.estimatedHours && (
														<span className="task-hours">
															{task.estimatedHours}h
														</span>
													)}
													{task.priority && (
														<span className={`task-priority priority-${task.priority}`}>
															{task.priority}
														</span>
													)}
												</div>
											</div>
											<button
												onClick={() => handleAddTask(task.id)}
												disabled={isLoading}
												className="task-action-button add-button"
												title="添加到 Sprint"
											>
												添加
											</button>
										</div>
									))
								)}
							</div>
						</div>
					</div>
				</div>
				
				<div className="modal-button-container">
					<button onClick={onClose} className="mod-cta mod-primary">
						完成
					</button>
				</div>
			</div>
		</div>
	);
};