import React, { useState } from 'react';
import { Sprint } from '../../../models/Sprint';

interface SprintCreateModalProps {
	projectId: string;
	onCreateSprint: (name: string, projectId: string, startDate: Date, endDate: Date, goal?: string) => Promise<void>;
	onClose: () => void;
}

/**
 * Sprint 创建模态框组件
 */
export const SprintCreateModal: React.FC<SprintCreateModalProps> = ({
	projectId,
	onCreateSprint,
	onClose
}) => {
	const [name, setName] = useState('');
	const [goal, setGoal] = useState('');
	const [startDate, setStartDate] = useState('');
	const [endDate, setEndDate] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState('');

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!name.trim()) {
			setError('Sprint 名称不能为空');
			return;
		}
		
		if (!startDate || !endDate) {
			setError('请选择开始和结束日期');
			return;
		}
		
		const start = new Date(startDate);
		const end = new Date(endDate);
		
		if (start >= end) {
			setError('结束日期必须晚于开始日期');
			return;
		}
		
		setIsLoading(true);
		setError('');
		
		try {
			await onCreateSprint(name.trim(), projectId, start, end, goal.trim() || undefined);
			onClose();
		} catch (err) {
			setError(err instanceof Error ? err.message : '创建 Sprint 失败');
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="modal-container">
			<div className="modal-bg" onClick={onClose}></div>
			<div className="modal">
				<div className="modal-header">
					<h3>创建新 Sprint</h3>
					<button className="modal-close-button" onClick={onClose}>×</button>
				</div>
				
				<form onSubmit={handleSubmit} className="modal-content">
					{error && (
						<div className="notice notice-error">
							{error}
						</div>
					)}
					
					<div className="setting-item">
						<div className="setting-item-info">
							<div className="setting-item-name">Sprint 名称</div>
							<div className="setting-item-description">为这个 Sprint 起一个描述性的名称</div>
						</div>
						<div className="setting-item-control">
							<input
								type="text"
								value={name}
								onChange={(e) => setName(e.target.value)}
								placeholder="例如：Sprint 1 - 用户认证功能"
								disabled={isLoading}
								required
							/>
						</div>
					</div>
					
					<div className="setting-item">
						<div className="setting-item-info">
							<div className="setting-item-name">Sprint 目标</div>
							<div className="setting-item-description">描述这个 Sprint 要达成的主要目标（可选）</div>
						</div>
						<div className="setting-item-control">
							<textarea
								value={goal}
								onChange={(e) => setGoal(e.target.value)}
								placeholder="例如：完成用户注册和登录功能的开发和测试"
								rows={3}
								disabled={isLoading}
							/>
						</div>
					</div>
					
					<div className="setting-item">
						<div className="setting-item-info">
							<div className="setting-item-name">开始日期</div>
							<div className="setting-item-description">Sprint 的开始日期</div>
						</div>
						<div className="setting-item-control">
							<input
								type="date"
								value={startDate}
								onChange={(e) => setStartDate(e.target.value)}
								disabled={isLoading}
								required
							/>
						</div>
					</div>
					
					<div className="setting-item">
						<div className="setting-item-info">
							<div className="setting-item-name">结束日期</div>
							<div className="setting-item-description">Sprint 的结束日期</div>
						</div>
						<div className="setting-item-control">
							<input
								type="date"
								value={endDate}
								onChange={(e) => setEndDate(e.target.value)}
								disabled={isLoading}
								required
							/>
						</div>
					</div>
					
					<div className="modal-button-container">
						<button
							type="button"
							onClick={onClose}
							disabled={isLoading}
							className="mod-cta"
						>
							取消
						</button>
						<button
							type="submit"
							disabled={isLoading}
							className="mod-cta mod-primary"
						>
							{isLoading ? '创建中...' : '创建 Sprint'}
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};