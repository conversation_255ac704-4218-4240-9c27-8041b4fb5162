import React, { useState } from 'react';
import { Sprint, SprintStatus, SprintUtils } from '../../../models/Sprint';
import { Task, TaskStatus } from '../../../models/Task';
import { SprintTaskAssignment } from './SprintTaskAssignment';
import { BurndownChart } from './BurndownChart';

interface SprintCardProps {
	sprint: Sprint;
	tasks: Task[];
	allTasks: Task[];
	onUpdate: (updates: Partial<Sprint>) => Promise<void>;
	onDelete: () => Promise<void>;
	onStart: () => Promise<void>;
	onComplete: () => Promise<void>;
	onAddTask: (taskId: string) => Promise<void>;
	onRemoveTask: (taskId: string) => Promise<void>;
}

/**
 * Sprint 卡片组件
 */
export const SprintCard: React.FC<SprintCardProps> = ({
	sprint,
	tasks,
	allTasks,
	onUpdate,
	onDelete,
	onStart,
	onComplete,
	onAddTask,
	onRemoveTask
}) => {
	const [showTaskAssignment, setShowTaskAssignment] = useState(false);
	const [showBurndown, setShowBurndown] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [editName, setEditName] = useState(sprint.name);
	const [editGoal, setEditGoal] = useState(sprint.goal || '');

	// 计算统计信息
	const completedTasks = tasks.filter(task => task.status === TaskStatus.COMPLETED);
	const inProgressTasks = tasks.filter(task => task.status === TaskStatus.IN_PROGRESS);
	const todoTasks = tasks.filter(task => task.status === TaskStatus.TODO);
	const completionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;
	
	const totalEstimatedHours = tasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
	const completedHours = completedTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0);
	
	const daysRemaining = SprintUtils.getDaysRemaining(sprint);
	const duration = SprintUtils.getDuration(sprint);
	const isOverdue = SprintUtils.isOverdue(sprint);

	const getStatusColor = (status: SprintStatus) => {
		switch (status) {
			case SprintStatus.ACTIVE: return 'var(--color-accent)';
			case SprintStatus.COMPLETED: return 'var(--color-green)';
			case SprintStatus.CANCELLED: return 'var(--color-red)';
			default: return 'var(--text-muted)';
		}
	};

	const getStatusText = (status: SprintStatus) => {
		switch (status) {
			case SprintStatus.PLANNING: return '计划中';
			case SprintStatus.ACTIVE: return '进行中';
			case SprintStatus.COMPLETED: return '已完成';
			case SprintStatus.CANCELLED: return '已取消';
		}
	};

	const handleSaveEdit = async () => {
		try {
			await onUpdate({
				name: editName.trim(),
				goal: editGoal.trim() || undefined
			});
			setIsEditing(false);
		} catch (error) {
			console.error('更新 Sprint 失败:', error);
		}
	};

	const handleCancelEdit = () => {
		setEditName(sprint.name);
		setEditGoal(sprint.goal || '');
		setIsEditing(false);
	};

	return (
		<div className={`sprint-card ${sprint.status}`}>
			<div className="sprint-card-header">
				<div className="sprint-card-title">
					{isEditing ? (
						<div className="sprint-edit-form">
							<input
								type="text"
								value={editName}
								onChange={(e) => setEditName(e.target.value)}
								className="sprint-name-input"
								placeholder="Sprint 名称"
							/>
							<textarea
								value={editGoal}
								onChange={(e) => setEditGoal(e.target.value)}
								className="sprint-goal-input"
								placeholder="Sprint 目标（可选）"
								rows={2}
							/>
							<div className="sprint-edit-buttons">
								<button onClick={handleSaveEdit} className="mod-cta mod-primary">保存</button>
								<button onClick={handleCancelEdit} className="mod-cta">取消</button>
							</div>
						</div>
					) : (
						<div>
							<h3>{sprint.name}</h3>
							{sprint.goal && <p className="sprint-goal">{sprint.goal}</p>}
						</div>
					)}
				</div>
				
				<div className="sprint-card-status">
					<span 
						className="sprint-status-badge"
						style={{ backgroundColor: getStatusColor(sprint.status) }}
					>
						{getStatusText(sprint.status)}
					</span>
					{isOverdue && (
						<span className="sprint-overdue-badge">逾期</span>
					)}
				</div>
			</div>

			<div className="sprint-card-dates">
				<div className="sprint-date">
					<span className="sprint-date-label">开始:</span>
					<span>{sprint.startDate.toLocaleDateString('zh-CN')}</span>
				</div>
				<div className="sprint-date">
					<span className="sprint-date-label">结束:</span>
					<span>{sprint.endDate.toLocaleDateString('zh-CN')}</span>
				</div>
				<div className="sprint-duration">
					<span className="sprint-date-label">持续:</span>
					<span>{duration} 天</span>
					{sprint.status === SprintStatus.ACTIVE && (
						<span className="sprint-remaining">（剩余 {daysRemaining} 天）</span>
					)}
				</div>
			</div>

			<div className="sprint-card-stats">
				<div className="sprint-stat-item">
					<span className="sprint-stat-label">任务进度</span>
					<div className="sprint-progress-bar">
						<div 
							className="sprint-progress-fill"
							style={{ width: `${completionRate}%` }}
						></div>
					</div>
					<span className="sprint-stat-value">
						{completedTasks.length}/{tasks.length} ({Math.round(completionRate)}%)
					</span>
				</div>
				
				<div className="sprint-stat-grid">
					<div className="sprint-stat-item">
						<span className="sprint-stat-label">待办</span>
						<span className="sprint-stat-value">{todoTasks.length}</span>
					</div>
					<div className="sprint-stat-item">
						<span className="sprint-stat-label">进行中</span>
						<span className="sprint-stat-value">{inProgressTasks.length}</span>
					</div>
					<div className="sprint-stat-item">
						<span className="sprint-stat-label">已完成</span>
						<span className="sprint-stat-value">{completedTasks.length}</span>
					</div>
				</div>

				{totalEstimatedHours > 0 && (
					<div className="sprint-stat-item">
						<span className="sprint-stat-label">工时进度</span>
						<span className="sprint-stat-value">
							{completedHours}/{totalEstimatedHours} 小时
						</span>
					</div>
				)}

				{sprint.velocity && (
					<div className="sprint-stat-item">
						<span className="sprint-stat-label">速度</span>
						<span className="sprint-stat-value">{sprint.velocity} 点</span>
					</div>
				)}
			</div>

			<div className="sprint-card-actions">
				<div className="sprint-action-group">
					<button
						onClick={() => setShowTaskAssignment(true)}
						className="mod-cta"
						title="管理任务"
					>
						任务分配
					</button>
					
					{sprint.status === SprintStatus.ACTIVE && (
						<button
							onClick={() => setShowBurndown(true)}
							className="mod-cta"
							title="查看燃尽图"
						>
							燃尽图
						</button>
					)}
				</div>

				<div className="sprint-action-group">
					{!isEditing && (
						<button
							onClick={() => setIsEditing(true)}
							className="mod-cta"
							title="编辑 Sprint"
						>
							编辑
						</button>
					)}

					{SprintUtils.canStart(sprint) && (
						<button
							onClick={onStart}
							className="mod-cta mod-primary"
							title="开始 Sprint"
						>
							开始
						</button>
					)}

					{SprintUtils.canComplete(sprint) && (
						<button
							onClick={onComplete}
							className="mod-cta mod-primary"
							title="完成 Sprint"
						>
							完成
						</button>
					)}

					<button
						onClick={onDelete}
						className="mod-cta mod-warning"
						title="删除 Sprint"
					>
						删除
					</button>
				</div>
			</div>

			{showTaskAssignment && (
				<SprintTaskAssignment
					sprint={sprint}
					sprintTasks={tasks}
					availableTasks={allTasks}
					onAddTask={onAddTask}
					onRemoveTask={onRemoveTask}
					onClose={() => setShowTaskAssignment(false)}
				/>
			)}

			{showBurndown && (
				<BurndownChart
					sprint={sprint}
					tasks={tasks}
					onClose={() => setShowBurndown(false)}
				/>
			)}
		</div>
	);
};