/**
 * 统一的UI组件接口规范
 * 确保所有组件使用一致的属性定义和类型检查
 */

import React from 'react';

// ============================================================================
// 基础类型定义
// ============================================================================

/** 标准尺寸类型 - 统一使用完整单词 */
export type StandardSize = 'small' | 'medium' | 'large';

/** 标准变体类型 */
export type StandardVariant = 'primary' | 'secondary' | 'ghost' | 'danger';

/** 标准颜色类型 */
export type StandardColor = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';

/** 标准间距类型 */
export type StandardSpacing = 'none' | 'small' | 'medium' | 'large';

/** 标准圆角类型 */
export type StandardRadius = 'none' | 'small' | 'medium' | 'large' | 'full';

// ============================================================================
// 基础组件接口
// ============================================================================

/** 基础组件属性 - 所有组件都应该继承这个接口 */
export interface BaseComponentProps {
  /** 子元素 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 测试ID */
  'data-testid'?: string;
}

/** 可交互组件属性 */
export interface InteractiveComponentProps extends BaseComponentProps {
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否加载中 */
  loading?: boolean;
  /** 点击事件 */
  onClick?: (event: React.MouseEvent) => void;
  /** 焦点事件 */
  onFocus?: (event: React.FocusEvent) => void;
  /** 失焦事件 */
  onBlur?: (event: React.FocusEvent) => void;
}

/** 表单组件属性 */
export interface FormComponentProps extends InteractiveComponentProps {
  /** 字段名称 */
  name?: string;
  /** 字段值 */
  value?: any;
  /** 值变化事件 */
  onChange?: (value: any) => void;
  /** 是否必填 */
  required?: boolean;
  /** 错误信息 */
  error?: string;
  /** 帮助文本 */
  helperText?: string;
}

// ============================================================================
// 标准化的组件属性接口
// ============================================================================

/** 标准化的Button组件属性 */
export interface StandardButtonProps extends InteractiveComponentProps {
  /** 按钮变体 */
  variant?: StandardVariant;
  /** 按钮尺寸 */
  size?: StandardSize;
  /** 按钮类型 */
  type?: 'button' | 'submit' | 'reset';
  /** 是否为图标按钮 */
  iconOnly?: boolean;
  /** 图标 */
  icon?: React.ReactNode;
  /** 图标位置 */
  iconPosition?: 'left' | 'right';
  /** 标题提示 */
  title?: string;
}

/** 标准化的Card组件属性 */
export interface StandardCardProps extends BaseComponentProps {
  /** 卡片变体 */
  variant?: 'default' | 'elevated' | 'outlined';
  /** 内边距 */
  padding?: StandardSpacing;
  /** 是否可点击 */
  clickable?: boolean;
  /** 点击事件 */
  onClick?: (event: React.MouseEvent) => void;
}

/** 标准化的Input组件属性 */
export interface StandardInputProps extends FormComponentProps {
  /** 输入框类型 */
  type?: 'text' | 'password' | 'email' | 'number' | 'search' | 'tel' | 'url';
  /** 占位符 */
  placeholder?: string;
  /** 输入框尺寸 */
  size?: StandardSize;
  /** 是否只读 */
  readOnly?: boolean;
  /** 最大长度 */
  maxLength?: number;
  /** 最小长度 */
  minLength?: number;
  /** 前缀图标 */
  prefixIcon?: React.ReactNode;
  /** 后缀图标 */
  suffixIcon?: React.ReactNode;
}

/** 标准化的Select组件属性 */
export interface StandardSelectProps extends FormComponentProps {
  /** 选项列表 */
  options: Array<{
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  /** 占位符 */
  placeholder?: string;
  /** 选择框尺寸 */
  size?: StandardSize;
  /** 是否可搜索 */
  searchable?: boolean;
  /** 是否多选 */
  multiple?: boolean;
  /** 是否可清空 */
  clearable?: boolean;
}

/** 标准化的Modal组件属性 */
export interface StandardModalProps extends BaseComponentProps {
  /** 是否显示 */
  visible?: boolean;
  /** 标题 */
  title?: string;
  /** 宽度 */
  width?: number | string;
  /** 是否可关闭 */
  closable?: boolean;
  /** 是否点击遮罩关闭 */
  maskClosable?: boolean;
  /** 关闭事件 */
  onClose?: () => void;
  /** 确认事件 */
  onOk?: () => void;
  /** 取消事件 */
  onCancel?: () => void;
}

/** 标准化的Table组件属性 */
export interface StandardTableProps extends BaseComponentProps {
  /** 数据源 */
  dataSource: any[];
  /** 列定义 */
  columns: Array<{
    key: string;
    title: string;
    dataIndex?: string;
    width?: number | string;
    align?: 'left' | 'center' | 'right';
    render?: (value: any, record: any, index: number) => React.ReactNode;
  }>;
  /** 行键 */
  rowKey?: string | ((record: any) => string);
  /** 是否加载中 */
  loading?: boolean;
  /** 分页配置 */
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

// ============================================================================
// 布局组件属性接口
// ============================================================================

/** 标准化的Container组件属性 */
export interface StandardContainerProps extends BaseComponentProps {
  /** 最大宽度 */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 是否居中 */
  centered?: boolean;
  /** 内边距 */
  padding?: StandardSpacing;
}

/** 标准化的Flex组件属性 */
export interface StandardFlexProps extends BaseComponentProps {
  /** 主轴方向 */
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /** 主轴对齐 */
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  /** 交叉轴对齐 */
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  /** 是否换行 */
  wrap?: boolean;
  /** 间距 */
  gap?: StandardSpacing;
}

/** 标准化的Grid组件属性 */
export interface StandardGridProps extends BaseComponentProps {
  /** 列数 */
  columns?: number | string;
  /** 行数 */
  rows?: number | string;
  /** 间距 */
  gap?: StandardSpacing;
  /** 列间距 */
  columnGap?: StandardSpacing;
  /** 行间距 */
  rowGap?: StandardSpacing;
}

// ============================================================================
// 反馈组件属性接口
// ============================================================================

/** 标准化的Alert组件属性 */
export interface StandardAlertProps extends BaseComponentProps {
  /** 警告类型 */
  type?: 'info' | 'success' | 'warning' | 'error';
  /** 标题 */
  title?: string;
  /** 描述 */
  description?: string;
  /** 是否可关闭 */
  closable?: boolean;
  /** 关闭事件 */
  onClose?: () => void;
  /** 是否显示图标 */
  showIcon?: boolean;
}

/** 标准化的Progress组件属性 */
export interface StandardProgressProps extends BaseComponentProps {
  /** 进度值 (0-100) */
  value: number;
  /** 进度条类型 */
  type?: 'line' | 'circle';
  /** 尺寸 */
  size?: StandardSize;
  /** 颜色 */
  color?: StandardColor;
  /** 是否显示百分比 */
  showPercent?: boolean;
  /** 状态 */
  status?: 'normal' | 'active' | 'exception' | 'success';
}

/** 标准化的Loading组件属性 */
export interface StandardLoadingProps extends BaseComponentProps {
  /** 是否显示 */
  visible?: boolean;
  /** 加载文本 */
  text?: string;
  /** 尺寸 */
  size?: StandardSize;
  /** 是否全屏 */
  fullscreen?: boolean;
}

// ============================================================================
// 导航组件属性接口
// ============================================================================

/** 标准化的Menu组件属性 */
export interface StandardMenuProps extends BaseComponentProps {
  /** 菜单项 */
  items: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    disabled?: boolean;
    children?: any[];
  }>;
  /** 选中的键 */
  selectedKeys?: string[];
  /** 展开的键 */
  openKeys?: string[];
  /** 模式 */
  mode?: 'horizontal' | 'vertical' | 'inline';
  /** 选择事件 */
  onSelect?: (selectedKeys: string[]) => void;
  /** 展开事件 */
  onOpenChange?: (openKeys: string[]) => void;
}

/** 标准化的Tabs组件属性 */
export interface StandardTabsProps extends BaseComponentProps {
  /** 标签页 */
  items: Array<{
    key: string;
    label: string;
    children: React.ReactNode;
    disabled?: boolean;
    closable?: boolean;
  }>;
  /** 当前激活的标签页 */
  activeKey?: string;
  /** 标签页位置 */
  tabPosition?: 'top' | 'bottom' | 'left' | 'right';
  /** 标签页类型 */
  type?: 'line' | 'card' | 'editable-card';
  /** 切换事件 */
  onChange?: (activeKey: string) => void;
  /** 编辑事件 */
  onEdit?: (targetKey: string, action: 'add' | 'remove') => void;
}

// ============================================================================
// 数据展示组件属性接口
// ============================================================================

/** 标准化的Avatar组件属性 */
export interface StandardAvatarProps extends BaseComponentProps {
  /** 头像大小 */
  size?: StandardSize | number;
  /** 头像形状 */
  shape?: 'circle' | 'square';
  /** 图片地址 */
  src?: string;
  /** 图片描述 */
  alt?: string;
  /** 用户名称 */
  name?: string;
  /** 图标 */
  icon?: React.ReactNode;
}

/** 标准化的Badge组件属性 */
export interface StandardBadgeProps extends BaseComponentProps {
  /** 徽标数量 */
  count?: number;
  /** 最大显示数量 */
  overflowCount?: number;
  /** 是否显示小红点 */
  dot?: boolean;
  /** 颜色 */
  color?: StandardColor;
  /** 状态 */
  status?: 'success' | 'processing' | 'default' | 'error' | 'warning';
  /** 状态文本 */
  text?: string;
}

/** 标准化的Tag组件属性 */
export interface StandardTagProps extends BaseComponentProps {
  /** 标签颜色 */
  color?: StandardColor;
  /** 是否可关闭 */
  closable?: boolean;
  /** 关闭事件 */
  onClose?: () => void;
  /** 图标 */
  icon?: React.ReactNode;
}

// ============================================================================
// 类型守卫和验证函数
// ============================================================================

/** 检查是否为标准尺寸 */
export function isStandardSize(value: any): value is StandardSize {
  return ['small', 'medium', 'large'].includes(value);
}

/** 检查是否为标准变体 */
export function isStandardVariant(value: any): value is StandardVariant {
  return ['primary', 'secondary', 'ghost', 'danger'].includes(value);
}

/** 检查是否为标准颜色 */
export function isStandardColor(value: any): value is StandardColor {
  return ['primary', 'secondary', 'success', 'warning', 'danger', 'info'].includes(value);
}

/** 检查是否为标准间距 */
export function isStandardSpacing(value: any): value is StandardSpacing {
  return ['none', 'small', 'medium', 'large'].includes(value);
}

// ============================================================================
// 属性转换工具函数
// ============================================================================

/** 将旧的尺寸属性转换为标准尺寸 */
export function normalizeSize(size?: string): StandardSize {
  switch (size) {
    case 'xs':
    case 'sm':
      return 'small';
    case 'lg':
    case 'xl':
      return 'large';
    case 'md':
    case 'medium':
      return 'medium';
    default:
      return 'medium';
  }
}

/** 将旧的变体属性转换为标准变体 */
export function normalizeVariant(variant?: string): StandardVariant {
  switch (variant) {
    case 'destructive':
      return 'danger';
    case 'outline':
      return 'secondary';
    case 'link':
      return 'ghost';
    default:
      return (variant as StandardVariant) || 'primary';
  }
}

/** 将旧的间距属性转换为标准间距 */
export function normalizeSpacing(spacing?: string): StandardSpacing {
  switch (spacing) {
    case 'xs':
      return 'small';
    case 'sm':
      return 'small';
    case 'md':
      return 'medium';
    case 'lg':
      return 'large';
    case 'xl':
      return 'large';
    default:
      return (spacing as StandardSpacing) || 'medium';
  }
}

// ============================================================================
// 组件工厂函数
// ============================================================================

/** 创建标准化的组件属性 */
export function createStandardProps<T extends BaseComponentProps>(
  props: T,
  defaults?: Partial<T>
): T {
  return {
    ...defaults,
    ...props,
    className: [defaults?.className, props.className].filter(Boolean).join(' '),
    style: { ...defaults?.style, ...props.style },
  };
}

/** 创建标准化的交互组件属性 */
export function createInteractiveProps<T extends InteractiveComponentProps>(
  props: T,
  defaults?: Partial<T>
): T {
  return createStandardProps(props, {
    disabled: false,
    loading: false,
    ...defaults,
  });
}

// ============================================================================
// 导出所有类型
// ============================================================================

export type {
  BaseComponentProps,
  InteractiveComponentProps,
  FormComponentProps,
};