/**
 * 搜索功能Hook
 * 提供搜索逻辑、防抖功能和搜索历史管理
 */

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useSearchHistoryStorage } from './useLocalStorage';

export interface SearchOptions {
  debounceMs?: number;
  minLength?: number;
  maxHistoryItems?: number;
  caseSensitive?: boolean;
  searchFields?: string[];
}

export interface SearchResult<T> {
  item: T;
  matches: {
    field: string;
    indices: [number, number][];
  }[];
  score: number;
}

/**
 * 基础搜索Hook
 */
export function useSearch<T>(
  items: T[],
  options: SearchOptions = {}
) {
  const {
    debounceMs = 300,
    minLength = 1,
    maxHistoryItems = 10,
    caseSensitive = false,
    searchFields = [],
  } = options;

  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useSearchHistoryStorage();

  // 防抖处理搜索词
  useEffect(() => {
    setIsSearching(true);
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setIsSearching(false);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  // 执行搜索
  const searchResults = useMemo(() => {
    if (!debouncedSearchTerm || debouncedSearchTerm.length < minLength) {
      return items;
    }

    return performSearch(items, debouncedSearchTerm, {
      caseSensitive,
      searchFields,
    });
  }, [items, debouncedSearchTerm, minLength, caseSensitive, searchFields]);

  // 添加到搜索历史
  const addToHistory = useCallback((term: string) => {
    if (!term || term.length < minLength) return;

    setSearchHistory(prev => {
      const filtered = prev.filter(item => item !== term);
      const newHistory = [term, ...filtered].slice(0, maxHistoryItems);
      return newHistory;
    });
  }, [minLength, maxHistoryItems, setSearchHistory]);

  // 清空搜索历史
  const clearHistory = useCallback(() => {
    setSearchHistory([]);
  }, [setSearchHistory]);

  // 搜索处理函数
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    if (term && term.length >= minLength) {
      addToHistory(term);
    }
  }, [minLength, addToHistory]);

  // 清空搜索
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
  }, []);

  return {
    searchTerm,
    debouncedSearchTerm,
    searchResults,
    isSearching,
    searchHistory,
    handleSearch,
    clearSearch,
    clearHistory,
    setSearchTerm,
  };
}

/**
 * 高级搜索Hook
 * 支持多字段搜索和高亮显示
 */
export function useAdvancedSearch<T>(
  items: T[],
  options: SearchOptions & {
    highlightClassName?: string;
  } = {}
) {
  const {
    highlightClassName = 'ptm-search-highlight',
    ...searchOptions
  } = options;

  const searchHook = useSearch(items, searchOptions);

  // 高亮搜索结果
  const highlightText = useCallback((text: string, searchTerm: string): string => {
    if (!searchTerm || !text) return text;

    const regex = new RegExp(
      `(${escapeRegExp(searchTerm)})`,
      searchOptions.caseSensitive ? 'g' : 'gi'
    );

    return text.replace(regex, `<span class="${highlightClassName}">$1</span>`);
  }, [searchOptions.caseSensitive, highlightClassName]);

  return {
    ...searchHook,
    highlightText,
  };
}

/**
 * 实时搜索Hook
 * 用于API搜索等场景
 */
export function useRealtimeSearch<T>(
  searchFunction: (term: string) => Promise<T[]>,
  options: SearchOptions = {}
) {
  const {
    debounceMs = 500,
    minLength = 2,
  } = options;

  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!searchTerm || searchTerm.length < minLength) {
      setResults([]);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    const timer = setTimeout(async () => {
      try {
        const searchResults = await searchFunction(searchTerm);
        setResults(searchResults);
      } catch (err) {
        setError(err instanceof Error ? err.message : '搜索失败');
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, minLength, debounceMs, searchFunction]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setResults([]);
    setError(null);
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    results,
    isLoading,
    error,
    clearSearch,
  };
}

/**
 * 搜索建议Hook
 */
export function useSearchSuggestions<T>(
  items: T[],
  getSearchableText: (item: T) => string,
  options: SearchOptions = {}
) {
  const { minLength = 1 } = options;
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  useEffect(() => {
    if (!searchTerm || searchTerm.length < minLength) {
      setSuggestions([]);
      return;
    }

    const uniqueSuggestions = new Set<string>();
    const lowerSearchTerm = searchTerm.toLowerCase();

    items.forEach(item => {
      const text = getSearchableText(item).toLowerCase();
      const words = text.split(/\s+/);
      
      words.forEach(word => {
        if (word.startsWith(lowerSearchTerm) && word !== lowerSearchTerm) {
          uniqueSuggestions.add(word);
        }
      });
    });

    setSuggestions(Array.from(uniqueSuggestions).slice(0, 10));
  }, [searchTerm, items, getSearchableText, minLength]);

  return {
    searchTerm,
    setSearchTerm,
    suggestions,
  };
}

/**
 * 执行搜索的核心函数
 */
function performSearch<T>(
  items: T[],
  searchTerm: string,
  options: {
    caseSensitive?: boolean;
    searchFields?: string[];
  }
): T[] {
  const { caseSensitive = false, searchFields = [] } = options;
  
  if (!searchTerm) return items;

  const searchRegex = new RegExp(
    escapeRegExp(searchTerm),
    caseSensitive ? 'g' : 'gi'
  );

  return items.filter(item => {
    // 如果指定了搜索字段，只在这些字段中搜索
    if (searchFields.length > 0) {
      return searchFields.some(field => {
        const value = getNestedValue(item, field);
        return value && searchRegex.test(String(value));
      });
    }

    // 否则在所有字符串字段中搜索
    return searchInObject(item, searchRegex);
  });
}

/**
 * 在对象中递归搜索
 */
function searchInObject(obj: any, regex: RegExp): boolean {
  if (obj === null || obj === undefined) return false;

  if (typeof obj === 'string') {
    return regex.test(obj);
  }

  if (typeof obj === 'number') {
    return regex.test(String(obj));
  }

  if (Array.isArray(obj)) {
    return obj.some(item => searchInObject(item, regex));
  }

  if (typeof obj === 'object') {
    return Object.values(obj).some(value => searchInObject(value, regex));
  }

  return false;
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * PTM特定的搜索Hooks
 */

// 项目搜索
export const useProjectSearch = (projects: any[]) => {
  return useAdvancedSearch(projects, {
    searchFields: ['name', 'description', 'tags'],
    minLength: 1,
    debounceMs: 300,
  });
};

// 任务搜索
export const useTaskSearch = (tasks: any[]) => {
  return useAdvancedSearch(tasks, {
    searchFields: ['title', 'description', 'project.name', 'assignee.name', 'tags'],
    minLength: 1,
    debounceMs: 300,
  });
};

// 全局搜索
export const useGlobalSearch = (data: {
  projects: any[];
  tasks: any[];
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchHistory, setSearchHistory] = useSearchHistoryStorage();

  const projectResults = useProjectSearch(data.projects);
  const taskResults = useTaskSearch(data.tasks);

  useEffect(() => {
    projectResults.handleSearch(searchTerm);
    taskResults.handleSearch(searchTerm);
  }, [searchTerm]);

  const addToHistory = useCallback((term: string) => {
    if (!term || term.length < 2) return;

    setSearchHistory(prev => {
      const filtered = prev.filter(item => item !== term);
      return [term, ...filtered].slice(0, 10);
    });
  }, [setSearchHistory]);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    if (term && term.length >= 2) {
      addToHistory(term);
    }
  }, [addToHistory]);

  return {
    searchTerm,
    handleSearch,
    setSearchTerm,
    searchHistory,
    results: {
      projects: projectResults.searchResults,
      tasks: taskResults.searchResults,
    },
    isSearching: projectResults.isSearching || taskResults.isSearching,
    highlightText: projectResults.highlightText,
    clearSearch: () => {
      setSearchTerm('');
      projectResults.clearSearch();
      taskResults.clearSearch();
    },
  };
};