/**
 * 响应式设计Hook
 * 提供屏幕尺寸检测和响应式断点判断
 */

import { useState, useEffect } from 'react';
import { designTokens } from '../styles/tokens';
import { styleManager } from '../utils/styleManager';

export interface ResponsiveInfo {
  current: 'mobile' | 'tablet' | 'desktop' | 'wide';
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isWide: boolean;
  width: number;
  height: number;
}

/**
 * 响应式Hook
 * 监听窗口大小变化并提供断点信息
 */
export const useResponsive = (): ResponsiveInfo => {
  const [responsiveInfo, setResponsiveInfo] = useState<ResponsiveInfo>(() => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 1024;
    const height = typeof window !== 'undefined' ? window.innerHeight : 768;
    
    return {
      ...getBreakpointInfo(width),
      width,
      height,
    };
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateResponsiveInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setResponsiveInfo({
        ...getBreakpointInfo(width),
        width,
        height,
      });
    };

    // 防抖处理
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateResponsiveInfo, 100);
    };

    window.addEventListener('resize', debouncedUpdate);
    
    // 初始化时更新一次
    updateResponsiveInfo();

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, []);

  return responsiveInfo;
};

/**
 * 获取断点信息
 */
function getBreakpointInfo(width: number): Omit<ResponsiveInfo, 'width' | 'height'> {
  const breakpoints = designTokens.breakpoints;
  
  let current: ResponsiveInfo['current'] = 'mobile';
  if (width >= parseInt(breakpoints.wide)) current = 'wide';
  else if (width >= parseInt(breakpoints.desktop)) current = 'desktop';
  else if (width >= parseInt(breakpoints.tablet)) current = 'tablet';
  
  return {
    current,
    isMobile: current === 'mobile',
    isTablet: current === 'tablet',
    isDesktop: current === 'desktop',
    isWide: current === 'wide',
  };
}

/**
 * 媒体查询Hook
 * 监听特定的媒体查询条件
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // 现代浏览器使用addEventListener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handler);
    } else {
      // 兼容旧版浏览器
      mediaQuery.addListener(handler);
    }

    // 初始化状态
    setMatches(mediaQuery.matches);

    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handler);
      } else {
        mediaQuery.removeListener(handler);
      }
    };
  }, [query]);

  return matches;
};

/**
 * 预定义的媒体查询Hooks
 */
export const useIsMobile = (): boolean => {
  return useMediaQuery(`(max-width: ${designTokens.breakpoints.mobile})`);
};

export const useIsTablet = (): boolean => {
  return useMediaQuery(`(min-width: ${designTokens.breakpoints.mobile}) and (max-width: ${designTokens.breakpoints.tablet})`);
};

export const useIsDesktop = (): boolean => {
  return useMediaQuery(`(min-width: ${designTokens.breakpoints.tablet}) and (max-width: ${designTokens.breakpoints.desktop})`);
};

export const useIsWide = (): boolean => {
  return useMediaQuery(`(min-width: ${designTokens.breakpoints.desktop})`);
};

/**
 * 侧边栏状态Hook
 * 根据屏幕尺寸自动管理侧边栏折叠状态
 */
export const useSidebarState = (defaultCollapsed = false) => {
  const { isMobile, isTablet } = useResponsive();
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  useEffect(() => {
    // 在移动设备上自动折叠侧边栏
    if (isMobile) {
      setIsCollapsed(true);
    } else if (!isMobile && !isTablet && defaultCollapsed) {
      // 在桌面设备上恢复默认状态
      setIsCollapsed(defaultCollapsed);
    }
  }, [isMobile, isTablet, defaultCollapsed]);

  const toggleSidebar = () => {
    setIsCollapsed(prev => !prev);
  };

  return {
    isCollapsed,
    setIsCollapsed,
    toggleSidebar,
    shouldAutoCollapse: isMobile,
  };
};

/**
 * 右侧面板状态Hook
 * 根据屏幕尺寸自动管理右侧面板显示状态
 */
export const useRightPanelState = (defaultCollapsed = false) => {
  const { isMobile, isTablet } = useResponsive();
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  useEffect(() => {
    // 在移动设备和平板上自动隐藏右侧面板
    if (isMobile || isTablet) {
      setIsCollapsed(true);
    } else if (!isMobile && !isTablet && defaultCollapsed) {
      // 在桌面设备上恢复默认状态
      setIsCollapsed(defaultCollapsed);
    }
  }, [isMobile, isTablet, defaultCollapsed]);

  const togglePanel = () => {
    setIsCollapsed(prev => !prev);
  };

  return {
    isCollapsed,
    setIsCollapsed,
    togglePanel,
    shouldAutoCollapse: isMobile || isTablet,
  };
};

/**
 * 响应式网格Hook
 * 根据屏幕尺寸返回合适的网格列数
 */
export const useResponsiveGrid = (config: {
  mobile?: number;
  tablet?: number;
  desktop?: number;
  wide?: number;
}) => {
  const { current } = useResponsive();
  
  const defaultConfig = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    wide: 4,
    ...config,
  };
  
  return defaultConfig[current];
};

/**
 * 容器查询Hook（实验性）
 * 监听容器尺寸变化
 */
export const useContainerQuery = (containerRef: React.RefObject<HTMLElement>) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);

  return containerSize;
};