/**
 * 本地存储Hook
 * 提供类型安全的本地存储操作
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * 本地存储Hook
 * 提供与localStorage的同步状态管理
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: {
    serializer?: {
      read: (value: string) => T;
      write: (value: T) => string;
    };
    syncAcrossTabs?: boolean;
  } = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify,
    },
    syncAcrossTabs = true,
  } = options;

  // 读取初始值
  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return defaultValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      return serializer.read(item);
    } catch (error) {
      console.warn(`读取localStorage键"${key}"时出错:`, error);
      return defaultValue;
    }
  }, [key, defaultValue, serializer]);

  const [storedValue, setStoredValue] = useState<T>(readValue);

  // 设置值到localStorage和state
  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      if (typeof window === 'undefined') {
        console.warn('localStorage在服务端不可用');
        return;
      }

      try {
        const newValue = value instanceof Function ? value(storedValue) : value;
        
        // 保存到localStorage
        window.localStorage.setItem(key, serializer.write(newValue));
        
        // 更新state
        setStoredValue(newValue);
        
        // 触发自定义事件，用于跨标签页同步
        if (syncAcrossTabs) {
          window.dispatchEvent(
            new CustomEvent('local-storage-change', {
              detail: { key, newValue },
            })
          );
        }
      } catch (error) {
        console.warn(`设置localStorage键"${key}"时出错:`, error);
      }
    },
    [key, storedValue, serializer, syncAcrossTabs]
  );

  // 移除值
  const removeValue = useCallback(() => {
    if (typeof window === 'undefined') {
      console.warn('localStorage在服务端不可用');
      return;
    }

    try {
      window.localStorage.removeItem(key);
      setStoredValue(defaultValue);
      
      if (syncAcrossTabs) {
        window.dispatchEvent(
          new CustomEvent('local-storage-change', {
            detail: { key, newValue: defaultValue },
          })
        );
      }
    } catch (error) {
      console.warn(`移除localStorage键"${key}"时出错:`, error);
    }
  }, [key, defaultValue, syncAcrossTabs]);

  // 监听localStorage变化（跨标签页同步）
  useEffect(() => {
    if (!syncAcrossTabs) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key !== key || e.storageArea !== window.localStorage) {
        return;
      }

      try {
        const newValue = e.newValue ? serializer.read(e.newValue) : defaultValue;
        setStoredValue(newValue);
      } catch (error) {
        console.warn(`处理localStorage变化事件时出错:`, error);
      }
    };

    const handleCustomStorageChange = (e: CustomEvent) => {
      if (e.detail.key === key) {
        setStoredValue(e.detail.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('local-storage-change', handleCustomStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('local-storage-change', handleCustomStorageChange as EventListener);
    };
  }, [key, defaultValue, serializer, syncAcrossTabs]);

  // 监听key变化，重新读取值
  useEffect(() => {
    setStoredValue(readValue());
  }, [readValue]);

  return [storedValue, setValue, removeValue];
}

/**
 * 会话存储Hook
 * 提供与sessionStorage的同步状态管理
 */
export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: {
    serializer?: {
      read: (value: string) => T;
      write: (value: T) => string;
    };
  } = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify,
    },
  } = options;

  // 读取初始值
  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return defaultValue;
    }

    try {
      const item = window.sessionStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      return serializer.read(item);
    } catch (error) {
      console.warn(`读取sessionStorage键"${key}"时出错:`, error);
      return defaultValue;
    }
  }, [key, defaultValue, serializer]);

  const [storedValue, setStoredValue] = useState<T>(readValue);

  // 设置值到sessionStorage和state
  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      if (typeof window === 'undefined') {
        console.warn('sessionStorage在服务端不可用');
        return;
      }

      try {
        const newValue = value instanceof Function ? value(storedValue) : value;
        
        // 保存到sessionStorage
        window.sessionStorage.setItem(key, serializer.write(newValue));
        
        // 更新state
        setStoredValue(newValue);
      } catch (error) {
        console.warn(`设置sessionStorage键"${key}"时出错:`, error);
      }
    },
    [key, storedValue, serializer]
  );

  // 移除值
  const removeValue = useCallback(() => {
    if (typeof window === 'undefined') {
      console.warn('sessionStorage在服务端不可用');
      return;
    }

    try {
      window.sessionStorage.removeItem(key);
      setStoredValue(defaultValue);
    } catch (error) {
      console.warn(`移除sessionStorage键"${key}"时出错:`, error);
    }
  }, [key, defaultValue]);

  // 监听key变化，重新读取值
  useEffect(() => {
    setStoredValue(readValue());
  }, [readValue]);

  return [storedValue, setValue, removeValue];
}

/**
 * PTM特定的本地存储Hooks
 */

// 侧边栏状态
export const useSidebarStorage = () => {
  return useLocalStorage('ptm-sidebar-collapsed', false);
};

// 右侧面板状态
export const useRightPanelStorage = () => {
  return useLocalStorage('ptm-right-panel-collapsed', false);
};

// 当前页面状态
export const useCurrentPageStorage = () => {
  return useLocalStorage('ptm-current-page', 'dashboard');
};

// 搜索历史
export const useSearchHistoryStorage = () => {
  return useLocalStorage<string[]>('ptm-search-history', []);
};

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh' | 'en';
  compactMode: boolean;
  showCompletedTasks: boolean;
  defaultView: 'dashboard' | 'projects' | 'tasks' | 'kanban';
}

export const useUserPreferencesStorage = () => {
  return useLocalStorage<UserPreferences>('ptm-user-preferences', {
    theme: 'auto',
    language: 'zh',
    compactMode: false,
    showCompletedTasks: true,
    defaultView: 'dashboard',
  });
};

// 看板列设置
export interface KanbanColumnSettings {
  [columnId: string]: {
    collapsed: boolean;
    wipLimit?: number;
    color?: string;
  };
}

export const useKanbanSettingsStorage = () => {
  return useLocalStorage<KanbanColumnSettings>('ptm-kanban-settings', {});
};

// 项目过滤器设置
export interface ProjectFilters {
  status: string[];
  tags: string[];
  assignee: string[];
  sortBy: 'name' | 'created' | 'updated' | 'progress';
  sortOrder: 'asc' | 'desc';
}

export const useProjectFiltersStorage = () => {
  return useLocalStorage<ProjectFilters>('ptm-project-filters', {
    status: [],
    tags: [],
    assignee: [],
    sortBy: 'updated',
    sortOrder: 'desc',
  });
};

// 任务过滤器设置
export interface TaskFilters {
  status: string[];
  priority: string[];
  assignee: string[];
  project: string[];
  tags: string[];
  sortBy: 'title' | 'created' | 'updated' | 'dueDate' | 'priority';
  sortOrder: 'asc' | 'desc';
  showCompleted: boolean;
}

export const useTaskFiltersStorage = () => {
  return useLocalStorage<TaskFilters>('ptm-task-filters', {
    status: [],
    priority: [],
    assignee: [],
    project: [],
    tags: [],
    sortBy: 'updated',
    sortOrder: 'desc',
    showCompleted: false,
  });
};

// 窗口尺寸存储（用于记住用户调整的面板大小）
export interface WindowSizes {
  sidebarWidth: number;
  rightPanelWidth: number;
}

export const useWindowSizesStorage = () => {
  return useLocalStorage<WindowSizes>('ptm-window-sizes', {
    sidebarWidth: 280,
    rightPanelWidth: 320,
  });
};